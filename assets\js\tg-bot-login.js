/**
 * Teleg<PERSON>gin JavaScript
 */
jQuery(document).ready(function($) {
    // Copy shortcode to clipboard
    $('.copy-shortcode').on('click', function() {
        const shortcode = $(this).data('shortcode');
        const tempInput = $('<input>');
        $('body').append(tempInput);
        tempInput.val(shortcode).select();
        document.execCommand('copy');
        tempInput.remove();
        
        // Show copied message
        const originalText = $(this).text();
        $(this).text('Copied!');
        setTimeout(() => {
            $(this).text(originalText);
        }, 2000);
    });
    
    // Toggle login enabled/disabled status
    $('#farmfaucet_tg_login_enabled').on('change', function() {
        const isEnabled = $(this).is(':checked');
        const statusLabel = $(this).closest('.form-group').find('.status-label');
        
        if (isEnabled) {
            statusLabel.text('Enabled');
        } else {
            statusLabel.text('Disabled');
        }
    });
    
    // Handle OTP expiration input validation
    $('#farmfaucet_tg_otp_expiration').on('input', function() {
        const value = parseInt($(this).val());
        if (isNaN(value) || value < 1) {
            $(this).val(1);
        } else if (value > 60) {
            $(this).val(60);
        }
    });
    
    // Frontend form validation for login form
    $(document).on('submit', '.farmfaucet-tg-login-form', function(e) {
        const usernameOrEmail = $(this).find('input[name="username_or_email"]').val();
        const password = $(this).find('input[name="password"]').val();
        
        if (!usernameOrEmail || !password) {
            e.preventDefault();
            showFormError($(this), 'Please fill in all required fields');
            return false;
        }
        
        return true;
    });
    
    // Frontend form validation for signup form
    $(document).on('submit', '.farmfaucet-tg-signup-form', function(e) {
        const name = $(this).find('input[name="name"]').val();
        const username = $(this).find('input[name="username"]').val();
        const email = $(this).find('input[name="email"]').val();
        const telegramNumber = $(this).find('input[name="telegram_number"]').val();
        const password = $(this).find('input[name="password"]').val();
        const confirmPassword = $(this).find('input[name="confirm_password"]').val();
        
        if (!name || !username || !email || !telegramNumber || !password || !confirmPassword) {
            e.preventDefault();
            showFormError($(this), 'Please fill in all required fields');
            return false;
        }
        
        if (password !== confirmPassword) {
            e.preventDefault();
            showFormError($(this), 'Passwords do not match');
            return false;
        }
        
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            showFormError($(this), 'Please enter a valid email address');
            return false;
        }
        
        // Validate telegram number format (should be a number)
        const telegramNumberRegex = /^\+?[0-9]{10,15}$/;
        if (!telegramNumberRegex.test(telegramNumber)) {
            e.preventDefault();
            showFormError($(this), 'Please enter a valid telegram number');
            return false;
        }
        
        return true;
    });
    
    // OTP verification form validation
    $(document).on('submit', '.farmfaucet-tg-otp-form', function(e) {
        const otpCode = $(this).find('input[name="otp_code"]').val();
        
        if (!otpCode) {
            e.preventDefault();
            showFormError($(this), 'Please enter the OTP code');
            return false;
        }
        
        return true;
    });
    
    // Helper function to show form errors
    function showFormError(form, message) {
        let errorDiv = form.find('.form-error');
        
        if (errorDiv.length === 0) {
            errorDiv = $('<div class="form-error"></div>');
            form.prepend(errorDiv);
        }
        
        errorDiv.text(message).show();
        
        // Hide error after 5 seconds
        setTimeout(() => {
            errorDiv.fadeOut();
        }, 5000);
    }
    
    // OTP timer countdown
    if ($('.otp-timer').length > 0) {
        const timerElement = $('.otp-timer');
        let timeLeft = parseInt(timerElement.data('time')) || 300; // Default 5 minutes (300 seconds)
        
        const timerInterval = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            
            timerElement.text(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
            
            if (timeLeft <= 0) {
                clearInterval(timerInterval);
                timerElement.closest('.otp-message').html('<span class="otp-expired">OTP code has expired. Please request a new one.</span>');
            }
            
            timeLeft--;
        }, 1000);
    }
});
