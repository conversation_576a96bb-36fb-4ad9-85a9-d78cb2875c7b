<?php
/**
 * Farm Faucet Database Updater - Transactions Table
 *
 * Ensures the transactions table exists for the earnings graph
 */
class Farmfaucet_DB_Updater_Transactions
{
    /**
     * Initialize the updater
     */
    public static function init()
    {
        add_action('admin_init', [__CLASS__, 'update_database']);
        add_action('wp_loaded', [__CLASS__, 'update_database']);
    }

    /**
     * Update the database structure
     */
    public static function update_database()
    {
        global $wpdb;
        $transactions_table = $wpdb->prefix . 'farmfaucet_transactions';

        // Check if the transactions table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$transactions_table}'") === $transactions_table;

        if (!$table_exists) {
            // Create the transactions table
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $transactions_table (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                user_id bigint(20) NOT NULL,
                user_hash varchar(255) NOT NULL,
                amount decimal(18,8) NOT NULL DEFAULT 0,
                currency varchar(10) NOT NULL DEFAULT 'LTC',
                type varchar(50) NOT NULL DEFAULT 'claim',
                status varchar(50) NOT NULL DEFAULT 'pending',
                message text NOT NULL,
                timestamp datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY  (id),
                KEY user_id (user_id),
                KEY user_hash (user_hash),
                KEY timestamp (timestamp)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            error_log('Farmfaucet: Created transactions table');
        }
    }
}
