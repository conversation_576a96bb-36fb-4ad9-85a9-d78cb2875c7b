/**
 * Faucet Admin Fix
 *
 * Fixes issues with the faucet admin interface, including:
 * - Faucet type selection not being properly saved
 * - Faucet name being populated with button text when editing
 */
jQuery(document).ready(function($) {
    console.log('Faucet Admin Fix loaded');

    // Fix for faucet type selection
    function initFaucetTypeSelector() {
        var $faucetType = $('#faucet-type');

        if ($faucetType.length === 0) {
            return;
        }

        console.log('Initializing faucet type selector');

        // Function to show/hide fields based on faucet type
        function toggleFaucetTypeFields() {
            var selectedType = $faucetType.val();
            console.log('Selected faucet type:', selectedType);

            // Hide all type-specific fields
            $('.faucet-type-fields').hide();

            // Show fields for the selected type
            $('.' + selectedType + '-faucet-fields').show();

            // Handle specific field visibility
            if (selectedType === 'stage') {
                // Standard faucet fields
                $('#faucet-currency').closest('.form-field').show();
                $('#faucet-amount').closest('.form-field').show();
            } else if (selectedType === 'dummy') {
                // Dummy faucet fields - uses created currencies
                $('#faucet-currency').closest('.form-field').hide();
                $('#faucet-amount').closest('.form-field').show();
            } else if (selectedType === 'withdrawal') {
                // Withdrawal faucet fields
                $('#faucet-currency').closest('.form-field').hide();
                $('#faucet-amount').closest('.form-field').hide();
            }
        }

        // Initialize on page load
        toggleFaucetTypeFields();

        // Listen for changes to the faucet type dropdown
        $faucetType.on('change', toggleFaucetTypeFields);
    }

    // Fix for faucet form submission
    function fixFaucetFormSubmission() {
        var $faucetForm = $('#faucet-form');

        if ($faucetForm.length === 0) {
            return;
        }

        console.log('Fixing faucet form submission');

        // Override the original saveFaucet function
        if (typeof window.saveFaucet === 'function') {
            var originalSaveFaucet = window.saveFaucet;

            window.saveFaucet = function() {
                var faucetId = $('#faucet-id').val();
                var isNew = faucetId === '0';

                // Get faucet type
                var faucetType = $('#faucet-type').val();
                console.log('Saving faucet with type:', faucetType);

                // Get selected captcha type
                var captchaType = $('input[name="captcha_type"]:checked').val() || '';

                // Prepare form data
                var formData = {
                    action: isNew ? 'farmfaucet_create_faucet' : 'farmfaucet_update_faucet',
                    nonce: farmfaucet_admin.nonce,
                    faucet_id: faucetId,
                    name: $('#faucet-name').val(),
                    faucet_type: faucetType,
                    currency: $('#faucet-currency').val(),
                    amount: $('#faucet-amount').val(),
                    cooldown: $('#faucet-cooldown').val(),
                    api_key: $('#faucet-api-key').val(),
                    shortcode: $('#faucet-shortcode').val(),
                    captcha_type: captchaType,
                    faucet_color: $('#faucet-color').val() || 'green',
                    transparent_bg: $('#faucet-transparent-bg').is(':checked') ? 1 : 0,
                    bg_style: $('input[name="bg_style"]:checked').val(),
                    bg_color: $('#faucet-bg-color').val(),
                    bg_gradient_start: $('#faucet-gradient-start').val(),
                    bg_gradient_end: $('#faucet-gradient-end').val(),
                    form_bg_color: $('#faucet-form-bg-color').val() || '#ffffff',
                    form_transparent: $('#faucet-form-transparent').is(':checked') ? 1 : 0,
                    text_color: $('#faucet-text-color').val(),
                    text_shadow: $('#faucet-text-shadow').val()
                };

                // Add type-specific fields
                if (faucetType === 'dummy' || faucetType === 'withdrawal') {
                    formData.currency_id = $('#' + faucetType + '-currency-id').val();
                    formData.view_style = $('#faucet-view-style').val();

                    if (faucetType === 'withdrawal') {
                        formData.min_withdrawal = $('#min-withdrawal').val();
                        formData.ads_only_conversion = $('#ads-only-conversion').is(':checked') ? 1 : 0;

                        // Get selected available currencies
                        var availableCurrencies = [];
                        $('input[name="available_currencies[]"]:checked').each(function() {
                            availableCurrencies.push($(this).val());
                        });
                        formData.available_currencies = availableCurrencies;
                    }
                }

                // Validate form
                if (!formData.name || !formData.shortcode) {
                    alert('Name and shortcode are required');
                    return;
                }

                console.log('Sending faucet data:', formData);

                // Send AJAX request
                $.ajax({
                    url: farmfaucet_admin.ajax_url,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        console.log('Faucet save response:', response);

                        if (response.success) {
                            // Close dialog
                            $('#faucet-form-dialog').dialog('close');

                            // Show success message
                            alert(isNew ? farmfaucet_admin.strings.create_success : farmfaucet_admin.strings.update_success);

                            // Reload the page to show updated faucet
                            location.reload();
                        } else {
                            alert(response.data.message || farmfaucet_admin.strings.error);
                        }
                    },
                    error: function() {
                        alert(farmfaucet_admin.strings.error);
                    }
                });
            };
        }
    }

    // Fix for faucet editing
    function fixFaucetEditing() {
        // Store the original editFaucet function
        if (typeof window.editFaucet === 'function') {
            var originalEditFaucet = window.editFaucet;

            window.editFaucet = function(faucetId) {
                console.log('Editing faucet with ID:', faucetId);

                // Call the original function
                originalEditFaucet(faucetId);

                // Add our custom logic after the original function
                $.ajax({
                    url: farmfaucet_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_get_faucet',
                        nonce: farmfaucet_admin.nonce,
                        faucet_id: faucetId
                    },
                    success: function(response) {
                        console.log('Faucet data loaded:', response);

                        if (response.success && response.data) {
                            var faucet = response.data;

                            // Fix for faucet name field getting populated with button text
                            if (faucet.name) {
                                $('#faucet-name').val(faucet.name);
                            }

                            // Set faucet type
                            if (faucet.faucet_type) {
                                $('#faucet-type').val(faucet.faucet_type).trigger('change');

                                // Disable changing faucet type when editing
                                $('#faucet-type').prop('disabled', true);
                                $('#faucet-type').after('<p class="description">' +
                                    'Faucet type cannot be changed after creation.' +
                                    '</p>');
                            }

                            // For dummy and withdrawal faucets, handle currency_id
                            if (faucet.currency_id) {
                                $('#dummy-currency-id, #withdrawal-currency-id').val(faucet.currency_id);
                            }

                            // Handle min_withdrawal for withdrawal faucets
                            if (faucet.min_withdrawal) {
                                $('#min-withdrawal').val(faucet.min_withdrawal);
                            }

                            // Handle view_style
                            if (faucet.view_style) {
                                $('#faucet-view-style').val(faucet.view_style);
                            }

                            // Handle ads_only_conversion for withdrawal faucets
                            if (faucet.ads_only_conversion) {
                                $('#ads-only-conversion').prop('checked', faucet.ads_only_conversion == 1);
                            }

                            // Handle form background color and transparency
                            if (faucet.form_bg_color) {
                                $('#faucet-form-bg-color').val(faucet.form_bg_color);
                            }

                            if (faucet.form_transparent) {
                                $('#faucet-form-transparent').prop('checked', faucet.form_transparent == 1);
                            }

                            // Update form background options visibility
                            if (typeof updateFormBgOptions === 'function') {
                                updateFormBgOptions();
                            }

                            // Handle available currencies for withdrawal faucets
                            if (faucet.available_currencies) {
                                try {
                                    var availableCurrencies = JSON.parse(faucet.available_currencies);
                                    if (Array.isArray(availableCurrencies)) {
                                        // Uncheck all first
                                        $('input[name="available_currencies[]"]').prop('checked', false);

                                        // Check the ones that are available
                                        availableCurrencies.forEach(function(currency) {
                                            $('input[name="available_currencies[]"][value="' + currency + '"]').prop('checked', true);
                                        });
                                    }
                                } catch (e) {
                                    console.error('Error parsing available currencies:', e);
                                }
                            }

                            // Trigger a custom event that other scripts can listen for
                            $(document).trigger('faucetLoaded', [faucet]);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading faucet data:', error);
                    }
                });
            };
        }
    }

    // Initialize all fixes
    initFaucetTypeSelector();
    fixFaucetFormSubmission();
    fixFaucetEditing();
});
