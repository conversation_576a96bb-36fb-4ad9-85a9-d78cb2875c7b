/* Captcha container styling - simplified to avoid layering issues */
.farmfaucet-captcha-container {
    margin: 20px auto;
    padding: 10px;
    position: relative;
    min-height: 100px;
    max-width: 320px;
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    overflow: visible;
    visibility: visible !important;
    opacity: 1 !important;
    /* Remove z-index to prevent conflicts */
}

/* Base captcha styling */
.farmfaucet-captcha {
    margin: 0 auto;
    display: block;
    min-height: 78px;
    width: 100%;
    max-width: 300px;
    position: relative;
    overflow: visible;
}

/* Fix for hCaptcha, reCAPTCHA, and Turnstile specific elements */
.h-captcha > div,
.g-recaptcha > div,
.cf-turnstile > div {
    margin: 0 auto !important;
    display: block !important;
}

/* Fix for captcha wrappers - simplified */
.h-captcha-wrapper,
.g-recaptcha-wrapper,
.cf-turnstile-wrapper {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 78px !important;
    width: 100% !important;
    max-width: 300px !important;
    margin: 0 auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Make sure the iframe is visible without excessive z-index */
.farmfaucet-captcha iframe {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 0 auto;
}

/* Add a border to make the captcha area more visible - simplified */
.h-captcha, .g-recaptcha, .cf-turnstile {
    padding: 5px;
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    width: 100% !important;
    max-width: 300px !important;
    margin: 0 auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 78px !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Loading message styling */
.farmfaucet-captcha-loading {
    display: block;
    text-align: center;
    font-style: italic;
    color: #666;
    padding: 10px;
    margin: 10px 0;
    background-color: #f0f0f0;
    border-radius: 4px;
    position: relative;
    z-index: 5;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

/* Ensure only one loading message is shown */
.farmfaucet-captcha-container .farmfaucet-captcha-loading:not(:first-of-type) {
    display: none;
}

/* Error message styling */
.farmfaucet-error {
    color: #d63638;
    background-color: #ffeeee;
    border: 1px solid #ffcccc;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

/* Make sure the captcha is centered */
.h-captcha > div,
.g-recaptcha > div,
.cf-turnstile > div {
    margin: 0 auto !important;
}

/* Fix for invisible captcha */
.grecaptcha-badge {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Special fix for reCAPTCHA */
.g-recaptcha {
    transform: scale(0.95);
    transform-origin: center;
    margin: 0 auto;
}

/* Fix for reCAPTCHA iframe container */
div.g-recaptcha > div {
    margin: 0 auto !important;
    width: 302px !important;
    height: 76px !important;
    overflow: hidden !important;
    position: relative !important;
    display: block !important;
}

/* Fix for captcha iframe - simplified to prevent layering issues */
iframe[src*="hcaptcha.com"],
iframe[src*="google.com/recaptcha"],
iframe[src*="challenges.cloudflare.com"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 78px !important;
    width: 100% !important;
    max-width: 300px !important;
    margin: 0 auto !important;
    position: relative !important;
    transform: scale(1) !important;
    transform-origin: center !important;
    z-index: 10 !important;
}

/* Ensure captcha container is visible during initialization */
.farmfaucet-captcha.initializing {
    border: 1px dashed #ccc;
    min-height: 78px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for multiple layers issue */
.h-captcha iframe,
.g-recaptcha iframe,
.cf-turnstile iframe {
    z-index: 10 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for captcha badge */
.grecaptcha-badge,
.hcaptcha-badge,
.cf-turnstile-badge {
    z-index: 1 !important;
    position: fixed !important;
    bottom: 0 !important;
    right: 0 !important;
}

/* Fix for captcha popup */
div[style*="z-index: 2000000000"],
div[style*="z-index: 2147483647"],
div[style*="z-index: 100000"] {
    z-index: 9999999 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure claim button has rounded borders */
.farmfaucet-claim-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 25px !important; /* Force rounded borders */
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    margin-top: 20px;
}

.farmfaucet-claim-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
    border-radius: 25px !important; /* Force rounded borders even when disabled */
}

/* Notification styling */
.farmfaucet-notification {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
    display: none;
}

.farmfaucet-notification.success {
    background-color: #eeffee;
    border: 1px solid #ccffcc;
    color: #00a32a;
}

.farmfaucet-notification.error {
    background-color: #ffeeee;
    border: 1px solid #ffcccc;
    color: #d63638;
}
