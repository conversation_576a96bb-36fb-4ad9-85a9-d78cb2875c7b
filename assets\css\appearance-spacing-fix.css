/**
 * Farm Faucet Appearance Spacing Fix
 * 
 * This CSS file fixes overlapping issues between container background, 
 * form background, and border colors when transparent background is not set
 */

/* Base container styling with proper spacing */
.farmfaucet-container {
    position: relative;
    margin: 20px auto;
    padding: 0;
    max-width: 500px;
    border-radius: var(--border-radius, 8px);
    overflow: hidden;
    box-sizing: border-box;
}

/* Container background layer */
.farmfaucet-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-color, #f8fff8);
    background: var(--bg-style, solid) === 'gradient' 
        ? linear-gradient(135deg, var(--bg-gradient-start, #f8fff8), var(--bg-gradient-end, #e8f5e9))
        : var(--bg-color, #f8fff8);
    border-radius: var(--border-radius, 8px);
    z-index: 1;
}

/* Form wrapper with proper spacing */
.farmfaucet-form {
    position: relative;
    z-index: 2;
    margin: 15px;
    padding: 20px;
    background-color: var(--form-bg-color, #ffffff);
    border-radius: calc(var(--border-radius, 8px) - 4px);
    border: 2px solid var(--border-color, #4CAF50);
    box-sizing: border-box;
}

/* When form transparency is enabled, remove form background but keep spacing */
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form {
    background-color: transparent !important;
    border: none !important;
    margin: 15px;
    padding: 20px;
}

/* When container transparency is enabled, remove container background */
.farmfaucet-container[data-transparent-bg="1"]::before {
    display: none;
}

.farmfaucet-container[data-transparent-bg="1"] {
    background-color: transparent !important;
}

/* Form inner content spacing */
.farmfaucet-form-inner {
    position: relative;
    z-index: 3;
}

/* Input field styling with proper spacing */
.farmfaucet-form .form-group {
    margin-bottom: 15px;
}

.farmfaucet-form .form-group:last-child {
    margin-bottom: 0;
}

.farmfaucet-form label {
    display: block;
    margin-bottom: 5px;
    color: var(--input-label-color, #333333);
    font-weight: 500;
}

.farmfaucet-form input,
.farmfaucet-form select,
.farmfaucet-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color, #4CAF50);
    border-radius: var(--button-border-radius, 25px);
    background-color: var(--form-bg-color, #ffffff);
    color: var(--input-label-color, #333333);
    box-sizing: border-box;
    font-size: 14px;
}

.farmfaucet-form input::placeholder {
    color: var(--input-placeholder-color, #999999);
}

/* Button styling with proper spacing */
.farmfaucet-form button {
    width: 100%;
    padding: 12px 20px;
    background-color: var(--button-color, #4CAF50);
    color: white;
    border: 2px solid var(--border-color, #4CAF50);
    border-radius: var(--button-border-radius, 25px);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-sizing: border-box;
}

.farmfaucet-form button:hover {
    background-color: var(--button-hover-color, #45a049);
    border-color: var(--button-hover-color, #45a049);
    transform: translateY(-1px);
}

/* Captcha container spacing */
.farmfaucet-captcha-container {
    margin: 15px 0;
    text-align: center;
}

/* Notice elements spacing */
.farmfaucet-form .reward-notice,
.farmfaucet-form .balance-notice,
.farmfaucet-form .min-withdrawal-notice,
.farmfaucet-form .min-conversion-notice {
    margin: 10px 0;
    padding: 8px 12px;
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid var(--border-color, #4CAF50);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color, #4CAF50);
}

/* Conversion faucet specific spacing */
.conversion-faucet .conversion-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
    align-items: center;
    justify-content: space-between;
}

.conversion-faucet .conversion-from,
.conversion-faucet .conversion-to {
    flex: 1;
    min-width: 200px;
    position: relative;
}

.conversion-faucet .conversion-arrow {
    flex: 0 0 auto;
    font-size: 24px;
    color: var(--text-color, #4CAF50);
    margin: 0 10px;
}

/* Withdrawal faucet specific spacing */
.withdrawal-faucet .withdrawal-amount-container {
    margin: 15px 0;
}

.withdrawal-faucet .conversion-currency-container {
    margin: 15px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .farmfaucet-container {
        margin: 15px;
        max-width: none;
    }
    
    .farmfaucet-form {
        margin: 10px;
        padding: 15px;
    }
    
    .conversion-faucet .conversion-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .conversion-faucet .conversion-from,
    .conversion-faucet .conversion-to {
        min-width: 100%;
    }
    
    .conversion-faucet .conversion-arrow {
        transform: rotate(90deg);
        margin: 5px 0;
    }
}

/* Specific fixes for different faucet types */
.stage-faucet .farmfaucet-form,
.dummy-faucet .farmfaucet-form,
.withdrawal-faucet .farmfaucet-form,
.conversion-faucet .farmfaucet-form {
    /* Ensure consistent spacing across all faucet types */
    margin: 15px;
    padding: 20px;
}

/* Fix for overlapping borders when transparency is disabled */
.farmfaucet-container:not([data-form-transparent="1"]):not([data-transparent-bg="1"]) .farmfaucet-form {
    /* Add extra margin to prevent overlap with container background */
    margin: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Fix for text color visibility */
.farmfaucet-form .form-group label,
.farmfaucet-form .reward-notice,
.farmfaucet-form .balance-notice {
    color: var(--text-color, #4CAF50);
}

/* Ensure proper z-index stacking */
.farmfaucet-container {
    z-index: 1;
}

.farmfaucet-form {
    z-index: 2;
}

.farmfaucet-form-inner {
    z-index: 3;
}

.farmfaucet-captcha-container {
    z-index: 4;
}

/* Fix for dropdown transparency when form is not transparent */
.farmfaucet-container:not([data-form-transparent="1"]) select {
    background-color: var(--form-bg-color, #ffffff) !important;
    color: var(--input-label-color, #333333) !important;
}

.farmfaucet-container:not([data-form-transparent="1"]) select option {
    background-color: var(--form-bg-color, #ffffff) !important;
    color: var(--input-label-color, #333333) !important;
}

/* Fix for input field backgrounds when form is not transparent */
.farmfaucet-container:not([data-form-transparent="1"]) input,
.farmfaucet-container:not([data-form-transparent="1"]) textarea {
    background-color: var(--form-bg-color, #ffffff) !important;
    color: var(--input-label-color, #333333) !important;
}

/* Ensure buttons maintain their styling */
.farmfaucet-form button {
    background-color: var(--button-color, #4CAF50) !important;
    border-color: var(--border-color, #4CAF50) !important;
    color: white !important;
}

/* Fix for notification elements */
.farmfaucet-notification {
    z-index: 9999;
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
}
