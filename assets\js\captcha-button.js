/**
 * Farm Faucet - Captcha Button JavaScript
 *
 * Handles the captcha verification for countdown buttons
 */

(function($) {
    'use strict';

    // Global callback function for captcha verification
    window.farmfaucetCountdownCaptchaCallback = function(token) {
        console.log("Captcha callback triggered with token: " + (token ? token.substring(0, 10) + "..." : "empty"));
        
        // Find all captcha wrappers
        $('.farmfaucet-captcha-wrapper').each(function() {
            var $captchaWrapper = $(this);
            var $mainButtonWrapper = $captchaWrapper.siblings('.farmfaucet-main-button-wrapper');
            
            if ($mainButtonWrapper.length) {
                // Hide captcha, show main button
                $captchaWrapper.hide();
                $mainButtonWrapper.show();
                
                // Add animation class to button
                $mainButtonWrapper.find('.farmfaucet-button').addClass('farmfaucet-button-appear');
            }
        });
    };
    
    // Initialize captcha buttons when the document is ready
    $(document).ready(function() {
        // Find all captcha elements and initialize them
        $('.farmfaucet-captcha').each(function() {
            var $captcha = $(this);
            var captchaId = $captcha.attr('id');
            var captchaType = $captcha.hasClass('h-captcha') ? 'hcaptcha' : 
                             ($captcha.hasClass('g-recaptcha') ? 'recaptcha' : 'turnstile');
            var sitekey = $captcha.data('sitekey');
            
            // Initialize the captcha based on type
            try {
                if (captchaType === 'hcaptcha' && typeof hcaptcha !== 'undefined') {
                    hcaptcha.render(captchaId, {
                        sitekey: sitekey,
                        callback: window.farmfaucetCountdownCaptchaCallback
                    });
                } else if (captchaType === 'recaptcha' && typeof grecaptcha !== 'undefined') {
                    grecaptcha.render(captchaId, {
                        sitekey: sitekey,
                        callback: window.farmfaucetCountdownCaptchaCallback
                    });
                } else if (captchaType === 'turnstile' && typeof turnstile !== 'undefined') {
                    turnstile.render(captchaId, {
                        sitekey: sitekey,
                        callback: window.farmfaucetCountdownCaptchaCallback
                    });
                }
            } catch (e) {
                console.error("Error rendering captcha:", e);
            }
        });
    });

})(jQuery);
