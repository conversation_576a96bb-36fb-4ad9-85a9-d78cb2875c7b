<?php
/**
 * Farm Faucet - Simple Settings Test
 * 
 * This bypasses all plugin complexity and tests basic settings save
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load WordPress
if (!defined('ABSPATH')) {
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

// COMPLETELY BYPASS the plugin and test raw WordPress settings
function test_raw_wordpress_settings() {
    // Register a simple test setting
    register_setting('test_group', 'test_setting', [
        'sanitize_callback' => 'sanitize_text_field'
    ]);
    
    add_settings_section(
        'test_section',
        'Test Section',
        function() { echo '<p>Test section description</p>'; },
        'test_page'
    );
    
    add_settings_field(
        'test_field',
        'Test Field',
        function() {
            $value = get_option('test_setting', '');
            echo '<input type="text" name="test_setting" value="' . esc_attr($value) . '" />';
        },
        'test_page',
        'test_section'
    );
}

// Hook into admin_init
add_action('admin_init', 'test_raw_wordpress_settings');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Settings Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Simple Settings Test</h1>
    <p>This tests WordPress settings API without any Farm Faucet complexity.</p>

    <div class="info">
        <h3>Test 1: Raw WordPress Settings Form</h3>
        <p>This form uses pure WordPress settings API with no Farm Faucet code involved.</p>
        
        <form method="post" action="options.php">
            <?php 
            try {
                settings_fields('test_group');
                echo '<div class="success">✅ settings_fields() worked</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ settings_fields() failed: ' . $e->getMessage() . '</div>';
            }
            ?>
            
            <table>
                <tr>
                    <th>Test Setting:</th>
                    <td><input type="text" name="test_setting" value="<?php echo esc_attr(get_option('test_setting', '')); ?>" /></td>
                </tr>
            </table>
            
            <?php submit_button('Save Test Setting'); ?>
        </form>
    </div>

    <div class="info">
        <h3>Test 2: Direct Option Update</h3>
        
        <?php if (isset($_POST['direct_test'])): ?>
            <?php
            $test_value = 'direct_test_' . time();
            $result = update_option('farmfaucet_direct_test', $test_value);
            
            if ($result) {
                echo '<div class="success">✅ Direct option update successful: ' . esc_html($test_value) . '</div>';
                
                // Verify
                $saved = get_option('farmfaucet_direct_test');
                if ($saved === $test_value) {
                    echo '<div class="success">✅ Value verified: ' . esc_html($saved) . '</div>';
                } else {
                    echo '<div class="error">❌ Value mismatch: ' . esc_html($saved) . '</div>';
                }
                
                // Clean up
                delete_option('farmfaucet_direct_test');
            } else {
                echo '<div class="error">❌ Direct option update failed</div>';
            }
            ?>
        <?php else: ?>
            <form method="post">
                <p>Test direct WordPress option update (bypassing settings API):</p>
                <input type="submit" name="direct_test" value="Test Direct Update" class="btn">
            </form>
        <?php endif; ?>
    </div>

    <div class="info">
        <h3>Test 3: Farm Faucet Settings Registration Check</h3>
        
        <?php
        // Check what's actually registered
        $registered = get_registered_settings();
        $farmfaucet_settings = [];
        
        foreach ($registered as $setting => $data) {
            if (strpos($setting, 'farmfaucet_') === 0) {
                $farmfaucet_settings[$setting] = $data;
            }
        }
        
        if (!empty($farmfaucet_settings)) {
            echo '<div class="success">✅ Found ' . count($farmfaucet_settings) . ' Farm Faucet settings registered</div>';
            
            echo '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';
            echo '<tr><th style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;">Setting</th><th style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;">Group</th><th style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;">Callback</th></tr>';
            
            foreach ($farmfaucet_settings as $setting => $data) {
                $group = isset($data['group']) ? $data['group'] : 'unknown';
                $callback = isset($data['sanitize_callback']) ? 
                    (is_array($data['sanitize_callback']) ? 
                        implode('::', $data['sanitize_callback']) : 
                        $data['sanitize_callback']) : 
                    'none';
                
                echo '<tr>';
                echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($setting) . '</td>';
                echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($group) . '</td>';
                echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($callback) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // Check for conflicts
            $groups = [];
            foreach ($farmfaucet_settings as $setting => $data) {
                $group = isset($data['group']) ? $data['group'] : 'unknown';
                if (!isset($groups[$group])) {
                    $groups[$group] = [];
                }
                $groups[$group][] = $setting;
            }
            
            echo '<h4>Settings by Group:</h4>';
            foreach ($groups as $group => $settings) {
                echo '<div class="info">';
                echo '<strong>' . esc_html($group) . '</strong> (' . count($settings) . ' settings)';
                if ($group === 'farmfaucet_settings' && count($settings) > 10) {
                    echo ' <span style="color: red;">⚠️ Too many settings in main group!</span>';
                }
                echo '</div>';
            }
            
        } else {
            echo '<div class="error">❌ No Farm Faucet settings found registered</div>';
        }
        ?>
    </div>

    <div class="info">
        <h3>Test 4: Minimal Farm Faucet Settings Form</h3>
        <p><strong>WARNING:</strong> This will attempt to save using Farm Faucet's settings group. If this fails, we know the issue is with the group registration.</p>
        
        <form method="post" action="options.php">
            <?php 
            try {
                settings_fields('farmfaucet_settings');
                echo '<div class="success">✅ Farm Faucet settings_fields() worked</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ Farm Faucet settings_fields() failed: ' . $e->getMessage() . '</div>';
            } catch (Error $e) {
                echo '<div class="error">❌ Farm Faucet settings_fields() fatal error: ' . $e->getMessage() . '</div>';
            }
            ?>
            
            <table>
                <tr>
                    <th>Captcha Type:</th>
                    <td>
                        <select name="farmfaucet_captcha_type">
                            <option value="hcaptcha" <?php selected(get_option('farmfaucet_captcha_type'), 'hcaptcha'); ?>>hCaptcha</option>
                            <option value="recaptcha" <?php selected(get_option('farmfaucet_captcha_type'), 'recaptcha'); ?>>reCAPTCHA</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Site Key:</th>
                    <td><input type="text" name="farmfaucet_hcaptcha_sitekey" value="<?php echo esc_attr(get_option('farmfaucet_hcaptcha_sitekey', '')); ?>" /></td>
                </tr>
            </table>
            
            <?php submit_button('Save Farm Faucet Settings (This might fail)'); ?>
        </form>
    </div>

    <div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f44336;">
        <h3 style="color: #c62828; margin-top: 0;">🚨 DEBUGGING STRATEGY</h3>
        <ol style="color: #c62828;">
            <li><strong>Test 1 should work</strong> - it's pure WordPress with no Farm Faucet code</li>
            <li><strong>Test 2 should work</strong> - it bypasses the settings API entirely</li>
            <li><strong>Test 3 shows what's registered</strong> - look for conflicts or missing registrations</li>
            <li><strong>Test 4 will likely fail</strong> - this is where we'll see the exact error</li>
        </ol>
        <p style="color: #c62828; margin-bottom: 0;"><strong>Run each test and tell me which ones work and which fail!</strong></p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Go to Farm Faucet Settings</a>
    </div>

</body>
</html>
