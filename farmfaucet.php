<?php

/**
 * Plugin Name: Farmfaucet
 * Plugin URI: https://example.com/farmfaucet
 * Description: Instant cryptocurrency faucet with FaucetPay integration
 * Version: 2.1
 * Author: ZpromoterZ
 * Author URI: https://example.com
 * License: Proprietary
 * Text Domain: farmfaucet
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Ensure WordPress functions are available
if (!function_exists('add_action') || !function_exists('plugin_dir_path')) {
    echo 'This plugin requires WordPress to function properly.';
    exit;
}

// Prevent the plugin from being loaded twice
if (defined('FARMFAUCET_VERSION') || defined('FARMFAUCET_PLUGIN_FILE')) {
    return;
}

// Wrap everything in a function to ensure WordPress is fully loaded
function farmfaucet_plugin_init()
{
    try {

        // Define plugin constants
        define('FARMFAUCET_VERSION', '2.1');
        define('FARMFAUCET_PLUGIN_FILE', __FILE__);

        // Define paths without using WordPress functions
        if (!defined('FARMFAUCET_DIR')) {
            define('FARMFAUCET_DIR', dirname(__FILE__) . '/');
        }
        if (!defined('FARMFAUCET_URL')) {
            // Get the URL from the WordPress functions if available
            if (function_exists('plugins_url')) {
                define('FARMFAUCET_URL', plugins_url('', __FILE__) . '/');
            } else {
                // Fallback to a relative path
                if (defined('WP_PLUGIN_URL')) {
                    define('FARMFAUCET_URL', WP_PLUGIN_URL . '/' . basename(dirname(__FILE__)) . '/');
                } else {
                    // Ultimate fallback
                    define('FARMFAUCET_URL', '/wp-content/plugins/' . basename(dirname(__FILE__)) . '/');
                }
            }
        }
        if (!defined('FARMFAUCET_BASENAME')) {
            // Get the basename from the WordPress functions if available
            if (function_exists('plugin_basename')) {
                define('FARMFAUCET_BASENAME', plugin_basename(__FILE__));
            } else {
                // Fallback to a simple basename
                define('FARMFAUCET_BASENAME', basename(dirname(__FILE__)) . '/' . basename(__FILE__));
            }
        }

        // Load required files with error handling
        $required_files = [
            'includes/class-farmfaucet-security.php',
            'includes/class-farmfaucet-settings-manager.php', // Load settings manager first
            'includes/class-farmfaucet-admin.php',
            'includes/class-farmfaucet-frontend.php',
            'includes/class-farmfaucet-api.php',
            'includes/class-farmfaucet-logger.php',
            'includes/class-farmfaucet-compatibility.php',
            'includes/class-farmfaucet-safe-loader.php',
            'includes/class-farmfaucet-scheduler.php',
            'includes/class-farmfaucet-installer.php',
            'includes/class-farmfaucet-users.php',
            'includes/class-farmfaucet-db-updater.php',
            'includes/class-farmfaucet-task-completion.php',
            'includes/class-farmfaucet-captcha-handler.php',
            'includes/class-farmfaucet-tg-bot-login.php',
            'includes/class-farmfaucet-tg-bot-login-shortcodes.php',
            'includes/class-farmfaucet-tg-bot-builder.php',
            'includes/class-farmfaucet-tg-bot-db.php',
            'includes/class-farmfaucet-tg-bot-ajax.php',
            'includes/class-farmfaucet-dashboard.php',
            'includes/class-farmfaucet-currency-maker.php',
            'includes/class-farmfaucet-exchange-rates.php',
            'includes/class-farmfaucet-referral.php',
            'includes/class-farmfaucet-advertising.php',
            'includes/class-farmfaucet-conversion.php'
        ];

        foreach ($required_files as $file) {
            try {
                $full_path = FARMFAUCET_DIR . $file;
                if (file_exists($full_path)) {
                    require_once $full_path;
                } else {
                    error_log("Farmfaucet required file not found: $full_path");
                }
            } catch (Exception $e) {
                error_log("Farmfaucet error loading file $file: " . $e->getMessage());
            } catch (Error $e) {
                error_log("Farmfaucet fatal error loading file $file: " . $e->getMessage());
            }
        }

        // Check if the plugin is properly installed
        try {
            if (class_exists('Farmfaucet_Installer')) {
                Farmfaucet_Installer::check_and_install();
            } else {
                error_log('Farmfaucet_Installer class not found');
            }
        } catch (Exception $e) {
            error_log('Farmfaucet installation check error: ' . $e->getMessage());
        } catch (Error $e) {
            error_log('Farmfaucet installation check fatal error: ' . $e->getMessage());
        }

        // Register activation/deactivation hooks if WordPress functions are available
        if (function_exists('register_activation_hook')) {
            register_activation_hook(__FILE__, function () {
                try {
                    // Make sure all required classes are loaded
                    require_once plugin_dir_path(__FILE__) . 'includes/class-farmfaucet-security.php';
                    require_once plugin_dir_path(__FILE__) . 'includes/class-farmfaucet-logger.php';
                    require_once plugin_dir_path(__FILE__) . 'includes/class-farmfaucet-api.php';

                    // Activate the plugin
                    Farmfaucet_Security::activate_plugin();
                } catch (Exception $e) {
                    error_log('Farmfaucet activation error: ' . $e->getMessage());
                }
            });
        }
        if (function_exists('register_deactivation_hook')) {
            register_deactivation_hook(__FILE__, function () {
                try {
                    // Make sure all required classes are loaded
                    require_once plugin_dir_path(__FILE__) . 'includes/class-farmfaucet-security.php';

                    // Deactivate the plugin
                    Farmfaucet_Security::deactivate_plugin();
                } catch (Exception $e) {
                    error_log('Farmfaucet deactivation error: ' . $e->getMessage());
                }
            });
        }

        // Initialize plugin components when WordPress is loaded
        if (function_exists('add_action')) {
            add_action('plugins_loaded', 'farmfaucet_init_plugin');
        } else {
            // If WordPress functions aren't available, call the init function directly
            farmfaucet_init_plugin();
        }

        function farmfaucet_init_plugin()
        {
            try {
                // Check compatibility
                try {
                    $is_compatible = false;
                    if (class_exists('Farmfaucet_Compatibility')) {
                        $is_compatible = Farmfaucet_Compatibility::is_wp_compatible() && Farmfaucet_Compatibility::is_php_compatible();
                    } else {
                        error_log('Farmfaucet_Compatibility class not found');
                        // Default to compatible if we can't check
                        $is_compatible = true;
                    }

                    if ($is_compatible) {
                        // Load translations if WordPress functions are available
                        if (function_exists('load_plugin_textdomain') && function_exists('plugin_basename')) {
                            load_plugin_textdomain(
                                'farmfaucet',
                                false,
                                dirname(plugin_basename(__FILE__)) . '/languages/'
                            );
                        }

                        // Fix potential conflicts
                        try {
                            Farmfaucet_Compatibility::fix_conflicts();
                        } catch (Exception $e) {
                            error_log('Farmfaucet conflict fix error: ' . $e->getMessage());
                        }

                        // Initialize settings manager first
                        try {
                            if (class_exists('Farmfaucet_Settings_Manager')) {
                                Farmfaucet_Settings_Manager::init();
                            } else {
                                error_log('Farmfaucet_Settings_Manager class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet settings manager initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet settings manager initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize classes with error handling
                        try {
                            if (class_exists('Farmfaucet_Admin')) {
                                Farmfaucet_Admin::init();
                            } else {
                                error_log('Farmfaucet_Admin class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet admin initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet admin initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize the database updater
                        try {
                            if (class_exists('Farmfaucet_DB_Updater')) {
                                // Run database updates
                                Farmfaucet_DB_Updater::run_updates(true);
                            } else {
                                error_log('Farmfaucet_DB_Updater class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet database updater initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet database updater initialization fatal error: ' . $e->getMessage());
                        }

                        try {
                            if (class_exists('Farmfaucet_Logger')) {
                                Farmfaucet_Logger::init();
                            } else {
                                error_log('Farmfaucet_Logger class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet logger initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet logger initialization fatal error: ' . $e->getMessage());
                        }

                        try {
                            if (class_exists('Farmfaucet_Scheduler')) {
                                Farmfaucet_Scheduler::init();
                            } else {
                                error_log('Farmfaucet_Scheduler class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet scheduler initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet scheduler initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize task completion class
                        try {
                            if (class_exists('Farmfaucet_Task_Completion')) {
                                Farmfaucet_Task_Completion::init();
                            } else {
                                error_log('Farmfaucet_Task_Completion class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet task completion initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet task completion initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Telegram Bot Login class
                        try {
                            if (class_exists('Farmfaucet_Tg_Bot_Login')) {
                                Farmfaucet_Tg_Bot_Login::init();
                            } else {
                                error_log('Farmfaucet_Tg_Bot_Login class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Telegram Bot Login initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Telegram Bot Login initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Telegram Bot Login Shortcodes
                        try {
                            if (class_exists('Farmfaucet_Tg_Bot_Login_Shortcodes')) {
                                Farmfaucet_Tg_Bot_Login_Shortcodes::init();
                            } else {
                                error_log('Farmfaucet_Tg_Bot_Login_Shortcodes class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Telegram Bot Login Shortcodes initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Telegram Bot Login Shortcodes initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Telegram Bot Builder class
                        try {
                            if (class_exists('Farmfaucet_Tg_Bot_Builder')) {
                                Farmfaucet_Tg_Bot_Builder::init();
                            } else {
                                error_log('Farmfaucet_Tg_Bot_Builder class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Telegram Bot Builder initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Telegram Bot Builder initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Telegram Bot AJAX Handler
                        try {
                            if (class_exists('Farmfaucet_Tg_Bot_AJAX')) {
                                Farmfaucet_Tg_Bot_AJAX::init();
                            } else {
                                error_log('Farmfaucet_Tg_Bot_AJAX class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Telegram Bot AJAX initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Telegram Bot AJAX initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Dashboard class
                        try {
                            if (class_exists('Farmfaucet_Dashboard')) {
                                Farmfaucet_Dashboard::init();
                            } else {
                                error_log('Farmfaucet_Dashboard class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Dashboard initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Dashboard initialization fatal error: ' . $e->getMessage());
                        }

                        // Deposit/Withdraw class has been removed

                        // Initialize Currency Maker class
                        try {
                            if (class_exists('Farmfaucet_Currency_Maker')) {
                                Farmfaucet_Currency_Maker::init();
                            } else {
                                error_log('Farmfaucet_Currency_Maker class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Currency Maker initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Currency Maker initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Exchange Rates class
                        try {
                            if (class_exists('Farmfaucet_Exchange_Rates')) {
                                Farmfaucet_Exchange_Rates::init();
                            } else {
                                error_log('Farmfaucet_Exchange_Rates class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Exchange Rates initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Exchange Rates initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Referral class
                        try {
                            if (class_exists('Farmfaucet_Referral')) {
                                Farmfaucet_Referral::init();
                            } else {
                                error_log('Farmfaucet_Referral class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Referral initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Referral initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Advertising class
                        try {
                            if (class_exists('Farmfaucet_Advertising')) {
                                Farmfaucet_Advertising::init();
                            } else {
                                error_log('Farmfaucet_Advertising class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Advertising initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Advertising initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Conversion class
                        try {
                            if (class_exists('Farmfaucet_Conversion')) {
                                Farmfaucet_Conversion::init();
                            } else {
                                error_log('Farmfaucet_Conversion class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Conversion initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Conversion initialization fatal error: ' . $e->getMessage());
                        }

                        // Initialize Captcha Handler class
                        try {
                            if (class_exists('Farmfaucet_Captcha_Handler')) {
                                Farmfaucet_Captcha_Handler::init();
                            } else {
                                error_log('Farmfaucet_Captcha_Handler class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet Captcha Handler initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet Captcha Handler initialization fatal error: ' . $e->getMessage());
                        }

                        // Use the safe loader for frontend functionality
                        try {
                            if (class_exists('Farmfaucet_Safe_Loader')) {
                                Farmfaucet_Safe_Loader::init();
                            } else {
                                error_log('Farmfaucet_Safe_Loader class not found');
                            }
                        } catch (Exception $e) {
                            error_log('Farmfaucet safe loader initialization error: ' . $e->getMessage());
                        } catch (Error $e) {
                            error_log('Farmfaucet safe loader initialization fatal error: ' . $e->getMessage());
                        }

                        // Listen for settings changes to update scheduler if WordPress functions are available
                        if (function_exists('add_action')) {
                            add_action('update_option_farmfaucet_daily_reset', ['Farmfaucet_Scheduler', 'update_scheduler']);
                            add_action('update_option_farmfaucet_leaderboard_reset_date', ['Farmfaucet_Scheduler', 'update_scheduler']);
                        }
                    }
                } catch (Exception $e) {
                    error_log('Farmfaucet compatibility check error: ' . $e->getMessage());
                } catch (Error $e) {
                    error_log('Farmfaucet compatibility check fatal error: ' . $e->getMessage());
                }

                // Display compatibility notices
                try {
                    if (class_exists('Farmfaucet_Compatibility')) {
                        Farmfaucet_Compatibility::display_compatibility_notices();
                    }
                } catch (Exception $e) {
                    error_log('Farmfaucet compatibility notices error: ' . $e->getMessage());
                } catch (Error $e) {
                    error_log('Farmfaucet compatibility notices fatal error: ' . $e->getMessage());
                }
            } catch (Exception $e) {
                // Log the error but don't break the site
                error_log('Farmfaucet initialization error: ' . $e->getMessage());
            }
        }
    } catch (Exception $e) {
        // Log the error but don't break the site
        error_log('Farmfaucet fatal error: ' . $e->getMessage());

        // If we're in the admin, show a friendly message
        if (function_exists('is_admin') && is_admin()) {
            add_action('admin_notices', function () use ($e) {
                echo '<div class="error"><p><strong>Farm Faucet Error:</strong> ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }
} // End of farmfaucet_plugin_init function

// Call the initialization function with error handling
try {
    farmfaucet_plugin_init();
} catch (Exception $e) {
    // Log the error but don't break the site
    error_log('Farmfaucet initialization error: ' . $e->getMessage());

    // If we're in the admin, show a friendly message
    if (function_exists('is_admin') && is_admin()) {
        add_action('admin_notices', function () use ($e) {
            echo '<div class="error"><p><strong>Farm Faucet Error:</strong> ' . esc_html($e->getMessage()) . '</p></div>';
        });
    }
}
