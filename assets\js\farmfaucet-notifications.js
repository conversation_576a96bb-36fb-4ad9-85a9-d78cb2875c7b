/**
 * Farm Faucet Interactive Notification System
 * 
 * Provides slide-in notifications for all faucet operations
 */

(function($) {
    'use strict';

    // Notification system object
    window.FarmfaucetNotifications = {
        
        // Initialize the notification system
        init: function() {
            this.createNotificationContainer();
            this.bindEvents();
        },

        // Create the notification container
        createNotificationContainer: function() {
            if ($('#farmfaucet-notifications').length === 0) {
                $('body').append(`
                    <div id="farmfaucet-notifications" class="farmfaucet-notifications-container">
                        <!-- Notifications will be added here -->
                    </div>
                `);
            }
        },

        // Show a notification
        show: function(message, type, duration) {
            type = type || 'info';
            duration = duration || 5000;

            const notificationId = 'notification-' + Date.now();
            const iconMap = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️',
                'loading': '⏳'
            };

            const notification = $(`
                <div id="${notificationId}" class="farmfaucet-notification farmfaucet-notification-${type}">
                    <div class="farmfaucet-notification-content">
                        <span class="farmfaucet-notification-icon">${iconMap[type] || iconMap.info}</span>
                        <span class="farmfaucet-notification-message">${message}</span>
                        <button class="farmfaucet-notification-close" aria-label="Close notification">&times;</button>
                    </div>
                    <div class="farmfaucet-notification-progress"></div>
                </div>
            `);

            $('#farmfaucet-notifications').append(notification);

            // Animate in
            setTimeout(() => {
                notification.addClass('farmfaucet-notification-show');
            }, 100);

            // Auto-hide after duration (except for loading notifications)
            if (type !== 'loading' && duration > 0) {
                setTimeout(() => {
                    this.hide(notificationId);
                }, duration);

                // Progress bar animation
                notification.find('.farmfaucet-notification-progress').css({
                    'animation': `farmfaucet-progress ${duration}ms linear forwards`
                });
            }

            return notificationId;
        },

        // Hide a notification
        hide: function(notificationId) {
            const notification = $('#' + notificationId);
            if (notification.length) {
                notification.removeClass('farmfaucet-notification-show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        },

        // Update an existing notification
        update: function(notificationId, message, type) {
            const notification = $('#' + notificationId);
            if (notification.length) {
                const iconMap = {
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️',
                    'info': 'ℹ️',
                    'loading': '⏳'
                };

                notification.removeClass('farmfaucet-notification-success farmfaucet-notification-error farmfaucet-notification-warning farmfaucet-notification-info farmfaucet-notification-loading');
                notification.addClass('farmfaucet-notification-' + type);
                notification.find('.farmfaucet-notification-icon').text(iconMap[type] || iconMap.info);
                notification.find('.farmfaucet-notification-message').html(message);

                // If changing from loading to another type, start auto-hide
                if (type !== 'loading') {
                    setTimeout(() => {
                        this.hide(notificationId);
                    }, 5000);

                    notification.find('.farmfaucet-notification-progress').css({
                        'animation': 'farmfaucet-progress 5000ms linear forwards'
                    });
                }
            }
        },

        // Show success notification
        success: function(message, duration) {
            return this.show(message, 'success', duration);
        },

        // Show error notification
        error: function(message, duration) {
            return this.show(message, 'error', duration || 8000);
        },

        // Show warning notification
        warning: function(message, duration) {
            return this.show(message, 'warning', duration);
        },

        // Show info notification
        info: function(message, duration) {
            return this.show(message, 'info', duration);
        },

        // Show loading notification
        loading: function(message) {
            return this.show(message, 'loading', 0);
        },

        // Clear all notifications
        clearAll: function() {
            $('.farmfaucet-notification').removeClass('farmfaucet-notification-show');
            setTimeout(() => {
                $('#farmfaucet-notifications').empty();
            }, 300);
        },

        // Bind events
        bindEvents: function() {
            // Close button click
            $(document).on('click', '.farmfaucet-notification-close', function() {
                const notification = $(this).closest('.farmfaucet-notification');
                const notificationId = notification.attr('id');
                FarmfaucetNotifications.hide(notificationId);
            });

            // Click to dismiss
            $(document).on('click', '.farmfaucet-notification', function(e) {
                if (!$(e.target).hasClass('farmfaucet-notification-close')) {
                    const notificationId = $(this).attr('id');
                    FarmfaucetNotifications.hide(notificationId);
                }
            });
        },

        // Faucet-specific notification methods
        faucet: {
            // Claim started
            claimStarted: function() {
                return FarmfaucetNotifications.loading('Processing your claim...');
            },

            // Claim success
            claimSuccess: function(amount, currency) {
                return FarmfaucetNotifications.success(`🎉 Successfully claimed ${amount} ${currency}!`);
            },

            // Claim error
            claimError: function(message) {
                return FarmfaucetNotifications.error(`Claim failed: ${message}`);
            },

            // Withdrawal started
            withdrawalStarted: function() {
                return FarmfaucetNotifications.loading('Processing your withdrawal...');
            },

            // Withdrawal success
            withdrawalSuccess: function(amount, currency) {
                return FarmfaucetNotifications.success(`💰 Successfully withdrew ${amount} ${currency}!`);
            },

            // Withdrawal error
            withdrawalError: function(message) {
                return FarmfaucetNotifications.error(`Withdrawal failed: ${message}`);
            },

            // Conversion started
            conversionStarted: function() {
                return FarmfaucetNotifications.loading('Processing your conversion...');
            },

            // Conversion success
            conversionSuccess: function(fromAmount, fromCurrency, toAmount, toCurrency) {
                return FarmfaucetNotifications.success(`🔄 Successfully converted ${fromAmount} ${fromCurrency} to ${toAmount} ${toCurrency}!`);
            },

            // Conversion error
            conversionError: function(message) {
                return FarmfaucetNotifications.error(`Conversion failed: ${message}`);
            },

            // Cooldown active
            cooldownActive: function(timeRemaining) {
                return FarmfaucetNotifications.warning(`⏰ Please wait ${timeRemaining} before claiming again.`);
            },

            // Captcha required
            captchaRequired: function() {
                return FarmfaucetNotifications.info('Please complete the captcha to continue.');
            },

            // Login required
            loginRequired: function() {
                return FarmfaucetNotifications.warning('Please log in to use this faucet.');
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        FarmfaucetNotifications.init();
    });

})(jQuery);
