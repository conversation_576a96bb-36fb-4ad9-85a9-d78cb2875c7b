/**
 * Telegram Bot Builder Redesigned JavaScript
 * Enhanced with modern UI interactions and improved flow builder
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initTelegramBotBuilder();
    });

    /**
     * Initialize Telegram Bot Builder functionality
     */
    function initTelegramBotBuilder() {
        // Add animation class to cards
        $('.farmfaucet-admin-card').addClass('animated fadeIn');

        // Initialize bot tabs
        initBotTabs();

        // Load commands for the active bot
        loadCommandsForActiveBot();

        // Initialize event handlers
        initEventHandlers();
    }

    /**
     * Initialize bot tabs
     */
    function initBotTabs() {
        // Handle tab clicks
        $('.bot-tab').on('click', function() {
            const botId = $(this).data('bot-id');

            // Update active tab
            $('.bot-tab').removeClass('active');
            $(this).addClass('active');

            // Show corresponding content
            $('.bot-tab-content').removeClass('active');
            $(`.bot-tab-content[data-bot-id="${botId}"]`).addClass('active');

            // Load commands for this bot
            loadCommands(botId);
        });
    }

    /**
     * Load commands for the active bot
     */
    function loadCommandsForActiveBot() {
        const activeTab = $('.bot-tab.active');
        if (activeTab.length) {
            const botId = activeTab.data('bot-id');
            loadCommands(botId);
        }
    }

    /**
     * Load commands for a specific bot
     *
     * @param {number} botId Bot ID
     */
    function loadCommands(botId) {
        const commandsList = $(`#commands-list-${botId}`);

        // Show loading indicator
        commandsList.html('<div class="loading-commands"><div class="spinner"></div><p>Loading commands...</p></div>');

        // AJAX request to get commands
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_bot_commands',
                bot_id: botId,
                nonce: farmfaucetTgBotBuilder.nonce
            },
            success: function(response) {
                if (response.success && response.data.commands) {
                    renderCommands(commandsList, response.data.commands);
                } else {
                    commandsList.html('<div class="no-commands-message"><p>No commands found for this bot. Add your first command to get started.</p></div>');
                }
            },
            error: function() {
                commandsList.html('<div class="error-message"><p>Failed to load commands. Please try again.</p></div>');
            }
        });
    }

    /**
     * Render commands in the commands list
     *
     * @param {jQuery} container Commands list container
     * @param {Array} commands Array of command objects
     */
    function renderCommands(container, commands) {
        if (!commands.length) {
            container.html('<div class="no-commands-message"><p>No commands found for this bot. Add your first command to get started.</p></div>');
            return;
        }

        let html = '<div class="commands-grid">';

        commands.forEach(function(command) {
            html += `
                <div class="command-card" data-command-id="${command.id}">
                    <div class="command-card-header">
                        <h4>${command.command_trigger}</h4>
                        <span class="command-status ${command.is_active ? 'active' : 'inactive'}">
                            ${command.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <div class="command-card-body">
                        <p class="command-description">${command.description || 'No description'}</p>
                        <p class="command-type">
                            ${command.response_type === 'text' ? 'Text Response' : 'Flow Response'}
                        </p>
                    </div>
                    <div class="command-card-actions">
                        <button type="button" class="button edit-command" data-command-id="${command.id}">
                            <span class="dashicons dashicons-edit"></span> Edit
                        </button>
                        <button type="button" class="button button-link-delete delete-command" data-command-id="${command.id}">
                            <span class="dashicons dashicons-trash"></span> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.html(html);
    }

    /**
     * Initialize event handlers
     */
    function initEventHandlers() {
        // Add new bot button
        $('.add-new-bot').on('click', function() {
            openBotModal();
        });

        // Edit bot button
        $(document).on('click', '.edit-bot', function() {
            const botId = $(this).data('bot-id');
            openBotModal(botId);
        });

        // Delete bot button
        $(document).on('click', '.delete-bot', function() {
            const botId = $(this).data('bot-id');
            const botName = $(this).closest('.bot-tab-content').find('.bot-title').text();

            if (confirm(`Are you sure you want to delete the bot "${botName}"? This action cannot be undone.`)) {
                deleteBot(botId);
            }
        });

        // Add new command button
        $(document).on('click', '.add-new-command', function() {
            const botId = $(this).data('bot-id');
            openCommandModal(botId);
        });

        // Edit command button
        $(document).on('click', '.edit-command', function() {
            const commandId = $(this).data('command-id');
            openCommandModal(null, commandId);
        });

        // Delete command button
        $(document).on('click', '.delete-command', function() {
            const commandId = $(this).data('command-id');
            const commandName = $(this).closest('.command-card').find('h4').text();

            if (confirm(`Are you sure you want to delete the command "${commandName}"? This action cannot be undone.`)) {
                deleteCommand(commandId);
            }
        });

        // Send message in chat
        $(document).on('click', '.telegram-send-button', function() {
            const botId = $(this).data('bot-id');
            const input = $(`.telegram-message-input[data-bot-id="${botId}"]`);
            const message = input.val().trim();

            if (message) {
                sendMessage(botId, message);
                input.val('');
            }
        });

        // Send message on Enter key
        $(document).on('keypress', '.telegram-message-input', function(e) {
            if (e.which === 13) {
                const botId = $(this).data('bot-id');
                const message = $(this).val().trim();

                if (message) {
                    sendMessage(botId, message);
                    $(this).val('');
                }

                e.preventDefault();
            }
        });
    }

    /**
     * Open bot modal for adding or editing a bot
     *
     * @param {number|null} botId Bot ID for editing, null for new bot
     */
    function openBotModal(botId = null) {
        // Create modal if it doesn't exist
        if ($('#bot-modal').length === 0) {
            const modalHtml = `
                <div id="bot-modal" class="farmfaucet-modal">
                    <div class="farmfaucet-modal-content">
                        <div class="farmfaucet-modal-header">
                            <h3>${botId ? 'Edit Bot' : 'Add New Bot'}</h3>
                            <button type="button" class="farmfaucet-modal-close">&times;</button>
                        </div>
                        <div class="farmfaucet-modal-body">
                            <form id="bot-form" class="farmfaucet-form">
                                <input type="hidden" name="bot_id" id="bot-id" value="${botId || ''}">

                                <div class="form-group">
                                    <label for="bot-name">Bot Name</label>
                                    <input type="text" name="bot_name" id="bot-name" class="regular-text" required>
                                    <p class="description">Enter a name for your bot (for your reference only).</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-token">Bot Token</label>
                                    <input type="text" name="bot_token" id="bot-token" class="regular-text" required>
                                    <p class="description">Enter the token provided by BotFather.</p>
                                    <button type="button" id="test-token" class="button">Test Token</button>
                                    <span id="test-token-result"></span>
                                </div>

                                <div class="form-group">
                                    <label for="bot-username">Bot Username</label>
                                    <input type="text" name="bot_username" id="bot-username" class="regular-text" required>
                                    <p class="description">Enter the username of your bot (without @).</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-type">Bot Type</label>
                                    <select name="bot_type" id="bot-type" required>
                                        <option value="text">Text Bot</option>
                                        <option value="chat">Chat Bot</option>
                                    </select>
                                    <p class="description">Select the type of bot you want to create.</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-active">Status</label>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="is_active" id="bot-active" value="1" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <p class="description">Enable or disable this bot.</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-start-message">Start Message</label>
                                    <textarea name="start_message" id="bot-start-message" rows="5" class="regular-text"></textarea>
                                    <p class="description">Enter the message that will be sent when a user starts the bot.</p>
                                </div>
                            </form>
                        </div>
                        <div class="farmfaucet-modal-footer">
                            <button type="button" class="button" id="cancel-bot">Cancel</button>
                            <button type="button" class="button button-primary" id="save-bot">Save Bot</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
        }

        // Show modal
        $('#bot-modal').fadeIn(300);

        // If editing, load bot data
        if (botId) {
            loadBotData(botId);
        } else {
            // Reset form for new bot
            $('#bot-form')[0].reset();
            $('#bot-id').val('');
        }

        // Close modal
        $(document).on('click', '.farmfaucet-modal-close, #cancel-bot', function() {
            $('#bot-modal').fadeOut(300);
        });

        // Test token
        $(document).on('click', '#test-token', function(e) {
            e.preventDefault();
            testBotToken();
        });

        // Save bot
        $(document).on('click', '#save-bot', function() {
            saveBot();
        });
    }

    /**
     * Test bot token
     */
    function testBotToken() {
        const token = $('#bot-token').val().trim();

        if (!token) {
            $('#test-token-result').html('<span class="error">Please enter a token</span>');
            return;
        }

        $('#test-token-result').html('<span class="loading">Testing...</span>');

        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_test_bot_token',
                token: token,
                nonce: farmfaucetTgBotBuilder.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#test-token-result').html('<span class="success">' + farmfaucetTgBotBuilder.i18n.tokenValid + '</span>');

                    // Auto-fill username if available
                    if (response.data && response.data.username) {
                        $('#bot-username').val(response.data.username);
                    }
                } else {
                    $('#test-token-result').html('<span class="error">' + farmfaucetTgBotBuilder.i18n.tokenInvalid + '</span>');
                }
            },
            error: function() {
                $('#test-token-result').html('<span class="error">' + farmfaucetTgBotBuilder.i18n.tokenInvalid + '</span>');
            }
        });
    }

    /**
     * Save bot
     */
    function saveBot() {
        const form = $('#bot-form');

        // Validate form
        if (!form[0].checkValidity()) {
            form[0].reportValidity();
            return;
        }

        // Get form data
        const formData = {
            bot_id: $('#bot-id').val(),
            bot_name: $('#bot-name').val(),
            bot_token: $('#bot-token').val(),
            bot_username: $('#bot-username').val(),
            bot_type: $('#bot-type').val(),
            is_active: $('#bot-active').is(':checked') ? 1 : 0,
            start_message: $('#bot-start-message').val()
        };

        // Disable save button
        $('#save-bot').prop('disabled', true).text('Saving...');

        // AJAX request to save bot
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_save_bot',
                bot_data: formData,
                nonce: farmfaucetTgBotBuilder.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Close modal
                    $('#bot-modal').fadeOut(300);

                    // Reload page to show updated bot list
                    location.reload();
                } else {
                    alert(response.data.message || 'Failed to save bot. Please try again.');
                    $('#save-bot').prop('disabled', false).text('Save Bot');
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                $('#save-bot').prop('disabled', false).text('Save Bot');
            }
        });
    }

    /**
     * Load bot data for editing
     *
     * @param {number} botId Bot ID
     */
    function loadBotData(botId) {
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_bot',
                bot_id: botId,
                nonce: farmfaucetTgBotBuilder.nonce
            },
            success: function(response) {
                if (response.success && response.data.bot) {
                    const bot = response.data.bot;

                    // Fill form fields
                    $('#bot-id').val(bot.id);
                    $('#bot-name').val(bot.bot_name);
                    $('#bot-token').val(bot.bot_token);
                    $('#bot-username').val(bot.bot_username);
                    $('#bot-type').val(bot.bot_type);
                    $('#bot-active').prop('checked', bot.is_active == 1);
                    $('#bot-start-message').val(bot.start_message);
                } else {
                    alert('Failed to load bot data. Please try again.');
                    $('#bot-modal').fadeOut(300);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                $('#bot-modal').fadeOut(300);
            }
        });
    }

    /**
     * Delete bot
     *
     * @param {number} botId Bot ID
     */
    function deleteBot(botId) {
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_bot',
                bot_id: botId,
                nonce: farmfaucetTgBotBuilder.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Reload page to update bot list
                    location.reload();
                } else {
                    alert(response.data.message || 'Failed to delete bot. Please try again.');
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    }

    /**
     * Open command modal for adding or editing a command
     *
     * @param {number|null} botId Bot ID for new command
     * @param {number|null} commandId Command ID for editing
     */
    function openCommandModal(botId = null, commandId = null) {
        // Create modal if it doesn't exist
        if ($('#command-modal').length === 0) {
            const modalHtml = `
                <div id="command-modal" class="farmfaucet-modal">
                    <div class="farmfaucet-modal-content">
                        <div class="farmfaucet-modal-header">
                            <h3>${commandId ? 'Edit Command' : 'Add New Command'}</h3>
                            <button type="button" class="farmfaucet-modal-close">&times;</button>
                        </div>
                        <div class="farmfaucet-modal-body">
                            <form id="command-form" class="farmfaucet-form">
                                <input type="hidden" name="command_id" id="command-id" value="${commandId || ''}">
                                <input type="hidden" name="bot_id" id="command-bot-id" value="${botId || ''}">

                                <div class="form-group">
                                    <label for="command-trigger">Command</label>
                                    <input type="text" name="command_trigger" id="command-trigger" class="regular-text" required>
                                    <p class="description">Enter the command that users will type (e.g., /start).</p>
                                </div>

                                <div class="form-group">
                                    <label for="command-description">Description</label>
                                    <input type="text" name="description" id="command-description" class="regular-text">
                                    <p class="description">Enter a description for this command (optional).</p>
                                </div>

                                <div class="form-group">
                                    <label for="command-response-type">Response Type</label>
                                    <select name="response_type" id="command-response-type" required>
                                        <option value="text">Text Response</option>
                                        <option value="flow">Flow Response</option>
                                    </select>
                                    <p class="description">Select the type of response for this command.</p>
                                </div>

                                <div class="form-group response-text-group">
                                    <label for="command-response-text">Response Text</label>
                                    <textarea name="response_text" id="command-response-text" rows="5" class="regular-text"></textarea>
                                    <p class="description">Enter the text that will be sent when this command is triggered.</p>
                                </div>

                                <div class="form-group response-flow-group" style="display: none;">
                                    <label for="command-flow-id">Flow</label>
                                    <select name="flow_id" id="command-flow-id">
                                        <option value="">Select a flow</option>
                                        <!-- Flows will be loaded here -->
                                    </select>
                                    <p class="description">Select the flow that will be triggered by this command.</p>
                                </div>

                                <div class="form-group">
                                    <label for="command-active">Status</label>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="is_active" id="command-active" value="1" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <p class="description">Enable or disable this command.</p>
                                </div>
                            </form>
                        </div>
                        <div class="farmfaucet-modal-footer">
                            <button type="button" class="button" id="cancel-command">Cancel</button>
                            <button type="button" class="button button-primary" id="save-command">Save Command</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // Toggle response type fields
            $('#command-response-type').on('change', function() {
                const responseType = $(this).val();

                if (responseType === 'text') {
                    $('.response-text-group').show();
                    $('.response-flow-group').hide();
                } else {
                    $('.response-text-group').hide();
                    $('.response-flow-group').show();
                }
            });
        }

        // Show modal
        $('#command-modal').fadeIn(300);

        // If editing, load command data
        if (commandId) {
            loadCommandData(commandId);
        } else {
            // Reset form for new command
            $('#command-form')[0].reset();
            $('#command-id').val('');
            $('#command-bot-id').val(botId);
            $('#command-response-type').val('text').trigger('change');
        }

        // Close modal
        $(document).on('click', '.farmfaucet-modal-close, #cancel-command', function() {
            $('#command-modal').fadeOut(300);
        });

        // Save command
        $(document).on('click', '#save-command', function() {
            saveCommand();
        });
    }

    /**
     * Send message in chat
     *
     * @param {number} botId Bot ID
     * @param {string} message Message text
     */
    function sendMessage(botId, message) {
        const chatContainer = $(`#telegram-chat-${botId}`);

        // Add user message
        addMessageToChat(chatContainer, message, 'user');

        // Simulate bot response
        simulateBotResponse(botId, message, chatContainer);
    }

    /**
     * Add message to chat
     *
     * @param {jQuery} container Chat container
     * @param {string} text Message text
     * @param {string} type Message type (user or bot)
     */
    function addMessageToChat(container, text, type) {
        // Get current time
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const timeString = hours + ':' + minutes;

        // Create message HTML
        const messageHtml = `
            <div class="telegram-message ${type}-message">
                <div class="message-content">${formatMessageText(text)}</div>
                <div class="message-time">${timeString}</div>
            </div>
        `;

        // Add message to chat
        container.append(messageHtml);

        // Scroll to bottom
        container.scrollTop(container[0].scrollHeight);
    }

    /**
     * Format message text with newlines
     *
     * @param {string} text Message text
     * @return {string} Formatted text
     */
    function formatMessageText(text) {
        return text.replace(/\n/g, '<br>');
    }

    /**
     * Simulate bot response
     *
     * @param {number} botId Bot ID
     * @param {string} message User message
     * @param {jQuery} chatContainer Chat container
     */
    function simulateBotResponse(botId, message, chatContainer) {
        // Show typing indicator
        const typingHtml = '<div class="telegram-message bot-message typing-indicator"><span></span><span></span><span></span></div>';
        chatContainer.append(typingHtml);
        chatContainer.scrollTop(chatContainer[0].scrollHeight);

        // Simulate delay
        setTimeout(function() {
            // Remove typing indicator
            chatContainer.find('.typing-indicator').remove();

            // Check if message is a command
            if (message.startsWith('/')) {
                // Get command
                const command = message.split(' ')[0];

                // AJAX request to get command response
                $.ajax({
                    url: farmfaucetTgBotBuilder.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_get_command_response',
                        bot_id: botId,
                        command: command,
                        nonce: farmfaucetTgBotBuilder.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.response) {
                            // Add bot response
                            addMessageToChat(chatContainer, response.data.response, 'bot');
                        } else {
                            // Command not found
                            addMessageToChat(chatContainer, 'Command not found. Try /help for a list of available commands.', 'bot');
                        }
                    },
                    error: function() {
                        // Error
                        addMessageToChat(chatContainer, 'An error occurred. Please try again.', 'bot');
                    }
                });
            } else {
                // Not a command
                addMessageToChat(chatContainer, 'I only respond to commands. Try /help for a list of available commands.', 'bot');
            }
        }, 1000);
    }
})(jQuery);
