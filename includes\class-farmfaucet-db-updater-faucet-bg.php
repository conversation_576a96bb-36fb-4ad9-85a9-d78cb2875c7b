<?php

/**
 * Farmfaucet Database Updater for Faucet Background Styling
 *
 * Adds background styling columns to the faucets table
 */
class Farmfaucet_DB_Updater_Faucet_BG
{
    /**
     * Run the update to add the background styling columns
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add transparent_bg column if it doesn't exist
        if (!in_array('transparent_bg', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER faucet_color");
        }

        // Add bg_style column if it doesn't exist
        if (!in_array('bg_style', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_style varchar(20) NOT NULL DEFAULT 'solid' AFTER transparent_bg");
        }

        // Add bg_color column if it doesn't exist
        if (!in_array('bg_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_color varchar(50) NOT NULL DEFAULT '#f8fff8' AFTER bg_style");
        }

        // Add bg_gradient_start column if it doesn't exist
        if (!in_array('bg_gradient_start', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_gradient_start varchar(50) NOT NULL DEFAULT '#f8fff8' AFTER bg_color");
        }

        // Add bg_gradient_end column if it doesn't exist
        if (!in_array('bg_gradient_end', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_gradient_end varchar(50) NOT NULL DEFAULT '#e8f5e9' AFTER bg_gradient_start");
        }

        // Add text_color column if it doesn't exist
        if (!in_array('text_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN text_color varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER bg_gradient_end");
        }

        // Add text_shadow column if it doesn't exist
        if (!in_array('text_shadow', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN text_shadow varchar(100) NOT NULL DEFAULT 'none' AFTER text_color");
        }
    }
}
