/**
 * Currency Maker Fix
 * 
 * This script fixes issues with the currency maker functionality
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Currency Maker Fix loaded');
        initCurrencyMakerFix();
    });

    /**
     * Initialize Currency Maker Fix
     */
    function initCurrencyMakerFix() {
        // Override the saveCurrency function
        if (typeof window.saveCurrency === 'function') {
            console.log('Overriding saveCurrency function');
            var originalSaveCurrency = window.saveCurrency;
            
            window.saveCurrency = function() {
                // Get form data
                const currencyId = $('#currency-id').val();
                const isUpdate = currencyId !== '';
                
                // Create a button to show saving state
                const $saveButton = $('#farmfaucet-save-currency');
                const originalButtonText = $saveButton.text();
                $saveButton.prop('disabled', true).text('Saving...');
                
                // Get color value directly from the input
                let colorValue = $('#currency-color').val();
                if (!colorValue || colorValue === '') {
                    colorValue = '#4CAF50'; // Default color
                }
                
                console.log('Color value before submission:', colorValue);
                
                const formData = {
                    action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
                    nonce: farmfaucetCurrencyMakerAdmin.nonce,
                    name: $('#currency-name').val(),
                    code: $('#currency-code').val(),
                    symbol: $('#currency-symbol').val(),
                    base_currency: $('#currency-base').val(),
                    exchange_rate: $('#currency-rate').val(),
                    color: colorValue,
                    icon: $('#currency-icon').val(),
                    currency_type: $('#currency-type').val(),
                    is_active: $('#currency-active').is(':checked') ? 1 : 0
                };
                
                console.log('Sending form data:', formData);
                
                if (isUpdate) {
                    formData.currency_id = currencyId;
                }
                
                // Send AJAX request
                $.ajax({
                    url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        console.log('Currency save response:', response);
                        if (response.success) {
                            alert(response.data.message || (isUpdate ? farmfaucetCurrencyMakerAdmin.i18n.updateSuccess : farmfaucetCurrencyMakerAdmin.i18n.createSuccess));
                            
                            // Reload the page to show the updated table
                            window.location.reload();
                        } else {
                            $saveButton.prop('disabled', false).text(originalButtonText);
                            alert(response.data.message || (isUpdate ? farmfaucetCurrencyMakerAdmin.i18n.updateFailed : farmfaucetCurrencyMakerAdmin.i18n.createFailed));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', xhr.responseText);
                        $saveButton.prop('disabled', false).text(originalButtonText);
                        
                        // Try to parse the error response
                        let errorMessage = error;
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            if (errorResponse.data && errorResponse.data.message) {
                                errorMessage = errorResponse.data.message;
                            }
                        } catch (e) {
                            // If parsing fails, use the original error
                        }
                        
                        alert(farmfaucetCurrencyMakerAdmin.i18n.error + ' - ' + errorMessage);
                    }
                });
            };
        }
        
        // Fix color picker initialization
        if ($.fn.wpColorPicker) {
            // Initialize any existing color pickers
            $('.color-picker').wpColorPicker({
                change: function(event, ui) {
                    // Update the input value when color changes
                    $(this).val(ui.color.toString());
                    $(this).trigger('change');
                    console.log('Color picker changed to:', ui.color.toString());
                },
                clear: function() {
                    // Set default color when cleared
                    $(this).val('#4CAF50');
                    $(this).trigger('change');
                    console.log('Color picker cleared, set to default: #4CAF50');
                }
            });
        }
    }
})(jQuery);
