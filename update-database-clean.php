<?php
/**
 * Farm Faucet Database Update Script
 * 
 * This script updates the database schema for the Farm Faucet plugin.
 * It adds missing columns and tables required for the plugin to function properly.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    // Try to load WordPress
    if (file_exists('../wp-load.php')) {
        require_once('../wp-load.php');
    } elseif (file_exists('../../wp-load.php')) {
        require_once('../../wp-load.php');
    } elseif (file_exists('../../../wp-load.php')) {
        require_once('../../../wp-load.php');
    } else {
        die('WordPress not found. Please run this script from within your WordPress installation.');
    }
}

// Check if user is an admin
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

// Get the database prefix
global $wpdb;
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
$currencies_table = $wpdb->prefix . 'farmfaucet_currencies';
$bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';

// Output header
echo '<html><head><title>Farm Faucet Database Update</title>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background-color: #dff0d8; color: #3c763d; padding: 15px; border: 1px solid #d6e9c6; border-radius: 4px; margin: 20px 0; }
    .error { background-color: #f2dede; color: #a94442; padding: 15px; border: 1px solid #ebccd1; border-radius: 4px; margin: 20px 0; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; }
</style>';
echo '</head><body>';
echo '<h1>Farm Faucet Database Update</h1>';

// Check if the faucets table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
if (!$table_exists) {
    echo '<div class="error"><p>The faucets table does not exist. Please make sure the Farm Faucet plugin is installed correctly.</p></div>';
} else {
    echo '<p>Found faucets table: ' . $faucets_table . '</p>';
    
    // Add form_bg_color column if it doesn't exist
    $column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$faucets_table} LIKE 'form_bg_color'");
    if (!$column_exists) {
        $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius");
        echo '<div class="success"><p>Added form_bg_color column to the faucets table.</p></div>';
    } else {
        echo '<p>The form_bg_color column already exists.</p>';
    }
    
    // Add form_transparent column if it doesn't exist
    $column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$faucets_table} LIKE 'form_transparent'");
    if (!$column_exists) {
        $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color");
        echo '<div class="success"><p>Added form_transparent column to the faucets table.</p></div>';
    } else {
        echo '<p>The form_transparent column already exists.</p>';
    }
    
    // Update existing faucets to set default values
    $wpdb->query("UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''");
    echo '<div class="success"><p>Updated existing faucets with default form background values.</p></div>';
}

// Check if the currencies table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$currencies_table}'") === $currencies_table;
if (!$table_exists) {
    echo '<div class="error"><p>The currencies table does not exist. Creating it now...</p></div>';
    
    // Create the currencies table
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE {$currencies_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        code varchar(10) NOT NULL,
        symbol varchar(10) NOT NULL,
        base_currency varchar(10) NOT NULL,
        exchange_rate decimal(18,8) NOT NULL,
        color varchar(20) DEFAULT '#4CAF50',
        icon varchar(255) DEFAULT NULL,
        currency_type varchar(20) DEFAULT 'earnings',
        is_active tinyint(1) NOT NULL DEFAULT 1,
        created_by bigint(20) NOT NULL,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY code (code),
        KEY base_currency (base_currency),
        KEY currency_type (currency_type),
        KEY is_active (is_active)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo '<div class="success"><p>Created currencies table.</p></div>';
} else {
    echo '<p>Found currencies table: ' . $currencies_table . '</p>';
}

// Check if the bots table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bots_table}'") === $bots_table;
if (!$table_exists) {
    echo '<div class="error"><p>The bots table does not exist. Creating it now...</p></div>';
    
    // Create the bots table
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE {$bots_table} (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        bot_name varchar(100) NOT NULL,
        bot_token varchar(255) NOT NULL,
        bot_username varchar(100) NOT NULL,
        bot_type varchar(20) NOT NULL DEFAULT 'text',
        webhook_url varchar(255) DEFAULT '',
        created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        settings longtext DEFAULT NULL,
        is_active tinyint(1) DEFAULT 1,
        PRIMARY KEY  (id),
        UNIQUE KEY bot_token (bot_token)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo '<div class="success"><p>Created bots table.</p></div>';
} else {
    echo '<p>Found bots table: ' . $bots_table . '</p>';
}

echo '<div class="success"><h2>Update Complete</h2>';
echo '<p>The Farm Faucet database has been updated successfully.</p>';
echo '<p>You can now return to the Farm Faucet admin page and configure your faucets.</p>';
echo '</div>';

echo '</body></html>';
?>
