<?php
if (!defined('ABSPATH')) exit;

// Get faucet data from the template variable
if (!isset($faucet)) {
  echo '<div class="farmfaucet-error">' . esc_html__('No faucet available', 'farmfaucet') . '</div>';
  return;
}

// Check if user is logged in
$user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
if ($user_id === 0) {
  echo '<div class="farmfaucet-error">' . esc_html__('Please log in to use the conversion system', 'farmfaucet') . '</div>';
  return;
}

// Generate background style based on faucet settings
$bg_style = '';
$text_style = '';

// Apply background style
if (isset($faucet['transparent_bg']) && $faucet['transparent_bg'] == 1) {
  $bg_style = 'background: transparent;';
} else {
  if (isset($faucet['bg_style']) && $faucet['bg_style'] == 'gradient' && isset($faucet['bg_gradient_start']) && isset($faucet['bg_gradient_end'])) {
    $bg_style = "background: linear-gradient(135deg, {$faucet['bg_gradient_start']}, {$faucet['bg_gradient_end']});";
  } else if (isset($faucet['bg_color'])) {
    $bg_style = "background-color: {$faucet['bg_color']};";
  }
}

// Apply text color and shadow
$text_color = isset($faucet['text_color']) ? $faucet['text_color'] : '#4CAF50';
$text_shadow = isset($faucet['text_shadow']) && $faucet['text_shadow'] != 'none' ? "text-shadow: {$faucet['text_shadow']};" : '';
$text_style = "color: {$text_color}; {$text_shadow}";

// Get currency data
$currency_id = isset($faucet['currency_id']) ? $faucet['currency_id'] : 0;
$currency_name = '';
$currency_symbol = '';
$currency_type = 'earnings'; // Default type
$user_balance = 0;
$min_conversion = isset($faucet['min_conversion']) ? floatval($faucet['min_conversion']) : 0;

if ($currency_id && class_exists('Farmfaucet_Currency_Maker')) {
  $currency_maker = Farmfaucet_Currency_Maker::init();
  $currency = $currency_maker->get_currency($currency_id);
  if ($currency) {
    $currency_name = $currency['name'];
    $currency_symbol = $currency['symbol'];
    $currency_type = isset($currency['currency_type']) ? $currency['currency_type'] : 'earnings';
    $user_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);
  }
}

// Check if this is an advertisement-only conversion faucet
$ads_only_conversion = isset($faucet['ads_only_conversion']) && $faucet['ads_only_conversion'] == 1;
?>
<?php
// Get button color
$button_color = isset($faucet['button_color']) ? $faucet['button_color'] : '#4CAF50';

// Get border color
$border_color = isset($faucet['border_color']) ? $faucet['border_color'] : '#4CAF50';

// Get border radius
$border_radius = isset($faucet['border_radius']) ? $faucet['border_radius'] : '8px';

// Get form background color
$form_bg_color = isset($faucet['form_bg_color']) ? $faucet['form_bg_color'] : '#ffffff';

// Check if form background should be transparent
$form_transparent = isset($faucet['form_transparent']) && $faucet['form_transparent'] ? 1 : 0;

// Add transparent class if needed
$transparent_class = isset($faucet['transparent_bg']) && $faucet['transparent_bg'] ? 'transparent-bg' : '';

// Add custom border class if needed
$border_class = isset($faucet['border_color']) && $faucet['border_color'] ? 'custom-border' : '';
?>
<div class="farmfaucet-container conversion-faucet <?php echo esc_attr($transparent_class); ?> <?php echo esc_attr($border_class); ?>"
  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
  data-faucet-name="<?php echo esc_attr($faucet['name']); ?>"
  data-faucet-shortcode="<?php echo esc_attr($faucet['shortcode']); ?>"
  data-border-radius="<?php echo esc_attr($border_radius); ?>"
  data-form-bg-color="<?php echo esc_attr($form_bg_color); ?>"
  data-form-transparent="<?php echo esc_attr($form_transparent); ?>"
  role="region"
  aria-label="<?php esc_attr_e('Currency Conversion Form', 'farmfaucet'); ?>"
  style="<?php echo esc_attr($bg_style); ?> --button-color: <?php echo esc_attr($button_color); ?>; --border-color: <?php echo esc_attr($border_color); ?>; --form-bg-color: <?php echo esc_attr($form_bg_color); ?>">

  <div class="farmfaucet-header">
    <h2 class="status-header" id="form-heading" style="<?php echo esc_attr($text_style); ?>">
      💱 <?php esc_html_e('CONVERT CURRENCY', 'farmfaucet'); ?>
    </h2>
    <div class="balance-notice" aria-live="polite" style="<?php echo esc_attr($text_style); ?>">
      <?php echo wp_kses_post(sprintf(
        __('Your Balance: %s %s', 'farmfaucet'),
        '<span class="balance">' . esc_html(number_format($user_balance, 8)) . '</span>',
        '<span class="currency">' . esc_html($currency_name) . ' ' . esc_html($currency_symbol) . '</span>'
      )); ?>
    </div>
    <div class="min-conversion-notice" style="<?php echo esc_attr($text_style); ?>">
      <?php
      // Format the minimum conversion amount with 8 decimal places
      $formatted_min_conversion = number_format($min_conversion, 8, '.', '');
      echo wp_kses_post(sprintf(
        __('Minimum Conversion: %s %s', 'farmfaucet'),
        '<span class="min-amount">' . esc_html($formatted_min_conversion) . '</span>',
        '<span class="currency">' . esc_html($currency_name) . ' ' . esc_html($currency_symbol) . '</span>'
      )); ?>
    </div>
  </div>

  <form id="farmfaucet-conversion-form" method="post" class="farmfaucet-form" aria-labelledby="form-heading">
    <div class="conversion-container">
      <div class="conversion-from">
        <div class="form-group">
          <label for="conversion-amount"><?php esc_html_e('Amount to Convert', 'farmfaucet'); ?></label>
          <input type="number"
            id="conversion-amount"
            name="conversion_amount"
            placeholder="<?php esc_attr_e('Enter amount to convert', 'farmfaucet'); ?>"
            required
            step="0.00000001"
            min="<?php echo esc_attr($min_conversion); ?>"
            max="<?php echo esc_attr($user_balance); ?>"
            class="farmfaucet-input"
            aria-required="true">
          <div class="currency-label"><?php echo esc_html($currency_symbol); ?></div>
        </div>
      </div>

      <div class="conversion-arrow">
        <span class="dashicons dashicons-arrow-right-alt"></span>
      </div>

      <div class="conversion-to">
        <div class="form-group">
          <label for="target-currency"><?php esc_html_e('Convert To', 'farmfaucet'); ?></label>
          <div class="target-currency-wrapper">
            <select id="target-currency" name="target_currency_id" required class="farmfaucet-select">
              <?php
              // Get currencies for conversion
              global $wpdb;
              $table_name = $wpdb->prefix . 'farmfaucet_currencies';

              // If ads_only_conversion is enabled, only show advertisement currencies
              if ($ads_only_conversion) {
                $query = "SELECT * FROM $table_name WHERE currency_type = 'advertisement' AND is_active = 1 ORDER BY name ASC";
              } else {
                // Show all currencies but prioritize advertisement currencies
                $query = "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY FIELD(currency_type, 'advertisement') DESC, name ASC";
              }

              // Define ARRAY_A if not defined (for compatibility)
              if (!defined('ARRAY_A')) {
                define('ARRAY_A', 'ARRAY_A');
              }
              $target_currencies = $wpdb->get_results($query, ARRAY_A);

              if (empty($target_currencies)) {
                echo '<option value="">' . esc_html__('No currencies available', 'farmfaucet') . '</option>';
              } else {
                foreach ($target_currencies as $target_currency) :
                  // Skip the source currency
                  if ($target_currency['id'] == $currency_id) continue;
              ?>
                  <option value="<?php echo esc_attr($target_currency['id']); ?>"
                    data-rate="<?php echo esc_attr($target_currency['exchange_rate']); ?>"
                    data-symbol="<?php echo esc_attr($target_currency['symbol']); ?>"
                    data-type="<?php echo esc_attr(isset($target_currency['currency_type']) ? $target_currency['currency_type'] : 'earnings'); ?>">
                    <?php echo esc_html($target_currency['name']); ?> (<?php echo esc_html($target_currency['code']); ?>)
                    <?php if (isset($target_currency['currency_type']) && $target_currency['currency_type'] === 'advertisement') : ?>
                      <span class="ad-currency-tag"><?php esc_html_e('Advertisement', 'farmfaucet'); ?></span>
                    <?php endif; ?>
                  </option>
              <?php
                endforeach;
              }
              ?>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div class="form-group conversion-result">
      <label><?php esc_html_e('You Will Receive', 'farmfaucet'); ?></label>
      <div class="receive-amount">0.00000000 <span class="receive-symbol"></span></div>
    </div>

    <div class="form-group conversion-rate">
      <label><?php esc_html_e('Conversion Rate', 'farmfaucet'); ?></label>
      <div class="conversion-rate-display">
        1 <?php echo esc_html($currency_symbol); ?> = <span class="target-rate">0.00000000</span> <span class="target-currency"></span>
      </div>
    </div>

    <div class="currency-type-notice"></div>

    <!-- Hidden fields -->
    <input type="hidden" name="faucet_id" value="<?php echo esc_attr($faucet['id']); ?>">
    <input type="hidden" name="currency_id" value="<?php echo esc_attr($currency_id); ?>">
    <input type="hidden" name="conversion_rate" id="conversion-rate-field" value="0">

    <div class="farmfaucet-captcha-container" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>" style="display: flex; visibility: visible; opacity: 1;">
      <?php
      if (class_exists('Farmfaucet_Captcha_Handler')) {
        echo Farmfaucet_Captcha_Handler::render_captcha($faucet);
      } else {
        try {
          // Get captcha type from faucet-specific setting or fallback to global settings
          $captcha_type = !empty($faucet['captcha_type']) ? $faucet['captcha_type'] : get_option('farmfaucet_captcha_type', 'hcaptcha');

          // Validate captcha type
          if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
          }

          // Get captcha site keys - check both possible option names
          $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_site_key', '');
          if (empty($hcaptcha_sitekey)) {
            $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
          }

          $recaptcha_sitekey = get_option('farmfaucet_recaptcha_site_key', '');
          if (empty($recaptcha_sitekey)) {
            $recaptcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
          }

          $turnstile_sitekey = get_option('farmfaucet_turnstile_site_key', '');
          if (empty($turnstile_sitekey)) {
            $turnstile_sitekey = get_option('farmfaucet_turnstile_sitekey', '');
          }

          // Generate a unique ID for this captcha
          $captcha_id = 'farmfaucet-captcha-' . $faucet['id'] . '-' . uniqid();

          // Add hidden field for captcha type
          echo '<input type="hidden" name="captcha_type" value="' . esc_attr($captcha_type) . '">';

          // Render the appropriate captcha
          if ($captcha_type === 'hcaptcha') {
            if (empty($hcaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('hCaptcha site key not configured', 'farmfaucet') . '</div>';
            } else {
      ?>
              <div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for hCaptcha -->
                <div id="<?php echo $captcha_id; ?>"
                  class="h-captcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($hcaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableConversionButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'recaptcha') {
            if (empty($recaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('reCAPTCHA site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for reCAPTCHA -->
                <div id="<?php echo $captcha_id; ?>"
                  class="g-recaptcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($recaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableConversionButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'turnstile') {
            if (empty($turnstile_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('Cloudflare Turnstile site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for Cloudflare Turnstile -->
                <div id="<?php echo $captcha_id; ?>"
                  class="cf-turnstile farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($turnstile_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableConversionButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
      <?php
            }
          }
        } catch (Exception $e) {
          echo '<div class="farmfaucet-error">' . esc_html__('Error loading captcha', 'farmfaucet') . '</div>';
        }
      }
      ?>
    </div>

    <button type="submit"
      class="farmfaucet-convert-btn farmfaucet-button custom-color"
      disabled
      aria-disabled="true"
      id="convert-button">
      <span class="conversion-btn-text"><?php esc_html_e('CONVERT', 'farmfaucet'); ?></span>
    </button>
  </form>

  <div class="farmfaucet-notification" role="status" aria-live="assertive"></div>
  <div class="farmfaucet-error-message" style="display: none;"></div>
</div>

<!-- Conversion history is now available via the [farmfaucet_conversion_history faucet_id="X"] shortcode -->

<script>
  jQuery(document).ready(function($) {
    // Calculate conversion amount
    $('#conversion-amount, #target-currency').on('change input', function() {
      calculateConversionAmount();
    });

    function calculateConversionAmount() {
      var amount = parseFloat($('#conversion-amount').val()) || 0;
      var selectedOption = $('#target-currency option:selected');
      var rate = parseFloat(selectedOption.data('rate')) || 0;
      var symbol = selectedOption.data('symbol') || '';
      var currencyType = selectedOption.data('type') || '';
      var currencyName = selectedOption.text() || '';

      if (!isNaN(amount) && !isNaN(rate)) {
        // Apply conversion rate
        var convertedAmount = amount * rate;

        // Format the amount with 8 decimal places
        $('.receive-amount').text(convertedAmount.toFixed(8) + ' ' + symbol);
        $('.receive-symbol').text(symbol);

        // Update conversion rate display
        $('.target-rate').text((rate).toFixed(8));
        $('.target-currency').text(symbol);

        // Update currency type notice
        if (currencyType === 'advertisement') {
          $('.currency-type-notice').html('<div class="ads-only-notice">Converting to <strong>' + currencyName + '</strong> (Advertisement Balance)</div>').show();
        } else {
          $('.currency-type-notice').html('').hide();
        }

        // Update hidden field for form submission
        $('#conversion-rate-field').val(rate.toFixed(8));
      }
    }

    // Initialize conversion display
    if ($('#target-currency option').length > 0) {
      $('#target-currency').trigger('change');
    }

    // Enable submit button when captcha is completed
    window.enableConversionButton = function() {
      $('#convert-button').prop('disabled', false).attr('aria-disabled', 'false');
    };

    // Form submission handler
    $('#farmfaucet-conversion-form').on('submit', function(e) {
      // Validate form
      var amount = parseFloat($('#conversion-amount').val()) || 0;
      var minConversion = parseFloat('<?php echo esc_attr($min_conversion); ?>') || 0;
      var userBalance = parseFloat('<?php echo esc_attr($user_balance); ?>') || 0;
      var targetCurrencyId = $('#target-currency').val();

      if (amount <= 0) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Please enter a valid amount', 'farmfaucet')); ?>');
        return false;
      }

      if (amount < minConversion) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Amount must be at least the minimum conversion amount', 'farmfaucet')); ?>');
        return false;
      }

      if (amount > userBalance) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Amount cannot exceed your balance', 'farmfaucet')); ?>');
        return false;
      }

      if (!targetCurrencyId) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Please select a currency to convert to', 'farmfaucet')); ?>');
        return false;
      }

      // If all validations pass, show loading state
      if (!e.isDefaultPrevented()) {
        $('#convert-button').prop('disabled', true).text('<?php echo esc_attr(__('Processing...', 'farmfaucet')); ?>');
      }
    });
  });
</script>