<?php

/**
 * Currency Manager System for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.2
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Currency_Maker
 *
 * Handles custom currency creation and management
 * (Renamed from Currency Maker to Currency Manager for better clarity)
 */
class Farmfaucet_Currency_Maker
{

    /**
     * Singleton instance
     *
     * @var Farmfaucet_Currency_Maker
     */
    private static $instance;

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Currency_Maker
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);

        // Register shortcodes
        add_shortcode('farmfaucet_currency_list', [$this, 'render_currency_list_shortcode']);
        add_shortcode('farmfaucet_currency_balance', [$this, 'render_currency_balance_shortcode']);

        // AJAX handlers
        add_action('wp_ajax_farmfaucet_create_currency', [$this, 'ajax_create_currency']);
        add_action('wp_ajax_farmfaucet_update_currency', [$this, 'ajax_update_currency']);
        add_action('wp_ajax_farmfaucet_delete_currency', [$this, 'ajax_delete_currency']);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Create database tables on plugin activation
        add_action('farmfaucet_activate', [$this, 'create_tables']);
    }

    /**
     * Register settings for Currency Maker
     */
    public function register_settings()
    {
        // Settings registration now handled by Farmfaucet_Settings_Manager
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets()
    {
        wp_enqueue_style(
            'farmfaucet-currency-maker',
            FARMFAUCET_URL . 'assets/css/currency-maker.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-currency-maker',
            FARMFAUCET_URL . 'assets/js/currency-maker.js',
            ['jquery'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-currency-maker', 'farmfaucetCurrencyMaker', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-currency-maker-nonce'),
            'i18n' => [
                'exchangeSuccess' => __('Currency exchange successful.', 'farmfaucet'),
                'exchangeFailed' => __('Currency exchange failed. Please try again.', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook)
    {
        // Only load on Farm Faucet admin pages
        if (strpos($hook, 'farmfaucet') === false) {
            return;
        }

        // Load the green-themed CSS for consistent styling
        wp_enqueue_style(
            'farmfaucet-currency-maker-green',
            FARMFAUCET_URL . 'assets/css/currency-maker-green.css',
            [],
            FARMFAUCET_VERSION . '.' . time()
        );

        wp_enqueue_style(
            'farmfaucet-currency-maker-admin',
            FARMFAUCET_URL . 'assets/css/currency-maker-admin.css',
            ['farmfaucet-currency-maker-green'],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-currency-maker-admin',
            FARMFAUCET_URL . 'assets/js/currency-maker-admin.js',
            ['jquery', 'wp-color-picker'],
            FARMFAUCET_VERSION . '.' . time(),
            true
        );

        // Add our targeted Add Currency button fix
        wp_enqueue_script(
            'farmfaucet-add-currency-button-fix',
            FARMFAUCET_URL . 'assets/js/add-currency-button-fix.js',
            ['jquery', 'wp-color-picker', 'farmfaucet-currency-maker-admin'],
            FARMFAUCET_VERSION . '.' . time(),
            true
        );

        // Add form transparency fix CSS
        wp_enqueue_style(
            'farmfaucet-form-transparency-fix',
            FARMFAUCET_URL . 'assets/css/form-transparency-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time()
        );

        wp_localize_script('farmfaucet-currency-maker-admin', 'farmfaucetCurrencyMakerAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-currency-maker-admin-nonce'),
            'i18n' => [
                'createSuccess' => __('Currency created successfully.', 'farmfaucet'),
                'createFailed' => __('Failed to create currency. Please try again.', 'farmfaucet'),
                'updateSuccess' => __('Currency updated successfully.', 'farmfaucet'),
                'updateFailed' => __('Failed to update currency. Please try again.', 'farmfaucet'),
                'deleteSuccess' => __('Currency deleted successfully.', 'farmfaucet'),
                'deleteFailed' => __('Failed to delete currency. Please try again.', 'farmfaucet'),
                'confirmDelete' => __('Are you sure you want to delete this currency? This action cannot be undone.', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);

        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
    }

    /**
     * Create database tables
     */
    public function create_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Currencies table
        $currencies_table = $wpdb->prefix . 'farmfaucet_currencies';

        $sql = "CREATE TABLE IF NOT EXISTS $currencies_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            code varchar(10) NOT NULL,
            symbol varchar(10) NOT NULL,
            base_currency varchar(10) NOT NULL,
            exchange_rate decimal(18,8) NOT NULL,
            color varchar(20) DEFAULT '#4CAF50',
            icon varchar(255) DEFAULT NULL,
            currency_type varchar(20) DEFAULT 'earnings',
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_by bigint(20) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY code (code),
            KEY base_currency (base_currency),
            KEY currency_type (currency_type),
            KEY is_active (is_active)
        ) $charset_collate;";

        // User currency balances table
        $balances_table = $wpdb->prefix . 'farmfaucet_user_currency_balances';

        $sql .= "CREATE TABLE IF NOT EXISTS $balances_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            currency_id bigint(20) NOT NULL,
            balance decimal(18,8) NOT NULL DEFAULT 0,
            last_updated datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_currency (user_id, currency_id),
            KEY user_id (user_id),
            KEY currency_id (currency_id)
        ) $charset_collate;";

        // Currency exchange transactions table
        $exchanges_table = $wpdb->prefix . 'farmfaucet_currency_exchanges';

        $sql .= "CREATE TABLE IF NOT EXISTS $exchanges_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            from_currency_id bigint(20) NOT NULL,
            to_currency_id bigint(20) NOT NULL,
            from_amount decimal(18,8) NOT NULL,
            to_amount decimal(18,8) NOT NULL,
            fee_amount decimal(18,8) NOT NULL,
            exchange_rate decimal(18,8) NOT NULL,
            status varchar(20) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY from_currency_id (from_currency_id),
            KEY to_currency_id (to_currency_id),
            KEY status (status)
        ) $charset_collate;";

        // Execute SQL
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get all currencies
     *
     * @param bool $active_only Whether to return only active currencies
     * @return array Array of currencies
     */
    public function get_currencies($active_only = true)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        $sql = "SELECT * FROM $table_name";

        if ($active_only) {
            $sql .= " WHERE is_active = 1";
        }

        $sql .= " ORDER BY name ASC";

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Get currency by ID
     *
     * @param int $currency_id Currency ID
     * @return array|null Currency data or null if not found
     */
    public function get_currency($currency_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $currency_id
            ),
            ARRAY_A
        );

        return $result;
    }

    /**
     * Get currency by code
     *
     * @param string $currency_code Currency code
     * @return array|null Currency data or null if not found
     */
    public function get_currency_by_code($currency_code)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE code = %s",
                $currency_code
            ),
            ARRAY_A
        );

        return $result;
    }

    /**
     * Get currencies by type
     *
     * @param string $type Currency type (earnings or advertisement)
     * @param bool $active_only Whether to return only active currencies
     * @return array Array of currencies
     */
    public function get_currencies_by_type($type, $active_only = true)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name WHERE currency_type = %s",
            $type
        );

        if ($active_only) {
            $sql .= " AND is_active = 1";
        }

        $sql .= " ORDER BY name ASC";

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Create a new currency
     *
     * @param array $data Currency data
     * @return int|false Currency ID on success, false on failure
     */
    public function create_currency($data)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        // Validate required fields
        if (
            empty($data['name']) || empty($data['code']) || empty($data['symbol']) ||
            empty($data['base_currency']) || !isset($data['exchange_rate'])
        ) {
            error_log('Missing required field for currency creation: ' .
                (empty($data['name']) ? 'name' : (empty($data['code']) ? 'code' : (empty($data['symbol']) ? 'symbol' : (empty($data['base_currency']) ? 'base_currency' : 'exchange_rate')))));
            return false;
        }

        // Check if currency code already exists
        $existing = $this->get_currency_by_code($data['code']);
        if ($existing) {
            error_log('Currency code already exists: ' . $data['code']);
            return false;
        }

        // Validate currency type
        $currency_type = isset($data['currency_type']) ? $data['currency_type'] : 'earnings';
        if (!in_array($currency_type, ['earnings', 'advertisement'])) {
            $currency_type = 'earnings';
        }

        // Sanitize color or use default
        $color = isset($data['color']) ? $data['color'] : '#4CAF50';
        if (!preg_match('/^#[a-f0-9]{3,6}$/i', $color)) {
            $color = '#4CAF50';
        }

        // Prepare data for insertion
        $insert_data = [
            'name' => $data['name'],
            'code' => strtoupper($data['code']),
            'symbol' => $data['symbol'],
            'base_currency' => strtoupper($data['base_currency']),
            'exchange_rate' => floatval($data['exchange_rate']),
            'color' => $color,
            'icon' => isset($data['icon']) ? $data['icon'] : null,
            'currency_type' => $currency_type,
            'is_active' => isset($data['is_active']) ? intval($data['is_active']) : 1,
            'created_by' => function_exists('get_current_user_id') ? get_current_user_id() : 1,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Log the data we're inserting
        error_log('Inserting currency data: ' . json_encode($insert_data));

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            // Insert currency
            $result = $wpdb->insert(
                $table_name,
                $insert_data,
                [
                    '%s', // name
                    '%s', // code
                    '%s', // symbol
                    '%s', // base_currency
                    '%f', // exchange_rate
                    '%s', // color
                    '%s', // icon
                    '%s', // currency_type
                    '%d', // is_active
                    '%d', // created_by
                    '%s', // created_at
                    '%s'  // updated_at
                ]
            );

            if ($result === false) {
                error_log('Failed to insert currency. MySQL error: ' . $wpdb->last_error);
                $wpdb->query('ROLLBACK');
                return false;
            }

            $insert_id = $wpdb->insert_id;

            // Commit transaction
            $wpdb->query('COMMIT');

            error_log('Currency created successfully with ID: ' . $insert_id);
            return $insert_id;
        } catch (Exception $e) {
            error_log('Exception during currency creation: ' . $e->getMessage());
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * Update an existing currency
     *
     * @param int $currency_id Currency ID
     * @param array $data Currency data
     * @return bool True on success, false on failure
     */
    public function update_currency($currency_id, $data)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        // Check if currency exists
        $currency = $this->get_currency($currency_id);
        if (!$currency) {
            return false;
        }

        // Prepare data for update
        $update_data = [];
        $update_format = [];

        if (isset($data['name'])) {
            $update_data['name'] = sanitize_text_field($data['name']);
            $update_format[] = '%s';
        }

        if (isset($data['symbol'])) {
            $update_data['symbol'] = sanitize_text_field($data['symbol']);
            $update_format[] = '%s';
        }

        if (isset($data['base_currency'])) {
            $update_data['base_currency'] = strtoupper(sanitize_text_field($data['base_currency']));
            $update_format[] = '%s';
        }

        if (isset($data['exchange_rate'])) {
            $update_data['exchange_rate'] = floatval($data['exchange_rate']);
            $update_format[] = '%f';
        }

        if (isset($data['color'])) {
            $update_data['color'] = sanitize_hex_color($data['color']);
            $update_format[] = '%s';
        }

        if (isset($data['icon'])) {
            $update_data['icon'] = esc_url_raw($data['icon']);
            $update_format[] = '%s';
        }

        if (isset($data['currency_type'])) {
            $update_data['currency_type'] = sanitize_text_field($data['currency_type']);
            $update_format[] = '%s';
        }

        if (isset($data['is_active'])) {
            $update_data['is_active'] = intval($data['is_active']);
            $update_format[] = '%d';
        }

        // Update timestamp
        $update_data['updated_at'] = current_time('mysql');
        $update_format[] = '%s';

        // Update currency
        $result = $wpdb->update(
            $table_name,
            $update_data,
            ['id' => $currency_id],
            $update_format,
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Delete a currency
     *
     * @param int $currency_id Currency ID
     * @return bool True on success, false on failure
     */
    public function delete_currency($currency_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_currencies';

        // Check if currency exists
        $currency = $this->get_currency($currency_id);
        if (!$currency) {
            return false;
        }

        // Delete currency
        $result = $wpdb->delete(
            $table_name,
            ['id' => $currency_id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get user currency balance
     *
     * @param int $user_id User ID
     * @param int $currency_id Currency ID
     * @return float User balance for the specified currency
     */
    public function get_user_currency_balance($user_id, $currency_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_user_currency_balances';

        $result = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT balance FROM $table_name WHERE user_id = %d AND currency_id = %d",
                $user_id,
                $currency_id
            )
        );

        return $result ? floatval($result) : 0.0;
    }

    /**
     * Get all user currency balances
     *
     * @param int $user_id User ID
     * @return array Array of user balances
     */
    public function get_user_currency_balances($user_id)
    {
        global $wpdb;

        $balances_table = $wpdb->prefix . 'farmfaucet_user_currency_balances';
        $currencies_table = $wpdb->prefix . 'farmfaucet_currencies';

        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT b.*, c.name, c.code, c.symbol, c.color, c.icon
                FROM $balances_table b
                JOIN $currencies_table c ON b.currency_id = c.id
                WHERE b.user_id = %d AND c.is_active = 1
                ORDER BY c.name ASC",
                $user_id
            ),
            ARRAY_A
        );

        return $results ?: [];
    }

    /**
     * Update user currency balance
     *
     * @param int $user_id User ID
     * @param int $currency_id Currency ID
     * @param float $amount Amount to add (positive) or subtract (negative)
     * @return bool True on success, false on failure
     */
    public function update_user_currency_balance($user_id, $currency_id, $amount)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_user_currency_balances';

        // Check if user has a balance record for this currency
        $balance = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT balance FROM $table_name WHERE user_id = %d AND currency_id = %d",
                $user_id,
                $currency_id
            )
        );

        if ($balance !== null) {
            // Update existing balance
            $new_balance = max(0, floatval($balance) + $amount);

            $result = $wpdb->update(
                $table_name,
                [
                    'balance' => $new_balance,
                    'last_updated' => current_time('mysql')
                ],
                [
                    'user_id' => $user_id,
                    'currency_id' => $currency_id
                ],
                [
                    '%f', // balance
                    '%s'  // last_updated
                ],
                [
                    '%d', // user_id
                    '%d'  // currency_id
                ]
            );
        } else {
            // Create new balance record
            $result = $wpdb->insert(
                $table_name,
                [
                    'user_id' => $user_id,
                    'currency_id' => $currency_id,
                    'balance' => max(0, $amount),
                    'last_updated' => current_time('mysql')
                ],
                [
                    '%d', // user_id
                    '%d', // currency_id
                    '%f', // balance
                    '%s'  // last_updated
                ]
            );
        }

        return $result !== false;
    }

    /**
     * Exchange currency
     *
     * @param int $user_id User ID
     * @param int $from_currency_id Source currency ID
     * @param int $to_currency_id Target currency ID
     * @param float $amount Amount to exchange
     * @return array|false Exchange result on success, false on failure
     */
    public function exchange_currency($user_id, $from_currency_id, $to_currency_id, $amount)
    {
        global $wpdb;

        // Validate parameters
        if ($from_currency_id === $to_currency_id) {
            return false;
        }

        if ($amount <= 0) {
            return false;
        }

        // Check if user has enough balance
        $from_balance = $this->get_user_currency_balance($user_id, $from_currency_id);
        if ($from_balance < $amount) {
            return false;
        }

        // Get currencies
        $from_currency = $this->get_currency($from_currency_id);
        $to_currency = $this->get_currency($to_currency_id);

        if (!$from_currency || !$to_currency) {
            return false;
        }

        // Calculate exchange rate and amount
        $exchange_rate = $this->calculate_exchange_rate($from_currency, $to_currency);

        // Calculate fee
        $fee_percentage = get_option('farmfaucet_currency_exchange_fee', 2.0);
        $fee_amount = $amount * ($fee_percentage / 100);

        // Calculate final amount
        $from_amount = $amount;
        $to_amount = ($amount - $fee_amount) * $exchange_rate;

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            // Deduct from source currency
            $result1 = $this->update_user_currency_balance($user_id, $from_currency_id, -$from_amount);

            // Add to target currency
            $result2 = $this->update_user_currency_balance($user_id, $to_currency_id, $to_amount);

            // Record exchange transaction
            $exchanges_table = $wpdb->prefix . 'farmfaucet_currency_exchanges';

            $result3 = $wpdb->insert(
                $exchanges_table,
                [
                    'user_id' => $user_id,
                    'from_currency_id' => $from_currency_id,
                    'to_currency_id' => $to_currency_id,
                    'from_amount' => $from_amount,
                    'to_amount' => $to_amount,
                    'fee_amount' => $fee_amount,
                    'exchange_rate' => $exchange_rate,
                    'status' => 'completed',
                    'created_at' => current_time('mysql')
                ],
                [
                    '%d', // user_id
                    '%d', // from_currency_id
                    '%d', // to_currency_id
                    '%f', // from_amount
                    '%f', // to_amount
                    '%f', // fee_amount
                    '%f', // exchange_rate
                    '%s', // status
                    '%s'  // created_at
                ]
            );

            if ($result1 && $result2 && $result3) {
                $wpdb->query('COMMIT');

                return [
                    'from_currency' => $from_currency,
                    'to_currency' => $to_currency,
                    'from_amount' => $from_amount,
                    'to_amount' => $to_amount,
                    'fee_amount' => $fee_amount,
                    'exchange_rate' => $exchange_rate,
                    'transaction_id' => $wpdb->insert_id
                ];
            } else {
                $wpdb->query('ROLLBACK');
                return false;
            }
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * Calculate exchange rate between two currencies
     *
     * @param array $from_currency Source currency data
     * @param array $to_currency Target currency data
     * @return float Exchange rate
     */
    private function calculate_exchange_rate($from_currency, $to_currency)
    {
        // If both currencies have the same base, direct calculation
        if ($from_currency['base_currency'] === $to_currency['base_currency']) {
            return $to_currency['exchange_rate'] / $from_currency['exchange_rate'];
        }

        // Otherwise, convert through base currencies
        // This is a simplified approach and might need refinement for real-world use

        // Get base currency exchange rates (e.g., BTC to USD, ETH to USD)
        $base_rates = $this->get_base_currency_rates();

        // Calculate exchange rate
        $from_base_rate = isset($base_rates[$from_currency['base_currency']]) ? $base_rates[$from_currency['base_currency']] : 1;
        $to_base_rate = isset($base_rates[$to_currency['base_currency']]) ? $base_rates[$to_currency['base_currency']] : 1;

        // Convert from source currency to its base, then to target base, then to target currency
        $from_to_base = $from_currency['exchange_rate'] * $from_base_rate;
        $base_to_target_base = $from_to_base / $to_base_rate;
        $target_base_to_target = $base_to_target_base / $to_currency['exchange_rate'];

        return $target_base_to_target;
    }

    /**
     * Get base currency exchange rates
     *
     * In a real implementation, this would fetch rates from an API
     * For now, we'll use hardcoded values for demonstration
     *
     * @return array Base currency exchange rates
     */
    private function get_base_currency_rates()
    {
        return [
            'BTC' => 1.0,
            'ETH' => 0.06, // 1 ETH = 0.06 BTC
            'LTC' => 0.004, // 1 LTC = 0.004 BTC
            'DOGE' => 0.00000625, // 1 DOGE = 0.00000625 BTC
            'BCH' => 0.01, // 1 BCH = 0.01 BTC
            'TRX' => 0.00000125, // 1 TRX = 0.00000125 BTC
            'USDT' => 0.00004 // 1 USDT = 0.00004 BTC
        ];
    }

    /**
     * AJAX handler for creating a currency
     */
    public function ajax_create_currency()
    {
        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to create currencies.', 'farmfaucet')]);
        }

        // Check nonce
        if (!isset($_POST['nonce'])) {
            wp_send_json_error(['message' => __('Security check failed: Missing nonce.', 'farmfaucet')]);
            return;
        }

        // Accept any nonce format for maximum compatibility
        $valid_nonce = wp_verify_nonce($_POST['nonce'], 'farmfaucet-currency-maker-admin-nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_currency_maker_admin_nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_admin_nonce');

        if (!$valid_nonce) {
            error_log('Invalid nonce in ajax_create_currency: ' . $_POST['nonce']);
            wp_send_json_error(['message' => __('Security check failed: Invalid nonce.', 'farmfaucet')]);
            return;
        }

        // Validate required fields
        if (
            !isset($_POST['name']) || !isset($_POST['code']) || !isset($_POST['symbol']) ||
            !isset($_POST['base_currency']) || !isset($_POST['exchange_rate'])
        ) {
            wp_send_json_error(['message' => __('Missing required fields.', 'farmfaucet')]);
            return; // Added return statement to prevent further execution
        }

        // Sanitize input data
        $name = sanitize_text_field($_POST['name']);
        $code = sanitize_text_field($_POST['code']);
        $symbol = sanitize_text_field($_POST['symbol']);
        $base_currency = sanitize_text_field($_POST['base_currency']);
        $exchange_rate = floatval($_POST['exchange_rate']);
        $color = isset($_POST['color']) ? $_POST['color'] : '#4CAF50';
        $icon = isset($_POST['icon']) ? esc_url_raw($_POST['icon']) : null;
        $currency_type = isset($_POST['currency_type']) ? sanitize_text_field($_POST['currency_type']) : 'earnings';
        $is_active = isset($_POST['is_active']) ? intval($_POST['is_active']) : 1;

        // Log the data we're about to use
        error_log('Creating currency with data: ' . json_encode([
            'name' => $name,
            'code' => $code,
            'symbol' => $symbol,
            'base_currency' => $base_currency,
            'exchange_rate' => $exchange_rate,
            'color' => $color,
            'icon' => $icon,
            'currency_type' => $currency_type,
            'is_active' => $is_active
        ]));

        // Create currency
        $currency_id = $this->create_currency([
            'name' => $name,
            'code' => $code,
            'symbol' => $symbol,
            'base_currency' => $base_currency,
            'exchange_rate' => $exchange_rate,
            'color' => $color,
            'icon' => $icon,
            'currency_type' => $currency_type,
            'is_active' => $is_active
        ]);

        if ($currency_id) {
            error_log('Currency created successfully with ID: ' . $currency_id);
            wp_send_json_success([
                'message' => __('Currency created successfully.', 'farmfaucet'),
                'currency_id' => $currency_id
            ]);
        } else {
            error_log('Failed to create currency in ajax_create_currency');
            wp_send_json_error(['message' => __('Failed to create currency. Please check the error logs.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for updating a currency
     */
    public function ajax_update_currency()
    {
        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to update currencies.', 'farmfaucet')]);
        }

        // Check nonce
        if (!isset($_POST['nonce'])) {
            wp_send_json_error(['message' => __('Security check failed: Missing nonce.', 'farmfaucet')]);
            return;
        }

        // Accept any nonce format for maximum compatibility
        $valid_nonce = wp_verify_nonce($_POST['nonce'], 'farmfaucet-currency-maker-admin-nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_currency_maker_admin_nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_admin_nonce');

        if (!$valid_nonce) {
            error_log('Invalid nonce in ajax_update_currency: ' . $_POST['nonce']);
            wp_send_json_error(['message' => __('Security check failed: Invalid nonce.', 'farmfaucet')]);
            return;
        }

        // Validate required fields
        if (!isset($_POST['currency_id'])) {
            wp_send_json_error(['message' => __('Missing currency ID.', 'farmfaucet')]);
        }

        $currency_id = intval($_POST['currency_id']);

        // Prepare update data
        $update_data = [];

        if (isset($_POST['name'])) {
            $update_data['name'] = $_POST['name'];
        }

        if (isset($_POST['symbol'])) {
            $update_data['symbol'] = $_POST['symbol'];
        }

        if (isset($_POST['base_currency'])) {
            $update_data['base_currency'] = $_POST['base_currency'];
        }

        if (isset($_POST['exchange_rate'])) {
            $update_data['exchange_rate'] = $_POST['exchange_rate'];
        }

        if (isset($_POST['color'])) {
            $update_data['color'] = $_POST['color'];
        }

        if (isset($_POST['icon'])) {
            $update_data['icon'] = $_POST['icon'];
        }

        if (isset($_POST['currency_type'])) {
            $update_data['currency_type'] = $_POST['currency_type'];
        }

        if (isset($_POST['is_active'])) {
            $update_data['is_active'] = intval($_POST['is_active']);
        }

        // Update currency
        $result = $this->update_currency($currency_id, $update_data);

        if ($result) {
            wp_send_json_success([
                'message' => __('Currency updated successfully.', 'farmfaucet')
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to update currency. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for deleting a currency
     */
    public function ajax_delete_currency()
    {
        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to delete currencies.', 'farmfaucet')]);
        }

        // Check nonce
        if (!isset($_POST['nonce'])) {
            wp_send_json_error(['message' => __('Security check failed: Missing nonce.', 'farmfaucet')]);
            return;
        }

        // Accept any nonce format for maximum compatibility
        $valid_nonce = wp_verify_nonce($_POST['nonce'], 'farmfaucet-currency-maker-admin-nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_currency_maker_admin_nonce') ||
            wp_verify_nonce($_POST['nonce'], 'farmfaucet_admin_nonce');

        if (!$valid_nonce) {
            error_log('Invalid nonce in ajax_delete_currency: ' . $_POST['nonce']);
            wp_send_json_error(['message' => __('Security check failed: Invalid nonce.', 'farmfaucet')]);
            return;
        }

        // Validate required fields
        if (!isset($_POST['currency_id'])) {
            wp_send_json_error(['message' => __('Missing currency ID.', 'farmfaucet')]);
        }

        $currency_id = intval($_POST['currency_id']);

        // Delete currency
        $result = $this->delete_currency($currency_id);

        if ($result) {
            wp_send_json_success([
                'message' => __('Currency deleted successfully.', 'farmfaucet')
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete currency. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * Render currency list shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_currency_list_shortcode($atts)
    {
        $atts = shortcode_atts([
            'title' => __('Available Currencies', 'farmfaucet'),
            'show_exchange' => 'yes',
            'columns' => 3,
        ], $atts, 'farmfaucet_currency_list');

        // Check if Currency Maker is enabled
        if (!get_option('farmfaucet_currency_maker_enabled', 1)) {
            return '<div class="farmfaucet-info">' . __('Currency system is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Get active currencies
        $currencies = $this->get_currencies(true);

        if (empty($currencies)) {
            return '<div class="farmfaucet-info">' . __('No currencies available.', 'farmfaucet') . '</div>';
        }

        // Get user balances if logged in
        $user_balances = [];
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            foreach ($currencies as $currency) {
                $user_balances[$currency['id']] = $this->get_user_currency_balance($user_id, $currency['id']);
            }
        }

        // Determine number of columns
        $columns = intval($atts['columns']);
        if ($columns < 1 || $columns > 4) {
            $columns = 3;
        }

        $show_exchange = $atts['show_exchange'] === 'yes';

        ob_start();
?>
        <div class="farmfaucet-currency-list">
            <?php if (!empty($atts['title'])) : ?>
                <h3 class="farmfaucet-currency-list-title"><?php echo esc_html($atts['title']); ?></h3>
            <?php endif; ?>

            <div class="farmfaucet-currency-grid columns-<?php echo esc_attr($columns); ?>">
                <?php foreach ($currencies as $currency) : ?>
                    <div class="farmfaucet-currency-card" style="border-color: <?php echo esc_attr($currency['color']); ?>">
                        <div class="farmfaucet-currency-header" style="background-color: <?php echo esc_attr($currency['color']); ?>">
                            <?php if (!empty($currency['icon'])) : ?>
                                <img src="<?php echo esc_url($currency['icon']); ?>" alt="<?php echo esc_attr($currency['name']); ?>" class="farmfaucet-currency-icon">
                            <?php endif; ?>
                            <h4 class="farmfaucet-currency-name"><?php echo esc_html($currency['name']); ?></h4>
                            <div class="farmfaucet-currency-code"><?php echo esc_html($currency['code']); ?></div>
                        </div>

                        <div class="farmfaucet-currency-body">
                            <?php if (is_user_logged_in()) : ?>
                                <div class="farmfaucet-currency-balance">
                                    <span class="balance-value"><?php echo esc_html(number_format($user_balances[$currency['id']], 8)); ?></span>
                                    <span class="balance-symbol"><?php echo esc_html($currency['symbol']); ?></span>
                                </div>
                            <?php endif; ?>

                            <div class="farmfaucet-currency-info">
                                <div class="info-row">
                                    <span class="info-label"><?php _e('Base Currency:', 'farmfaucet'); ?></span>
                                    <span class="info-value"><?php echo esc_html($currency['base_currency']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label"><?php _e('Exchange Rate:', 'farmfaucet'); ?></span>
                                    <span class="info-value">1 <?php echo esc_html($currency['code']); ?> = <?php echo esc_html(number_format($currency['exchange_rate'], 8)); ?> <?php echo esc_html($currency['base_currency']); ?></span>
                                </div>
                            </div>
                        </div>

                        <?php if ($show_exchange && is_user_logged_in() && count($currencies) > 1) : ?>
                            <div class="farmfaucet-currency-footer">
                                <button class="farmfaucet-exchange-button" data-currency-id="<?php echo esc_attr($currency['id']); ?>">
                                    <?php _e('Exchange', 'farmfaucet'); ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if ($show_exchange && is_user_logged_in() && count($currencies) > 1) : ?>
                <div class="farmfaucet-exchange-form" style="display: none;">
                    <h4 class="exchange-form-title"><?php _e('Exchange Currency', 'farmfaucet'); ?></h4>

                    <div class="farmfaucet-form-group">
                        <label for="farmfaucet-exchange-from"><?php _e('From Currency', 'farmfaucet'); ?></label>
                        <select id="farmfaucet-exchange-from" name="from_currency_id">
                            <?php foreach ($currencies as $currency) : ?>
                                <option value="<?php echo esc_attr($currency['id']); ?>" data-balance="<?php echo esc_attr($user_balances[$currency['id']]); ?>" data-symbol="<?php echo esc_attr($currency['symbol']); ?>">
                                    <?php echo esc_html($currency['name']); ?> (<?php echo esc_html(number_format($user_balances[$currency['id']], 8)); ?> <?php echo esc_html($currency['symbol']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="farmfaucet-form-group">
                        <label for="farmfaucet-exchange-to"><?php _e('To Currency', 'farmfaucet'); ?></label>
                        <select id="farmfaucet-exchange-to" name="to_currency_id">
                            <?php foreach ($currencies as $currency) : ?>
                                <option value="<?php echo esc_attr($currency['id']); ?>" data-symbol="<?php echo esc_attr($currency['symbol']); ?>">
                                    <?php echo esc_html($currency['name']); ?> (<?php echo esc_html($currency['symbol']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="farmfaucet-form-group">
                        <label for="farmfaucet-exchange-amount"><?php _e('Amount', 'farmfaucet'); ?></label>
                        <div class="amount-input-wrapper">
                            <input type="number" id="farmfaucet-exchange-amount" name="amount" min="0.00000001" step="0.00000001" required>
                            <span class="amount-symbol"></span>
                        </div>
                        <small class="balance-info"></small>
                    </div>

                    <div class="farmfaucet-form-group">
                        <label><?php _e('Exchange Fee', 'farmfaucet'); ?></label>
                        <div class="fee-info">
                            <?php echo esc_html(get_option('farmfaucet_currency_exchange_fee', 2.0)); ?>%
                        </div>
                    </div>

                    <div class="farmfaucet-form-group">
                        <label><?php _e('You Will Receive', 'farmfaucet'); ?></label>
                        <div class="receive-amount">0.00000000 <span class="receive-symbol"></span></div>
                    </div>

                    <div class="farmfaucet-form-actions">
                        <button type="button" class="farmfaucet-exchange-submit"><?php _e('Exchange', 'farmfaucet'); ?></button>
                        <button type="button" class="farmfaucet-exchange-cancel"><?php _e('Cancel', 'farmfaucet'); ?></button>
                    </div>

                    <div class="farmfaucet-exchange-message" style="display: none;"></div>
                </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render currency balance shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_currency_balance_shortcode($atts)
    {
        $atts = shortcode_atts([
            'currency' => '',
            'display' => 'card', // card, inline, badge
            'show_name' => 'yes',
            'show_icon' => 'yes',
        ], $atts, 'farmfaucet_currency_balance');

        // Check if Currency Maker is enabled
        if (!get_option('farmfaucet_currency_maker_enabled', 1)) {
            return '';
        }

        // Don't show balance for non-logged-in users
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-info">' . __('Please log in to view your balance.', 'farmfaucet') . '</div>';
        }

        $user_id = get_current_user_id();

        // Get currency
        $currency = null;
        if (!empty($atts['currency'])) {
            $currency = $this->get_currency_by_code($atts['currency']);

            if (!$currency) {
                return '<div class="farmfaucet-error">' . __('Currency not found.', 'farmfaucet') . '</div>';
            }

            // Get user balance for this currency
            $balance = $this->get_user_currency_balance($user_id, $currency['id']);
        } else {
            // Get all user balances
            $balances = $this->get_user_currency_balances($user_id);

            if (empty($balances)) {
                return '<div class="farmfaucet-info">' . __('No currency balances available.', 'farmfaucet') . '</div>';
            }

            // Use the first balance
            $first_balance = reset($balances);
            $currency_id = $first_balance['currency_id'];
            $currency = $this->get_currency($currency_id);
            $balance = $first_balance['balance'];
        }

        $show_name = $atts['show_name'] === 'yes';
        $show_icon = $atts['show_icon'] === 'yes';
        $display_type = $atts['display'];

        ob_start();

        switch ($display_type) {
            case 'inline':
        ?>
                <span class="farmfaucet-currency-balance-inline">
                    <?php if ($show_icon && !empty($currency['icon'])) : ?>
                        <img src="<?php echo esc_url($currency['icon']); ?>" alt="<?php echo esc_attr($currency['code']); ?>" class="balance-icon">
                    <?php endif; ?>

                    <?php if ($show_name) : ?>
                        <span class="balance-name"><?php echo esc_html($currency['name']); ?>:</span>
                    <?php endif; ?>

                    <span class="balance-value"><?php echo esc_html(number_format($balance, 8)); ?></span>
                    <span class="balance-symbol"><?php echo esc_html($currency['symbol']); ?></span>
                </span>
            <?php
                break;

            case 'badge':
            ?>
                <div class="farmfaucet-currency-balance-badge" style="background-color: <?php echo esc_attr($currency['color']); ?>">
                    <?php if ($show_icon && !empty($currency['icon'])) : ?>
                        <img src="<?php echo esc_url($currency['icon']); ?>" alt="<?php echo esc_attr($currency['code']); ?>" class="badge-icon">
                    <?php endif; ?>

                    <span class="badge-value"><?php echo esc_html(number_format($balance, 8)); ?></span>
                    <span class="badge-symbol"><?php echo esc_html($currency['symbol']); ?></span>
                </div>
            <?php
                break;

            case 'card':
            default:
            ?>
                <div class="farmfaucet-currency-balance-card" style="border-color: <?php echo esc_attr($currency['color']); ?>">
                    <div class="card-header" style="background-color: <?php echo esc_attr($currency['color']); ?>">
                        <?php if ($show_icon && !empty($currency['icon'])) : ?>
                            <img src="<?php echo esc_url($currency['icon']); ?>" alt="<?php echo esc_attr($currency['code']); ?>" class="card-icon">
                        <?php endif; ?>

                        <?php if ($show_name) : ?>
                            <span class="card-name"><?php echo esc_html($currency['name']); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="card-body">
                        <div class="card-balance">
                            <span class="balance-value"><?php echo esc_html(number_format($balance, 8)); ?></span>
                            <span class="balance-symbol"><?php echo esc_html($currency['symbol']); ?></span>
                        </div>
                    </div>
                </div>
        <?php
                break;
        }

        return ob_get_clean();
    }

    /**
     * Render admin settings page
     */
    public static function render_admin_page()
    {
        // Get currencies
        $currency_maker = self::init();
        $currencies = $currency_maker->get_currencies(false);

        // Get base currencies
        $base_currencies = [
            'BTC' => __('Bitcoin (BTC)', 'farmfaucet'),
            'ETH' => __('Ethereum (ETH)', 'farmfaucet'),
            'LTC' => __('Litecoin (LTC)', 'farmfaucet'),
            'DOGE' => __('Dogecoin (DOGE)', 'farmfaucet'),
            'BCH' => __('Bitcoin Cash (BCH)', 'farmfaucet'),
            'TRX' => __('Tron (TRX)', 'farmfaucet'),
            'USDT' => __('Tether (USDT)', 'farmfaucet'),
        ];

        ?>
        <div class="farmfaucet-admin-section currency-maker-section">
            <h2><?php _e('Currency Manager Settings', 'farmfaucet'); ?></h2>

            <?php include(FARMFAUCET_DIR . 'templates/admin/currency-maker-welcome.php'); ?>

            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php _e('General Settings', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="farmfaucet_currency_maker_enabled"><?php _e('Enable Currency Manager', 'farmfaucet'); ?></label>
                            </th>
                            <td>
                                <input type="checkbox"
                                    id="farmfaucet_currency_maker_enabled"
                                    name="farmfaucet_currency_maker_enabled"
                                    value="1"
                                    <?php checked(get_option('farmfaucet_currency_maker_enabled', 1), 1); ?>>
                                <p class="description">
                                    <?php _e('Enable or disable the Currency Manager system.', 'farmfaucet'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="farmfaucet_currency_exchange_fee"><?php _e('Exchange Fee (%)', 'farmfaucet'); ?></label>
                            </th>
                            <td>
                                <input type="number"
                                    id="farmfaucet_currency_exchange_fee"
                                    name="farmfaucet_currency_exchange_fee"
                                    value="<?php echo esc_attr(get_option('farmfaucet_currency_exchange_fee', 2.0)); ?>"
                                    class="small-text"
                                    min="0"
                                    max="100"
                                    step="0.01">
                                <p class="description">
                                    <?php _e('Fee percentage for currency exchanges.', 'farmfaucet'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php _e('Manage Currencies', 'farmfaucet'); ?></h3>
                    <button type="button" class="farmfaucet-add-currency-button button button-primary">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Add Currency', 'farmfaucet'); ?>
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($currencies)) : ?>
                        <p class="farmfaucet-no-currencies">
                            <?php _e('No currencies found. Click the "Add Currency" button to create your first currency.', 'farmfaucet'); ?>
                        </p>
                    <?php else : ?>
                        <table class="farmfaucet-currencies-table widefat">
                            <thead>
                                <tr>
                                    <th><?php _e('Name', 'farmfaucet'); ?></th>
                                    <th><?php _e('Code', 'farmfaucet'); ?></th>
                                    <th><?php _e('Symbol', 'farmfaucet'); ?></th>
                                    <th><?php _e('Base Currency', 'farmfaucet'); ?></th>
                                    <th><?php _e('Exchange Rate', 'farmfaucet'); ?></th>
                                    <th><?php _e('Type', 'farmfaucet'); ?></th>
                                    <th><?php _e('Color', 'farmfaucet'); ?></th>
                                    <th><?php _e('Status', 'farmfaucet'); ?></th>
                                    <th><?php _e('Actions', 'farmfaucet'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($currencies as $currency) : ?>
                                    <tr data-currency-id="<?php echo esc_attr($currency['id']); ?>" data-currency-type="<?php echo esc_attr($currency['currency_type'] ?? 'earnings'); ?>">
                                        <td>
                                            <?php if (!empty($currency['icon'])) : ?>
                                                <img src="<?php echo esc_url($currency['icon']); ?>" alt="<?php echo esc_attr($currency['name']); ?>" class="currency-icon">
                                            <?php endif; ?>
                                            <?php echo esc_html($currency['name']); ?>
                                        </td>
                                        <td><?php echo esc_html($currency['code']); ?></td>
                                        <td><?php echo esc_html($currency['symbol']); ?></td>
                                        <td><?php echo esc_html($currency['base_currency']); ?></td>
                                        <td><?php echo esc_html(number_format($currency['exchange_rate'], 8)); ?></td>
                                        <td>
                                            <span class="currency-type-badge <?php echo esc_attr($currency['currency_type'] ?? 'earnings'); ?>">
                                                <?php echo esc_html(ucfirst($currency['currency_type'] ?? 'earnings')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="color-preview" style="background-color: <?php echo esc_attr($currency['color']); ?>"></div>
                                        </td>
                                        <td>
                                            <?php if ($currency['is_active']) : ?>
                                                <span class="status-active"><?php _e('Active', 'farmfaucet'); ?></span>
                                            <?php else : ?>
                                                <span class="status-inactive"><?php _e('Inactive', 'farmfaucet'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button type="button" class="farmfaucet-edit-currency-button button button-small">
                                                <span class="dashicons dashicons-edit"></span> <?php _e('Edit', 'farmfaucet'); ?>
                                            </button>
                                            <button type="button" class="farmfaucet-delete-currency-button button button-small">
                                                <span class="dashicons dashicons-trash"></span> <?php _e('Delete', 'farmfaucet'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php _e('Shortcodes', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <p class="description">
                        <?php _e('Use these shortcodes to display currencies and balances on your site.', 'farmfaucet'); ?>
                    </p>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Currency List', 'farmfaucet'); ?></th>
                            <td>
                                <code>[farmfaucet_currency_list]</code>
                                <p class="description">
                                    <?php _e('Displays a list of available currencies with exchange functionality.', 'farmfaucet'); ?>
                                </p>
                                <p class="description">
                                    <?php _e('Optional attributes:', 'farmfaucet'); ?>
                                    <code>title="Available Currencies"</code>
                                    <code>show_exchange="yes"</code>
                                    <code>columns="3"</code>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Currency Balance', 'farmfaucet'); ?></th>
                            <td>
                                <code>[farmfaucet_currency_balance]</code>
                                <p class="description">
                                    <?php _e('Displays the user\'s balance for a specific currency.', 'farmfaucet'); ?>
                                </p>
                                <p class="description">
                                    <?php _e('Optional attributes:', 'farmfaucet'); ?>
                                    <code>currency="BTC"</code>
                                    <code>display="card"</code> (card, inline, badge)
                                    <code>show_name="yes"</code>
                                    <code>show_icon="yes"</code>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Currency Form Modal -->
            <div id="farmfaucet-currency-modal" class="farmfaucet-modal" style="display: none;">
                <div class="farmfaucet-modal-content">
                    <div class="farmfaucet-modal-header">
                        <h3 class="modal-title"><?php _e('Add Currency', 'farmfaucet'); ?></h3>
                        <span class="farmfaucet-modal-close">&times;</span>
                    </div>
                    <div class="farmfaucet-modal-body">
                        <form id="farmfaucet-currency-form">
                            <input type="hidden" id="currency-id" name="currency_id" value="">

                            <div class="farmfaucet-form-group">
                                <label for="currency-name"><?php _e('Currency Name', 'farmfaucet'); ?></label>
                                <input type="text" id="currency-name" name="name" required>
                                <p class="description"><?php _e('Enter the full name of the currency (e.g., "Bitcoin").', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-code"><?php _e('Currency Code', 'farmfaucet'); ?></label>
                                <input type="text" id="currency-code" name="code" required maxlength="10">
                                <p class="description"><?php _e('Enter a unique code for the currency (e.g., "BTC").', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-symbol"><?php _e('Currency Symbol', 'farmfaucet'); ?></label>
                                <input type="text" id="currency-symbol" name="symbol" required maxlength="10">
                                <p class="description"><?php _e('Enter the symbol for the currency (e.g., "₿").', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-base"><?php _e('Base Currency', 'farmfaucet'); ?></label>
                                <select id="currency-base" name="base_currency" required>
                                    <?php foreach ($base_currencies as $code => $name) : ?>
                                        <option value="<?php echo esc_attr($code); ?>"><?php echo esc_html($name); ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description"><?php _e('Select the base currency for exchange rate calculation.', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-rate"><?php _e('Exchange Rate', 'farmfaucet'); ?></label>
                                <input type="number" id="currency-rate" name="exchange_rate" required min="0.00000001" step="0.00000001">
                                <p class="description"><?php _e('Enter the exchange rate relative to the base currency.', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-color"><?php _e('Color', 'farmfaucet'); ?></label>
                                <input type="text" id="currency-color" name="color" class="color-picker" value="#4CAF50">
                                <p class="description"><?php _e('Choose a color for the currency.', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-icon"><?php _e('Icon URL', 'farmfaucet'); ?></label>
                                <input type="url" id="currency-icon" name="icon">
                                <p class="description"><?php _e('Enter a URL for the currency icon (optional).', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-type"><?php _e('Currency Type', 'farmfaucet'); ?></label>
                                <select id="currency-type" name="currency_type" required>
                                    <option value="earnings"><?php _e('Earnings Currency', 'farmfaucet'); ?></option>
                                    <option value="advertisement"><?php _e('Advertisement Balance Currency', 'farmfaucet'); ?></option>
                                </select>
                                <p class="description"><?php _e('Select the type of currency. Earnings currencies can be converted to Advertisement Balance currencies.', 'farmfaucet'); ?></p>
                            </div>

                            <div class="farmfaucet-form-group">
                                <label for="currency-active"><?php _e('Status', 'farmfaucet'); ?></label>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="currency-active" name="is_active" value="1" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <p class="description"><?php _e('Enable or disable this currency.', 'farmfaucet'); ?></p>
                            </div>
                        </form>
                    </div>
                    <div class="farmfaucet-modal-footer">
                        <button type="button" id="farmfaucet-save-currency" class="button button-primary"><?php _e('Save Currency', 'farmfaucet'); ?></button>
                        <button type="button" class="farmfaucet-modal-close button"><?php _e('Cancel', 'farmfaucet'); ?></button>
                    </div>
                </div>
            </div>
        </div>
<?php
    }
}
