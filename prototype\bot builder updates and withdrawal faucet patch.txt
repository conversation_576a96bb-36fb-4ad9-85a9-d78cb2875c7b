
  in the bot builder tab the bot managment section isnt built properly... i see 2 buttons to create a new bot one says "add new bot "and the other says "create your first bot" and also there are some input fields for the details of the bot, scrap the fields and the button and redesign it, ensure the design of the bot management card is modern and follows this pattern, first there will be a button to the side to create a new bot, te button will be green and consistent with the plugin design..make it so the lookout design is similar to the faucet tab where admin will create a bot and add the details in a pop up, there will be basic detail to create a bot which will be the bot api the basic details of the bot and the start message as an example of how to add commands to the bot. when the bot is created the bots will have thier own sub tab section where now individual bots will show,  ensure the admin can add a new comand and then admin see a pop up to configure the details and features of the command, the features can be reply with inline buttons on the messages, or reply with bot button menus and also specify the text and link of the button menu, also there should be a way for each button command repons for admin get the link of each respond or acurate program the response button to bring the next feature, use all the features of the telegram bots as it should be available for admin to customise the bot by adding a command and a response to that command, and admin can can respond . create an interactive menu after the user have created the bot there will be vertical row of all the existing commnd of the bot in round borders and also clicking them will allow admin edit thier functions , and admin can still click to add new commands to a existing bot, ensure the bot managment section is similar to faucets tab where each created bot shows in horizontal tab and using modern css and java smooth animation will rotate the bot and allow it show each one based on which is selected , also create a chat box in the bot management section where admin can chat with the bot with the commands he has made and saved for the bot, ensure the chat box has the bot reply with a demo and well design structure of the chat bot with messages similar to how basic telegrram bots respond, with inline buttons and buttons or those strong buttons, 

moving to the faucet types, there are some issues with the withdrawal faucet and dummy faucet,

add an option in the currency maker where admin can set the currency type to earnings type currency or advertisement ballance currency .. if a currency is set as withdrawal then it will only show up in the front end when the user is using the convert feature in the convert form to currency from one type to the other, now the convert form will allow mainly for admin to convert currency only from earning type tp advertisement ballance currency

for the withdrawal faucet, observe the way the front end looks, change the button from saying claim now to withdraw...remove the dynamic text that says complete task and the one that shows the amount the user will receive as this type will not be based on the amount the admin set , since it is only the list of currency set by admin the user can withdraw to, the only option that should be beside the user email or crypto adress field should be a drop down option for the user to select which crypto they want to withdrawn in and then select the ballance input field to set the amount they want to withdraw, if the ballance is lower than the withdrawal ballance set by the admin then it will show them an error 
user will fill this field and the withdraw faucet will check the withdraw currency rate based on he base currency and convert it . there will be an option in the form to change the withdraw form from withdraw to convert, if convert form is set then the user will not see the option to set thier email  instead the form will be different with different function for converting ballance from earning ballance to advertisement ballance , they will set the balance the want and the option to set the currency they want to convert to, this will now show the list of currencies on the website with the ADVERTISEMENT BALLANCE currency type.  instead of the list of currencies to withdraw, also the option for admin to set which currencies on the websie will be listed on the front end to be converted to will be right beside the option to set the currencies to withdraw to and will only be the currencies with the advertisement ballance type that can be selected. 

 in the admin section of creating the withdrawal faucet they will see another field that cannot be edited but is autto populated based on the value they will receive, the convert form will always be attached to the withdraw front end form as they perform the same thing which is to allow the user deduct their ballance and either select which currency they want to withdraw to or set the currency advertisement ballance they want to convert to . ensure the  of each will be there and will changed based on which version. add improved styling so admin can set the default card display format, or a compact display format, the compact display will have a more horizontal look to it in i'ts designs and will still have the transaparent or color gradient background editing features, also cross check it seems the captcha might not be rendering properly in the faucets,  ensure to implement the updates correctly without any duplicate files, cross check the plugin update requirements parts to ensure every thing fully implemented.

user should never be able to convert advertisement balance to earning ballance as it is against the law so people shouldn't use it for money laundering..
the front end user ballance display ensure when the currency created is displayed with the user ballance, ensure the user ballance front end shows the selected currency on the left in a horizontal tab and / sign with then base currency on the left showing the user what their current ballance is worth in real time base on that currency, then if the user click on the base currency a drop down will show where user can select another currency to see the ballance, ensure the information is correct live from the faucet api and the value is correctly based on the intitial value set when admin is createing the currency. also ensure the user ballance always shows 000.00 even if the ballance is empty and minimum 000.0000000 like so, incase of crypto currencies that are less than . in their real time value but every currency both the created ad ballance and the real onces should use that format as basis to display currency. 

the front end user display name front end form now works, but the user display name shortcode for displaying just the name no longer shows properly, ensure the user display shortcode shows the name properly, and instead of the display name showing the display name, let it show the users username instead and let the users username form when updating after changing the name and clicking edit, let it also show a otp field requiring otp and then sending the otp to that users telegram number using the telegram bot configured for the login and sign up process... 
add the option in the telegram bot login section in the tg bot login tab, the option for admin to set the background color and gradient or either transparent background and ensure it saves properly and the tab is consistent with the design of the plugin. 
