/**
 * Farm Faucet Milestone Display Fix
 * 
 * Fixes issues with milestone display style selection in the admin panel
 */
(function($) {
    $(document).ready(function() {
        // Fix for milestone display style selection
        function initMilestoneDisplayStyles() {
            // Handle milestone display style selection
            $('.milestone-display-style-option').on('click', function() {
                var $this = $(this);
                var style = $this.data('style');
                
                // Update radio button
                $('input[name="milestone_display_style"][value="' + style + '"]').prop('checked', true);
                
                // Update visual selection
                $('.milestone-display-style-option').removeClass('selected');
                $this.addClass('selected');
                
                console.log('Selected milestone display style:', style);
            });
            
            // Initialize milestone display style based on selected radio button
            var selectedStyle = $('input[name="milestone_display_style"]:checked').val() || 'card';
            $('.milestone-display-style-option[data-style="' + selectedStyle + '"]').addClass('selected');
            
            // Handle milestone card background style changes
            $('input[name="milestone_card_bg_style"]').on('change', function() {
                var style = $(this).val();
                
                // Show/hide appropriate color pickers
                if (style === 'solid') {
                    $('.milestone-card-solid-color').show();
                    $('.milestone-card-gradient-colors').hide();
                } else {
                    $('.milestone-card-solid-color').hide();
                    $('.milestone-card-gradient-colors').show();
                }
                
                updateMilestonePreview();
            });
            
            // Handle milestone bar style changes
            $('input[name="milestone_bar_style"]').on('change', function() {
                var style = $(this).val();
                
                // Show/hide appropriate color pickers
                if (style === 'solid') {
                    $('.milestone-solid-color').show();
                    $('.milestone-gradient-colors').hide();
                } else {
                    $('.milestone-solid-color').hide();
                    $('.milestone-gradient-colors').show();
                }
                
                updateMilestonePreview();
            });
            
            // Handle transparent background checkbox
            $('#button-milestone-transparent-bg').on('change', function() {
                if ($(this).is(':checked')) {
                    $('.milestone-card-bg-options').hide();
                } else {
                    $('.milestone-card-bg-options').show();
                }
                
                updateMilestonePreview();
            });
            
            // Handle color picker changes
            $('.milestone-color-picker').on('input', function() {
                updateMilestonePreview();
            });
            
            // Update milestone preview
            function updateMilestonePreview() {
                var barStyle = $('input[name="milestone_bar_style"]:checked').val();
                var $previewBar = $('#milestone-preview-bar');
                
                if (barStyle === 'solid') {
                    var barColor = $('#milestone-bar-color').val();
                    $previewBar.css({
                        'background': barColor,
                        'background-image': 'none'
                    });
                } else {
                    var gradientStart = $('#milestone-gradient-start').val();
                    var gradientEnd = $('#milestone-gradient-end').val();
                    $previewBar.css({
                        'background': 'linear-gradient(to right, ' + gradientStart + ', ' + gradientEnd + ')'
                    });
                }
            }
            
            // Initialize preview
            updateMilestonePreview();
        }
        
        // Initialize when the dialog opens
        $(document).on('dialogopen', '#button-form-dialog', function() {
            setTimeout(initMilestoneDisplayStyles, 500);
        });
        
        // Also initialize on document ready for any existing forms
        initMilestoneDisplayStyles();
    });
})(jQuery);
