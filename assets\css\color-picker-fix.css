/**
 * Color Picker Fix for Farm Faucet
 *
 * This CSS file fixes the color picker styling in the admin panel.
 */

/* Fix for color picker in admin panel */
.wp-picker-container {
    position: relative;
    display: inline-block;
    margin-right: 10px !important;
    vertical-align: middle !important;
}

/* Make the color picker button circular */
.wp-color-result {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    border: 1px solid #ddd !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    overflow: hidden !important;
    position: relative !important;
    cursor: pointer !important;
    display: inline-block !important;
    margin-right: 0 !important;
}

/* Hide the text label on the color picker button */
.wp-color-result-text {
    display: none !important;
}

/* Position the color picker popup */
.wp-picker-container .iris-picker {
    position: absolute !important;
    z-index: 999999 !important;
    display: block !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
    background: #fff !important;
    margin-top: 5px !important;
    left: 0 !important;
}

/* Fix for color input field */
.wp-picker-container input[type="text"].wp-color-picker {
    width: 65px !important;
    height: 32px !important;
    margin-left: 5px !important;
    vertical-align: top !important;
    border-radius: 4px !important;
    border: 1px solid #ddd !important;
    padding: 0 5px !important;
    font-size: 12px !important;
}

/* Fix for color picker clear button */
.wp-picker-container .wp-picker-clear {
    height: 32px !important;
    margin-left: 2px !important;
    vertical-align: top !important;
    border-radius: 4px !important;
    border: 1px solid #ddd !important;
    background: #f7f7f7 !important;
    color: #555 !important;
    padding: 0 5px !important;
    font-size: 11px !important;
    min-width: 40px !important;
}

/* Fix for color picker in faucet form */
#faucet-form .wp-picker-container,
#button-form .wp-picker-container,
#currency-form .wp-picker-container {
    margin-bottom: 5px !important;
    margin-top: 5px !important;
}

/* Fix for color picker in currency maker */
#currency-form .wp-picker-container {
    z-index: 999999 !important;
}

/* Fix for color picker in modals */
.ui-dialog .wp-picker-container .iris-picker {
    z-index: 999999 !important;
}

/* Fix for all color inputs in the admin panel */
input[type="color"] {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    border: 1px solid #ddd !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    background-color: transparent !important;
    vertical-align: middle !important;
    margin-right: 5px !important;
}

/* Fix for color input container */
.form-field input[type="color"] {
    margin-right: 5px !important;
}

/* Fix for color picker in faucet form dialog */
#faucet-form-dialog .wp-picker-container {
    z-index: 999999 !important;
}

/* Fix for color picker in button form dialog */
#button-form-dialog .wp-picker-container {
    z-index: 999999 !important;
}

/* Fix for color picker in currency form dialog */
#currency-form-dialog .wp-picker-container {
    z-index: 999999 !important;
}

/* Improve form field layout for color pickers */
.form-field {
    margin-bottom: 15px !important;
    position: relative !important;
}

.form-field label {
    display: block !important;
    margin-bottom: 5px !important;
    font-weight: 600 !important;
}

/* Color picker label and description layout */
.form-field .wp-picker-container + p.description {
    margin-top: 8px !important;
    margin-left: 0 !important;
    display: block !important;
    clear: both !important;
}

/* Fix for appearance settings section */
#faucet-form h3 {
    margin-top: 25px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #eee !important;
    color: #23282d !important;
}

/* Compact appearance settings layout */
#faucet-form .appearance-settings-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 15px !important;
    margin-top: 15px !important;
}

/* Fix for background options layout */
.faucet-bg-options {
    padding: 15px !important;
    background-color: #f9f9f9 !important;
    border-radius: 5px !important;
    margin-bottom: 15px !important;
    border: 1px solid #e5e5e5 !important;
}

/* Fix for radio button groups */
.radio-group {
    display: flex !important;
    gap: 15px !important;
    margin-bottom: 10px !important;
}

/* Fix for toggle switches */
.toggle-switch {
    position: relative !important;
    display: inline-block !important;
    width: 50px !important;
    height: 24px !important;
    vertical-align: middle !important;
}

.toggle-switch input {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.toggle-switch label {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: #ccc !important;
    transition: .4s !important;
    border-radius: 24px !important;
}

.toggle-switch label:before {
    position: absolute !important;
    content: "" !important;
    height: 16px !important;
    width: 16px !important;
    left: 4px !important;
    bottom: 4px !important;
    background-color: white !important;
    transition: .4s !important;
    border-radius: 50% !important;
}

.toggle-switch input:checked + label {
    background-color: #4CAF50 !important;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px) !important;
}

/* Fix for color picker alignment in form fields */
.form-field .wp-picker-container {
    display: inline-flex !important;
    align-items: center !important;
}

/* Fix for color picker in background options */
.bg-solid-option .wp-picker-container,
.bg-gradient-options .wp-picker-container {
    margin-top: 5px !important;
}
