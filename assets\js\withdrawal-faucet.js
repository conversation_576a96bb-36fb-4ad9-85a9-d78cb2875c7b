/**
 * Withdrawal Faucet JavaScript
 *
 * Handles the withdrawal faucet functionality including:
 * - Captcha validation
 * - Form submission
 * - Conversion between currencies
 * - Toggle between withdrawal and conversion modes
 */
(function($) {
    'use strict';

    // Initialize withdrawal faucet functionality
    $(document).ready(function() {
        initWithdrawalFaucet();
        loadWithdrawalHistory();
    });

    /**
     * Initialize withdrawal faucet functionality
     */
    function initWithdrawalFaucet() {
        const $form = $('#farmfaucet-withdrawal-form');
        const $withdrawalModeSwitch = $('#withdrawal-mode-switch');
        const $withdrawalCrypto = $('.withdrawal-mode.withdrawal-crypto');
        const $conversionMode = $('.conversion-mode');
        const $withdrawalAmount = $('#withdrawal-amount');
        const $conversionCurrency = $('#conversion-currency');
        const $walletAddress = $('#wallet-address');
        const $withdrawButton = $('.farmfaucet-withdraw-btn');
        const $notification = $('#farmfaucet-notification');
        const $toggleLabel = $('.toggle-label.withdrawal-label');

        // Handle mode toggle
        $withdrawalModeSwitch.on('change', function() {
            if ($(this).is(':checked')) {
                // Convert mode
                $withdrawalCrypto.hide();
                $conversionMode.show();
                $walletAddress.closest('.wallet-address-container').hide();
                updateConversionRate();
                // Change button text
                $withdrawButton.text(farmfaucetWithdrawal.i18n.convertButtonText || 'Convert');
                $toggleLabel.text(farmfaucetWithdrawal.i18n.convertModeText || 'Convert to Advertisement Balance');
            } else {
                // Withdrawal mode
                $withdrawalCrypto.show();
                $conversionMode.hide();
                $walletAddress.closest('.wallet-address-container').show();
                // Change button text
                $withdrawButton.text(farmfaucetWithdrawal.i18n.withdrawButtonText || 'Withdraw');
                $toggleLabel.text(farmfaucetWithdrawal.i18n.withdrawModeText || 'Withdraw to Wallet');
            }
        });

        // Update conversion rate when amount or currency changes
        $withdrawalAmount.on('input', updateConversionRate);
        $conversionCurrency.on('change', updateConversionRate);

        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();

            // Validate form
            if (!validateForm()) {
                return;
            }

            // Disable button to prevent multiple submissions
            $withdrawButton.prop('disabled', true);

            // Show loading notification
            let loadingNotificationId = null;
            if (window.FarmfaucetNotifications) {
                loadingNotificationId = window.FarmfaucetNotifications.faucet.withdrawalStarted();
            } else {
                $notification.removeClass('error success').addClass('info').text('Processing your request...').show();
            }

            // Prepare form data
            const formData = {
                action: 'farmfaucet_process_withdrawal',
                nonce: farmfaucetWithdrawal.nonce,
                faucet_id: farmfaucetWithdrawal.faucetId,
                amount: $withdrawalAmount.val(),
                mode: $withdrawalModeSwitch.is(':checked') ? 'convert' : 'withdraw'
            };

            // Add appropriate fields based on mode
            if (formData.mode === 'withdraw') {
                formData.withdrawal_currency = $('#withdrawal-currency').val();
                formData.wallet_address = $('#wallet-address').val();
            } else {
                formData.conversion_currency = $('#conversion-currency').val();
            }

            // Add captcha response if available
            const captchaResponse = getCaptchaResponse();
            if (captchaResponse) {
                formData.captcha_response = captchaResponse;
            }

            // Submit form via AJAX
            $.ajax({
                url: farmfaucetWithdrawal.ajaxUrl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        if (window.FarmfaucetNotifications) {
                            if (loadingNotificationId) {
                                window.FarmfaucetNotifications.hide(loadingNotificationId);
                            }
                            const amount = $withdrawalAmount.val();
                            const currency = $conversionCurrency.val();
                            window.FarmfaucetNotifications.faucet.withdrawalSuccess(amount, currency);
                        } else {
                            $notification.removeClass('error info').addClass('success').html(response.data.message || response.data).show();
                        }
                        $('.farmfaucet-error-message').hide();
                        $form[0].reset();

                        // Reset captcha
                        if (typeof hcaptcha !== 'undefined') {
                            hcaptcha.reset();
                        } else if (typeof grecaptcha !== 'undefined') {
                            grecaptcha.reset();
                        } else if (typeof turnstile !== 'undefined') {
                            turnstile.reset();
                        }

                        // Reload withdrawal history
                        loadWithdrawalHistory();

                        // Update user balance display if provided
                        if (response.data.new_balance) {
                            $('.balance').text(response.data.new_balance);
                        }
                    } else {
                        // Show error notification
                        if (window.FarmfaucetNotifications) {
                            if (loadingNotificationId) {
                                window.FarmfaucetNotifications.hide(loadingNotificationId);
                            }
                            const errorMessage = response.data && typeof response.data === 'object' && response.data.message
                                ? response.data.message
                                : response.data;
                            window.FarmfaucetNotifications.faucet.withdrawalError(errorMessage);
                        } else {
                            // Check if the response has a message object format
                            if (response.data && typeof response.data === 'object' && response.data.message) {
                                // Display in the error message div
                                $('.farmfaucet-error-message').html(response.data.message).show();
                                $notification.hide();
                            } else {
                                // Use the regular notification area
                                $notification.removeClass('success info').addClass('error').html(response.data).show();
                                $('.farmfaucet-error-message').hide();
                            }
                        }
                    }
                },
                error: function() {
                    $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.error).show();
                    $('.farmfaucet-error-message').hide();
                },
                complete: function() {
                    // Re-enable button
                    $withdrawButton.prop('disabled', false);
                }
            });
        });

        // Enable button when captcha is completed
        window.onCaptchaComplete = function() {
            $withdrawButton.prop('disabled', false).attr('aria-disabled', 'false');
        };
    }

    /**
     * Update conversion rate display
     */
    function updateConversionRate() {
        const $withdrawalAmount = $('#withdrawal-amount');
        const $conversionCurrency = $('#conversion-currency');
        const $rateValue = $('.rate-value');
        const $targetValue = $('.target-value');
        const $targetCurrency = $('.target-currency');
        const $receiveValue = $('.receive-value');
        const $receiveCurrency = $('.receive-currency');
        const $sourceCurrency = $('.source-currency');
        const $receiveAmount = $('.receive-amount');
        const $targetRate = $('.target-rate');

        const amount = parseFloat($withdrawalAmount.val()) || 0;
        const selectedOption = $conversionCurrency.find('option:selected');
        const conversionRate = parseFloat(selectedOption.data('rate')) || 1;
        const symbol = selectedOption.data('symbol') || '';
        const currencyName = selectedOption.text() || '';

        // Update currency displays
        $targetCurrency.text(symbol);
        $receiveCurrency.text(symbol);

        // Set source currency from farmfaucetWithdrawal data
        if (farmfaucetWithdrawal && farmfaucetWithdrawal.currencySymbol) {
            $sourceCurrency.text(farmfaucetWithdrawal.currencySymbol);
        }

        // Calculate and display converted amount
        const convertedAmount = amount * conversionRate;

        // Update rate display
        if ($rateValue.length) {
            $rateValue.text(conversionRate.toFixed(8));
        }

        // Update target rate display
        if ($targetRate.length) {
            $targetRate.text(conversionRate.toFixed(8));
        }

        // Update target value display
        if ($targetValue.length) {
            $targetValue.text('1');
        }

        // Update receive value display
        if ($receiveValue.length) {
            $receiveValue.text(convertedAmount.toFixed(8));
        }

        // Update receive amount display
        if ($receiveAmount.length) {
            $receiveAmount.html(convertedAmount.toFixed(8) + ' <span class="receive-symbol">' + symbol + '</span>');
        }

        // Update currency type notice
        const $currencyTypeNotice = $('.currency-type-notice');
        if ($currencyTypeNotice.length) {
            const currencyType = selectedOption.data('type') || '';
            if (currencyType === 'advertisement') {
                $currencyTypeNotice.html('<div class="ads-only-notice">Converting to <strong>' + currencyName + '</strong> (Advertisement Balance)</div>').show();
            } else {
                $currencyTypeNotice.html('').hide();
            }
        }
    }

    /**
     * Validate withdrawal form
     *
     * @returns {boolean} True if form is valid, false otherwise
     */
    function validateForm() {
        const $form = $('#farmfaucet-withdrawal-form');
        const $withdrawalAmount = $('#withdrawal-amount');
        const $notification = $('#farmfaucet-notification');
        const $withdrawalModeSwitch = $('#withdrawal-mode-switch');
        const $walletAddress = $('#wallet-address');

        // Check if amount is valid
        const amount = parseFloat($withdrawalAmount.val());
        const minAmount = parseFloat(farmfaucetWithdrawal.minAmount) || 0;
        const maxAmount = parseFloat(farmfaucetWithdrawal.maxAmount) || parseFloat(farmfaucetWithdrawal.userBalance) || 0;

        if (isNaN(amount) || amount <= 0) {
            $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.invalidAmount || 'Please enter a valid amount').show();
            return false;
        }

        if (amount < minAmount) {
            $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.belowMinimum || 'Amount is below minimum withdrawal').show();
            return false;
        }

        if (amount > maxAmount) {
            $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.aboveMaximum || 'Amount exceeds maximum withdrawal').show();
            return false;
        }

        // Check wallet address if in withdrawal mode
        if (!$withdrawalModeSwitch.is(':checked') && $walletAddress.val().trim() === '') {
            $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.invalidAddress || 'Please enter a valid wallet address').show();
            return false;
        }

        // Check captcha if required
        const captchaResponse = getCaptchaResponse();
        if ($('.farmfaucet-captcha').length > 0 && !captchaResponse) {
            $notification.removeClass('success info').addClass('error').text(farmfaucetWithdrawal.i18n.captchaRequired || 'Please complete the captcha').show();
            return false;
        }

        return true;
    }

    /**
     * Get captcha response based on captcha type
     *
     * @returns {string|null} Captcha response or null if not completed
     */
    function getCaptchaResponse() {
        const captchaType = $('input[name="captcha_type"]').val();

        if (captchaType === 'hcaptcha' && typeof hcaptcha !== 'undefined') {
            return hcaptcha.getResponse();
        } else if (captchaType === 'recaptcha' && typeof grecaptcha !== 'undefined') {
            return grecaptcha.getResponse();
        } else if (captchaType === 'turnstile' && typeof turnstile !== 'undefined') {
            return turnstile.getResponse();
        }

        return null;
    }

    /**
     * Load withdrawal history
     */
    function loadWithdrawalHistory() {
        const $historyContainer = $('.withdrawal-history-container');
        const faucetId = $('.farmfaucet-withdrawal-history').data('faucet-id');

        if (!$historyContainer.length) {
            return;
        }

        $.ajax({
            url: farmfaucetWithdrawal.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_withdrawal_history',
                faucet_id: faucetId,
                nonce: farmfaucetWithdrawal.nonce
            },
            success: function(response) {
                if (response.success) {
                    $historyContainer.html(response.data.html);
                } else {
                    $historyContainer.html('<div class="error">' + response.data.message + '</div>');
                }
            },
            error: function() {
                $historyContainer.html('<div class="error">' + farmfaucetWithdrawal.i18n.error + '</div>');
            }
        });
    }

})(jQuery);
