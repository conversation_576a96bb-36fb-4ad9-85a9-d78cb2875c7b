<?php
/**
 * Test Plugin Loading and Settings Save
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Plugin Loading Test</title>";
echo "<style>body{font-family:Arial;margin:40px;} .success{color:#4CAF50;background:#f0f8f0;padding:15px;margin:15px 0;border-left:4px solid #4CAF50;} .error{color:#f44336;background:#fdf0f0;padding:15px;margin:15px 0;border-left:4px solid #f44336;} .info{color:#2196F3;background:#f0f7ff;padding:15px;margin:15px 0;border-left:4px solid #2196F3;} .btn{background:#4CAF50;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;margin:10px 5px 10px 0;border:none;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 Plugin Loading Test</h1>";

// Test 1: Check if main plugin file can be loaded
echo "<div class='info'><h3>🔍 Test 1: Loading Main Plugin File</h3></div>";

try {
    // First check if the file exists
    if (file_exists('farmfaucet.php')) {
        echo "<div class='success'>✅ Main plugin file exists</div>";
        
        // Try to read the file content to check for syntax errors
        $plugin_content = file_get_contents('farmfaucet.php');
        if ($plugin_content !== false) {
            echo "<div class='success'>✅ Main plugin file is readable</div>";
            
            // Check for basic PHP syntax by using php -l (lint)
            $temp_file = tempnam(sys_get_temp_dir(), 'farmfaucet_test');
            file_put_contents($temp_file, $plugin_content);
            
            $output = [];
            $return_code = 0;
            exec("php -l $temp_file 2>&1", $output, $return_code);
            
            if ($return_code === 0) {
                echo "<div class='success'>✅ Main plugin file has valid PHP syntax</div>";
            } else {
                echo "<div class='error'>❌ Main plugin file has syntax errors:</div>";
                foreach ($output as $line) {
                    echo "<div class='error'>• " . htmlspecialchars($line) . "</div>";
                }
            }
            
            unlink($temp_file);
        } else {
            echo "<div class='error'>❌ Cannot read main plugin file</div>";
        }
    } else {
        echo "<div class='error'>❌ Main plugin file not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing main plugin file: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 2: Check admin class
echo "<div class='info'><h3>🔍 Test 2: Loading Admin Class</h3></div>";

try {
    if (file_exists('includes/class-farmfaucet-admin.php')) {
        echo "<div class='success'>✅ Admin class file exists</div>";
        
        // Check for syntax errors
        $admin_content = file_get_contents('includes/class-farmfaucet-admin.php');
        $temp_file = tempnam(sys_get_temp_dir(), 'farmfaucet_admin_test');
        file_put_contents($temp_file, $admin_content);
        
        $output = [];
        $return_code = 0;
        exec("php -l $temp_file 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "<div class='success'>✅ Admin class has valid PHP syntax</div>";
        } else {
            echo "<div class='error'>❌ Admin class has syntax errors:</div>";
            foreach ($output as $line) {
                echo "<div class='error'>• " . htmlspecialchars($line) . "</div>";
            }
        }
        
        unlink($temp_file);
    } else {
        echo "<div class='error'>❌ Admin class file not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing admin class: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 3: Check settings manager
echo "<div class='info'><h3>🔍 Test 3: Loading Settings Manager</h3></div>";

try {
    if (file_exists('includes/class-farmfaucet-settings-manager.php')) {
        echo "<div class='success'>✅ Settings manager file exists</div>";
        
        // Check for syntax errors
        $settings_content = file_get_contents('includes/class-farmfaucet-settings-manager.php');
        $temp_file = tempnam(sys_get_temp_dir(), 'farmfaucet_settings_test');
        file_put_contents($temp_file, $settings_content);
        
        $output = [];
        $return_code = 0;
        exec("php -l $temp_file 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "<div class='success'>✅ Settings manager has valid PHP syntax</div>";
        } else {
            echo "<div class='error'>❌ Settings manager has syntax errors:</div>";
            foreach ($output as $line) {
                echo "<div class='error'>• " . htmlspecialchars($line) . "</div>";
            }
        }
        
        unlink($temp_file);
    } else {
        echo "<div class='error'>❌ Settings manager file not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing settings manager: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 4: Test WordPress settings API integration
echo "<div class='info'><h3>🔍 Test 4: WordPress Settings API Integration</h3></div>";

try {
    // Check if the form is properly configured
    if (file_exists('includes/class-farmfaucet-admin.php')) {
        $admin_content = file_get_contents('includes/class-farmfaucet-admin.php');
        
        // Check for WordPress settings API usage
        if (strpos($admin_content, 'action="options.php"') !== false) {
            echo "<div class='success'>✅ Form action set to options.php (WordPress settings API)</div>";
        } else {
            echo "<div class='error'>❌ Form action not set to options.php</div>";
        }
        
        if (strpos($admin_content, 'settings_fields(\'farmfaucet_settings\')') !== false) {
            echo "<div class='success'>✅ settings_fields() properly called</div>";
        } else {
            echo "<div class='error'>❌ settings_fields() not found</div>";
        }
        
        if (strpos($admin_content, 'submit_button(') !== false) {
            echo "<div class='success'>✅ WordPress submit_button() used</div>";
        } else {
            echo "<div class='error'>❌ WordPress submit_button() not found</div>";
        }
        
        // Check if form values are populated
        if (strpos($admin_content, 'get_option(\'farmfaucet_captcha_type\'') !== false) {
            echo "<div class='success'>✅ Form values populated from WordPress options</div>";
        } else {
            echo "<div class='error'>❌ Form values not populated from options</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking WordPress integration: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 5: Check if settings are registered
echo "<div class='info'><h3>🔍 Test 5: Settings Registration</h3></div>";

try {
    if (file_exists('includes/class-farmfaucet-settings-manager.php')) {
        $settings_content = file_get_contents('includes/class-farmfaucet-settings-manager.php');
        
        // Check for settings registration
        if (strpos($settings_content, 'register_setting(') !== false) {
            echo "<div class='success'>✅ Settings are registered with WordPress</div>";
        } else {
            echo "<div class='error'>❌ Settings registration not found</div>";
        }
        
        // Check for specific settings
        $required_settings = [
            'farmfaucet_captcha_type',
            'farmfaucet_hcaptcha_sitekey',
            'farmfaucet_hcaptcha_secret',
            'farmfaucet_faucetpay_api'
        ];
        
        foreach ($required_settings as $setting) {
            if (strpos($settings_content, "'$setting'") !== false) {
                echo "<div class='success'>✅ Setting '$setting' is registered</div>";
            } else {
                echo "<div class='error'>❌ Setting '$setting' not found</div>";
            }
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking settings registration: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div style='background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #4CAF50;'>";
echo "<h3 style='color: #2e7d32; margin-top: 0;'>🎯 PLUGIN LOADING ANALYSIS</h3>";
echo "<div style='color: #2e7d32;'>";
echo "<h4>✅ What Should Work Now:</h4>";
echo "<ul>";
echo "<li><strong>Clean PHP Syntax:</strong> All files should have valid PHP syntax</li>";
echo "<li><strong>WordPress Settings API:</strong> Form uses proper WordPress settings handling</li>";
echo "<li><strong>Settings Registration:</strong> All settings are registered with WordPress</li>";
echo "<li><strong>Form Integration:</strong> Form submits to options.php and uses WordPress functions</li>";
echo "<li><strong>Value Population:</strong> Form fields show current saved values</li>";
echo "</ul>";

echo "<h4>🔧 How Settings Save Works:</h4>";
echo "<ul>";
echo "<li><strong>Form Submission:</strong> Form submits to WordPress options.php</li>";
echo "<li><strong>WordPress Handling:</strong> WordPress automatically processes the form</li>";
echo "<li><strong>Settings Manager:</strong> Registers all settings with proper sanitization</li>";
echo "<li><strong>Automatic Save:</strong> WordPress saves settings to database automatically</li>";
echo "<li><strong>Success Redirect:</strong> WordPress redirects back with success message</li>";
echo "</ul>";

echo "<h4>🎉 Expected Results:</h4>";
echo "<ul>";
echo "<li><strong>Plugin Loads:</strong> No more critical errors when loading admin pages</li>";
echo "<li><strong>Settings Save:</strong> All form fields save properly to WordPress database</li>";
echo "<li><strong>Values Persist:</strong> Saved values appear in form after page reload</li>";
echo "<li><strong>Success Messages:</strong> WordPress shows success messages after saving</li>";
echo "<li><strong>No Conflicts:</strong> No more method conflicts or duplicate code</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='admin.php?page=farmfaucet&tab=settings' class='btn' style='background: #2196F3;'>🚀 Test Settings Page</a>";
echo "</div>";

echo "</body></html>";
?>
