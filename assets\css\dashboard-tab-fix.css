/**
 * Farm Faucet - Dashboard Tab Fix
 * Ensures consistent styling for the dashboard tab with the rest of the plugin
 */

/* Dashboard tab container */
.farmfaucet-admin-container {
    margin: 20px 0;
}

.farmfaucet-admin-section {
    margin-bottom: 30px;
}

/* Dashboard card styling */
.farmfaucet-admin-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.farmfaucet-admin-card .card-header {
    padding: 12px 15px;
    border-bottom: 1px solid #ccd0d4;
    background: #f8f9fa;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #4CAF50; /* Green color for consistency */
}

.farmfaucet-admin-card .card-body {
    padding: 15px;
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

/* Range slider */
.range-slider-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.range-slider {
    flex: 1;
    margin-right: 10px;
    accent-color: #4CAF50; /* Green color for slider */
}

#avatar-size-value {
    min-width: 50px;
    font-weight: bold;
}

.avatar-preview {
    margin: 15px 0;
    display: flex;
    justify-content: center;
}

/* Shortcodes table */
.farmfaucet-shortcodes-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 15px;
}

.farmfaucet-shortcodes-table th {
    text-align: left;
    padding: 10px;
    background-color: #4CAF50; /* Green header background */
    color: white;
    font-weight: 600;
}

.farmfaucet-shortcodes-table td {
    padding: 10px;
    border-top: 1px solid #f0f0f1;
    vertical-align: top;
}

.farmfaucet-shortcodes-table code {
    background: #f6f7f7;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 13px;
}

/* Submit button */
.farmfaucet-admin-card .submit .button-primary {
    background: #4CAF50 !important;
    border-color: #43A047 !important;
    color: white !important;
}

.farmfaucet-admin-card .submit .button-primary:hover {
    background: #43A047 !important;
    border-color: #388E3C !important;
}

/* Dashboard tab in the tab navigation */
.nav-tab-wrapper .nav-tab[href*="tab=dashboard"].nav-tab-active {
    border-top: 3px solid #4CAF50;
    color: #4CAF50;
}

/* Make all active tabs consistent */
.nav-tab-wrapper .nav-tab.nav-tab-active {
    border-top: 3px solid #4CAF50;
    color: #4CAF50;
}

/* Tabs navigation */
.faucets-tabs-nav .ui-tabs-active a {
    color: #4CAF50 !important;
    border-bottom-color: #4CAF50 !important;
}

/* Ensure consistent styling with other tabs */
.farmfaucet-admin h2 {
    color: #4CAF50;
    font-size: 24px;
    margin-bottom: 20px;
}

/* Make all buttons consistent */
.farmfaucet-admin .button-primary {
    background: #4CAF50 !important;
    border-color: #43A047 !important;
    color: white !important;
}

.farmfaucet-admin .button-primary:hover {
    background: #43A047 !important;
    border-color: #388E3C !important;
}

/* Dashboard specific elements */
.dashboard-section h2,
.dashboard-section h3,
.dashboard-section h4 {
    color: #4CAF50;
}

.dashboard-section .card-header {
    border-left: 4px solid #4CAF50;
}

/* Fix for copy shortcode buttons */
.copy-shortcode {
    background: #4CAF50 !important;
    border-color: #43A047 !important;
    color: white !important;
}

.copy-shortcode:hover {
    background: #43A047 !important;
    border-color: #388E3C !important;
}
