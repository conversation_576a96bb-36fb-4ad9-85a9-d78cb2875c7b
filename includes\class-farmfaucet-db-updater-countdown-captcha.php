<?php

/**
 * Farmfaucet Database Updater for Countdown Captcha Feature
 *
 * Adds the countdown_captcha column to the buttons table
 */
class Farmfaucet_DB_Updater_Countdown_Captcha
{
    /**
     * Run the update to add the countdown_captcha column
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add countdown_captcha column if it doesn't exist
        if (!in_array('countdown_captcha', $column_names)) {
            $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_captcha tinyint(1) NOT NULL DEFAULT 0 AFTER countdown_standby");
        }
        
        // Add countdown_captcha_type column if it doesn't exist
        if (!in_array('countdown_captcha_type', $column_names)) {
            $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_captcha_type varchar(20) DEFAULT 'default' AFTER countdown_captcha");
        }
    }
}
