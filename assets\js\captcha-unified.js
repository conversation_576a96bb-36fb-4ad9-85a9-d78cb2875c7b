/**
 * Farm Faucet Captcha Unified
 * 
 * This script provides a unified approach to handling captchas across all faucet types.
 */
(function($) {
    'use strict';
    
    // Store captcha widgets by ID
    window.farmfaucetCaptchaWidgets = {};
    
    // Flag to track if captchas have been initialized
    window.farmfaucetCaptchasInitialized = false;
    
    // Define global callback functions for all captcha types
    window.farmfaucetCaptchaCallback = function(token) {
        console.log('Captcha callback triggered with token:', token ? token.substring(0, 10) + '...' : 'empty');
        
        // Enable all claim buttons
        $('.farmfaucet-claim-btn, .farmfaucet-withdraw-btn, .farmfaucet-convert-btn').prop('disabled', false).attr('aria-disabled', 'false');
        
        // Update status messages
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
        
        // Remove loading message
        $('.farmfaucet-captcha-loading').remove();
    };
    
    // Alias the callback for different captcha types
    window.hcaptchaCallback = window.farmfaucetCaptchaCallback;
    window.recaptchaCallback = window.farmfaucetCaptchaCallback;
    window.turnstileCallback = window.farmfaucetCaptchaCallback;
    window.enableWithdrawalButton = window.farmfaucetCaptchaCallback;
    window.enableConversionButton = window.farmfaucetCaptchaCallback;
    
    // Function to initialize hCaptcha
    function initHCaptcha() {
        console.log('Initializing hCaptcha');
        
        // Check if hCaptcha is already loaded
        if (typeof hcaptcha === 'undefined') {
            // Load hCaptcha script
            var script = document.createElement('script');
            script.src = 'https://js.hcaptcha.com/1/api.js?onload=hcaptchaCallback&render=explicit';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
            
            console.log('hCaptcha script loaded');
        } else {
            // Render hCaptcha widgets
            $('.h-captcha').each(function() {
                var $container = $(this);
                var containerId = $container.attr('id');
                var sitekey = $container.data('sitekey');
                
                if (containerId && sitekey) {
                    try {
                        var widgetId = hcaptcha.render(containerId, {
                            sitekey: sitekey,
                            callback: window.farmfaucetCaptchaCallback
                        });
                        
                        window.farmfaucetCaptchaWidgets[containerId] = widgetId;
                        console.log('hCaptcha widget rendered:', containerId);
                    } catch (e) {
                        console.error('Error rendering hCaptcha widget:', e);
                    }
                }
            });
        }
    }
    
    // Function to initialize reCAPTCHA
    function initReCaptcha() {
        console.log('Initializing reCAPTCHA');
        
        // Check if reCAPTCHA is already loaded
        if (typeof grecaptcha === 'undefined') {
            // Load reCAPTCHA script
            var script = document.createElement('script');
            script.src = 'https://www.google.com/recaptcha/api.js?onload=recaptchaCallback&render=explicit';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
            
            console.log('reCAPTCHA script loaded');
        } else {
            // Render reCAPTCHA widgets
            $('.g-recaptcha').each(function() {
                var $container = $(this);
                var containerId = $container.attr('id');
                var sitekey = $container.data('sitekey');
                
                if (containerId && sitekey) {
                    try {
                        var widgetId = grecaptcha.render(containerId, {
                            sitekey: sitekey,
                            callback: window.farmfaucetCaptchaCallback
                        });
                        
                        window.farmfaucetCaptchaWidgets[containerId] = widgetId;
                        console.log('reCAPTCHA widget rendered:', containerId);
                    } catch (e) {
                        console.error('Error rendering reCAPTCHA widget:', e);
                    }
                }
            });
        }
    }
    
    // Function to initialize Turnstile
    function initTurnstile() {
        console.log('Initializing Turnstile');
        
        // Check if Turnstile is already loaded
        if (typeof turnstile === 'undefined') {
            // Load Turnstile script
            var script = document.createElement('script');
            script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=turnstileCallback&render=explicit';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
            
            console.log('Turnstile script loaded');
        } else {
            // Render Turnstile widgets
            $('.cf-turnstile').each(function() {
                var $container = $(this);
                var containerId = $container.attr('id');
                var sitekey = $container.data('sitekey');
                
                if (containerId && sitekey) {
                    try {
                        var widgetId = turnstile.render(containerId, {
                            sitekey: sitekey,
                            callback: window.farmfaucetCaptchaCallback
                        });
                        
                        window.farmfaucetCaptchaWidgets[containerId] = widgetId;
                        console.log('Turnstile widget rendered:', containerId);
                    } catch (e) {
                        console.error('Error rendering Turnstile widget:', e);
                    }
                }
            });
        }
    }
    
    // Function to initialize all captchas
    function initCaptchas() {
        console.log('Initializing captchas');
        
        // Make sure captcha containers are visible
        $('.farmfaucet-captcha-container').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1'
        });
        
        // Initialize captchas based on what's on the page
        if ($('.h-captcha').length > 0) {
            initHCaptcha();
        }
        
        if ($('.g-recaptcha').length > 0) {
            initReCaptcha();
        }
        
        if ($('.cf-turnstile').length > 0) {
            initTurnstile();
        }
        
        // Set flag to indicate captchas have been initialized
        window.farmfaucetCaptchasInitialized = true;
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Document ready, initializing captchas');
        
        // Initialize captchas
        initCaptchas();
        
        // Try again after a delay
        setTimeout(function() {
            if (!window.farmfaucetCaptchasInitialized) {
                console.log('Captcha not initialized, trying again');
                initCaptchas();
            }
        }, 2000);
    });
    
    // Also initialize when window is fully loaded
    $(window).on('load', function() {
        console.log('Window loaded, initializing captchas');
        
        // Initialize captchas if not already initialized
        if (!window.farmfaucetCaptchasInitialized) {
            initCaptchas();
        }
    });
})(jQuery);
