<?php
/**
 * Farm Faucet - Debug Save Errors
 * 
 * This script helps identify the root cause of save errors
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Debug Save Errors</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        .step h3 { margin-top: 0; color: #333; }
        .code-block { background: #2d2d2d; color: #f8f8f2; padding: 15px; border-radius: 4px; overflow-x: auto; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Farm Faucet - Debug Save Errors</h1>
    <p>This script will identify the root cause of save errors in the Farm Faucet plugin.</p>

<?php

// Step 1: Check PHP errors
echo '<div class="step">';
echo '<h3>Step 1: PHP Error Analysis</h3>';

// Check if error logging is enabled
$error_log_enabled = ini_get('log_errors');
$error_log_file = ini_get('error_log');

echo '<p><strong>Error Logging:</strong> ' . ($error_log_enabled ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>Error Log File:</strong> ' . ($error_log_file ? $error_log_file : 'Not set') . '</p>';

// Check recent PHP errors
if ($error_log_file && file_exists($error_log_file)) {
    $log_content = file_get_contents($error_log_file);
    $recent_lines = array_slice(explode("\n", $log_content), -50); // Last 50 lines
    $farmfaucet_errors = array_filter($recent_lines, function($line) {
        return stripos($line, 'farmfaucet') !== false || stripos($line, 'fatal') !== false;
    });
    
    if (!empty($farmfaucet_errors)) {
        echo '<div class="error"><p>❌ Recent PHP errors found:</p>';
        echo '<div class="code-block">';
        foreach ($farmfaucet_errors as $error) {
            echo esc_html($error) . "\n";
        }
        echo '</div></div>';
    } else {
        echo '<div class="success"><p>✅ No recent Farm Faucet related PHP errors found</p></div>';
    }
} else {
    echo '<div class="warning"><p>⚠️ Cannot access PHP error log</p></div>';
}

echo '</div>';

// Step 2: Check class loading
echo '<div class="step">';
echo '<h3>Step 2: Class Loading Check</h3>';

$required_classes = [
    'Farmfaucet_Admin',
    'Farmfaucet_Security', 
    'Farmfaucet_Logger',
    'Farmfaucet_API'
];

foreach ($required_classes as $class) {
    if (class_exists($class)) {
        echo '<div class="success"><p>✅ ' . $class . ' - Loaded</p></div>';
    } else {
        echo '<div class="error"><p>❌ ' . $class . ' - NOT LOADED</p></div>';
    }
}

echo '</div>';

// Step 3: Check database connectivity
echo '<div class="step">';
echo '<h3>Step 3: Database Connectivity</h3>';

global $wpdb;

try {
    $result = $wpdb->get_var("SELECT 1");
    if ($result == 1) {
        echo '<div class="success"><p>✅ Database connection working</p></div>';
    } else {
        echo '<div class="error"><p>❌ Database connection issue</p></div>';
    }
} catch (Exception $e) {
    echo '<div class="error"><p>❌ Database error: ' . esc_html($e->getMessage()) . '</p></div>';
}

// Check faucets table
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if ($table_exists) {
    echo '<div class="success"><p>✅ Faucets table exists</p></div>';
    
    // Check table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    $column_count = count($columns);
    echo '<p>Table has ' . $column_count . ' columns</p>';
    
    // Check for new appearance fields
    $appearance_fields = ['button_border_radius', 'input_label_color', 'input_placeholder_color'];
    $missing_fields = [];
    
    foreach ($appearance_fields as $field) {
        $found = false;
        foreach ($columns as $column) {
            if ($column->Field === $field) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $missing_fields[] = $field;
        }
    }
    
    if (empty($missing_fields)) {
        echo '<div class="success"><p>✅ All appearance fields exist</p></div>';
    } else {
        echo '<div class="error"><p>❌ Missing fields: ' . implode(', ', $missing_fields) . '</p></div>';
    }
    
} else {
    echo '<div class="error"><p>❌ Faucets table does not exist</p></div>';
}

echo '</div>';

// Step 4: Test AJAX functionality
echo '<div class="step">';
echo '<h3>Step 4: AJAX Functionality Test</h3>';

// Check if AJAX is working
$ajax_url = admin_url('admin-ajax.php');
echo '<p><strong>AJAX URL:</strong> ' . esc_html($ajax_url) . '</p>';

// Check nonce functionality
$nonce = wp_create_nonce('farmfaucet_admin_nonce');
echo '<p><strong>Nonce generated:</strong> ' . esc_html($nonce) . '</p>';

// Verify nonce
$nonce_valid = wp_verify_nonce($nonce, 'farmfaucet_admin_nonce');
echo '<p><strong>Nonce verification:</strong> ' . ($nonce_valid ? 'Valid' : 'Invalid') . '</p>';

echo '</div>';

// Step 5: Check method existence
echo '<div class="step">';
echo '<h3>Step 5: Method Existence Check</h3>';

if (class_exists('Farmfaucet_Admin')) {
    $admin_methods = [
        'ajax_create_faucet',
        'ajax_update_faucet', 
        'encrypt_api_key_setting'
    ];
    
    foreach ($admin_methods as $method) {
        if (method_exists('Farmfaucet_Admin', $method)) {
            echo '<div class="success"><p>✅ Farmfaucet_Admin::' . $method . ' - Exists</p></div>';
        } else {
            echo '<div class="error"><p>❌ Farmfaucet_Admin::' . $method . ' - NOT FOUND</p></div>';
        }
    }
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Admin class not loaded</p></div>';
}

echo '</div>';

// Step 6: Test simple database operation
echo '<div class="step">';
echo '<h3>Step 6: Database Operation Test</h3>';

if ($table_exists) {
    try {
        // Try to get faucet count
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$faucets_table}");
        echo '<div class="success"><p>✅ Database read successful. Found ' . $count . ' faucets</p></div>';
        
        // Try a simple insert test
        $test_data = [
            'name' => 'Debug Test Faucet',
            'shortcode' => 'debug_test_' . time(),
            'faucet_type' => 'stage',
            'currency' => 'LTC',
            'amount' => '0.001',
            'cooldown' => 3600,
            'is_enabled' => 1,
            'created_at' => current_time('mysql')
        ];
        
        $insert_result = $wpdb->insert($faucets_table, $test_data);
        
        if ($insert_result !== false) {
            $test_id = $wpdb->insert_id;
            echo '<div class="success"><p>✅ Database insert successful (ID: ' . $test_id . ')</p></div>';
            
            // Clean up
            $wpdb->delete($faucets_table, ['id' => $test_id], ['%d']);
            echo '<div class="info"><p>🧹 Test record cleaned up</p></div>';
        } else {
            echo '<div class="error"><p>❌ Database insert failed: ' . $wpdb->last_error . '</p></div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error"><p>❌ Database operation error: ' . esc_html($e->getMessage()) . '</p></div>';
    }
}

echo '</div>';

// Step 7: Check WordPress hooks
echo '<div class="step">';
echo '<h3>Step 7: WordPress Hooks Check</h3>';

// Check if admin hooks are registered
$admin_hooks = [
    'wp_ajax_ajax_create_faucet',
    'wp_ajax_ajax_update_faucet'
];

foreach ($admin_hooks as $hook) {
    $has_action = has_action($hook);
    if ($has_action) {
        echo '<div class="success"><p>✅ Hook ' . $hook . ' - Registered</p></div>';
    } else {
        echo '<div class="error"><p>❌ Hook ' . $hook . ' - NOT REGISTERED</p></div>';
    }
}

echo '</div>';

// Step 8: Memory and execution limits
echo '<div class="step">';
echo '<h3>Step 8: Server Limits Check</h3>';

$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');
$max_input_vars = ini_get('max_input_vars');
$post_max_size = ini_get('post_max_size');

echo '<table style="border-collapse: collapse; width: 100%;">';
echo '<tr><th style="border: 1px solid #ddd; padding: 8px;">Setting</th><th style="border: 1px solid #ddd; padding: 8px;">Value</th><th style="border: 1px solid #ddd; padding: 8px;">Status</th></tr>';

$limits = [
    'Memory Limit' => [$memory_limit, 'Should be at least 128M'],
    'Max Execution Time' => [$max_execution_time, 'Should be at least 30 seconds'],
    'Max Input Vars' => [$max_input_vars, 'Should be at least 1000'],
    'Post Max Size' => [$post_max_size, 'Should be at least 8M']
];

foreach ($limits as $name => [$value, $recommendation]) {
    $status = '✅ OK';
    if ($name === 'Memory Limit' && (int)$value < 128) $status = '⚠️ Low';
    if ($name === 'Max Execution Time' && (int)$value < 30 && (int)$value !== 0) $status = '⚠️ Low';
    if ($name === 'Max Input Vars' && (int)$value < 1000) $status = '⚠️ Low';
    if ($name === 'Post Max Size' && (int)$value < 8) $status = '⚠️ Low';
    
    echo '<tr>';
    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $name . '</td>';
    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $value . '</td>';
    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $status . '</td>';
    echo '</tr>';
}

echo '</table>';

echo '</div>';

?>

<div class="step">
    <h3>🔧 Recommended Fixes</h3>
    
    <div class="error">
        <h4>❌ If you see errors above, try these fixes:</h4>
        <ol>
            <li><strong>Missing Database Fields:</strong> Run the <a href="fix-appearance-settings.php">Database Fix Script</a></li>
            <li><strong>Class Loading Issues:</strong> Check if all plugin files are uploaded correctly</li>
            <li><strong>PHP Errors:</strong> Fix any fatal errors shown in the error log</li>
            <li><strong>Database Issues:</strong> Check database permissions and connectivity</li>
            <li><strong>AJAX Issues:</strong> Verify WordPress admin-ajax.php is accessible</li>
            <li><strong>Server Limits:</strong> Increase PHP limits if they're too low</li>
        </ol>
    </div>
    
    <div class="warning">
        <h4>⚠️ Common Causes of "Website Can't Handle Request":</h4>
        <ul>
            <li><strong>Fatal PHP Errors:</strong> Check error log for fatal errors</li>
            <li><strong>Memory Exhaustion:</strong> Increase memory_limit in php.ini</li>
            <li><strong>Missing Database Fields:</strong> Run database update scripts</li>
            <li><strong>Class Not Found:</strong> Ensure all plugin files are present</li>
            <li><strong>Infinite Loops:</strong> Check for recursive function calls</li>
            <li><strong>Plugin Conflicts:</strong> Deactivate other plugins temporarily</li>
        </ul>
    </div>
    
    <div class="info">
        <h4>📋 Next Steps:</h4>
        <ol>
            <li><strong>Fix any red ❌ issues</strong> shown above</li>
            <li><strong>Run database update:</strong> <a href="fix-appearance-settings.php">fix-appearance-settings.php</a></li>
            <li><strong>Check PHP error log</strong> for recent errors</li>
            <li><strong>Test with minimal data</strong> first</li>
            <li><strong>Enable WordPress debug mode</strong> for more detailed errors</li>
        </ol>
    </div>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="fix-appearance-settings.php" style="background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 5px;">🔧 Run Database Fix</a>
    <a href="<?php echo admin_url('admin.php?page=farmfaucet'); ?>" style="background: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 5px;">🚀 Back to Admin</a>
</div>

</body>
</html>
