/**
 * Farm Faucet - Conversion Faucet Admin Styles
 */

/* Conversion Faucet Fields */
.conversion-faucet-fields {
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.conversion-faucet-fields .form-field {
    margin-bottom: 15px;
}

.conversion-faucet-fields label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.conversion-faucet-fields select,
.conversion-faucet-fields input[type="text"],
.conversion-faucet-fields input[type="number"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.conversion-faucet-fields .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
    font-size: 12px;
}

/* Vertical Checkbox Group */
.vertical-checkbox-group {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #fff;
}

.vertical-checkbox-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.vertical-checkbox-group label:last-child {
    margin-bottom: 0;
}

.vertical-checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

/* Advertisement Currency Highlighting */
.ad-currency {
    background-color: #e8f5e9;
    padding: 3px 5px;
    border-radius: 3px;
    border-left: 3px solid #4CAF50;
}

/* Conversion Settings Section */
.conversion-settings-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.conversion-settings-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

/* Toggle Switch for Advertisement Only */
.toggle-switch-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.toggle-switch-container label {
    margin-right: 10px;
    margin-bottom: 0;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Conversion Rate Display */
.conversion-rate-preview {
    background-color: #f0f0f0;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
}

.conversion-rate-preview strong {
    color: #4CAF50;
}

/* Help Text */
.conversion-help-text {
    background-color: #e8f5e9;
    border-left: 4px solid #4CAF50;
    padding: 10px 15px;
    margin: 15px 0;
    font-size: 13px;
    line-height: 1.5;
}

.conversion-help-text h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: #4CAF50;
}

.conversion-help-text ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.conversion-help-text li {
    margin-bottom: 5px;
}

/* Responsive Adjustments */
@media screen and (min-width: 783px) {
    .conversion-faucet-fields .form-field {
        display: flex;
        flex-wrap: wrap;
    }
    
    .conversion-faucet-fields .form-field > label {
        flex: 0 0 200px;
        padding-top: 8px;
    }
    
    .conversion-faucet-fields .form-field > select,
    .conversion-faucet-fields .form-field > input[type="text"],
    .conversion-faucet-fields .form-field > input[type="number"],
    .conversion-faucet-fields .form-field > .vertical-checkbox-group {
        flex: 1;
        min-width: 200px;
    }
    
    .conversion-faucet-fields .form-field > .description {
        flex: 0 0 100%;
        margin-left: 200px;
    }
}
