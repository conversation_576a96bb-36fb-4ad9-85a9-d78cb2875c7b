<?php
/**
 * Farm Faucet - Real Error Capture
 * 
 * This script captures the EXACT error when settings save fails
 */

// Enable ALL error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any output
ob_start();

// Custom error handler
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    $error_msg = "ERROR: [$errno] $errstr in $errfile on line $errline";
    file_put_contents(dirname(__FILE__) . '/farmfaucet_errors.log', date('Y-m-d H:i:s') . " - " . $error_msg . "\n", FILE_APPEND);
    echo "<div style='color: red; background: #ffebee; padding: 10px; margin: 10px 0; border-left: 4px solid #f44336;'>$error_msg</div>";
}

// Custom exception handler
function custom_exception_handler($exception) {
    $error_msg = "EXCEPTION: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    file_put_contents(dirname(__FILE__) . '/farmfaucet_errors.log', date('Y-m-d H:i:s') . " - " . $error_msg . "\n", FILE_APPEND);
    echo "<div style='color: red; background: #ffebee; padding: 10px; margin: 10px 0; border-left: 4px solid #f44336;'>$error_msg</div>";
}

// Set custom handlers
set_error_handler('custom_error_handler');
set_exception_handler('custom_exception_handler');

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Real Error Capture</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; border: none; cursor: pointer; }
        .btn:hover { background: #45a049; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .log { background: #2d2d2d; color: #f8f8f2; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Real Error Capture</h1>
    <p>This script will capture the EXACT error when you try to save settings.</p>

<?php

echo '<div class="info">';
echo '<h3>🔍 Step 1: Current Environment Check</h3>';

// Check PHP version
echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';

// Check memory limit
echo '<p><strong>Memory Limit:</strong> ' . ini_get('memory_limit') . '</p>';

// Check max execution time
echo '<p><strong>Max Execution Time:</strong> ' . ini_get('max_execution_time') . ' seconds</p>';

// Check if WordPress is loaded
echo '<p><strong>WordPress Loaded:</strong> ' . (function_exists('wp_verify_nonce') ? 'Yes' : 'No') . '</p>';

// Check if Farm Faucet classes exist
$classes_to_check = [
    'Farmfaucet_Admin',
    'Farmfaucet_Security',
    'Farmfaucet_Logger'
];

echo '<p><strong>Farm Faucet Classes:</strong></p>';
echo '<ul>';
foreach ($classes_to_check as $class) {
    echo '<li>' . $class . ': ' . (class_exists($class) ? '✅ Loaded' : '❌ Not Found') . '</li>';
}
echo '</ul>';

echo '</div>';

// Step 2: Simulate settings save
echo '<div class="info">';
echo '<h3>💾 Step 2: Simulate Settings Save Process</h3>';

if (isset($_POST['simulate_save'])) {
    echo '<div class="warning"><p>🧪 Simulating settings save process...</p></div>';
    
    // Clear any previous errors
    file_put_contents(dirname(__FILE__) . '/farmfaucet_errors.log', '');
    
    try {
        // Test 1: Basic option update
        echo '<h4>Test 1: Basic Option Update</h4>';
        $test_value = 'test_' . time();
        $result = update_option('farmfaucet_test_basic', $test_value);
        if ($result) {
            echo '<div class="success">✅ Basic option update works</div>';
            delete_option('farmfaucet_test_basic');
        } else {
            echo '<div class="error">❌ Basic option update failed</div>';
        }
        
        // Test 2: Settings API registration check
        echo '<h4>Test 2: Settings API Registration</h4>';
        $registered_settings = get_registered_settings();
        $farmfaucet_count = 0;
        foreach ($registered_settings as $setting => $data) {
            if (strpos($setting, 'farmfaucet_') === 0) {
                $farmfaucet_count++;
            }
        }
        echo '<div class="info">Found ' . $farmfaucet_count . ' registered Farm Faucet settings</div>';
        
        // Test 3: Try to register a test setting
        echo '<h4>Test 3: Test Setting Registration</h4>';
        try {
            register_setting('farmfaucet_test_group', 'farmfaucet_test_setting', [
                'sanitize_callback' => 'sanitize_text_field'
            ]);
            echo '<div class="success">✅ Test setting registration works</div>';
        } catch (Exception $e) {
            echo '<div class="error">❌ Setting registration failed: ' . $e->getMessage() . '</div>';
        }
        
        // Test 4: Try to save with WordPress settings API
        echo '<h4>Test 4: WordPress Settings API Save</h4>';
        try {
            // Simulate what happens when WordPress processes settings
            $_POST['option_page'] = 'farmfaucet_settings';
            $_POST['action'] = 'update';
            $_POST['farmfaucet_captcha_type'] = 'hcaptcha';
            $_POST['farmfaucet_hcaptcha_sitekey'] = 'test_site_key';
            $_POST['farmfaucet_hcaptcha_secret'] = 'test_secret';
            
            // Check nonce (this might be the issue)
            if (!isset($_POST['_wpnonce'])) {
                $_POST['_wpnonce'] = wp_create_nonce('farmfaucet_settings-options');
            }
            
            // Try to process like WordPress would
            if (isset($_POST['option_page']) && $_POST['option_page'] === 'farmfaucet_settings') {
                echo '<div class="info">Processing farmfaucet_settings group...</div>';
                
                // Get all registered settings for this group
                $whitelist_options = apply_filters('whitelist_options', []);
                if (isset($whitelist_options['farmfaucet_settings'])) {
                    echo '<div class="success">✅ Settings group is whitelisted</div>';
                    echo '<div class="info">Whitelisted options: ' . implode(', ', $whitelist_options['farmfaucet_settings']) . '</div>';
                } else {
                    echo '<div class="error">❌ Settings group NOT whitelisted - this is the problem!</div>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ WordPress settings API test failed: ' . $e->getMessage() . '</div>';
        }
        
        // Test 5: Check for fatal errors in admin class
        echo '<h4>Test 5: Admin Class Method Check</h4>';
        if (class_exists('Farmfaucet_Admin')) {
            $admin = Farmfaucet_Admin::get_instance();
            
            // Check if register_settings method exists and can be called
            if (method_exists($admin, 'register_settings')) {
                try {
                    // This might trigger the error
                    $admin->register_settings();
                    echo '<div class="success">✅ Admin register_settings method works</div>';
                } catch (Exception $e) {
                    echo '<div class="error">❌ Admin register_settings failed: ' . $e->getMessage() . '</div>';
                } catch (Error $e) {
                    echo '<div class="error">❌ Admin register_settings fatal error: ' . $e->getMessage() . '</div>';
                }
            } else {
                echo '<div class="error">❌ register_settings method not found in Admin class</div>';
            }
        } else {
            echo '<div class="error">❌ Farmfaucet_Admin class not found</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error">❌ Simulation failed: ' . $e->getMessage() . '</div>';
    } catch (Error $e) {
        echo '<div class="error">❌ Fatal error during simulation: ' . $e->getMessage() . '</div>';
    }
    
    // Show any logged errors
    $error_log_file = dirname(__FILE__) . '/farmfaucet_errors.log';
    if (file_exists($error_log_file)) {
        $errors = file_get_contents($error_log_file);
        if (!empty(trim($errors))) {
            echo '<h4>Captured Errors:</h4>';
            echo '<div class="log">' . nl2br(esc_html($errors)) . '</div>';
        }
    }
    
} else {
    echo '<form method="post">';
    echo '<p>Click this button to simulate the settings save process and capture any errors:</p>';
    echo '<input type="submit" name="simulate_save" value="Simulate Settings Save" class="btn">';
    echo '</form>';
}

echo '</div>';

// Step 3: Real settings form test
echo '<div class="info">';
echo '<h3>🔧 Step 3: Real Settings Form Test</h3>';

echo '<div class="warning">';
echo '<p><strong>IMPORTANT:</strong> This form will attempt to save settings using the exact same method as your plugin.</p>';
echo '<p>Watch for any errors that appear when you click "Save Changes".</p>';
echo '</div>';

// Create a real WordPress settings form
echo '<form method="post" action="options.php">';

// This is the critical part - settings_fields() might be causing the issue
try {
    settings_fields('farmfaucet_settings');
    echo '<div class="success">✅ settings_fields() worked</div>';
} catch (Exception $e) {
    echo '<div class="error">❌ settings_fields() failed: ' . $e->getMessage() . '</div>';
} catch (Error $e) {
    echo '<div class="error">❌ settings_fields() fatal error: ' . $e->getMessage() . '</div>';
}

echo '<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">';
echo '<tr><th style="padding: 10px; border: 1px solid #ddd; background: #f5f5f5;">Setting</th><th style="padding: 10px; border: 1px solid #ddd; background: #f5f5f5;">Value</th></tr>';

$test_settings = [
    'farmfaucet_captcha_type' => get_option('farmfaucet_captcha_type', 'hcaptcha'),
    'farmfaucet_hcaptcha_sitekey' => get_option('farmfaucet_hcaptcha_sitekey', ''),
    'farmfaucet_redirect_url' => get_option('farmfaucet_redirect_url', '')
];

foreach ($test_settings as $setting => $value) {
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">' . esc_html($setting) . '</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    
    if ($setting === 'farmfaucet_captcha_type') {
        echo '<select name="' . esc_attr($setting) . '">';
        $options = ['hcaptcha', 'recaptcha', 'turnstile'];
        foreach ($options as $option) {
            echo '<option value="' . esc_attr($option) . '" ' . selected($value, $option, false) . '>' . esc_html($option) . '</option>';
        }
        echo '</select>';
    } else {
        echo '<input type="text" name="' . esc_attr($setting) . '" value="' . esc_attr($value) . '" style="width: 300px; padding: 5px;">';
    }
    
    echo '</td>';
    echo '</tr>';
}

echo '</table>';

submit_button('Save Changes (This will show the real error)');
echo '</form>';

echo '</div>';

?>

<div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f44336;">
    <h3 style="color: #c62828; margin-top: 0;">🚨 CRITICAL DEBUGGING</h3>
    <p style="color: #c62828;"><strong>This script will capture the EXACT error that occurs when you try to save settings.</strong></p>
    <ol style="color: #c62828;">
        <li><strong>Run the simulation test above</strong> to see if there are any obvious issues</li>
        <li><strong>Try the real settings form</strong> to trigger the actual error</li>
        <li><strong>Check the error log</strong> that gets created in the plugin directory</li>
        <li><strong>Look at the browser's developer console</strong> for JavaScript errors</li>
        <li><strong>Check your server's error log</strong> for PHP fatal errors</li>
    </ol>
    <p style="color: #c62828; margin-bottom: 0;"><strong>Once we see the EXACT error message, I can fix it immediately!</strong></p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Go to Settings Tab</a>
</div>

</body>
</html>
