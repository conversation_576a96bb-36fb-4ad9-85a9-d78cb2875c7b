/**
 * Farm Faucet Milestone Progress Bar Fix
 *
 * This script ensures that the milestone progress bar is properly displayed
 * and animated in both card and compact views.
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Milestone Progress Bar Fix loaded');

        // Initialize progress bars
        initProgressBars();

        // Re-initialize progress bars when the DOM changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    initProgressBars();
                }
            });
        });

        // Start observing the document body for DOM changes
        observer.observe(document.body, { childList: true, subtree: true });
    });

    /**
     * Initialize progress bars
     */
    function initProgressBars() {
        // Find all milestone progress bars
        $('.farmfaucet-milestone-progress-bar').each(function() {
            const $progressBar = $(this);
            const targetProgress = parseInt($progressBar.data('progress')) || 0;
            const $container = $progressBar.closest('.farmfaucet-milestone-container');
            const $progressText = $container.find('.farmfaucet-milestone-progress-text');
            const isCompactView = $container.hasClass('compact-view');
            const isTransparent = $container.hasClass('transparent-bg');

            // Ensure the progress bar has the correct styles
            $progressBar.css({
                'height': '100%',
                'position': 'absolute',
                'left': '0',
                'top': '0',
                'transition': 'width 1.5s ease-in-out',
                'min-width': '5px',
                'z-index': '2'
            });

            // Ensure the progress container has the correct styles
            const $progressContainer = $progressBar.parent();
            $progressContainer.css({
                'position': 'relative',
                'overflow': 'hidden',
                'height': isCompactView ? '24px' : '30px',
                'border-radius': isCompactView ? '12px' : '15px',
                'background-color': isTransparent ? 'rgba(240, 240, 240, 0.3)' : '#f0f0f0',
                'margin': '10px 0',
                'width': '100%',
                'box-shadow': isTransparent ? 'none' : 'inset 0 1px 3px rgba(0,0,0,0.1)'
            });

            // Create or update progress text element
            if (!$progressText.length) {
                $progressText = $('<div class="farmfaucet-milestone-progress-text">' + targetProgress + '%</div>');
                $progressContainer.append($progressText);
            }

            // Style the progress text
            $progressText.css({
                'position': 'absolute',
                'width': '100%',
                'text-align': 'center',
                'color': '#fff',
                'font-weight': 'bold',
                'line-height': isCompactView ? '24px' : '30px',
                'font-size': isCompactView ? '0.9em' : '1.1em',
                'text-shadow': '0 1px 2px rgba(0,0,0,0.5)',
                'z-index': '5',
                'left': '0',
                'top': '0',
                'height': '100%',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'pointer-events': 'none',
                'margin': '0',
                'padding': '0'
            });

            // Animate the progress bar
            setTimeout(function() {
                $progressBar.css('width', targetProgress + '%');

                // Update the progress text if it exists
                $progressText.text(targetProgress + '%');

                // If progress is complete, add the completion class
                if (targetProgress >= 100) {
                    $container.addClass('farmfaucet-milestone-complete');
                }
            }, 100);
        });
    }
})(jQuery);
