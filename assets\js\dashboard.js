/**
 * Dashboard JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initDashboard();
    });

    /**
     * Initialize Dashboard functionality
     */
    function initDashboard() {
        // Initialize earnings chart if it exists
        if ($('#farmfaucet-earnings-chart').length) {
            initEarningsChart();
        }
    }

    /**
     * Initialize earnings chart
     */
    function initEarningsChart() {
        // Find all chart data containers
        $('.farmfaucet-earnings-chart-data').each(function() {
            const $chartData = $(this);
            const chartId = $chartData.attr('data-chart-id');
            const $chartCanvas = $('#' + chartId);

            if (!$chartCanvas.length) {
                console.error('Chart canvas not found for ID: ' + chartId);
                return;
            }

            // Get chart data from data attributes
            let labels = [];
            let values = [];

            try {
                labels = JSON.parse($chartData.attr('data-labels') || '[]');
                values = JSON.parse($chartData.attr('data-values') || '[]');
            } catch (e) {
                console.error('Error parsing chart data:', e);
                // Provide sample data if parsing fails
                labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
                values = [0, 0, 0, 0, 0, 0, 0];
            }

            const chartType = $chartData.attr('data-type') || 'line';
            const chartColor = $chartData.attr('data-color') || '#4CAF50';

            // Convert hex color to rgba for background
            const rgbaColor = hexToRgba(chartColor, 0.2);

            // Check if we have data
            if (!labels.length || !values.length) {
                // Create sample data if none exists
                labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
                values = [0, 0, 0, 0, 0, 0, 0];
            }

            // Create chart
            const ctx = $chartCanvas[0].getContext('2d');
            new Chart(ctx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Earnings',
                        data: values,
                        backgroundColor: rgbaColor,
                        borderColor: chartColor,
                        borderWidth: 2,
                        pointBackgroundColor: chartColor,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 1,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.3,
                        barPercentage: 0.7,
                        categoryPercentage: 0.8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(8);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Earned: ' + context.raw.toFixed(8);
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });
    }

    /**
     * Convert hex color to rgba
     *
     * @param {string} hex Hex color code
     * @param {number} alpha Alpha value (0-1)
     * @return {string} RGBA color string
     */
    function hexToRgba(hex, alpha) {
        // Default to green if invalid hex
        if (!hex || !/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
            hex = '#4CAF50';
        }

        // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
        let shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
        hex = hex.replace(shorthandRegex, function(m, r, g, b) {
            return r + r + g + g + b + b;
        });

        let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) {
            return 'rgba(76, 175, 80, ' + alpha + ')';
        }

        let r = parseInt(result[1], 16);
        let g = parseInt(result[2], 16);
        let b = parseInt(result[3], 16);

        return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + alpha + ')';
    }

})(jQuery);
