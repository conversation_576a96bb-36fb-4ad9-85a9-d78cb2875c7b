Prompt for Adding a Telegram Bot Builder UI Tab with Bot Type Selection and Telegram Login to an Existing WordPress Plugin

I need a detailed solution to add a new tab, titled “Telegram Bot Builder,” to an existing WordPress plugin, enabling administrators to create, customize, and manage Telegram bots directly from the plugin’s admin dashboard. The tab must integrate seamlessly with the plugin’s existing architecture, adhering to WordPress coding standards (e.g., WP Coding Standards, REST API usage) and security guidelines (e.g., data sanitization, nonce verification, encrypted storage). The UI should be modern, intuitive, and visually appealing, featuring rounded borders, smooth CSS transitions, and a drag-and-drop interface. The solution must allow admins to create a bot, select its type—either a text bot (for one-on-one interactions) or a chat bot (for group/channel interactions with moderation features)—and configure comprehensive functionalities, including commands, buttons, logic flows, permission-based actions (e.g., delete messages, ban users), customizable actions (e.g., displaying user balances or custom currencies from other plugin tabs), CAPTCHA integration, and a Telegram Login feature with OTP verification. The goal is to provide a production-ready tab that leverages the plugin’s existing capabilities (e.g., currency systems, user data) and offers admins full customization without requiring coding expertise. Below are the detailed requirements and specifications.

1. Core Functionality
Integration with Existing Plugin:
Add a new tab labeled “Telegram Bot Builder” to the plugin’s admin dashboard, accessible via the plugin’s main menu or settings page, using WordPress’s add_submenu_page() or equivalent hook.
Ensure the tab inherits the plugin’s styling (e.g., color scheme, typography) for consistency, while enhancing it with modern design elements like rounded borders (border-radius: 8px) and smooth CSS transitions (0.3s ease-in-out).
Reuse the plugin’s user authentication and role-based access controls (e.g., current_user_can('manage_options')) to restrict tab access to administrators or custom roles defined in the plugin.
Store bot-related data (e.g., API tokens, flows, login settings) in the plugin’s existing database tables (e.g., wp_options or custom tables), using a unique prefix (e.g., tg_bot_) to avoid conflicts with other tabs’ data (e.g., currency or user management).
Follow WordPress security guidelines for data storage, using sanitize_text_field(), wp_kses(), and encryption (e.g., Sodium) for sensitive data.
Bot Creation and Type Selection:
Require admins to create a bot by entering a Telegram Bot API token (obtained from @BotFather) and selecting the bot type:
Text Bot: For one-on-one interactions, focusing on personalized messaging, commands, and logic flows (e.g., customer support, lead generation).
Chat Bot: For group or channel interactions, with moderation features, permission-based actions, and group-specific logic (e.g., responding to rule violations, managing noisy users).
Provide a “Test Token” button that sends a getMe request to the Telegram Bot API to validate the token and auto-populate the bot’s username, displaying success or error messages using WordPress’s admin notices (admin_notices hook).
Allow admins to manage multiple bots (text or chat) via a table or dropdown interface, with options to add, edit, or delete configurations, using WordPress’s list table API (WP_List_Table) for consistency.
Store bot type and configurations securely, using update_option() with encryption for API tokens and wp_json_encode() for complex data structures.
Ensure nonce verification (wp_verify_nonce()) for all form submissions (e.g., bot creation, token updates) to prevent CSRF attacks.
Drag-and-Drop Flow Builder:
Implement a visual drag-and-drop interface for creating bot conversation flows, inspired by platforms like Botpress, SendPulse, and Botmother, tailored to the selected bot type.
Use a canvas-based editor (e.g., React Flow or Konva.js) where admins can add, connect, and rearrange elements (e.g., messages, buttons, conditions, actions, moderation triggers) to define bot behavior.
Support undo/redo, zoom controls, and auto-save, storing flows in the plugin’s database as serialized JSON or custom post types (register_post_type('tg_bot_flow')).
Ensure the editor is responsive, fitting within the tab’s layout and working on desktop and tablet devices, using CSS media queries and WordPress’s admin-responsive classes.
For chat bots, include group-specific elements like “Moderation Action” (e.g., ban user) or “Group Rule Trigger” (e.g., detect spam) in the drag-and-drop toolbar.
Secure AJAX requests for flow updates using WordPress’s wp_ajax_ actions, with nonce checks and capability validation.
Command and Button Customization:
Enable admins to create and manage bot commands (e.g., /start, /help) with customizable responses (text, images, files, buttons), tailored to the bot type, using a form-based interface with WordPress’s metabox styling.
Support all Telegram button types as of May 2025, including:
ReplyKeyboardMarkup: Custom reply keyboards for predefined options.
InlineKeyboardMarkup: Inline buttons for URLs, callbacks, or inline mode.
KeyboardButtonRequestUsers/Chats: Buttons for requesting user or chat data.
KeyboardButtonRequestPhone/GeoLocation: Buttons for phone numbers or locations.
KeyboardButtonUrlAuth: Buttons for Telegram Login authorization.
Provide a modal or sidebar interface (using WordPress’s Thickbox or custom modals) for configuring button properties (e.g., text, URL, callback data, permissions).
Include a preview mode to simulate how buttons and commands appear in Telegram, rendered within an iframe or canvas in the tab, using Telegram’s Web App styling for accuracy.
For chat bots, allow buttons to trigger moderation actions (e.g., “Report User,” “Mute”) or display group-specific data (e.g., user balance from plugin’s currency system).
Sanitize all inputs (e.g., sanitize_text_field(), esc_url_raw()) and validate button configurations before saving.
Logic and Conditional Flows:
Allow admins to define conditional logic for bot interactions using a visual interface, tailored to the bot type:
Trigger-Based Actions: Display personalized messages or pass parameters if a user accesses the bot via a deep link (e.g., t.me/MyBot?start=ref123).
Conditional Responses: Branch flows based on user inputs, button selections, or group events (e.g., new member joins, detected spam).
Access Control: Lock buttons or commands based on user input, time, prior interactions, or group permissions, showing alternative messages (e.g., “Please verify CAPTCHA”).
Support variables (e.g., {user_name}, {user_id}, {parameter}, {user_balance}) for personalized responses, pulling data from the plugin’s user or currency systems via WordPress’s get_user_meta() or custom tables.
For chat bots, include group-specific conditions, such as:
Trigger actions if a user violates group rules (e.g., posting spam, detected via regex-based keyword filters).
Respond to errors or noisy behavior (e.g., exceeding 10 messages in 1 minute) with warnings or moderation actions.
Enable loops, pauses, or filters to control flow progression (e.g., wait for user response), with a UI to set durations or conditions.
Store logic flows securely, using wp_kses_post() for rich text and sanitize_key() for identifiers.
Permission-Based Actions (Chat Bots):
For chat bots, provide a UI to configure Telegram permission-based actions, leveraging Telegram’s Bot API capabilities for group moderation:
Delete Messages: Delete specific messages (e.g., spam, rule-violating content) based on triggers like keywords, user behavior, or admin commands (e.g., /delete).
Ban/Kick Users: Ban or kick users for rule violations, with customizable durations (e.g., 1 hour, permanent) and reasons.
Mute/Restrict Users: Restrict users from sending messages, media, or stickers, with options for duration (e.g., 24 hours) and conditions (e.g., partial restrictions).
Pin Messages: Pin important messages or announcements in the group, with options to notify members.
Manage Invites: Generate or revoke group invite links, with expiration settings (e.g., 1 day, 100 uses).
Allow admins to define triggers for these actions, such as:
User sends a message containing banned words (e.g., configured via a keyword list).
User exceeds a message frequency threshold (e.g., 10 messages in 1 minute, configurable via sliders).
Admin or moderator triggers an action via a command (e.g., /ban @username reason).
Provide a moderation log within the tab, showing timestamps, affected users, actions, and reasons, stored in a custom database table (e.g., wp_tg_bot_logs) with WordPress’s $wpdb class.
Secure API requests for moderation actions using HTTPS and Telegram’s webhook system, with rate limiting to prevent abuse.
Customizable Actions (Integration with Plugin Features):
Enable admins to create actions that leverage the plugin’s existing features (e.g., currency systems, user profiles) across both bot types:
Display User Balance: Show a user’s balance (e.g., “Your balance: 100 Coins”) in bot responses, pulling data from the plugin’s currency or points system (e.g., stored in wp_usermeta or custom tables).
Custom Currencies: Display custom currencies created in other tabs (e.g., “Gold,” “Credits”) with formatted outputs (e.g., “Your Gold: 50”), supporting multiple currency types.
Update Balance: Add or deduct balance based on user interactions (e.g., +10 Coins for completing a task, -5 Credits for rule violation).
User Profile Data: Display or update user data (e.g., display name, email, custom fields) stored in the plugin’s user management system.
Provide a UI to map bot actions to plugin data fields, with support for variables (e.g., {user_balance}, {custom_currency_gold}) and dynamic formatting (e.g., currency symbols, decimals).
For chat bots, allow group-specific actions, such as:
Reward users with currency for positive contributions (e.g., answering questions, detected via keywords).
Penalize users with balance deductions for rule violations (e.g., spam, detected via moderation triggers).
Ensure data access follows WordPress security guidelines, using current_user_can() for permissions and esc_html() for outputs.
CAPTCHA Integration:
Integrate a CAPTCHA system to verify users during bot interactions, reducing spam and enhancing security for both bot types.
Support third-party CAPTCHA APIs compatible with Telegram, such as:
hCaptcha: Privacy-focused, with Telegram-compatible widgets.
Google reCAPTCHA v3: Reliable, with visual and invisible options.
Alternatively, provide a custom random CAPTCHA (e.g., math questions like “What is 7 + 3?” or text challenges like “Type ‘blue’”), generated server-side with PHP’s random_int().
Offer a UI to customize CAPTCHA appearance (e.g., colors, fonts, border radius) and behavior (e.g., retry limits, timeout duration), stored as plugin options.
Allow admins to define multiple CAPTCHA interaction points, such as:
At bot startup (e.g., after /start command).
Before accessing restricted commands or buttons.
After a set number of interactions (e.g., 5 messages) or time-based triggers (e.g., every 10 minutes).
For chat bots, before allowing new members to post in the group.
Ensure CAPTCHAs display correctly in Telegram’s mobile and desktop apps, with text-based fallbacks (e.g., sent as messages) for unsupported visual CAPTCHAs.
Secure CAPTCHA data with nonce verification and rate limiting, using WordPress’s transient API (set_transient()) for temporary storage.
Telegram Login Feature:
Add a “Telegram Login” sub-section within the “Telegram Bot Builder” tab to enable Telegram-based user authentication on the website.
Require admins to configure a Telegram bot API token for login functionality, using the same bot or a separate one from the bot builder, validated via the “Test Token” feature.
Disable login and signup forms on the website until the Telegram Login feature is configured, displaying an admin notice if unconfigured.
Provide shortcodes for login and signup forms (e.g., [tg_login] and [tg_signup]), rendering a button (e.g., “Sign Up” or “Log In”) that fades (0.5s CSS transition) to reveal the form when clicked, styled with the plugin’s CSS classes.
Signup Form Fields:
Email (type="email", validated with is_email())
Username (type="text", sanitized with sanitize_user())
Password (type="password", hashed with wp_hash_password())
Display Name (type="text", sanitized with sanitize_text_field())
Valid Telegram Phone Number (type="tel", validated with regex for international formats)
Verification Process:
After submitting the signup form, display an OTP input field with the message: “Enter Verification Code. to receive your code from the Telegram bot. Note: The code will be sent only to the Telegram number you entered.”
The “Click here” link redirects to the specified Telegram bot (e.g., t.me/MyBot), opening in a new tab.
Generate a random 6-digit OTP code using PHP’s random_int(100000, 999999), sent via the bot to the user’s Telegram number:
If the user has previously messaged the bot, send the code instantly.
If not, send the code immediately after the user sends any message to the bot (as bots cannot initiate messages), using Telegram’s webhook to detect incoming messages.
Set a configurable timer for code expiration (e.g., 5 minutes, adjustable in the tab), displayed on the form (e.g., “Code expires in 4:59”) using JavaScript countdown.
Validate the OTP server-side, allowing signup completion only if the code matches and is unexpired, using get_transient() for OTP storage.
Login Form:
Allow users to log in with username/email and password, with optional Telegram OTP verification (toggleable in the tab) for additional security.
Use the same OTP verification process as signup, sending codes to the user’s registered Telegram number.
Redirects:
Allow admins to specify a redirect page (e.g., user dashboard, homepage) after successful login or signup, configurable via a dropdown of WordPress pages in the tab.
Use wp_safe_redirect() to ensure secure redirects within the site.
Security:
Store Telegram phone numbers and OTP data securely, using the plugin’s encryption methods or WordPress’s wp_encrypt_data() (if available).
Ensure the bot only sends OTPs to the exact Telegram number entered during signup, verified via Telegram’s chat_id.
Implement rate limiting (e.g., max 3 OTP requests per hour per IP) using WordPress’s transient API.
Use nonces for form submissions and AJAX requests, with check_ajax_referer().
UI Customization:
Allow admins to customize login/signup button and form styles (e.g., colors, border radius, fonts) via a settings panel, saved as plugin options.
Ensure forms are responsive (using CSS Grid or Flexbox) and accessible, following WCAG 2.1 guidelines with ARIA labels and keyboard navigation.
2. UI/UX Design Requirements
Modern and Consistent Design:
Use a clean, minimalistic design with rounded borders (border-radius: 8px) for buttons, cards, modals, and forms, aligning with the plugin’s aesthetic and WordPress’s admin styling.
Implement smooth CSS transitions (0.3s ease-in-out) for hover effects, button clicks, drag-and-drop movements, modal animations, and Telegram Login form fades.
Use the plugin’s color scheme (e.g., blues, grays, whites, defined in plugin’s CSS) as a base, with an option for admins to customize primary colors via a color picker in the tab, saved as update_option().
Apply modern typography (e.g., Inter, Roboto, or system fonts like -apple-system) with clear hierarchy (e.g., h2 for section titles, p for descriptions), matching the plugin’s font stack.
Intuitive Navigation:
Organize the tab into sub-sections or panels, styled with WordPress’s metabox or card-based layout, including:
Bot Settings: API token, bot type selection, and general configurations.
Flow Builder: Drag-and-drop canvas for conversation flows.
Commands & Buttons: Interface for managing commands and buttons.
Permission Actions: Moderation settings for chat bots (e.g., delete, ban).
Custom Actions: Integration with plugin features (e.g., balance, currencies).
CAPTCHA Settings: Options for CAPTCHA types and triggers.
Telegram Login: Configuration for login/signup forms and OTP.
Analytics: Dashboard for bot usage, moderation, and login metrics.
Include tooltips (using WordPress’s wp_enqueue_script('wp-pointer')), inline help text, and a link to searchable documentation, styled consistently with the plugin’s help resources.
Provide a sidebar or toolbar with drag-and-drop elements (e.g., Message, Button, Condition, CAPTCHA, Moderation Action), styled as a WordPress admin widget.
Drag-and-Drop Experience:
Use a JavaScript library like React Flow, Interact.js, or Konva.js for smooth drag-and-drop functionality in the flow builder, enqueued via wp_enqueue_script().
Allow admins to drag elements onto the canvas, connect them with lines (e.g., arrows or bezier curves), and rearrange them effortlessly, with real-time feedback.
Highlight active elements during drag operations with animations (e.g., scale or box-shadow effects), using CSS or GSAP.
Support grid snapping for alignment and collision detection to prevent overlaps, with configurable grid sizes.
Secure drag-and-drop data updates with AJAX, using wp_ajax_ actions and nonce verification.
Responsive and Accessible:
Ensure the tab’s UI, including Telegram Login forms, is responsive, adapting to the plugin’s dashboard layout across desktop, tablet, and mobile devices, using WordPress’s .wrap and .form-table classes.
Follow WCAG 2.1 accessibility guidelines, including:
Keyboard navigation (e.g., Tab key for form fields, arrow keys for canvas).
Screen reader support (e.g., ARIA labels for buttons, modals, and drag-and-drop elements).
Sufficient color contrast (e.g., 4.5:1 ratio for text).
Use WordPress’s accessibility-ready classes (e.g., .screen-reader-text) and test with tools like WAVE or Lighthouse.
3. Advanced Customization Features
Template Library:
Offer pre-built flow templates for text and chat bots (e.g., customer support, group moderation, e-commerce), stored as JSON in the plugin’s database or as custom post types.
Allow admins to save custom flows as reusable templates, with an interface to import/export templates as JSON files, using WordPress’s wp_handle_upload() for file handling.
Provide a template gallery with previews, styled as a WordPress media library.
Analytics and Reporting:
Include a sub-panel for tracking bot and login performance, using WordPress’s chart libraries (e.g., Chart.js, enqueued via wp_enqueue_script()):
Number of active bot users and group members.
Most-used commands, buttons, and moderation actions.
CAPTCHA and OTP success/failure rates.
Engagement metrics (e.g., open rates, login attempts, average response time).
Provide exportable reports in CSV or PDF, using libraries like Dompdf or WordPress’s WP_Filesystem for file generation, integrated with the plugin’s export functionality.
Store analytics data in a custom table (e.g., wp_tg_bot_analytics), queried with $wpdb and sanitized outputs.
Third-Party Integrations:
Support integrations with tools via APIs or webhooks, such as:
CRM systems (e.g., HubSpot, Salesforce) for syncing user data.
Payment gateways (e.g., Stripe, PayPal) for in-bot purchases, using the plugin’s payment infrastructure if available.
Analytics platforms (e.g., Google Analytics) for tracking bot interactions.
Allow admins to configure webhooks for custom actions (e.g., send data to an external server on button click), using WordPress’s wp_remote_post() and the plugin’s webhook system.
Secure webhook endpoints with authentication tokens and SSL verification.
Multilingual Support:
Enable flows and login forms in multiple languages, with automatic detection (via get_user_locale()) or manual selection (via dropdown).
Support right-to-left (RTL) languages like Arabic and Hebrew, using WordPress’s is_rtl() and CSS adjustments.
Integrate with the plugin’s localization system, using load_plugin_textdomain() and .mo files for translations.
4. Security and Performance
Security Measures:
Encrypt sensitive data (e.g., API tokens, user phone numbers, OTPs) using WordPress’s Sodium library (sodium_crypto_secretbox) or a plugin-specific encryption method.
Restrict tab access to authorized roles, using current_user_can() and custom capabilities (e.g., manage_tg_bot).
Use secure webhooks for Telegram API communication, requiring SSL/TLS and validating incoming requests with Telegram’s X-Telegram-Bot-Api-Secret-Token.
Validate and sanitize all inputs:
Form fields: sanitize_text_field(), sanitize_email(), esc_url_raw().
Rich text: wp_kses_post() with allowed HTML tags.
Database queries: $wpdb->prepare() to prevent SQL injection.
Implement nonce verification for all forms and AJAX requests, using wp_create_nonce() and check_admin_referer().
Apply rate limiting for Telegram Login OTP requests (e.g., max 3 per hour per IP) using WordPress’s transient API.
Follow WordPress’s security best practices, including capability checks, data escaping (esc_html(), esc_attr()), and secure file handling.
Performance Optimization:
Optimize the drag-and-drop interface and login forms for low latency, using lazy loading for large flows (e.g., via IntersectionObserver) and minified assets (wp_enqueue_script() with .min.js).
Minimize database queries by caching data (e.g., bot configurations, templates) with WordPress’s object cache (wp_cache_set()) or transients.
Use asynchronous JavaScript (e.g., async/defer for scripts) and CSS optimization (e.g., critical CSS) to reduce admin page load times.
Ensure compatibility with WordPress multisite and high-traffic sites, using efficient queries and indexing for custom tables.
Test performance with tools like Query Monitor or New Relic, targeting <1s admin page loads.
5. Development Guidelines
Technology Stack:
Build the tab using PHP 7.4+ for WordPress compatibility, with JavaScript (React 17+ or Vue.js 3) for the frontend UI, matching the plugin’s tech stack.
Use WordPress REST API (register_rest_route()) or AJAX (wp_ajax_) for frontend-backend communication, integrating with the plugin’s API endpoints.
Leverage libraries for specific features, enqueued via wp_enqueue_script():
Drag-and-Drop: React Flow, Interact.js, or Konva.js.
CAPTCHA: hCaptcha or Google reCAPTCHA SDK.
Animations: GSAP or CSS transitions.
OTP Generation: PHP’s random_int() for secure random numbers.
Charts: Chart.js for analytics visualizations.
Ensure compatibility with the latest WordPress version (6.5+ as of May 2025), the plugin’s version, and popular themes/plugins (e.g., Elementor, WooCommerce).
Integration with Plugin Architecture:
Hook into the plugin’s admin menu system using add_submenu_page() or a filter on the plugin’s menu array.
Reuse the plugin’s CSS and JavaScript enqueuing mechanisms (wp_enqueue_style(), wp_enqueue_script()) to load tab-specific assets, with dependencies on WordPress’s core scripts (e.g., wp-i18n).
Extend the plugin’s database schema to store bot, moderation, and login data, using $wpdb->query() for table creation and dbDelta() for schema updates.
Use the plugin’s action/filter hooks (e.g., plugin_name_save_settings) to allow developers to extend the tab’s functionality, including Telegram Login.
WordPress Coding Standards:
Follow WordPress Coding Standards for PHP (e.g., PSR-2, snake_case for functions) and JavaScript (e.g., ES6+, camelCase for variables).
Use PHPDoc comments for functions and classes, with @since, @param, and @return tags.
Organize code into modular files (e.g., admin/class-tg-bot-builder.php, public/class-tg-login.php), loaded via the plugin’s loader class.
Avoid direct database queries where possible, using WordPress APIs like get_option(), update_user_meta(), and $wpdb->insert().
Extensibility:
Design the tab to be extensible, providing action hooks (e.g., tg_bot_before_save_flow) and filter hooks (e.g., tg_bot_login_form_fields) for developers.
Document hooks in the plugin’s developer documentation, using WordPress’s Codex format.
Allow third-party plugins to add custom flow elements, actions, or login fields via a public API.
Testing and Documentation:
Include unit tests for core functionality (e.g., bot creation, OTP verification, moderation actions) using PHPUnit, integrated with the plugin’s testing suite.
Test security with tools like WPScan and ensure compatibility with WordPress’s VIP Go standards.
Provide comprehensive documentation within the plugin’s help resources, including:
User guide for the Telegram Bot Builder and Telegram Login features, using WordPress’s add_help_tab().
Step-by-step tutorials for creating flows, commands, CAPTCHAs, and login forms.
Video walkthroughs, hosted on a CDN or YouTube, embedded in the tab via wp_oembed_get().
Offer a demo mode (toggleable in the tab) for testing bot flows and login forms without affecting live systems, using a sandboxed Telegram bot.
6. Deliverables
A fully functional “Telegram Bot Builder” tab integrated into the existing WordPress plugin, with bot type selection, moderation actions, custom actions, CAPTCHA, and Telegram Login features.
A settings panel for configuring bot API tokens, bot types, moderation triggers, custom actions, CAPTCHA settings, and Telegram Login options.
A drag-and-drop flow builder supporting text and chat bots, with moderation and plugin-integrated actions (e.g., balance updates).
Shortcode-based login/signup forms ([tg_login], [tg_signup]) with Telegram OTP verification and customizable redirects.
Sample flow templates and a library of reusable components, stored as custom post types or JSON in the plugin’s database.
Updated plugin documentation (in readme.txt and admin help tabs) covering the new tab’s features and usage.
Optional: Support for Telegram Mini Apps to enhance bot and login interactions, integrated into the flow builder using Telegram’s Web Apps API.
7. Inspiration and Benchmarks
Draw inspiration from chatbot builders like Botpress, SendPulse, Botmother, and BotPenguin for drag-and-drop interfaces, flow management, and group moderation features.
Model the UI after modern design systems like Material Design or Tailwind CSS, while aligning with the plugin’s styling and WordPress’s admin CSS.
For Telegram Login, reference Telegram’s official Login Widget and WordPress plugins like “Login with Telegram” for seamless authentication flows.
Ensure the tab matches the usability and flexibility of commercial solutions, tailored for the plugin’s user base and WordPress ecosystem.
8. Constraints and Considerations
The tab must work with Telegram’s Bot API and support all current button types, permission actions, and features as of May 2025, using Telegram’s official PHP SDK or REST API.
Avoid dependencies on external services requiring subscriptions, except for optional CAPTCHA APIs (e.g., hCaptcha, reCAPTCHA).
Ensure the tab and Telegram Login are lightweight, targeting <100ms additional load time on admin pages, tested with Query Monitor.
Comply with Telegram’s security and privacy standards, particularly for bots and login systems handling sensitive data (e.g., phone numbers, user IDs).
Maintain compatibility with the plugin’s existing tabs and features (e.g., currency systems, user management), avoiding conflicts by using namespaced functions and unique database keys.
Follow WordPress’s plugin submission guidelines for potential inclusion in the WordPress Plugin Directory, including GPL-compatible licensing and no external CDN dependencies unless optional.
Please provide a detailed plan or implementation for adding the “Telegram Bot Builder” tab, including bot type selection and Telegram Login, to the existing WordPress plugin. Include any necessary code, database schema updates, UI mockups, or recommendations to ensure a robust and user-friendly solution. If code is provided, wrap it in appropriate artifact tags with the same UUID as the original prompt (8407f03b-c089-4085-9de8-ce9f094ede7c) to reflect updates, along with titles and content types. Focus on delivering a production-ready tab that integrates seamlessly with the plugin, adheres to WordPress coding and security guidelines, and meets all specified requirements for usability, security, and performance.

Comprehensive Telegram Bot Builder Tab Prompt with Bot Type Selection and Telegram Login
plain
Show inline
Key Features and Enhancements
This prompt consolidates all requirements from your initial and subsequent requests, with additional details to ensure clarity, adherence to WordPress standards, and a robust implementation. Key updates include:

WordPress Coding and Security Guidelines:
Emphasized adherence to WordPress Coding Standards (e.g., PSR-2, PHPDoc) and security best practices (e.g., nonce verification, data sanitization, encryption with Sodium).
Detailed specific WordPress APIs (e.g., add_submenu_page(), wp_enqueue_script(), wp_verify_nonce()) for integration and security.
Included secure database handling with $wpdb, transients, and dbDelta() for schema updates.
Bot Type Selection:
Required admins to select text bot or chat bot during bot creation, with tailored features for each (e.g., moderation for chat bots).
Added a table-based interface for managing multiple bots, using WP_List_Table for consistency with WordPress’s admin UI.
Permission-Based Actions for Chat Bots:
Expanded chat bot functionality with Telegram’s moderation actions (e.g., delete messages, ban users, mute, pin messages, manage invites).
Included a UI for configuring triggers (e.g., keyword filters, message frequency) and a moderation log stored in a custom table.
Customizable Actions with Plugin Integration:
Integrated with the plugin’s existing features (e.g., currency systems, user profiles) to display/update balances, custom currencies (e.g., “Gold”), and user data.
Provided a UI for mapping actions to plugin data fields, with variable support (e.g., {user_balance}) and secure data access.
CAPTCHA Integration:
Supported hCaptcha, Google reCAPTCHA, and custom CAPTCHAs, with a UI for customization and multiple interaction points.
Ensured Telegram compatibility with text-based fallbacks and secure data handling.
Telegram Login Feature:
Added a detailed Telegram Login sub-section with shortcode-based forms ([tg_login], [tg_signup]), OTP verification, and customizable redirects.
Specified form fields, OTP generation (6-digit, random_int()), expiration timers, and security measures (e.g., rate limiting, number-specific OTP delivery).
Integrated with WordPress’s user system (wp_create_user(), wp_signon()) and ensured accessibility/responsiveness.
UI/UX and Performance:
Refined the UI with WordPress-specific styling (e.g., metaboxes, Thickbox modals) and modern enhancements (e.g., rounded borders, CSS transitions).
Added performance optimizations (e.g., lazy loading, caching) and accessibility compliance (WCAG 2.1, ARIA labels).
Extensibility and Documentation:
Designed for extensibility with action/filter hooks and a public API for third-party integrations.
Included comprehensive documentation (user guides, tutorials, videos) and a demo mode for testing.
Artifact Tag Compliance:
Retained the same artifact_id (8407f03b-c089-4085-9de8-ce9f094ede7c) to indicate this is an updated version of the original prompt.
Wrapped the entire prompt in the <xaiArtifact> tag with an updated title and content type, ensuring no nested tags or extraneous content.
This prompt is designed to be sent to an AI or development team to produce a production-ready solution that meets all your requirements while adhering to WordPress standards. Let me know if you need further refinements, additional details, or assistance with implementation steps!