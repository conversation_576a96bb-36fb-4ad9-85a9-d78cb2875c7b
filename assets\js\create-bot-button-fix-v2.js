/**
 * Create Bot Button Fix V2
 * 
 * This is an improved fix for the "Create New Bot" button in the Telegram Bot Builder.
 * It focuses on making the button work properly and styling it correctly.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Create Bot Button Fix V2 loaded');
        initCreateBotButton();
    });

    /**
     * Initialize Create Bot Button
     */
    function initCreateBotButton() {
        console.log('Initializing Create Bot Button Fix V2');
        
        // Style the Create New Bot button
        styleCreateBotButton();
        
        // Remove any existing click handlers to prevent duplicates
        $('#create-new-bot').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Create New Bot button clicked');
            openBotDialog();
        });
        
        // Initialize the dialog
        initBotDialog();
    }
    
    /**
     * Style the Create New Bot button to be green and center the text
     */
    function styleCreateBotButton() {
        console.log('Styling Create New Bot button');
        
        // Apply styles directly to the button
        $('#create-new-bot').css({
            'background-color': '#4CAF50',
            'border-color': '#4CAF50',
            'color': 'white',
            'text-align': 'center',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });
        
        // Add a style tag for more specific styling
        $('head').append('<style>' +
            '#create-new-bot { background-color: #4CAF50 !important; border-color: #4CAF50 !important; color: white !important; }' +
            '#create-new-bot:hover { background-color: #45a049 !important; }' +
            '.ui-dialog-buttonset button:first-child { background-color: #4CAF50 !important; border-color: #4CAF50 !important; color: white !important; }' +
            '.ui-dialog-buttonset button:first-child:hover { background-color: #45a049 !important; }' +
            '</style>'
        );
    }
    
    /**
     * Initialize the bot dialog
     */
    function initBotDialog() {
        console.log('Initializing bot dialog');
        
        // Initialize the dialog if it exists
        if ($('#bot-dialog').length > 0) {
            // Destroy any existing dialog to prevent duplicates
            if ($('#bot-dialog').hasClass('ui-dialog-content')) {
                $('#bot-dialog').dialog('destroy');
            }
            
            // Initialize the dialog with proper options
            $('#bot-dialog').dialog({
                autoOpen: false,
                modal: true,
                width: 500,
                buttons: {
                    'Create Bot': function() {
                        saveBot();
                    },
                    'Cancel': function() {
                        $(this).dialog('close');
                    }
                },
                open: function() {
                    // Style the buttons when dialog opens
                    setTimeout(function() {
                        $('.ui-dialog-buttonset button:first-child').css({
                            'background-color': '#4CAF50',
                            'border-color': '#4CAF50',
                            'color': 'white'
                        });
                    }, 10);
                }
            });
        } else {
            console.error('Bot dialog not found');
        }
    }
    
    /**
     * Open the bot dialog
     */
    function openBotDialog() {
        console.log('Opening bot dialog');
        
        // Reset the form
        $('#bot-form')[0].reset();
        
        // Open the dialog
        $('#bot-dialog').dialog('open');
    }
    
    /**
     * Save the bot
     */
    function saveBot() {
        console.log('Saving bot');
        
        // Get form data
        const botToken = $('#bot-token').val();
        
        // Validate form data
        if (!botToken) {
            alert('Please enter a bot token');
            return;
        }
        
        // Disable buttons during save
        const $buttons = $('.ui-dialog-buttonset button');
        $buttons.prop('disabled', true);
        $buttons.first().text('Creating...');
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_create_bot',
                nonce: farmfaucetTgBotBuilder.nonce,
                bot_token: botToken
            },
            success: function(response) {
                if (response.success) {
                    alert('Bot created successfully!');
                    window.location.reload();
                } else {
                    alert('Failed to create bot: ' + (response.data ? response.data.message : 'Unknown error'));
                    $buttons.prop('disabled', false);
                    $buttons.first().text('Create Bot');
                }
            },
            error: function() {
                alert('An error occurred while creating the bot');
                $buttons.prop('disabled', false);
                $buttons.first().text('Create Bot');
            }
        });
    }
})(jQuery);
