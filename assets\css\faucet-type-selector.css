/**
 * Faucet Type Selector Styles
 */

/* Faucet type dropdown styling */
#faucet-type {
    width: 100%;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
}

/* Faucet type fields container */
.faucet-type-fields {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    background-color: #f9f9f9;
}

/* Withdrawal currencies container */
.withdrawal-currencies-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 15px;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 5px;
}

/* Background style selector */
.bg-style-selector {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.radio-label {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.radio-label input[type="radio"] {
    margin-right: 5px;
}

/* Gradient color options */
.gradient-color-start,
.gradient-color-end {
    display: flex;
    align-items: center;
}

.gradient-color-start label,
.gradient-color-end label {
    margin-right: 10px;
    min-width: 100px;
}

/* Captcha options */
.captcha-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 5px;
}

.captcha-option {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.captcha-option.disabled {
    opacity: 0.6;
    background-color: #f5f5f5;
}

.captcha-not-configured {
    display: block;
    font-size: 12px;
    color: #d63638;
    margin-top: 5px;
}

/* Color picker positioning */
.color-picker {
    width: 50px;
    height: 30px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

/* Appearance section */
.faucet-type-fields h3 {
    margin-top: 20px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
}

/* Disabled faucet type */
#faucet-type:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}
