/**
 * Farm Faucet - Organized Appearance Settings
 * Clean, organized styles for the faucet appearance settings
 */

/* Main appearance container */
.appearance-settings-container {
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 20px;
    margin-bottom: 20px;
}

/* Appearance sections */
.appearance-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.appearance-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.appearance-section h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form field styling */
.appearance-section .form-field {
    margin-bottom: 15px;
}

.appearance-section .form-field:last-child {
    margin-bottom: 0;
}

.appearance-section .form-field label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
    font-size: 13px;
}

.appearance-section .form-field .description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Grid layouts for organized sections */
.color-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

.style-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: 1fr 1fr;
    }

    .style-grid {
        grid-template-columns: 1fr;
    }
}

/* Color picker container */
.color-picker-container {
    position: relative;
    margin-top: 10px;
}

/* Color circles layout */
.color-circles {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 5px;
}

.color-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-circle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.color-circle.active {
    border-color: #333;
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.color-circle.active::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .appearance-settings-compact {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 12px;
    }
    
    .color-circles {
        justify-content: center;
    }
    
    .color-circle {
        width: 28px;
        height: 28px;
    }
}

/* Form field improvements */
.appearance-section input[type="color"] {
    width: 100%;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    padding: 0;
    background: none;
    transition: border-color 0.2s ease;
}

.appearance-section input[type="color"]:hover {
    border-color: #4CAF50;
}

.appearance-section input[type="color"]:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.appearance-section select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 13px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.appearance-section select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Background settings improvements */
.faucet-bg-options {
    margin-top: 15px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.faucet-bg-options .form-field {
    margin-bottom: 15px;
}

.faucet-bg-options .form-field:last-child {
    margin-bottom: 0;
}

/* Radio group styling */
.radio-group {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* Toggle switch styling */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #4CAF50;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Gradient options styling */
.bg-gradient-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

@media (max-width: 768px) {
    .bg-gradient-options {
        grid-template-columns: 1fr;
    }
}

/* Improved spacing for background options */
.bg-solid-option {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

@media (max-width: 768px) {
    .bg-solid-option {
        grid-template-columns: 1fr;
    }
}

/* Form transparency toggle */
#form-bg-color-field {
    transition: opacity 0.3s ease;
}

#form-bg-color-field.hidden {
    opacity: 0.5;
    pointer-events: none;
}

/* Appearance settings header */
.appearance-settings-compact::before {
    content: '';
    grid-column: 1 / -1;
    height: 0;
    margin-bottom: -15px;
}

/* Color validation feedback */
.color-circle.invalid {
    border-color: #f44336;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Loading state for color circles */
.color-circles.loading .color-circle {
    opacity: 0.5;
    pointer-events: none;
}

/* Tooltip styling for color circles */
.color-circle[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* Loading overlay for faucet form */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    border-radius: 8px;
}

.loading-spinner {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-weight: 500;
    color: #333;
}

.loading-spinner::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #4CAF50;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
