/**
 * Farm Faucet - Organized Appearance Settings
 * Clean, organized styles for the faucet appearance settings
 */

/* Main appearance container */
.appearance-settings-container {
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 20px;
    margin-bottom: 20px;
}

/* Appearance sections */
.appearance-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.appearance-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.appearance-section h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form field styling */
.appearance-section .form-field {
    margin-bottom: 15px;
}

.appearance-section .form-field:last-child {
    margin-bottom: 0;
}

.appearance-section .form-field label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
    font-size: 13px;
}

.appearance-section .form-field .description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Grid layouts for organized sections */
.color-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

.style-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

/* Specific grid for background color options (2 columns) */
.bg-solid-option .color-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

/* Ensure background options don't overlap with other sections */
.faucet-bg-options {
    margin-top: 15px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: 1fr 1fr;
    }

    .style-grid {
        grid-template-columns: 1fr;
    }

    .bg-solid-option .color-grid {
        grid-template-columns: 1fr;
    }
}

/* Color picker container */
.color-picker-container {
    position: relative;
    margin-top: 10px;
}

/* Color circles layout - Override any conflicting styles */
.color-circles {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
    margin-top: 5px !important;
    align-items: center !important;
}

.color-circle {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    border: 3px solid transparent !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    display: inline-block !important;
    flex-shrink: 0 !important;
}

.color-circle:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.color-circle.active {
    border-color: #333 !important;
    transform: scale(1.15) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.color-circle.active::after {
    content: '✓' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: white !important;
    font-weight: bold !important;
    font-size: 14px !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7) !important;
    z-index: 10 !important;
}

/* Ensure color circles container is not affected by other styles */
.appearance-settings-container .color-picker-container {
    position: relative !important;
    margin-top: 10px !important;
}

.appearance-settings-container .color-circles {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
    margin-top: 5px !important;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .appearance-settings-compact {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 12px;
    }
    
    .color-circles {
        justify-content: center;
    }
    
    .color-circle {
        width: 28px;
        height: 28px;
    }
}

/* Form field improvements - Override conflicting styles */
.appearance-section input[type="color"] {
    width: 100% !important;
    height: 40px !important;
    border: 2px solid #ddd !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    padding: 0 !important;
    background: none !important;
    transition: border-color 0.2s ease !important;
    margin-right: 0 !important;
    vertical-align: top !important;
}

.appearance-section input[type="color"]:hover {
    border-color: #4CAF50 !important;
}

.appearance-section input[type="color"]:focus {
    outline: none !important;
    border-color: #4CAF50 !important;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
}

/* Override the circular color picker fix for our appearance section */
.appearance-settings-container input[type="color"] {
    width: 100% !important;
    height: 40px !important;
    border-radius: 6px !important;
    margin-right: 0 !important;
}

/* Ensure our color grid layout works properly */
.appearance-settings-container .color-grid input[type="color"] {
    width: 100% !important;
    height: 40px !important;
    border-radius: 6px !important;
}

.appearance-section select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 13px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.appearance-section select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Background settings improvements */
.faucet-bg-options {
    margin-top: 15px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.faucet-bg-options .form-field {
    margin-bottom: 15px;
}

.faucet-bg-options .form-field:last-child {
    margin-bottom: 0;
}

/* Radio group styling */
.radio-group {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* Toggle switch styling */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #4CAF50;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Gradient options styling */
.bg-gradient-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

@media (max-width: 768px) {
    .bg-gradient-options {
        grid-template-columns: 1fr;
    }
}

/* Improved spacing for background options */
.bg-solid-option {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
    clear: both;
    position: relative;
    z-index: 1;
}

/* Ensure background options container has proper spacing */
.bg-solid-option .color-grid {
    margin-bottom: 15px;
}

/* Form transparency toggle with proper spacing */
.bg-solid-option .form-field {
    margin-top: 15px;
    clear: both;
}

@media (max-width: 768px) {
    .bg-solid-option {
        grid-template-columns: 1fr;
    }
}

/* Form transparency toggle */
#form-bg-color-field {
    transition: all 0.3s ease;
    overflow: hidden;
}

#form-bg-color-field.hidden {
    opacity: 0.5;
    pointer-events: none;
    max-height: 0;
    margin: 0;
    padding: 0;
}

/* Ensure proper spacing between appearance sections */
.appearance-section {
    position: relative;
    z-index: auto;
    clear: both;
}

/* Fix for overlapping color pickers */
.appearance-section .color-grid {
    position: relative;
    z-index: 2;
}

.appearance-section .faucet-bg-options {
    position: relative;
    z-index: 1;
    margin-top: 20px;
}

/* Appearance settings header */
.appearance-settings-compact::before {
    content: '';
    grid-column: 1 / -1;
    height: 0;
    margin-bottom: -15px;
}

/* Color validation feedback */
.color-circle.invalid {
    border-color: #f44336;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Loading state for color circles */
.color-circles.loading .color-circle {
    opacity: 0.5;
    pointer-events: none;
}

/* Tooltip styling for color circles */
.color-circle[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* Loading overlay for faucet form */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    border-radius: 8px;
}

.loading-spinner {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-weight: 500;
    color: #333;
}

.loading-spinner::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #4CAF50;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Override WordPress color picker styles that conflict with our design */
.appearance-settings-container .wp-picker-container {
    display: none !important;
}

.appearance-settings-container .wp-color-result {
    display: none !important;
}

.appearance-settings-container .wp-color-result-text {
    display: none !important;
}

/* Ensure our custom color inputs are not converted to WordPress color pickers */
.appearance-settings-container input[type="color"].wp-color-picker {
    width: 100% !important;
    height: 40px !important;
    border-radius: 6px !important;
    display: block !important;
    visibility: visible !important;
}

/* Hide any WordPress color picker elements in our appearance section */
.appearance-settings-container .iris-picker {
    display: none !important;
}

.appearance-settings-container .wp-picker-clear {
    display: none !important;
}

/* Ensure our form fields maintain proper layout */
.appearance-settings-container .form-field {
    position: relative !important;
    margin-bottom: 15px !important;
}

.appearance-settings-container .form-field:last-child {
    margin-bottom: 0 !important;
}

/* Fix any z-index issues */
.appearance-settings-container {
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure color circles are always visible */
.appearance-settings-container .color-circles .color-circle {
    position: relative !important;
    z-index: 2 !important;
}
