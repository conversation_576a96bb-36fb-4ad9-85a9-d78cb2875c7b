/**
 * Farm Faucet - Faucet Appearance Fixes
 * Improves the appearance of faucets with transparent backgrounds and custom border radius
 */

/* Transparent background fixes */
.farmfaucet-container.transparent-bg {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-container.transparent-bg .farmfaucet-form,
.farmfaucet-container.transparent-bg .farmfaucet-header,
.farmfaucet-container.transparent-bg .farmfaucet-footer,
.farmfaucet-container.transparent-bg .form-group,
.farmfaucet-container.transparent-bg .conversion-form,
.farmfaucet-container.transparent-bg .withdrawal-form {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Form background color */
.farmfaucet-form {
    background-color: var(--form-bg-color, #ffffff) !important;
}

/* When transparent background is enabled, override form background */
.farmfaucet-container.transparent-bg .farmfaucet-form {
    background-color: transparent !important;
}

/* Fully transparent form */
.farmfaucet-form.transparent-form {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-form.transparent-form .form-group,
.farmfaucet-form.transparent-form .farmfaucet-header,
.farmfaucet-form.transparent-form .farmfaucet-footer {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-form.transparent-form input,
.farmfaucet-form.transparent-form select,
.farmfaucet-form.transparent-form textarea,
.farmfaucet-form.transparent-form button {
    background-color: transparent !important;
    border-color: rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
    color: inherit !important;
}

.farmfaucet-form.transparent-form input:hover,
.farmfaucet-form.transparent-form select:hover,
.farmfaucet-form.transparent-form textarea:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
}

.farmfaucet-form.transparent-form input:focus,
.farmfaucet-form.transparent-form select:focus,
.farmfaucet-form.transparent-form textarea:focus {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(0, 0, 0, 0.3) !important;
}

/* Form elements with transparent background */
.farmfaucet-container.transparent-bg input,
.farmfaucet-container.transparent-bg select,
.farmfaucet-container.transparent-bg button {
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.farmfaucet-container.transparent-bg input:hover,
.farmfaucet-container.transparent-bg select:hover {
    background-color: rgba(255, 255, 255, 0.5);
    border-color: rgba(0, 0, 0, 0.2);
}

.farmfaucet-container.transparent-bg input:focus,
.farmfaucet-container.transparent-bg select:focus {
    background-color: rgba(255, 255, 255, 0.7);
    border-color: rgba(0, 0, 0, 0.3);
}

/* Fully transparent form elements when form_transparent is enabled */
.farmfaucet-container.transparent-bg .transparent-form input,
.farmfaucet-container.transparent-bg .transparent-form select,
.farmfaucet-container.transparent-bg .transparent-form .form-group {
    background-color: transparent !important;
    border-color: rgba(0, 0, 0, 0.1);
}

.farmfaucet-container.transparent-bg .transparent-form input:hover,
.farmfaucet-container.transparent-bg .transparent-form select:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(0, 0, 0, 0.2);
}

.farmfaucet-container.transparent-bg .transparent-form input:focus,
.farmfaucet-container.transparent-bg .transparent-form select:focus {
    background-color: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(0, 0, 0, 0.3);
}

/* Hide elements on transparent background until hover */
.farmfaucet-container.transparent-bg .farmfaucet-form {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.farmfaucet-container.transparent-bg .farmfaucet-form:hover {
    opacity: 1;
}

/* Border radius customization */
.farmfaucet-container[data-border-radius="0"] .farmfaucet-button,
.farmfaucet-container[data-border-radius="0"] input,
.farmfaucet-container[data-border-radius="0"] select,
.farmfaucet-container[data-border-radius="0"] .farmfaucet-form {
    border-radius: 0 !important;
}

.farmfaucet-container[data-border-radius="4px"] .farmfaucet-button,
.farmfaucet-container[data-border-radius="4px"] input,
.farmfaucet-container[data-border-radius="4px"] select,
.farmfaucet-container[data-border-radius="4px"] .farmfaucet-form {
    border-radius: 4px !important;
}

.farmfaucet-container[data-border-radius="8px"] .farmfaucet-button,
.farmfaucet-container[data-border-radius="8px"] input,
.farmfaucet-container[data-border-radius="8px"] select,
.farmfaucet-container[data-border-radius="8px"] .farmfaucet-form {
    border-radius: 8px !important;
}

.farmfaucet-container[data-border-radius="16px"] .farmfaucet-button,
.farmfaucet-container[data-border-radius="16px"] input,
.farmfaucet-container[data-border-radius="16px"] select,
.farmfaucet-container[data-border-radius="16px"] .farmfaucet-form {
    border-radius: 16px !important;
}

.farmfaucet-container[data-border-radius="24px"] .farmfaucet-button,
.farmfaucet-container[data-border-radius="24px"] input,
.farmfaucet-container[data-border-radius="24px"] select,
.farmfaucet-container[data-border-radius="24px"] .farmfaucet-form {
    border-radius: 24px !important;
}

.farmfaucet-container[data-border-radius="50%"] .farmfaucet-button {
    border-radius: 50px !important;
}

/* Button color customization */
.farmfaucet-container .farmfaucet-button.custom-color {
    background-color: var(--button-color, #4CAF50) !important;
    border-color: var(--button-color, #4CAF50) !important;
}

.farmfaucet-container .farmfaucet-button.custom-color:hover {
    background-color: var(--button-hover-color, #45a049) !important;
    border-color: var(--button-hover-color, #45a049) !important;
}

/* Border color customization */
.farmfaucet-container.custom-border {
    border: 2px solid var(--border-color, #4CAF50) !important;
}

.farmfaucet-container.custom-border input,
.farmfaucet-container.custom-border select {
    border-color: var(--border-color, #4CAF50) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .farmfaucet-container.transparent-bg .farmfaucet-form {
        opacity: 1; /* Always visible on mobile */
    }
}
