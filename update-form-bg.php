<?php
/**
 * Farm Faucet Form Background Update Script
 * 
 * This script adds the form_bg_color and form_transparent columns to the faucets table.
 */

// Define the table prefix
$table_prefix = 'wp_';

// Try to load WordPress
if (file_exists('../../../wp-load.php')) {
    require_once('../../../wp-load.php');
    global $wpdb;
    $table_prefix = $wpdb->prefix;
} else {
    // If WordPress is not available, use a direct database connection
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'wordpress';
    
    // Connect to the database
    $mysqli = new mysqli($db_host, $db_user, $db_pass, $db_name);
    
    // Check connection
    if ($mysqli->connect_error) {
        die("Connection failed: " . $mysqli->connect_error);
    }
}

// Define the faucets table name
$faucets_table = $table_prefix . 'farmfaucet_faucets';

// Output header
echo '<html><head><title>Farm Faucet Form Background Update</title>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background-color: #dff0d8; color: #3c763d; padding: 15px; border: 1px solid #d6e9c6; border-radius: 4px; margin: 20px 0; }
    .error { background-color: #f2dede; color: #a94442; padding: 15px; border: 1px solid #ebccd1; border-radius: 4px; margin: 20px 0; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; }
</style>';
echo '</head><body>';
echo '<h1>Farm Faucet Form Background Update</h1>';

// Function to execute a query and handle errors
function execute_query($query) {
    global $mysqli, $wpdb;
    
    if (isset($wpdb)) {
        $result = $wpdb->query($query);
        if ($result === false) {
            echo '<div class="error"><p>Error executing query: ' . $wpdb->last_error . '</p>';
            echo '<pre>' . $query . '</pre></div>';
            return false;
        }
        return true;
    } else {
        $result = $mysqli->query($query);
        if ($result === false) {
            echo '<div class="error"><p>Error executing query: ' . $mysqli->error . '</p>';
            echo '<pre>' . $query . '</pre></div>';
            return false;
        }
        return true;
    }
}

// Function to check if a column exists
function column_exists($table, $column) {
    global $mysqli, $wpdb;
    
    if (isset($wpdb)) {
        $result = $wpdb->get_var("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
        return $result === $column;
    } else {
        $result = $mysqli->query("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
        return $result->num_rows > 0;
    }
}

// Check if the faucets table exists
if (isset($wpdb)) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
} else {
    $result = $mysqli->query("SHOW TABLES LIKE '{$faucets_table}'");
    $table_exists = $result->num_rows > 0;
}

if (!$table_exists) {
    echo '<div class="error"><p>The faucets table does not exist. Please make sure the Farm Faucet plugin is installed correctly.</p></div>';
} else {
    echo '<p>Found faucets table: ' . $faucets_table . '</p>';
    
    // Add form_bg_color column if it doesn't exist
    if (!column_exists($faucets_table, 'form_bg_color')) {
        $query = "ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius";
        if (execute_query($query)) {
            echo '<div class="success"><p>Added form_bg_color column to the faucets table.</p></div>';
        }
    } else {
        echo '<p>The form_bg_color column already exists.</p>';
    }
    
    // Add form_transparent column if it doesn't exist
    if (!column_exists($faucets_table, 'form_transparent')) {
        $query = "ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color";
        if (execute_query($query)) {
            echo '<div class="success"><p>Added form_transparent column to the faucets table.</p></div>';
        }
    } else {
        echo '<p>The form_transparent column already exists.</p>';
    }
    
    // Update existing faucets to set default values
    $query = "UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''";
    if (execute_query($query)) {
        echo '<div class="success"><p>Updated existing faucets with default form background values.</p></div>';
    }
}

// Close the database connection if using direct connection
if (isset($mysqli)) {
    $mysqli->close();
}

echo '<div class="success"><h2>Update Complete</h2>';
echo '<p>The Farm Faucet database has been updated to support form background color and transparency.</p>';
echo '<p>You can now return to the Farm Faucet admin page and configure your faucets.</p>';
echo '</div>';

echo '</body></html>';
?>
