<?php
/**
 * Farm Faucet - Debug Faucet Save Issues
 * 
 * This script helps debug issues with saving faucet settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Debug Faucet Save</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 10px; border-left: 4px solid #4CAF50; margin: 10px 0; }
        .error { color: #f44336; background: #fdf0f0; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }
        .info { color: #2196F3; background: #f0f7ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .warning { color: #ff9800; background: #fff8f0; padding: 10px; border-left: 4px solid #ff9800; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Farm Faucet - Debug Faucet Save</h1>

<?php

global $wpdb;

// Get the faucets table name
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

echo '<h2>1. Database Table Structure</h2>';

// Check if the faucets table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if (!$table_exists) {
    echo '<div class="error"><p>❌ The faucets table does not exist!</p></div>';
} else {
    echo '<div class="success"><p>✅ Faucets table exists: ' . $faucets_table . '</p></div>';
    
    // Get table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    
    echo '<h3>Table Columns:</h3>';
    echo '<table>';
    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
    
    $required_fields = ['button_border_radius', 'input_label_color', 'input_placeholder_color'];
    $found_fields = [];
    
    foreach ($columns as $column) {
        echo '<tr>';
        echo '<td>' . esc_html($column->Field) . '</td>';
        echo '<td>' . esc_html($column->Type) . '</td>';
        echo '<td>' . esc_html($column->Null) . '</td>';
        echo '<td>' . esc_html($column->Key) . '</td>';
        echo '<td>' . esc_html($column->Default) . '</td>';
        echo '</tr>';
        
        if (in_array($column->Field, $required_fields)) {
            $found_fields[] = $column->Field;
        }
    }
    echo '</table>';
    
    // Check if new fields exist
    echo '<h3>New Appearance Fields Status:</h3>';
    foreach ($required_fields as $field) {
        if (in_array($field, $found_fields)) {
            echo '<div class="success"><p>✅ ' . $field . ' - EXISTS</p></div>';
        } else {
            echo '<div class="error"><p>❌ ' . $field . ' - MISSING</p></div>';
        }
    }
}

echo '<h2>2. Test AJAX Handler Functionality</h2>';

// Simulate AJAX create faucet request
if (class_exists('Farmfaucet_Admin')) {
    echo '<div class="info"><p>✅ Farmfaucet_Admin class exists</p></div>';
    
    // Check if AJAX handlers are registered
    $admin_instance = Farmfaucet_Admin::init();
    
    // Test data
    $test_data = [
        'name' => 'Debug Test Faucet',
        'shortcode' => 'debug_test_' . time(),
        'faucet_type' => 'stage',
        'currency' => 'LTC',
        'amount' => '0.001',
        'cooldown' => '3600',
        'button_color' => '#FF5722',
        'button_border_radius' => '15px',
        'input_label_color' => '#FFFFFF',
        'input_placeholder_color' => '#CCCCCC',
        'border_color' => '#4CAF50',
        'border_radius' => '8px',
        'form_bg_color' => '#ffffff',
        'form_transparent' => 0,
        'is_enabled' => 1
    ];
    
    echo '<h3>Test Data:</h3>';
    echo '<pre>' . print_r($test_data, true) . '</pre>';
    
    // Simulate the database insert
    echo '<h3>Database Insert Test:</h3>';
    
    $insert_result = $wpdb->insert(
        $faucets_table,
        $test_data,
        [
            '%s', // name
            '%s', // shortcode
            '%s', // faucet_type
            '%s', // currency
            '%s', // amount
            '%d', // cooldown
            '%s', // button_color
            '%s', // button_border_radius
            '%s', // input_label_color
            '%s', // input_placeholder_color
            '%s', // border_color
            '%s', // border_radius
            '%s', // form_bg_color
            '%d', // form_transparent
            '%d'  // is_enabled
        ]
    );
    
    if ($insert_result === false) {
        echo '<div class="error"><p>❌ Database insert failed!</p>';
        echo '<p>Error: ' . $wpdb->last_error . '</p></div>';
    } else {
        $inserted_id = $wpdb->insert_id;
        echo '<div class="success"><p>✅ Database insert successful! New faucet ID: ' . $inserted_id . '</p></div>';
        
        // Retrieve the inserted data to verify
        $retrieved_data = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$faucets_table} WHERE id = %d", $inserted_id),
            ARRAY_A
        );
        
        if ($retrieved_data) {
            echo '<h3>Retrieved Data:</h3>';
            echo '<table>';
            echo '<tr><th>Field</th><th>Value</th></tr>';
            foreach ($retrieved_data as $field => $value) {
                echo '<tr><td>' . esc_html($field) . '</td><td>' . esc_html($value) . '</td></tr>';
            }
            echo '</table>';
            
            // Check if new fields were saved correctly
            $new_fields_check = [
                'button_border_radius' => $retrieved_data['button_border_radius'] ?? 'NOT FOUND',
                'input_label_color' => $retrieved_data['input_label_color'] ?? 'NOT FOUND',
                'input_placeholder_color' => $retrieved_data['input_placeholder_color'] ?? 'NOT FOUND'
            ];
            
            echo '<h3>New Fields Verification:</h3>';
            foreach ($new_fields_check as $field => $value) {
                if ($value === 'NOT FOUND') {
                    echo '<div class="error"><p>❌ ' . $field . ': NOT FOUND</p></div>';
                } else {
                    echo '<div class="success"><p>✅ ' . $field . ': ' . $value . '</p></div>';
                }
            }
        }
        
        // Clean up - delete the test faucet
        $wpdb->delete($faucets_table, ['id' => $inserted_id], ['%d']);
        echo '<div class="info"><p>🧹 Test faucet cleaned up (deleted)</p></div>';
    }
    
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Admin class not found!</p></div>';
}

echo '<h2>3. JavaScript Debug Information</h2>';
echo '<div class="info"><p>Check the browser console for JavaScript errors when saving faucets.</p>';
echo '<p>Common issues:</p>';
echo '<ul>';
echo '<li>Missing form fields in the admin form</li>';
echo '<li>JavaScript not sending the new field values</li>';
echo '<li>AJAX nonce verification failing</li>';
echo '<li>Database fields not existing</li>';
echo '</ul></div>';

echo '<h2>4. Recommendations</h2>';
echo '<div class="warning"><p><strong>If you see any errors above:</strong></p>';
echo '<ol>';
echo '<li>Run the <a href="update-appearance-fields.php">Database Update Script</a> first</li>';
echo '<li>Check browser console for JavaScript errors</li>';
echo '<li>Verify that all form fields exist in the admin template</li>';
echo '<li>Test creating a simple faucet without the new appearance settings</li>';
echo '</ol></div>';

?>

<p><a href="<?php echo admin_url('admin.php?page=farmfaucet'); ?>" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">← Back to Farm Faucet Admin</a></p>

</body>
</html>
