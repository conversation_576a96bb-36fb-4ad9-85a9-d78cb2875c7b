/* Fix for checkbox styling */
.form-field input[type="checkbox"].square-input {
    width: auto;
    height: auto;
    margin-right: 8px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

.form-field label {
    display: inline-block;
    margin-bottom: 5px;
}

.radio-label {
    display: inline-block;
    margin-right: 15px;
}

.radio-label input[type="radio"] {
    width: auto;
    height: auto;
    margin-right: 5px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

/* Milestone display style options */
.milestone-display-style-container {
    margin-bottom: 20px;
}

.milestone-display-style-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.milestone-display-style-option {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    width: 200px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.milestone-display-style-option:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.milestone-display-style-option.selected {
    border-color: #2271b1;
    background-color: #f0f7fc;
}

.milestone-display-style-option input[type="radio"] {
    position: absolute;
    top: 10px;
    right: 10px;
    margin: 0;
    width: auto;
    height: auto;
}

.style-preview {
    margin-bottom: 15px;
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 10px;
    background: #f9f9f9;
    min-height: 120px;
}

.style-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.style-description {
    color: #666;
    font-size: 0.9em;
}

/* Card view preview */
.card-view-preview {
    font-family: Arial, sans-serif;
}

.card-view-preview .preview-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.card-view-preview .preview-progress {
    height: 15px;
    background: #eee;
    border-radius: 10px;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
}

.card-view-preview .preview-progress-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 75%;
    background: #4CAF50;
}

.card-view-preview .preview-tasks {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.card-view-preview .preview-task {
    display: flex;
    align-items: center;
}

.card-view-preview .preview-task-icon {
    margin-right: 8px;
    color: #4CAF50;
}

/* Compact view preview */
.compact-view-preview {
    font-family: Arial, sans-serif;
}

.compact-view-preview .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.compact-view-preview .preview-title {
    font-weight: bold;
    font-size: 0.9em;
}

.compact-view-preview .preview-percentage {
    font-weight: bold;
    color: #4CAF50;
}

.compact-view-preview .preview-progress {
    height: 12px;
    background: #eee;
    border-radius: 6px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

.compact-view-preview .preview-progress-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 75%;
    background: #4CAF50;
}

.compact-view-preview .preview-progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 0.8em;
    line-height: 12px;
    color: #333;
    font-weight: bold;
}

.compact-view-preview .preview-summary {
    font-size: 0.85em;
    color: #666;
    margin-bottom: 8px;
}

.compact-view-preview .preview-dropdown {
    font-size: 0.8em;
    color: #666;
    text-align: center;
    background: #f0f0f0;
    padding: 3px;
    border-radius: 3px;
}
