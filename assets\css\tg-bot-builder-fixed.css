/**
 * <PERSON><PERSON><PERSON><PERSON>er CSS
 */

/* General Styles */
.farmfaucet-admin-section {
    margin: 20px 0;
}

.farmfaucet-admin-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    border-left: 4px solid #28a745;
}

.card-header {
    background: #f9f9f9;
    border-bottom: 1px solid #e5e5e5;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.card-body {
    padding: 20px;
}

/* Tutorial Section */
.tutorial-section {
    margin-top: 15px;
    padding: 15px;
    background: rgba(40, 167, 69, 0.05);
    border-radius: 6px;
    border-left: 3px solid #28a745;
}

.tutorial-section h4 {
    margin-top: 0;
    color: #28a745;
}

.tutorial-section ol {
    margin-left: 20px;
    padding-left: 0;
}

.tutorial-section li {
    margin-bottom: 8px;
}

/* Bot Management */
.no-bots-message {
    text-align: center;
    padding: 30px;
    color: #6c757d;
}

.bots-tabs-container {
    margin-top: 15px;
}

.bots-tabs-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #dee2e6;
}

.bots-tabs-nav li {
    margin-right: 5px;
}

.bots-tabs-nav a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: #495057;
    background-color: #f8f9fa;
    border: 1px solid transparent;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-weight: 500;
}

.bots-tabs-nav a:hover {
    background-color: #e9ecef;
}

.bots-tabs-nav li.ui-tabs-active a {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    color: #28a745;
}

.bot-tab-content {
    padding: 20px 0;
    display: none;
}

.bot-tab-content.active {
    display: block;
}

/* Bot Details */
.bot-details {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.bot-details h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #343a40;
}

.bot-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.bot-info-item {
    display: flex;
    align-items: center;
}

.bot-info-item strong {
    margin-right: 8px;
    color: #495057;
}

.bot-status-toggle {
    display: flex;
    align-items: center;
}

.bot-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* Commands Section */
.bot-commands-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.commands-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.commands-header h4 {
    margin: 0;
    color: #343a40;
}

.no-commands-message {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    background: #fff;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

.commands-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.command-item {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    border-left: 3px solid #28a745;
}

.command-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.command-header h5 {
    margin: 0;
    color: #28a745;
    font-family: monospace;
    font-size: 16px;
}

.command-actions {
    display: flex;
    gap: 5px;
}

.command-description {
    color: #6c757d;
    font-size: 14px;
}

/* Bot Testing Section */
.bot-testing-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.testing-header {
    margin-bottom: 15px;
}

.testing-header h4 {
    margin: 0;
    color: #343a40;
}

.chat-simulator {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-messages {
    height: 300px;
    overflow-y: auto;
    padding: 15px;
    background: #f5f5f5;
}

.bot-message {
    display: flex;
    margin-bottom: 15px;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    background: #e3f2fd;
    border-radius: 18px;
    padding: 10px 15px;
    max-width: 70%;
}

.message-content p {
    margin: 0;
    color: #333;
}

.chat-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid #e5e5e5;
}

.chat-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-right: 10px;
}

/* Dialog Styles */
.farmfaucet-dialog {
    padding: 0;
    border-radius: 8px;
    overflow: hidden;
}

.farmfaucet-dialog .ui-dialog-titlebar {
    background: #28a745;
    color: #fff;
    border: none;
    border-radius: 0;
    padding: 15px 20px;
}

.farmfaucet-dialog .ui-dialog-content {
    padding: 20px;
}

.farmfaucet-dialog form {
    margin: 0;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-group input[type="text"],
.form-group input[type="url"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.command-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.command-prefix {
    position: absolute;
    left: 10px;
    color: #6c757d;
    font-weight: bold;
}

#command-name {
    padding-left: 25px;
}

.response-type-options {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.response-type-options label {
    display: flex;
    align-items: center;
    font-weight: normal;
}

.response-type-options input {
    margin-right: 5px;
}

.buttons-list {
    margin-bottom: 10px;
}

.button-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

/* Form Actions */
.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #28a745;
}

input:focus + .slider {
    box-shadow: 0 0 1px #28a745;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Status Label */
.status-label {
    margin-left: 10px;
    font-weight: 500;
}

/* Button Styling */
.button-primary,
.farmfaucet-add-bot-button {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.button-primary:hover,
.farmfaucet-add-bot-button:hover {
    background-color: #3d8b40 !important;
    border-color: #3d8b40 !important;
}

/* Dialog buttons styling */
.ui-dialog-buttonset button:first-child {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

/* Description Text */
.description {
    color: #6c757d;
    font-size: 14px;
    margin-top: 5px;
}

/* Color Picker */
.wp-picker-container {
    display: inline-block;
}
