/**
 * Farm Faucet Button Color Picker Fix (New Version)
 *
 * Fixes issues with the color picker in the button edit form
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Button Color Picker Fix (New Version) loaded');

        // Fix for color grid display in button form
        function fixColorGrids() {
            $('.color-grid').each(function() {
                var $grid = $(this);
                
                // Make sure the grid has proper styling
                $grid.css({
                    'display': 'none',
                    'position': 'absolute',
                    'top': '100%',
                    'left': '0',
                    'background': 'white',
                    'border': '1px solid #ddd',
                    'border-radius': '4px',
                    'padding': '10px',
                    'z-index': '9999',
                    'margin-top': '5px',
                    'grid-template-columns': 'repeat(8, 1fr)',
                    'gap': '8px',
                    'width': '320px',
                    'box-shadow': '0 4px 8px rgba(0,0,0,0.1)'
                });
                
                // Make sure active grids display as grid
                if ($grid.hasClass('active')) {
                    $grid.css('display', 'grid');
                }
                
                // Add class to mark as fixed
                $grid.addClass('grid-fixed');
            });
            
            // Make sure color swatches are properly styled
            $('.color-swatch-option').each(function() {
                var $swatch = $(this);
                $swatch.css({
                    'width': '32px',
                    'height': '32px',
                    'border-radius': '50%',
                    'cursor': 'pointer',
                    'border': '1px solid rgba(0, 0, 0, 0.1)',
                    'transition': 'all 0.2s ease',
                    'margin': '0 auto'
                });
                
                // Add hover effect
                $swatch.hover(
                    function() {
                        $(this).css({
                            'transform': 'scale(1.1)',
                            'box-shadow': '0 2px 5px rgba(0, 0, 0, 0.2)'
                        });
                    },
                    function() {
                        $(this).css({
                            'transform': 'scale(1)',
                            'box-shadow': 'none'
                        });
                    }
                );
            });
            
            // Style selected color swatch
            $('.color-swatch-option.selected').css({
                'box-shadow': '0 0 0 2px white, 0 0 0 4px #4CAF50'
            });
        }

        // Override the color swatch selection handler for button form
        $(document).off('click', '.color-swatch-option').on('click', '.color-swatch-option', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $this = $(this);
            var value = $this.data('value');
            var name = $this.data('name');
            var color = $this.data('color');
            
            console.log('Color selected:', value, name, color);
            
            // Update hidden input
            $('#button-color').val(value);
            
            // Update visual elements
            $('.color-swatch-option').removeClass('selected');
            $this.addClass('selected');
            $('.color-name-display').text(name);
            $('.color-preview').css('background-color', color);
            
            // Style selected color swatch
            $('.color-swatch-option').css('box-shadow', 'none');
            $this.css('box-shadow', '0 0 0 2px white, 0 0 0 4px #4CAF50');
            
            // Hide color grid
            $('.color-grid').removeClass('active').hide();
            
            return false;
        });
        
        // Toggle color grid on preview click
        $(document).off('click', '.color-preview, .color-select-wrapper').on('click', '.color-preview, .color-select-wrapper', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $grid = $(this).closest('.color-grid-container').find('.color-grid');
            
            // Close any other open color grids first
            $('.color-grid').not($grid).removeClass('active').hide();
            
            // Toggle this color grid
            $grid.toggleClass('active');
            
            if ($grid.hasClass('active')) {
                $grid.css('display', 'grid');
            } else {
                $grid.hide();
            }
            
            return false;
        });
        
        // Close color grid when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').removeClass('active').hide();
            }
        });

        // Run the fix immediately
        fixColorGrids();
        
        // Fix color grids when button dialog opens
        $(document).on('dialogopen', '#button-form-dialog', function() {
            setTimeout(fixColorGrids, 100);
            setTimeout(fixColorGrids, 500); // Run again after a delay to ensure it works
        });

        // Fix color grid when editing a button
        $(document).on('click', '.edit-button', function() {
            setTimeout(fixColorGrids, 100);
            setTimeout(fixColorGrids, 500);
        });

        // Fix color grid when adding a button
        $(document).on('click', '.add-button', function() {
            setTimeout(fixColorGrids, 100);
            setTimeout(fixColorGrids, 500);
        });

        // Update color preview when button data is loaded
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.data && settings.data.indexOf('farmfaucet_get_button') !== -1) {
                setTimeout(function() {
                    var selectedColor = $('#button-color').val();
                    var colorOption = $('.color-swatch-option[data-value="' + selectedColor + '"]');
                    
                    if (colorOption.length) {
                        // Update visual elements
                        $('.color-swatch-option').removeClass('selected');
                        colorOption.addClass('selected');
                        $('.color-name-display').text(colorOption.data('name'));
                        $('.color-preview').css('background-color', colorOption.data('color'));
                        
                        // Style selected color swatch
                        $('.color-swatch-option').css('box-shadow', 'none');
                        colorOption.css('box-shadow', '0 0 0 2px white, 0 0 0 4px #4CAF50');
                    }
                    
                    fixColorGrids();
                }, 200);
            }
        });
    });

})(jQuery);
