/**
 * Teleg<PERSON><PERSON> Builder CSS
 */

/* General styles */
.farmfaucet-admin-section.tg-bot-builder-section {
    margin-top: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.farmfaucet-admin-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.farmfaucet-admin-card:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.farmfaucet-admin-card .card-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.farmfaucet-admin-card .card-body {
    padding: 25px;
    position: relative;
}

/* Tabs */
.farmfaucet-tg-bot-tabs {
    margin-top: 25px;
}

.farmfaucet-tg-bot-tabs-nav {
    display: flex;
    list-style: none;
    margin: 0 0 20px 0;
    padding: 0;
    border-bottom: 2px solid rgba(76, 175, 80, 0.2);
    position: relative;
    z-index: 1;
}

.farmfaucet-tg-bot-tabs-nav li {
    margin: 0 5px 0 0;
    padding: 0;
}

.farmfaucet-tg-bot-tabs-nav li a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #555;
    font-weight: 600;
    font-size: 15px;
    border-radius: 8px 8px 0 0;
    background-color: rgba(76, 175, 80, 0.05);
    border: 2px solid transparent;
    border-bottom: none;
    transition: all 0.3s ease;
    position: relative;
    top: 2px;
}

.farmfaucet-tg-bot-tabs-nav li.active a {
    color: #4CAF50;
    background-color: #fff;
    border-color: rgba(76, 175, 80, 0.2);
    border-bottom: 2px solid #fff;
}

.farmfaucet-tg-bot-tabs-nav li a:hover:not(.active) {
    color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
}

.farmfaucet-tg-bot-tabs-content {
    padding: 25px;
    background-color: #fff;
    border-radius: 0 8px 8px 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(76, 175, 80, 0.1);
    min-height: 300px;
}

.farmfaucet-tg-bot-tab {
    display: none;
    animation: fadeIn 0.5s ease;
}

.farmfaucet-tg-bot-tab.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Forms */
.farmfaucet-form {
    max-width: 100%;
}

.farmfaucet-form .form-group {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.farmfaucet-form .form-group:hover {
    transform: translateX(3px);
}

.farmfaucet-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.farmfaucet-form input[type="text"],
.farmfaucet-form input[type="password"],
.farmfaucet-form input[type="url"],
.farmfaucet-form select,
.farmfaucet-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.farmfaucet-form input[type="text"]:focus,
.farmfaucet-form input[type="password"]:focus,
.farmfaucet-form input[type="url"]:focus,
.farmfaucet-form select:focus,
.farmfaucet-form textarea:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
    outline: none;
}

.farmfaucet-form .description {
    margin-top: 8px;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
    font-style: italic;
}

.farmfaucet-form .form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.farmfaucet-form button {
    padding: 10px 20px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.farmfaucet-form button[type="submit"] {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
}

.farmfaucet-form button[type="submit"]:hover {
    box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

.farmfaucet-form button[type="button"] {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.farmfaucet-form button[type="button"]:hover {
    background-color: #eee;
}

/* Switch toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin-right: 15px;
    vertical-align: middle;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e0e0e0;
    transition: .4s;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:focus + .slider {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

input:checked + .slider:before {
    transform: translateX(30px);
}

.slider.round {
    border-radius: 30px;
}

.slider.round:before {
    border-radius: 50%;
}

.status-label {
    vertical-align: middle;
    font-weight: 600;
    font-size: 15px;
    margin-left: 5px;
    transition: all 0.3s ease;
}

input:checked ~ .status-label {
    color: #4CAF50;
}

/* Bot list */
.farmfaucet-tg-bot-tab table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
}

.farmfaucet-tg-bot-tab table th {
    background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
    color: #333;
    font-weight: 600;
    text-align: left;
    padding: 15px;
    font-size: 15px;
    border-bottom: 2px solid #ddd;
}

.farmfaucet-tg-bot-tab table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
    font-size: 14px;
}

.farmfaucet-tg-bot-tab table tr:last-child td {
    border-bottom: none;
}

.farmfaucet-tg-bot-tab table tr:hover {
    background-color: rgba(76, 175, 80, 0.05);
}

.status-active {
    color: #4CAF50;
    font-weight: 600;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
}

.status-inactive {
    color: #999;
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
}

.farmfaucet-tg-bot-tab table .button {
    margin: 0 5px 0 0;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.farmfaucet-tg-bot-tab table .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.farmfaucet-tg-bot-tab table .edit-bot {
    background-color: #2196F3;
    color: white;
    border: none;
}

.farmfaucet-tg-bot-tab table .delete-bot {
    background-color: #f44336;
    color: white;
    border: none;
}

.farmfaucet-tg-bot-tab table .manage-flows {
    background-color: #4CAF50;
    color: white;
    border: none;
}

/* Flow builder */
.flow-builder-card .card-header {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.flow-builder-card .card-header h3 {
    color: white;
}

.flow-builder-card .close-flow-builder {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.flow-builder-card .close-flow-builder:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.flow-editor-canvas-container {
    display: flex;
    margin-top: 25px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    height: 650px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.flow-editor-sidebar {
    width: 250px;
    background: linear-gradient(180deg, #f9f9f9, #f0f0f0);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
}

.flow-editor-sidebar h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    font-weight: 600;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(76, 175, 80, 0.2);
}

.flow-elements {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.flow-element {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.flow-element:hover {
    background-color: #f0f8ff;
    border-color: #4CAF50;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.flow-element i {
    margin-right: 15px;
    color: #4CAF50;
    font-size: 20px;
}

.flow-element span {
    font-weight: 500;
    font-size: 15px;
}

.flow-editor-canvas {
    flex: 1;
    background-color: #fff;
    background-image:
        linear-gradient(rgba(200, 200, 200, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(200, 200, 200, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    overflow: auto;
    padding: 20px;
}

/* Flow nodes */
.flow-node {
    position: absolute;
    width: 220px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    border: 2px solid #e0e0e0;
    overflow: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.flow-node:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.flow-node-header {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    cursor: move;
}

.flow-node-header i {
    margin-right: 10px;
    font-size: 18px;
}

.flow-node-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
}

.flow-node-content {
    padding: 15px;
    min-height: 50px;
}

.flow-node-text {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.flow-node-description {
    margin: 0;
    font-size: 12px;
    color: #999;
    font-style: italic;
}

.flow-node-footer {
    padding: 10px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
}

/* Node types */
.flow-node-message .flow-node-header {
    background-color: #E3F2FD;
    border-bottom-color: #BBDEFB;
}

.flow-node-message .flow-node-header i {
    color: #2196F3;
}

.flow-node-button .flow-node-header {
    background-color: #E8F5E9;
    border-bottom-color: #C8E6C9;
}

.flow-node-button .flow-node-header i {
    color: #4CAF50;
}

.flow-node-condition .flow-node-header {
    background-color: #FFF3E0;
    border-bottom-color: #FFE0B2;
}

.flow-node-condition .flow-node-header i {
    color: #FF9800;
}

.flow-node-captcha .flow-node-header {
    background-color: #E1F5FE;
    border-bottom-color: #B3E5FC;
}

.flow-node-captcha .flow-node-header i {
    color: #03A9F4;
}

.flow-node-action .flow-node-header {
    background-color: #F3E5F5;
    border-bottom-color: #E1BEE7;
}

.flow-node-action .flow-node-header i {
    color: #9C27B0;
}

.flow-node-moderation .flow-node-header {
    background-color: #FFEBEE;
    border-bottom-color: #FFCDD2;
}

.flow-node-moderation .flow-node-header i {
    color: #F44336;
}

/* jsPlumb connection styles */
.jtk-connector {
    z-index: 4;
}

.jtk-endpoint {
    z-index: 5;
    cursor: pointer;
}

.jtk-overlay {
    z-index: 6;
}

.connection-label {
    background-color: white;
    padding: 2px 5px;
    border-radius: 3px;
    border: 1px solid #ccc;
    font-size: 11px;
    color: #555;
}

.endpoint-label {
    background-color: white;
    padding: 2px 5px;
    border-radius: 3px;
    border: 1px solid #ccc;
    font-size: 11px;
    z-index: 10;
}

.yes-label {
    color: #4CAF50;
    border-color: #4CAF50;
}

.no-label {
    color: #f44336;
    border-color: #f44336;
}

/* Node configuration modal */
.node-config-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.node-config-modal.show {
    opacity: 1;
    visibility: visible;
}

.node-config-content {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.node-config-modal.show .node-config-content {
    transform: scale(1);
}

.node-config-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px 10px 0 0;
}

.node-config-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.node-config-body {
    padding: 20px;
    overflow-y: auto;
    max-height: 60vh;
}

.node-config-footer {
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    border-radius: 0 0 10px 10px;
}

/* Template preview modal */
.template-preview-description {
    margin-bottom: 20px;
}

.template-preview-description p {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
}

.template-preview-image {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.template-preview-image img {
    width: 100%;
    height: auto;
    display: block;
}

.template-preview-features {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.template-preview-features h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.template-preview-features ul {
    margin: 0;
    padding-left: 20px;
}

.template-preview-features li {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.use-template-from-preview {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.use-template-from-preview:hover {
    box-shadow: 0 3px 10px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

.save-node-config {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-node-config:hover {
    box-shadow: 0 3px 10px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

/* Node context menu */
.node-context-menu {
    position: absolute;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.node-context-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.node-context-menu li {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.node-context-menu li:hover {
    background-color: #f5f5f5;
}

.node-context-menu li i {
    margin-right: 10px;
    font-size: 16px;
}

.edit-node i {
    color: #2196F3;
}

.duplicate-node i {
    color: #4CAF50;
}

.delete-node i {
    color: #f44336;
}

.delete-node:hover {
    background-color: #FFEBEE;
}

/* Command management */
.command-list-actions {
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.command-list-container {
    margin-bottom: 30px;
}

.command-preview-section {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 25px;
    margin-top: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.command-preview-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Telegram preview */
.telegram-preview {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 400px;
    margin: 20px auto;
    border: 1px solid #e0e0e0;
}

.telegram-header {
    background-color: #5682a3;
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
}

.telegram-avatar {
    width: 40px;
    height: 40px;
    background-color: #4a76a8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.telegram-avatar i {
    font-size: 24px;
    color: white;
}

.telegram-info {
    flex: 1;
}

.telegram-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 3px;
}

.telegram-status {
    font-size: 12px;
    opacity: 0.8;
}

.telegram-chat {
    padding: 15px;
    background-color: #e6ebee;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.telegram-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 10px;
    position: relative;
}

.user-message {
    background-color: #fff;
    align-self: flex-end;
    border-top-right-radius: 0;
}

.bot-message {
    background-color: #d5f9ba;
    align-self: flex-start;
    border-top-left-radius: 0;
}

.message-content {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-time {
    font-size: 11px;
    color: #999;
    text-align: right;
}

.telegram-keyboard {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    align-self: center;
}

.telegram-button {
    background-color: #f0f0f0;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.telegram-button:hover {
    background-color: #e0e0e0;
}

.preview-controls {
    margin-top: 20px;
}

.preview-input-container {
    display: flex;
    gap: 10px;
}

.preview-input-container input {
    flex: 1;
}

/* Test token result */
#test-token-result {
    display: inline-block;
    margin-left: 15px;
    font-weight: 600;
    padding: 8px 15px;
    border-radius: 6px;
    animation: fadeIn 0.5s ease;
}

#test-token-result.success {
    color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
}

#test-token-result.error {
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
}

/* Template Library */
.template-library-section {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 25px;
    margin-top: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.template-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.template-category {
    background-color: #f0f0f0;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.template-category:hover {
    background-color: #e0e0e0;
}

.template-category.active {
    background-color: #4CAF50;
    color: white;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.template-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.template-preview {
    height: 120px;
    background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-preview i {
    font-size: 48px;
    color: #4CAF50;
}

.template-info {
    padding: 15px;
}

.template-info h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.template-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.template-actions {
    padding: 15px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.template-actions button {
    flex: 1;
}

/* Flow list */
.flow-list-actions {
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flow-list-actions .button {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
    transition: all 0.3s ease;
}

.flow-list-actions .button:hover {
    box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

.flow-list-table-container {
    margin-top: 25px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.flow-list-table-container p {
    font-size: 15px;
    line-height: 1.6;
    color: #666;
    text-align: center;
    padding: 30px;
    background-color: rgba(76, 175, 80, 0.05);
    border-radius: 8px;
    border: 1px dashed rgba(76, 175, 80, 0.3);
}

/* Empty state styling */
.farmfaucet-tg-bot-tab .empty-state {
    text-align: center;
    padding: 40px 20px;
    background-color: rgba(76, 175, 80, 0.05);
    border-radius: 10px;
    margin: 20px 0;
}

.farmfaucet-tg-bot-tab .empty-state p {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.farmfaucet-tg-bot-tab .empty-state .button {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
    transition: all 0.3s ease;
}

.farmfaucet-tg-bot-tab .empty-state .button:hover {
    box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

/* Animation classes */
.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fadeIn {
    animation-name: fadeIn;
}

/* Form animations */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 8px;
}

.form-success {
    animation: formSuccess 1s ease;
}

@keyframes formSuccess {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

.form-resetting {
    animation: formReset 0.3s ease;
}

@keyframes formReset {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Button animations */
.button-clicked {
    animation: buttonClick 0.3s ease;
}

@keyframes buttonClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Field highlight animations */
.error-highlight {
    animation: errorHighlight 1s ease;
    border-color: #f44336 !important;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2) !important;
}

@keyframes errorHighlight {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

.success-highlight {
    animation: successHighlight 1s ease;
    border-color: #4CAF50 !important;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2) !important;
}

@keyframes successHighlight {
    0% { background-color: rgba(76, 175, 80, 0); }
    50% { background-color: rgba(76, 175, 80, 0.1); }
    100% { background-color: rgba(76, 175, 80, 0); }
}

.highlight-text {
    animation: textHighlight 1s ease;
    color: #4CAF50 !important;
    font-weight: bold !important;
}

@keyframes textHighlight {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.row-highlight {
    animation: rowHighlight 1s ease;
    background-color: rgba(76, 175, 80, 0.1) !important;
}

@keyframes rowHighlight {
    0% { background-color: rgba(76, 175, 80, 0); }
    50% { background-color: rgba(76, 175, 80, 0.2); }
    100% { background-color: rgba(76, 175, 80, 0.1); }
}

.row-deleting {
    animation: rowDeleting 1s ease infinite;
    background-color: rgba(244, 67, 54, 0.1) !important;
}

@keyframes rowDeleting {
    0% { background-color: rgba(244, 67, 54, 0.1); }
    50% { background-color: rgba(244, 67, 54, 0.2); }
    100% { background-color: rgba(244, 67, 54, 0.1); }
}

/* Spinners */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 5px;
    vertical-align: middle;
}

.spinner-large {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(76, 175, 80, 0.3);
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Loading container */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.loading-container p {
    color: #666;
    font-size: 16px;
    margin-top: 15px;
}

.loading-text {
    color: #4CAF50;
    font-size: 16px;
    margin-top: 15px;
    font-weight: 500;
}

/* Notification */
.farmfaucet-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 9999;
    max-width: 400px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
}

.farmfaucet-notification.show {
    transform: translateX(0);
}

.farmfaucet-notification.success {
    border-left: 4px solid #4CAF50;
}

.farmfaucet-notification.error {
    border-left: 4px solid #f44336;
}

.notification-content {
    flex: 1;
    padding-right: 15px;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.notification-close:hover {
    color: #666;
}

/* Confirmation dialog */
.confirmation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirmation-dialog.show {
    opacity: 1;
}

.dialog-content {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
    padding: 25px;
    max-width: 400px;
    width: 100%;
    animation: dialogAppear 0.3s ease;
}

@keyframes dialogAppear {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.dialog-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 20px;
}

.dialog-content p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.5;
}

.dialog-content .warning {
    color: #f44336;
    font-weight: 500;
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dialog-buttons .button {
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
}

.dialog-buttons .button-cancel {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.dialog-buttons .button-delete {
    background-color: #f44336;
    color: white;
    border: none;
}

/* Flow empty state */
.flow-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px 20px;
    text-align: center;
    color: #666;
}

.flow-empty-icon {
    font-size: 60px;
    color: #ddd;
    margin-bottom: 20px;
}

.flow-empty-state h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 20px;
}

.flow-empty-state p {
    max-width: 300px;
    line-height: 1.5;
}

/* Edit title */
.edit-title {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2196F3;
    font-size: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(33, 150, 243, 0.2);
}

.edit-title span {
    font-weight: 600;
}

/* Responsive styles */
@media (max-width: 782px) {
    .farmfaucet-tg-bot-tabs-nav {
        flex-wrap: wrap;
    }

    .flow-editor-canvas-container {
        flex-direction: column;
        height: auto;
    }

    .flow-editor-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #ddd;
    }

    .flow-elements {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .flow-element {
        width: calc(50% - 10px);
    }

    .farmfaucet-form .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .farmfaucet-tg-bot-tab table .button {
        display: block;
        margin: 5px 0;
        width: 100%;
        text-align: center;
    }

    .farmfaucet-notification {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}
