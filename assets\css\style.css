/* Main container styling with CSS variables support */
.farmfaucet-container {
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  background: var(--form-bg-color, #f8fff8);
  border-radius: var(--border-radius, 15px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--border-color, #4CAF50);
  position: relative;

  /* Default CSS variables */
  --button-color: #4CAF50;
  --border-color: #4CAF50;
  --text-color: #333;
  --form-bg-color: #f8fff8;
  --button-border-radius: 25px;
  --input-label-color: #333333;
  --input-placeholder-color: #999999;
}

/* Header styling */
.farmfaucet-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.status-header {
  color: var(--text-color, #4CAF50);
  margin: 0 0 1rem;
  font-size: 1.5rem;
}

.post-captcha { display: block; }
.pre-captcha { display: none; }

.reward-notice {
  background: #e8f5e9;
  padding: 0.8rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-weight: 500;
  color: #2e7d32;
}

/* Form elements */
.farmfaucet-input {
  width: 100%;
  padding: 12px 15px;
  margin: 8px 0;
  border: 2px solid #9c27b0;
  border-radius: 25px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.farmfaucet-input:focus {
  outline: none;
  border-color: var(--border-color, #4CAF50);
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
}

/* Input labels */
.farmfaucet-form label,
.form-group label {
  color: var(--input-label-color, #333333);
  font-weight: 500;
  margin-bottom: 5px;
  display: block;
}

/* Input placeholders */
.farmfaucet-input::placeholder,
.farmfaucet-select::placeholder {
  color: var(--input-placeholder-color, #999999);
  opacity: 1;
}

/* Select dropdown styling */
.farmfaucet-select {
  background-color: var(--form-bg-color, #ffffff);
  color: var(--input-label-color, #333333);
  border: 1px solid var(--border-color, #4CAF50);
  border-radius: var(--border-radius, 8px);
  padding: 10px;
  width: 100%;
  font-size: 1rem;
}

.farmfaucet-select:focus {
  outline: none;
  border-color: var(--border-color, #4CAF50);
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
}

/* Dropdown options */
.farmfaucet-select option {
  background-color: var(--form-bg-color, #ffffff);
  color: var(--input-label-color, #333333);
}

.hcaptcha-container {
  margin: 1.5rem 0;
  min-height: 78px;
}

/* Claim button */
.farmfaucet-claim-btn {
  width: 100%;
  padding: 15px;
  background: var(--button-color, #4CAF50);
  border: none;
  border-radius: var(--button-border-radius, 25px);
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.farmfaucet-claim-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Cooldown styling */
.farmfaucet-cooldown {
  text-align: center;
  padding: 2rem;
}

.cooldown-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.cooldown-timer {
  font-size: 1.8rem;
  font-weight: bold;
  color: #9c27b0;
  margin: 1rem 0;
}

/* Notification popup */
.farmfaucet-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  background: #4CAF50;
  color: white;
  display: none;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@media (max-width: 600px) {
  .farmfaucet-container {
      margin: 1rem;
      padding: 1.5rem;
      max-width: 100%;
  }

  .farmfaucet-input {
      padding: 10px 12px;
      font-size: 14px;
  }

  .farmfaucet-claim-btn {
      padding: 12px;
      font-size: 1rem;
  }

  .status-header {
      font-size: 1.3rem;
  }

  .cooldown-timer {
      font-size: 1.5rem;
  }

  .farmfaucet-success-popup {
      width: 90%;
      padding: 15px 20px;
  }

  .popup-icon {
      font-size: 24px;
  }

  .popup-message {
      font-size: 14px;
  }

  .farmfaucet-button-container {
      flex-direction: column;
      align-items: center;
  }

  .farmfaucet-button {
      margin: 5px 0;
      width: 100%;
      max-width: 250px;
  }

  .hcaptcha-container {
      transform: scale(0.9);
      transform-origin: left top;
  }
}

/* Loading spinner */
.farmfaucet-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: farmfaucet-spin 1s ease-in-out infinite;
  vertical-align: middle;
  margin-right: 8px;
}

@keyframes farmfaucet-spin {
  to { transform: rotate(360deg); }
}

/* Loading state for container */
.farmfaucet-container.is-loading {
  position: relative;
}

.farmfaucet-container.is-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 15px;
  z-index: 1;
  pointer-events: none;
}

/* Success popup */
.farmfaucet-success-popup {
  position: fixed;
  top: -100%;
  left: 50%;
  transform: translateX(-50%);
  background: #4CAF50;
  color: white;
  padding: 20px 35px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  z-index: 9999;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.farmfaucet-success-popup.visible {
  top: 20px;
  opacity: 1;
}

.popup-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.popup-icon {
  font-size: 32px;
  animation: popupBounce 0.8s ease;
}

@keyframes popupBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.popup-message {
  font-size: 16px;
  font-weight: 500;
}



/* Amount Input Field */
input[name="farmfaucet_amount"] {
  min-width: 200px;
  width: auto;
  padding-right: 40px;
  transition: width 0.3s ease;
}

/* Cooldown Message */
.cooldown-message {
  text-align: center;
  color: #666;
  padding: 15px;
  background: #fff3cd;
  border-radius: 8px;
  margin: 15px 0;
}

.cooldown-message .cooldown-timer {
  font-size: 1.4em;
  color: #d35400;
  margin-top: 10px;
  font-weight: bold;
}

/* Error Message Styling */
.farmfaucet-notification.error {
  background: #e74c3c;
}

/* Faucet Button Styling */
.farmfaucet-button-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  text-align: center;
}

.farmfaucet-button {
  display: inline-block;
  color: white !important; /* Force white text */
  font-weight: bold;
  text-align: center;
  text-decoration: none !important; /* Remove underlines */
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16);
  border: none !important;
  line-height: 1.5;
  font-size: 14px;
  letter-spacing: 0.5px;
}

.farmfaucet-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0,0,0,0.2);
  color: white !important; /* Force white text on hover */
  filter: brightness(1.05);
}

.farmfaucet-button-disabled {
  cursor: not-allowed;
  opacity: 0.75;
  /* Allow the button to use its own color */
}

.farmfaucet-button-disabled:hover {
  transform: none;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16);
  filter: none;
}

.farmfaucet-cooldown-timer {
  display: block;
  font-size: 0.9em;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Locked button styling */
.farmfaucet-button-locked {
  cursor: not-allowed;
  opacity: 0.85;
  position: relative;
  box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
}

.farmfaucet-button-locked:hover {
  transform: none;
  box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
  filter: none;
}

.farmfaucet-lock-icon {
  display: inline-block;
  margin-right: 5px;
  font-size: 1em;
  animation: lock-bounce 1s ease infinite alternate;
}

@keyframes lock-bounce {
  from { transform: scale(1); }
  to { transform: scale(1.1); }
}

/* Accessibility styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for keyboard navigation */
a:focus, button:focus, input:focus, select:focus, textarea:focus, [tabindex]:focus {
  outline: 3px solid #4CAF50;
  outline-offset: 2px;
}

/* Conversion Faucet Styles */
.conversion-faucet {
  max-width: 600px;
  margin: 0 auto 30px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.conversion-faucet .farmfaucet-header {
  text-align: center;
  margin-bottom: 20px;
}

.conversion-faucet .status-header {
  font-size: 24px;
  margin-bottom: 15px;
  font-weight: 700;
}

.conversion-faucet .balance-notice,
.conversion-faucet .min-conversion-notice {
  font-size: 16px;
  margin-bottom: 10px;
}

.conversion-faucet .balance-notice .balance,
.conversion-faucet .min-conversion-notice .min-amount {
  font-weight: 700;
}

/* Conversion Form */
.conversion-faucet .farmfaucet-form {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
}

.conversion-faucet .form-group {
  margin-bottom: 20px;
}

.conversion-faucet .form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.conversion-faucet .farmfaucet-input,
.conversion-faucet .farmfaucet-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  background-color: #fff;
}

.conversion-faucet .farmfaucet-input:focus,
.conversion-faucet .farmfaucet-select:focus {
  border-color: var(--border-color, #4CAF50);
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Conversion Container */
.conversion-faucet .conversion-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.conversion-faucet .conversion-from,
.conversion-faucet .conversion-to {
  flex: 1;
}

.conversion-faucet .conversion-arrow {
  margin: 0 15px;
  font-size: 24px;
  color: #4CAF50;
}

.conversion-faucet .currency-label {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 600;
  color: #666;
}

.conversion-faucet .target-currency-wrapper {
  position: relative;
}

/* Conversion Result */
.conversion-faucet .conversion-result,
.conversion-faucet .conversion-rate {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

.conversion-faucet .receive-amount,
.conversion-faucet .conversion-rate-display {
  font-size: 18px;
  font-weight: 700;
  color: #4CAF50;
}

.conversion-faucet .currency-type-notice {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4CAF50;
  border-radius: 4px;
}

.conversion-faucet .ads-only-notice {
  font-size: 14px;
  color: #333;
}

/* Submit Button */
.conversion-faucet .farmfaucet-convert-btn {
  display: block;
  width: 100%;
  padding: 14px;
  background-color: var(--button-color, #4CAF50);
  color: white;
  border: none;
  border-radius: var(--border-radius, 6px);
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 20px;
}

.conversion-faucet .farmfaucet-convert-btn:hover {
  background-color: #3e8e41;
}

.conversion-faucet .farmfaucet-convert-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Conversion History */
.farmfaucet-conversion-history {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.farmfaucet-conversion-history h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.conversion-history-container {
  max-height: 300px;
  overflow-y: auto;
}

.farmfaucet-conversion-history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.farmfaucet-conversion-history-table th,
.farmfaucet-conversion-history-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.farmfaucet-conversion-history-table th {
  font-weight: 600;
  background-color: #f5f5f5;
}

.conversion-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-failed {
  background-color: #ffebee;
  color: #c62828;
}

.loading-history {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .conversion-faucet .conversion-container {
    flex-direction: column;
  }

  .conversion-faucet .conversion-arrow {
    transform: rotate(90deg);
    margin: 15px 0;
  }

  .conversion-faucet .conversion-from,
  .conversion-faucet .conversion-to {
    width: 100%;
  }

  .farmfaucet-conversion-history-table {
    font-size: 14px;
  }

  .farmfaucet-conversion-history-table th,
  .farmfaucet-conversion-history-table td {
    padding: 8px 5px;
  }
}