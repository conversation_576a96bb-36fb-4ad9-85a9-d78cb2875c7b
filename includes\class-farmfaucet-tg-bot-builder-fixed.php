<?php

/**
 * Telegram Bot Builder Class
 *
 * @package Farm Faucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Telegram Bot Builder Class
 */
class Farmfaucet_Tg_Bot_Builder
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Register AJAX handlers
        add_action('wp_ajax_farmfaucet_test_bot_token', array($this, 'test_bot_token'));
        add_action('wp_ajax_farmfaucet_save_bot', array($this, 'save_bot'));
        add_action('wp_ajax_farmfaucet_delete_bot', array($this, 'delete_bot'));
        add_action('wp_ajax_farmfaucet_get_bot', array($this, 'get_bot'));
        add_action('wp_ajax_farmfaucet_get_bots', array($this, 'get_bots'));
        add_action('wp_ajax_farmfaucet_save_command', array($this, 'save_command'));
        add_action('wp_ajax_farmfaucet_delete_command', array($this, 'delete_command'));
        add_action('wp_ajax_farmfaucet_get_command', array($this, 'get_command'));
        add_action('wp_ajax_farmfaucet_get_commands', array($this, 'get_commands'));
        add_action('wp_ajax_farmfaucet_save_flow', array($this, 'save_flow'));
        add_action('wp_ajax_farmfaucet_delete_flow', array($this, 'delete_flow'));
        add_action('wp_ajax_farmfaucet_get_flow', array($this, 'get_flow'));
        add_action('wp_ajax_farmfaucet_get_flows', array($this, 'get_flows'));
        add_action('wp_ajax_farmfaucet_save_login_settings', array($this, 'save_login_settings'));
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts($hook)
    {
        // Only load on our settings page
        if ($hook !== 'farmfaucet_page_farmfaucet-tg-bot-builder') {
            return;
        }

        // Enqueue jQuery UI
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-tabs');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');

        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Enqueue jsPlumb for the flow builder
        wp_enqueue_script(
            'jsplumb',
            'https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js',
            ['jquery'],
            '2.15.6',
            true
        );

        // Enqueue SortableJS for drag and drop
        wp_enqueue_script(
            'sortable',
            'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
            [],
            '1.15.0',
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-modern.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Enqueue Bot Builder JS
        wp_enqueue_script(
            'farmfaucet-tg-bot-builder',
            FARMFAUCET_URL . 'assets/js/tg-bot-builder-redesign.js',
            ['jquery', 'jquery-ui-core', 'jquery-ui-dialog', 'jquery-ui-tabs', 'wp-color-picker', 'jsplumb', 'sortable'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Localize script
        wp_localize_script('farmfaucet-tg-bot-builder', 'farmfaucetTgBotBuilder', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_tg_bot_builder_nonce'),
            'i18n' => [
                'confirmDelete' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                'confirmDeleteCommand' => __('Are you sure you want to delete this command? This action cannot be undone.', 'farmfaucet'),
                'confirmDeleteFlow' => __('Are you sure you want to delete this flow? This action cannot be undone.', 'farmfaucet'),
                'tokenValid' => __('Token is valid!', 'farmfaucet'),
                'tokenInvalid' => __('Token is invalid. Please check and try again.', 'farmfaucet'),
                'commandSaved' => __('Command saved successfully.', 'farmfaucet'),
                'commandDeleted' => __('Command deleted successfully.', 'farmfaucet'),
                'flowSaved' => __('Flow saved successfully.', 'farmfaucet'),
                'flowDeleted' => __('Flow deleted successfully.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Test bot token
     */
    public function test_bot_token()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        // Check token
        if (!isset($_POST['token']) || empty($_POST['token'])) {
            wp_send_json_error(__('Token is required.', 'farmfaucet'));
        }

        $token = sanitize_text_field($_POST['token']);

        // Test token by making a request to the Telegram API
        if (!function_exists('wp_remote_get')) {
            require_once(ABSPATH . WPINC . '/http.php');
        }
        $response = wp_remote_get("https://api.telegram.org/bot{$token}/getMe");

        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['ok']) || !$body['ok']) {
            wp_send_json_error(isset($body['description']) ? $body['description'] : __('Invalid token.', 'farmfaucet'));
        }

        wp_send_json_success([
            'message' => __('Token is valid!', 'farmfaucet'),
            'bot_info' => $body['result']
        ]);
    }

    /**
     * Save bot
     */
    public function save_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        // Check required fields
        if (!isset($_POST['bot_name']) || empty($_POST['bot_name'])) {
            wp_send_json_error(__('Bot name is required.', 'farmfaucet'));
        }

        if (!isset($_POST['bot_token']) || empty($_POST['bot_token'])) {
            wp_send_json_error(__('Bot token is required.', 'farmfaucet'));
        }

        if (!isset($_POST['bot_username']) || empty($_POST['bot_username'])) {
            wp_send_json_error(__('Bot username is required.', 'farmfaucet'));
        }

        // Prepare bot data
        $bot_data = [
            'bot_name' => sanitize_text_field($_POST['bot_name']),
            'bot_token' => sanitize_text_field($_POST['bot_token']),
            'bot_username' => sanitize_text_field($_POST['bot_username']),
            'bot_type' => isset($_POST['bot_type']) ? sanitize_text_field($_POST['bot_type']) : 'text',
            'webhook_url' => isset($_POST['webhook_url']) ? esc_url_raw($_POST['webhook_url']) : '',
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
        ];

        // Check if we're updating or creating
        $bot_id = isset($_POST['bot_id']) ? intval($_POST['bot_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($bot_id > 0) {
            // Update existing bot
            $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, $bot_data);
            $message = __('Bot updated successfully.', 'farmfaucet');
        } else {
            // Create new bot
            $result = Farmfaucet_Tg_Bot_DB::add_bot($bot_data);
            $bot_id = $result;
            $message = __('Bot created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(array('message' => __('Error saving bot. Please try again.', 'farmfaucet')));
        }

        wp_send_json_success(array(
            'message' => $message,
            'bot_id' => $bot_id
        ));
    }

    /**
     * Delete bot
     */
    public function delete_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        // Check bot ID
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(__('Bot ID is required.', 'farmfaucet'));
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        $result = Farmfaucet_Tg_Bot_DB::delete_bot($bot_id);

        if (!$result) {
            wp_send_json_error(__('Error deleting bot. Please try again.', 'farmfaucet'));
        }

        wp_send_json_success([
            'message' => __('Bot deleted successfully.', 'farmfaucet')
        ]);
    }

    /**
     * Get bot
     */
    public function get_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        // Check bot ID
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(__('Bot ID is required.', 'farmfaucet'));
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        $bot = Farmfaucet_Tg_Bot_DB::get_bot($bot_id);

        if (!$bot) {
            wp_send_json_error(__('Bot not found.', 'farmfaucet'));
        }

        wp_send_json_success([
            'bot' => $bot
        ]);
    }

    /**
     * Get bots
     */
    public function get_bots()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        $bots = Farmfaucet_Tg_Bot_DB::get_bots();

        wp_send_json_success([
            'bots' => $bots
        ]);
    }

    /**
     * Save login form settings
     */
    public function save_login_settings()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_builder_nonce')) {
            wp_send_json_error(__('Invalid nonce.', 'farmfaucet'));
        }

        // Sanitize and save settings
        $login_page_url = isset($_POST['login_page_url']) ? esc_url_raw($_POST['login_page_url']) : '';
        $register_page_url = isset($_POST['register_page_url']) ? esc_url_raw($_POST['register_page_url']) : '';
        $forgot_password_url = isset($_POST['forgot_password_url']) ? esc_url_raw($_POST['forgot_password_url']) : '';
        $profile_page_url = isset($_POST['profile_page_url']) ? esc_url_raw($_POST['profile_page_url']) : '';
        $enable_tg_login = isset($_POST['enable_tg_login']) ? (int)$_POST['enable_tg_login'] : 0;
        $tg_login_bot = isset($_POST['tg_login_bot']) ? (int)$_POST['tg_login_bot'] : 0;

        // Get OTP expiration
        $tg_otp_expiration = isset($_POST['tg_otp_expiration']) ? intval($_POST['tg_otp_expiration']) : 5;

        // Validate OTP expiration (1-60 minutes)
        $tg_otp_expiration = max(1, min(60, $tg_otp_expiration));

        // Update options
        update_option('farmfaucet_login_page_url', $login_page_url);
        update_option('farmfaucet_register_page_url', $register_page_url);
        update_option('farmfaucet_forgot_password_url', $forgot_password_url);
        update_option('farmfaucet_profile_page_url', $profile_page_url);
        update_option('farmfaucet_enable_tg_login', $enable_tg_login);
        update_option('farmfaucet_tg_login_bot', $tg_login_bot);
        update_option('farmfaucet_tg_otp_expiration', $tg_otp_expiration);

        wp_send_json_success([
            'message' => __('Login form settings saved successfully.', 'farmfaucet')
        ]);
    }

    /**
     * Render admin settings page
     */
    public static function render_admin_page()
    {
        // Include the modern bot builder template
        include(FARMFAUCET_DIR . 'templates/admin/bot-builder-modern.php');
    }
}
