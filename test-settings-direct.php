<?php
/**
 * Direct Settings Test - Bypasses WordPress to test settings save
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Direct Settings Test</title>";
echo "<style>body{font-family:Arial;margin:40px;} .success{color:#4CAF50;background:#f0f8f0;padding:15px;margin:15px 0;border-left:4px solid #4CAF50;} .error{color:#f44336;background:#fdf0f0;padding:15px;margin:15px 0;border-left:4px solid #f44336;} .info{color:#2196F3;background:#f0f7ff;padding:15px;margin:15px 0;border-left:4px solid #2196F3;} .btn{background:#4CAF50;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;margin:10px 5px 10px 0;border:none;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔧 Direct Settings Test</h1>";

// Test if we can simulate the settings save without WordPress
if (isset($_POST['test_save'])) {
    echo "<div class='info'><h3>🧪 Testing Settings Save...</h3></div>";
    
    // Simulate the ultra_safe_settings_save method
    $post_data = $_POST;
    $saved_count = 0;
    $total_count = 0;
    $errors = [];
    
    // Define settings with their sanitization
    $settings_map = [
        'farmfaucet_captcha_type' => 'text',
        'farmfaucet_hcaptcha_sitekey' => 'text',
        'farmfaucet_hcaptcha_secret' => 'text',
        'farmfaucet_recaptcha_sitekey' => 'text',
        'farmfaucet_recaptcha_secret' => 'text',
        'farmfaucet_turnstile_sitekey' => 'text',
        'farmfaucet_turnstile_secret' => 'text',
        'farmfaucet_faucetpay_api' => 'text',
        'farmfaucet_redirect_url' => 'url',
        'farmfaucet_daily_reset' => 'text',
        'farmfaucet_leaderboard_reset_date' => 'text'
    ];
    
    foreach ($settings_map as $setting => $type) {
        if (isset($post_data[$setting])) {
            $total_count++;
            $value = $post_data[$setting];
            
            try {
                // Simple sanitization
                if ($type === 'url') {
                    $value = filter_var($value, FILTER_SANITIZE_URL);
                } else {
                    $value = strip_tags(trim($value));
                }
                
                // Simulate saving (just echo what would be saved)
                echo "<div class='success'>✅ Would save {$setting} = " . htmlspecialchars($value) . "</div>";
                $saved_count++;
                
            } catch (Throwable $e) {
                $errors[] = $setting . ' (error: ' . $e->getMessage() . ')';
                echo "<div class='error'>❌ Error with {$setting}: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    
    // Show result
    if ($saved_count === $total_count && $total_count > 0) {
        echo "<div class='success'><h4>🎉 SUCCESS: All {$saved_count} settings would save correctly!</h4></div>";
    } else {
        $error_details = !empty($errors) ? ' Failed: ' . implode(', ', $errors) : '';
        echo "<div class='error'><h4>❌ PARTIAL: Saved {$saved_count} of {$total_count} settings.{$error_details}</h4></div>";
    }
} else {
    echo "<div class='info'><h3>🔧 Test Settings Form</h3></div>";
    
    echo '<form method="post">';
    echo '<table style="width: 100%; border-collapse: collapse;">';
    echo '<tr><th style="padding: 10px; border: 1px solid #ddd; background: #f5f5f5;">Setting</th><th style="padding: 10px; border: 1px solid #ddd; background: #f5f5f5;">Value</th></tr>';
    
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">Captcha Type</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    echo '<select name="farmfaucet_captcha_type">';
    echo '<option value="hcaptcha">hCaptcha</option>';
    echo '<option value="recaptcha">reCAPTCHA</option>';
    echo '<option value="turnstile">Turnstile</option>';
    echo '</select>';
    echo '</td>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">hCaptcha Site Key</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    echo '<input type="text" name="farmfaucet_hcaptcha_sitekey" value="test-site-key" style="width: 300px; padding: 5px;">';
    echo '</td>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">hCaptcha Secret</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    echo '<input type="password" name="farmfaucet_hcaptcha_secret" value="test-secret-key" style="width: 300px; padding: 5px;">';
    echo '</td>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">FaucetPay API</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    echo '<input type="password" name="farmfaucet_faucetpay_api" value="fp_test_api_key" style="width: 300px; padding: 5px;">';
    echo '</td>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">Redirect URL</td>';
    echo '<td style="padding: 10px; border: 1px solid #ddd;">';
    echo '<input type="url" name="farmfaucet_redirect_url" value="https://example.com/thank-you" style="width: 300px; padding: 5px;">';
    echo '</td>';
    echo '</tr>';
    
    echo '</table>';
    echo '<p><input type="submit" name="test_save" value="Test Settings Save" class="btn"></p>';
    echo '</form>';
}

echo "<div style='background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #4CAF50;'>";
echo "<h3 style='color: #2e7d32; margin-top: 0;'>🎯 What This Test Does</h3>";
echo "<div style='color: #2e7d32;'>";
echo "<ul>";
echo "<li><strong>Bypasses WordPress:</strong> Tests the settings save logic without WordPress functions</li>";
echo "<li><strong>Simulates Form Submission:</strong> Uses the same logic as the admin panel</li>";
echo "<li><strong>Shows Sanitization:</strong> Demonstrates how values are cleaned before saving</li>";
echo "<li><strong>Error Detection:</strong> Catches any errors in the save process</li>";
echo "<li><strong>Safe Testing:</strong> Won't cause critical errors or break the site</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='test-settings-save.php' class='btn' style='background: #2196F3;'>🔧 Full WordPress Test</a>";
echo "</div>";

echo "</body></html>";
?>
