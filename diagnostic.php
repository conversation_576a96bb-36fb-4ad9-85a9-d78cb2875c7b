  <?php
// Simple diagnostic file to identify where the error is occurring
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

echo "Starting diagnostic...\n";

// Define a simple function to log steps
function log_step($step) {
    echo "$step\n";
    error_log($step);
}

log_step("Step 1: Basic PHP is working");

// Check if we can access the file system
log_step("Step 2: Checking file system access");
$files = scandir('.');
log_step("Found " . count($files) . " files in current directory");

// Check if we can include files
log_step("Step 3: Checking if we can include files");
if (file_exists('includes/class-farmfaucet-security.php')) {
    log_step("Security class file exists");
} else {
    log_step("Security class file does not exist");
}

// Try to include the security class
log_step("Step 4: Trying to include security class");
try {
    include_once 'includes/class-farmfaucet-security.php';
    log_step("Security class included successfully");
} catch (Exception $e) {
    log_step("Error including security class: " . $e->getMessage());
}

// Check if the class exists
log_step("Step 5: Checking if security class exists");
if (class_exists('Farmfaucet_Security')) {
    log_step("Security class exists");
} else {
    log_step("Security class does not exist");
}

// Try to include the installer class
log_step("Step 6: Trying to include installer class");
try {
    include_once 'includes/class-farmfaucet-installer.php';
    log_step("Installer class included successfully");
} catch (Exception $e) {
    log_step("Error including installer class: " . $e->getMessage());
}

// Check if the class exists
log_step("Step 7: Checking if installer class exists");
if (class_exists('Farmfaucet_Installer')) {
    log_step("Installer class exists");
} else {
    log_step("Installer class does not exist");
}

// Try to include the main plugin file
log_step("Step 8: Trying to include main plugin file");
try {
    include_once 'farmfaucet.php';
    log_step("Main plugin file included successfully");
} catch (Exception $e) {
    log_step("Error including main plugin file: " . $e->getMessage());
}

log_step("Diagnostic completed");
?>
