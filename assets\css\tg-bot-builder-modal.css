/**
 * <PERSON><PERSON><PERSON><PERSON>uilder Modal Styles
 */

/* Modal Styles */
.farmfaucet-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    overflow-y: auto;
}

.farmfaucet-modal-content {
    background-color: #fff;
    margin: 50px auto;
    padding: 0;
    width: 90%;
    max-width: 800px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.farmfaucet-modal-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 20px 25px;
    border-radius: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.farmfaucet-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.farmfaucet-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    transition: all 0.3s ease;
}

.farmfaucet-modal-close:hover {
    transform: scale(1.2);
    opacity: 0.8;
}

.farmfaucet-modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.farmfaucet-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background-color: #f9f9f9;
}

.farmfaucet-modal-footer .button {
    padding: 10px 20px;
    height: auto;
    font-size: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.farmfaucet-modal-footer .button-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-color: #2E7D32;
    box-shadow: 0 2px 5px rgba(46, 125, 50, 0.2);
}

.farmfaucet-modal-footer .button-primary:hover {
    background: linear-gradient(135deg, #43A047, #2E7D32);
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

/* Bot Card Styles */
.bot-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.bot-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
}

.bot-card-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bot-card-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.bot-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.bot-status.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.bot-status.inactive {
    background-color: rgba(0, 0, 0, 0.2);
}

.bot-card-body {
    padding: 15px 20px;
}

.bot-username {
    font-size: 16px;
    color: #333;
    margin: 0 0 10px 0;
}

.bot-type {
    font-size: 14px;
    color: #666;
    margin: 0;
    background-color: #f5f5f5;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
}

.bot-card-actions {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
}

.bot-card-actions .button {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bot-card-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.bot-card-actions .edit-bot {
    background-color: #2196F3;
    color: white;
    border: none;
}

.bot-card-actions .manage-commands {
    background-color: #4CAF50;
    color: white;
    border: none;
}

.bot-card-actions .delete-bot {
    background-color: #f44336;
    color: white;
    border: none;
}

/* Command Card Styles */
.command-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.command-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
}

.command-card-header {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    padding: 15px 20px;
}

.command-card-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.command-card-body {
    padding: 15px 20px;
}

.command-description {
    font-size: 15px;
    color: #333;
    margin: 0 0 10px 0;
}

.command-type {
    font-size: 14px;
    color: #666;
    margin: 0;
    background-color: #f5f5f5;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
}

.command-card-actions {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
}

.command-card-actions .button {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.command-card-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.command-card-actions .edit-command {
    background-color: #2196F3;
    color: white;
    border: none;
}

.command-card-actions .delete-command {
    background-color: #f44336;
    color: white;
    border: none;
}

/* Button Item Styles */
.button-item {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    position: relative;
}

.button-items {
    margin-bottom: 15px;
}

.add-button {
    margin-bottom: 20px;
}

.remove-button {
    position: absolute;
    top: 15px;
    right: 15px;
}

/* Responsive Styles */
@media (max-width: 782px) {
    .farmfaucet-modal-content {
        width: 95%;
        margin: 30px auto;
    }
    
    .farmfaucet-modal-header {
        padding: 15px 20px;
    }
    
    .farmfaucet-modal-body {
        padding: 20px;
    }
    
    .farmfaucet-modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }
    
    .farmfaucet-modal-footer .button {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .bot-card-actions,
    .command-card-actions {
        flex-direction: column;
    }
    
    .bot-card-actions .button,
    .command-card-actions .button {
        width: 100%;
        margin-bottom: 10px;
    }
}
