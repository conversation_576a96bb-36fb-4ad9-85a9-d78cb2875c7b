<?php

/**
 * Compatibility class for the Farmfaucet plugin
 *
 * Handles compatibility checks and fixes for potential conflicts with other plugins
 */
class Farmfaucet_Compatibility
{
    /**
     * Check for potential conflicts with other plugins
     *
     * @return array Array of potential conflicts
     */
    public static function check_for_conflicts()
    {
        $conflicts = [];

        // Check for known conflicting plugins
        $conflicting_plugins = [
            'another-faucet-plugin/another-faucet-plugin.php' => 'Another Faucet Plugin',
            'crypto-faucet/crypto-faucet.php' => 'Crypto Faucet',
        ];

        foreach ($conflicting_plugins as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $conflicts[] = $plugin_name;
            }
        }

        return $conflicts;
    }

    /**
     * Fix potential conflicts with other plugins
     *
     * @return void
     */
    public static function fix_conflicts()
    {
        try {
            // Remove conflicting hooks from other plugins
            self::remove_conflicting_hooks();

            // Fix potential jQuery conflicts
            self::fix_jquery_conflicts();

            // Fix potential database conflicts
            self::fix_database_conflicts();
        } catch (Exception $e) {
            // Log the error but don't break the site
            error_log('Farmfaucet conflict fix error: ' . $e->getMessage());
        }
    }

    /**
     * Fix potential database conflicts
     *
     * @return void
     */
    private static function fix_database_conflicts()
    {
        global $wpdb;

        // Check if tables exist and create them if they don't
        $tables = [
            $wpdb->prefix . 'farmfaucet_logs',
            $wpdb->prefix . 'farmfaucet_faucets',
            $wpdb->prefix . 'farmfaucet_buttons'
        ];

        $missing_tables = false;

        foreach ($tables as $table) {
            $table_exists = false;

            // Use try-catch to handle potential database errors
            try {
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
            } catch (Exception $e) {
                error_log('Farmfaucet database check error: ' . $e->getMessage());
            }

            if (!$table_exists) {
                $missing_tables = true;
                break;
            }
        }

        // If any tables are missing, trigger activation to create them
        if ($missing_tables) {
            try {
                Farmfaucet_Security::activate_plugin();
            } catch (Exception $e) {
                error_log('Farmfaucet table creation error: ' . $e->getMessage());
            }
        }
    }

    /**
     * Remove conflicting hooks from other plugins
     *
     * @return void
     */
    private static function remove_conflicting_hooks()
    {
        // Remove conflicting AJAX actions
        remove_all_actions('wp_ajax_process_crypto_claim');
        remove_all_actions('wp_ajax_nopriv_process_crypto_claim');
    }

    /**
     * Fix potential jQuery conflicts
     *
     * @return void
     */
    private static function fix_jquery_conflicts()
    {
        // Ensure jQuery is loaded in no-conflict mode
        add_action('wp_enqueue_scripts', function () {
            wp_enqueue_script('farmfaucet-jquery-fix', FARMFAUCET_URL . 'assets/js/jquery-fix.js', ['jquery'], FARMFAUCET_VERSION, true);
        });
    }

    /**
     * Check if the current WordPress version is compatible
     *
     * @return bool True if compatible, false otherwise
     */
    public static function is_wp_compatible()
    {
        global $wp_version;

        // Require WordPress 5.0 or higher
        return version_compare($wp_version, '5.0', '>=');
    }

    /**
     * Check if the current PHP version is compatible
     *
     * @return bool True if compatible, false otherwise
     */
    public static function is_php_compatible()
    {
        // Require PHP 7.0 or higher
        return version_compare(PHP_VERSION, '7.0', '>=');
    }

    /**
     * Display admin notices for compatibility issues
     *
     * @return void
     */
    public static function display_compatibility_notices()
    {
        // Check WordPress version
        if (!self::is_wp_compatible()) {
            add_action('admin_notices', function () {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('Farmfaucet requires WordPress version %s or higher. Please upgrade WordPress to use this plugin.', 'farmfaucet'),
                    '5.0'
                );
                echo '</p></div>';
            });
        }

        // Check PHP version
        if (!self::is_php_compatible()) {
            add_action('admin_notices', function () {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('Farmfaucet requires PHP version %s or higher. Please upgrade PHP to use this plugin.', 'farmfaucet'),
                    '7.0'
                );
                echo '</p></div>';
            });
        }

        // Check for conflicts
        $conflicts = self::check_for_conflicts();
        if (!empty($conflicts)) {
            add_action('admin_notices', function () use ($conflicts) {
                echo '<div class="notice notice-warning"><p>';
                echo __('Farmfaucet may conflict with the following plugins:', 'farmfaucet') . ' ';
                echo implode(', ', $conflicts);
                echo '</p></div>';
            });
        }
    }
}
