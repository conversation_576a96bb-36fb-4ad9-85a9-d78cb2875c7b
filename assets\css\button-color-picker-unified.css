/**
 * Farm Faucet - Unified Button Color Picker CSS
 *
 * This stylesheet provides a clean implementation of the button color picker
 * that works consistently across the admin panel.
 */

/* Color grid container */
.color-grid-container {
    position: relative;
    margin-bottom: 15px;
}

/* Color select wrapper */
.color-select-wrapper {
    display: flex;
    align-items: center;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
}

/* Color name display */
.color-name-display {
    flex: 1;
    font-weight: 500;
    min-width: 80px;
}

/* Color preview */
.color-preview {
    width: 30px;
    height: 30px;
    min-width: 30px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-left: 10px;
}

/* Color grid */
.color-grid {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    width: 320px;
}

.color-grid.active {
    display: grid !important;
}

/* Color swatch options */
.color-swatch-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    margin: 0 auto;
}

.color-swatch-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.color-swatch-option.selected {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #4CAF50;
}

/* Custom color section */
.custom-color-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

#button-custom-color {
    width: 100%;
    height: 30px;
    margin-top: 5px;
    cursor: pointer;
    padding: 0;
    border: 1px solid #ddd;
}

/* Fix for dialog positioning */
.ui-dialog {
    z-index: 100000 !important;
}
