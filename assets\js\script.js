// Use the global jQuery object or fallback to the standard jQuery
var $ = window.farmfaucetJQuery || jQuery;

// Make sure we have a valid jQuery instance
if (typeof $ !== 'function') {
    console.error('Farmfaucet: jQuery not found!');
    return;
}

// Debug mode for additional logging
const DEBUG = window.farmfaucet_vars && window.farmfaucet_vars.debug_mode;

$(document).ready(function() {
    // Select necessary DOM elements
    const form = $('#farmfaucet-claim-form');
    const claimBtn = $('.farmfaucet-claim-btn');
    const notification = $('.farmfaucet-notification');
    const preCaptchaStatus = $('.pre-captcha-status');
    const postCaptchaStatus = $('.post-captcha-status');

    // Variable to store the captcha type
    let captchaType = farmfaucet_vars.captcha_type || 'hcaptcha';

    console.log('Farm Faucet initialized with captcha type:', captchaType);

    // Handle claim form submission for all forms
    $(document).on('submit', '#farmfaucet-claim-form', function(e) {
        e.preventDefault(); // Prevent default form submission

        // Get the specific form and button for this submission
        const thisForm = $(this);
        const thisClaimBtn = thisForm.find('.farmfaucet-claim-btn');

        if (thisClaimBtn.prop('disabled')) return; // Stop if button is disabled

        // Get user input from this specific form
        const userEmail = thisForm.find('input[name="user_email"]').val().trim();
        const faucetId = thisForm.find('input[name="faucet_id"]').val(); // Get faucet ID from hidden field
        const captchaId = thisForm.find('input[name="captcha_id"]').val(); // Get captcha ID from hidden field

        // Get captcha type from the form or fallback to global setting
        const captchaTypeInput = thisForm.find('input[name="captcha_type"]');
        console.log('Captcha type input found:', captchaTypeInput.length, 'elements');

        const formCaptchaType = captchaTypeInput.val() || captchaType;
        console.log('Form captcha type value:', formCaptchaType);

        console.log('Form submission for faucet:', faucetId, 'captcha:', captchaId, 'type:', formCaptchaType);

        // Get the appropriate captcha response based on type and ID
        let captchaResponse = '';
        console.log('Attempting to get captcha response for type:', formCaptchaType, 'and ID:', captchaId);
        console.log('Available captcha widgets:', window.farmfaucetCaptchaWidgets);

        if (formCaptchaType === 'hcaptcha' && captchaId) {
            console.log('Getting hCaptcha response, hcaptcha object available:', typeof hcaptcha !== 'undefined');
            try {
                // Try to get the response from the widget ID first (most reliable)
                const widgetId = window.farmfaucetCaptchaWidgets[captchaId];
                console.log('Widget ID for', captchaId, 'is', widgetId);

                if (widgetId !== undefined) {
                    captchaResponse = hcaptcha.getResponse(widgetId);
                    console.log('Got hCaptcha response for widget', widgetId, 'length:', captchaResponse.length);
                }

                // If that fails, try to get the response directly
                if (!captchaResponse && typeof hcaptcha !== 'undefined') {
                    captchaResponse = hcaptcha.getResponse();
                    if (DEBUG) console.log('Got hCaptcha response directly, length:', captchaResponse.length);
                }

                // If still no response, check if there's a hidden input with the response
                if (!captchaResponse) {
                    const hiddenResponse = thisForm.find('textarea[name="h-captcha-response"]').val();
                    if (hiddenResponse) {
                        captchaResponse = hiddenResponse;
                        if (DEBUG) console.log('Got hCaptcha response from hidden field, length:', captchaResponse.length);
                    }
                }

                if (!captchaResponse) {
                    console.error('Could not get hCaptcha response for', captchaId);
                }
            } catch (e) {
                console.error('Error getting hCaptcha response:', e);
            }
        } else if (formCaptchaType === 'recaptcha' && captchaId) {
            console.log('Getting reCAPTCHA response, grecaptcha object available:', typeof grecaptcha !== 'undefined');
            try {
                // Try to get the response from the widget ID first (most reliable)
                const widgetId = window.farmfaucetCaptchaWidgets[captchaId];
                console.log('Widget ID for', captchaId, 'is', widgetId);

                if (widgetId !== undefined) {
                    captchaResponse = grecaptcha.getResponse(widgetId);
                    console.log('Got reCAPTCHA response for widget', widgetId, 'length:', captchaResponse.length);
                }

                // If that fails, try to get the response directly
                if (!captchaResponse && typeof grecaptcha !== 'undefined') {
                    captchaResponse = grecaptcha.getResponse();
                    if (DEBUG) console.log('Got reCAPTCHA response directly, length:', captchaResponse.length);
                }

                // If still no response, check if there's a hidden input with the response
                if (!captchaResponse) {
                    const hiddenResponse = thisForm.find('textarea[name="g-recaptcha-response"]').val();
                    if (hiddenResponse) {
                        captchaResponse = hiddenResponse;
                        if (DEBUG) console.log('Got reCAPTCHA response from hidden field, length:', captchaResponse.length);
                    }
                }

                if (!captchaResponse) {
                    console.error('Could not get reCAPTCHA response for', captchaId);
                }
            } catch (e) {
                console.error('Error getting reCAPTCHA response:', e);
            }
        } else if (formCaptchaType === 'turnstile' && captchaId) {
            console.log('Getting Turnstile response, turnstile object available:', typeof turnstile !== 'undefined');
            try {
                // Try to get the response from the widget ID first (most reliable)
                const widgetId = window.farmfaucetCaptchaWidgets[captchaId];
                console.log('Widget ID for', captchaId, 'is', widgetId);

                if (widgetId !== undefined && typeof turnstile !== 'undefined') {
                    captchaResponse = turnstile.getResponse(widgetId);
                    console.log('Got Turnstile response for widget', widgetId, 'length:', captchaResponse.length);
                }

                // If that fails, try to get the response directly
                if (!captchaResponse && typeof turnstile !== 'undefined') {
                    captchaResponse = turnstile.getResponse();
                    if (DEBUG) console.log('Got Turnstile response directly, length:', captchaResponse.length);
                }

                // If still no response, check if there's a hidden input with the response
                if (!captchaResponse) {
                    const hiddenResponse = thisForm.find('textarea[name="cf-turnstile-response"]').val();
                    if (hiddenResponse) {
                        captchaResponse = hiddenResponse;
                        if (DEBUG) console.log('Got Turnstile response from hidden field, length:', captchaResponse.length);
                    }
                }

                if (!captchaResponse) {
                    console.error('Could not get Turnstile response for', captchaId);
                }
            } catch (e) {
                console.error('Error getting Turnstile response:', e);
            }
        }

        // Validate email format
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userEmail)) {
            showNotification(farmfaucet_vars.translations.invalid_email, 'error');
            return;
        }

        // Validate captcha
        if (!captchaResponse) {
            showNotification(farmfaucet_vars.translations.complete_captcha, 'error');
            return;
        }

        // Prepare data for AJAX request
        const formData = {
            action: 'process_claim',
            nonce: farmfaucet_vars.nonce,
            user_email: userEmail,
            faucet_id: faucetId,
            captcha_type: formCaptchaType
        };

        // Add the appropriate captcha response based on type
        if (formCaptchaType === 'hcaptcha') {
            formData['h-captcha-response'] = captchaResponse;
        } else if (formCaptchaType === 'recaptcha') {
            formData['g-recaptcha-response'] = captchaResponse;
        } else if (formCaptchaType === 'turnstile') {
            formData['cf-turnstile-response'] = captchaResponse;
        }

        // Get the notification element for this form
        const thisNotification = thisForm.closest('.farmfaucet-container').find('.farmfaucet-notification');

        // Function to show notification for this specific form using new notification system
        function showFormNotification(message, type = 'success', duration = 3000) {
            if (window.FarmfaucetNotifications) {
                // Use new notification system
                if (type === 'success') {
                    window.FarmfaucetNotifications.faucet.claimSuccess('', message);
                } else if (type === 'error') {
                    window.FarmfaucetNotifications.faucet.claimError(message);
                } else {
                    window.FarmfaucetNotifications.show(message, type, duration);
                }
            } else {
                // Fallback to old notification system
                thisNotification.removeClass('error success')
                    .addClass(type)
                    .text(message)
                    .attr('aria-hidden', 'false')
                    .fadeIn()
                    .delay(duration)
                    .fadeOut(function() {
                        $(this).attr('aria-hidden', 'true');
                    });
            }
        }

        // Function to handle errors for this specific form
        function handleFormError(message) {
            showFormNotification(message, 'error');
            thisForm.find('.pre-captcha-status').show();
            thisForm.find('.post-captcha-status').hide();
            thisClaimBtn.html(farmfaucet_vars.translations.claim_button)
                .prop('disabled', false)
                .attr('aria-disabled', 'false');

            try {
                // Reset the specific captcha widget
                if (captchaId && window.farmfaucetCaptchaWidgets[captchaId] !== undefined) {
                    if (formCaptchaType === 'hcaptcha') {
                        hcaptcha.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    } else if (formCaptchaType === 'recaptcha') {
                        grecaptcha.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    } else if (formCaptchaType === 'turnstile') {
                        turnstile.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    }
                }
            } catch (e) {
                console.error('Error resetting captcha during form error:', e);
            }
        }

        // Send AJAX request to process claim
        $.ajax({
            url: farmfaucet_vars.ajaxurl,
            type: 'POST',
            data: formData,
            beforeSend: function() {
                // Show loading notification
                if (window.FarmfaucetNotifications) {
                    window.FarmfaucetNotifications.faucet.claimStarted();
                }

                // Show loading spinner and disable button while processing
                thisClaimBtn.prop('disabled', true)
                       .attr('aria-disabled', 'true')
                       .html(`<div class="farmfaucet-spinner" aria-hidden="true"></div> <span>${farmfaucet_vars.translations.processing}</span>`);

                // Update form state for screen readers
                thisForm.attr('aria-busy', 'true');

                // Add loading state to container
                thisForm.closest('.farmfaucet-container').addClass('is-loading');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message before reload
                    showFormNotification(response.data || 'Claim successful!', 'success');

                    // Check if there's a redirect URL in the settings
                    const redirectUrl = farmfaucet_vars.redirect_url || '';

                    // Delay redirect/reload slightly to show the message
                    setTimeout(function() {
                        if (redirectUrl && redirectUrl.trim() !== '') {
                            console.log('Redirecting to:', redirectUrl);
                            window.location.href = redirectUrl;
                        } else {
                            console.log('No redirect URL configured, reloading page');
                            window.location.reload();
                        }
                    }, 1500);
                } else {
                    // Handle claim failure
                    handleFormError(response.data || farmfaucet_vars.translations.claim_failed);
                }
            },
            error: function(xhr) {
                // Handle connection errors
                handleFormError(xhr.responseJSON?.data || farmfaucet_vars.translations.connection_error);
            },
            complete: function() {
                // Reset form state for screen readers
                thisForm.attr('aria-busy', 'false');

                // Remove loading state from container
                thisForm.closest('.farmfaucet-container').removeClass('is-loading');
            }
        });
    });

    // Handle errors and reset captcha if needed
    function handleError(message) {
        showNotification(message, 'error');
        preCaptchaStatus.show();
        postCaptchaStatus.hide();
        claimBtn.html(farmfaucet_vars.translations.claim_button)
               .prop('disabled', false)
               .attr('aria-disabled', 'false');

        // Reset all captchas on the page
        try {
            // Get captcha ID from the form
            const captchaId = $('input[name="captcha_id"]').val();
            const formCaptchaType = $('input[name="captcha_type"]').val() || captchaType;

            if (captchaId && window.farmfaucetCaptchaWidgets[captchaId] !== undefined) {
                // Reset the specific captcha widget
                if (formCaptchaType === 'hcaptcha') {
                    hcaptcha.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    console.log('Reset hCaptcha for', captchaId);
                } else if (formCaptchaType === 'recaptcha') {
                    grecaptcha.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    console.log('Reset reCAPTCHA for', captchaId);
                } else if (formCaptchaType === 'turnstile') {
                    turnstile.reset(window.farmfaucetCaptchaWidgets[captchaId]);
                    console.log('Reset Turnstile for', captchaId);
                }
            } else {
                // Fallback: reset all captchas of the current type
                if (formCaptchaType === 'hcaptcha') {
                    Object.values(window.farmfaucetCaptchaWidgets).forEach(widgetId => {
                        try {
                            hcaptcha.reset(widgetId);
                            console.log('Reset hCaptcha widget in fallback');
                        } catch (e) {
                            console.error('Error resetting hCaptcha widget:', e);
                        }
                    });
                } else if (formCaptchaType === 'recaptcha') {
                    Object.values(window.farmfaucetCaptchaWidgets).forEach(widgetId => {
                        try {
                            grecaptcha.reset(widgetId);
                            console.log('Reset reCAPTCHA widget in fallback');
                        } catch (e) {
                            console.error('Error resetting reCAPTCHA widget:', e);
                        }
                    });
                } else if (formCaptchaType === 'turnstile') {
                    Object.values(window.farmfaucetCaptchaWidgets).forEach(widgetId => {
                        try {
                            turnstile.reset(widgetId);
                            console.log('Reset Turnstile widget in fallback');
                        } catch (e) {
                            console.error('Error resetting Turnstile widget:', e);
                        }
                    });
                }
            }
        } catch (e) {
            console.error('Error resetting captcha:', e);
        }
    }

    // Display notification messages with improved accessibility
    function showNotification(message, type = 'success', duration = 3000) {
        if (window.FarmfaucetNotifications) {
            // Use new notification system
            window.FarmfaucetNotifications.show(message, type, duration);
        } else {
            // Fallback to old notification system
            notification.removeClass('error success')
                       .addClass(type)
                       .text(message)
                       .attr('aria-hidden', 'false')
                       .fadeIn()
                       .delay(duration)
                       .fadeOut(function() {
                           $(this).attr('aria-hidden', 'true');
                       });
        }
    }

    // Function to update the cooldown timer
    function updateCooldownTimer() {
        $('.cooldown-timer').each(function() {
            try {
                const endTime = parseInt($(this).data('end'));
                if (isNaN(endTime)) {
                    console.error('Invalid end time for cooldown timer');
                    return;
                }

                const now = Math.floor(Date.now() / 1000);
                const diff = endTime - now;
                const faucetId = $(this).closest('.farmfaucet-container').data('faucet-id');

                if (diff <= 0) {
                    console.log('Cooldown ended for faucet:', faucetId);
                    location.reload(); // Reload page when cooldown ends
                    return;
                }

                // Format time as HH:MM:SS
                const hours = String(Math.floor(diff / 3600)).padStart(2, '0');
                const minutes = String(Math.floor((diff % 3600) / 60)).padStart(2, '0');
                const seconds = String(diff % 60).padStart(2, '0');

                $(this).text(`${hours}:${minutes}:${seconds}`);

                // Update the claim button to show it's disabled during cooldown
                const claimBtn = $(this).closest('.farmfaucet-container').find('.farmfaucet-claim-btn');
                if (claimBtn.length) {
                    claimBtn.prop('disabled', true);
                    claimBtn.attr('aria-disabled', 'true');
                }

                console.log('Updated cooldown timer for faucet:', faucetId, 'time remaining:', `${hours}:${minutes}:${seconds}`);
            } catch (e) {
                console.error('Error updating cooldown timer:', e);
            }
        });
    }

    // Make updateCooldownTimer available globally
    window.updateCooldownTimer = updateCooldownTimer;

    // Initialize cooldown timer if it exists on the page
    if ($('.cooldown-timer').length) {
        console.log('Initializing cooldown timers, found:', $('.cooldown-timer').length);
        const timerInterval = setInterval(updateCooldownTimer, 1000);
        updateCooldownTimer();

        // Store the interval ID globally so it can be cleared if needed
        window.farmfaucetTimerInterval = timerInterval;
    }

    // Function to update button cooldown timers
    function updateButtonTimers() {
        $('.farmfaucet-cooldown-timer').each(function() {
            try {
                let seconds = parseInt($(this).data('seconds'));
                if (isNaN(seconds)) {
                    console.error('Invalid seconds value for button timer');
                    return;
                }

                const buttonId = $(this).closest('.farmfaucet-button-container').data('button-id');
                const faucetId = $(this).closest('.farmfaucet-button-container').data('faucet-id');

                if (seconds <= 0) {
                    console.log('Button cooldown ended for button:', buttonId, 'faucet:', faucetId);
                    location.reload(); // Reload page when cooldown ends
                    return;
                }

                // Decrement seconds
                seconds = seconds - 1;
                $(this).data('seconds', seconds);

                // Format time as HH:MM:SS or MM:SS
                const hours = Math.floor(seconds / 3600);
                const minutes = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
                const secs = String(seconds % 60).padStart(2, '0');

                if (hours > 0) {
                    $(this).text(`${String(hours).padStart(2, '0')}:${minutes}:${secs}`);
                } else {
                    $(this).text(`${minutes}:${secs}`);
                }

                console.log('Updated button timer for button:', buttonId, 'faucet:', faucetId, 'time remaining:', seconds);
            } catch (e) {
                console.error('Error updating button timer:', e);
            }
        });
    }

    // Make updateButtonTimers available globally
    window.updateButtonTimers = updateButtonTimers;

    // Initialize button cooldown timers if they exist on the page
    if ($('.farmfaucet-cooldown-timer').length) {
        console.log('Initializing button timers, found:', $('.farmfaucet-cooldown-timer').length);
        const buttonTimerInterval = setInterval(updateButtonTimers, 1000);
        updateButtonTimers();

        // Store the interval ID globally so it can be cleared if needed
        window.farmfaucetButtonTimerInterval = buttonTimerInterval;
    }

    // Function to animate milestone progress bars on page load
    function animateMilestoneProgressBars() {
        $('.farmfaucet-milestone-progress-bar, .farmfaucet-progress-bar').each(function() {
            const $progressBar = $(this);
            const targetProgress = parseInt($progressBar.data('progress')) || 0;
            const targetWidth = targetProgress + '%';
            const $container = $progressBar.closest('.farmfaucet-milestone-container, .farmfaucet-completion-container');
            const $percentText = $container.find('.farmfaucet-milestone-percentage, .farmfaucet-completion-percentage');
            const $progressText = $container.find('.farmfaucet-milestone-progress-text');
            const $button = $container.find('.farmfaucet-button');

            // Start from zero width
            $progressBar.css('width', '0%');

            // If there's a percentage text element, start it at 0%
            if ($percentText.length) {
                $percentText.text('0%');
            }

            // If there's a progress text element inside the bar, start it at 0%
            if ($progressText.length) {
                $progressText.text('0%');
            }

            // Log for debugging
            console.log('Animating progress bar:', {
                targetProgress: targetProgress,
                targetWidth: targetWidth,
                container: $container.length ? 'Found' : 'Not found',
                percentText: $percentText.length ? 'Found' : 'Not found',
                progressText: $progressText.length ? 'Found' : 'Not found'
            });

            // Animate the progress bar with easing
            $progressBar.animate(
                { width: targetWidth },
                {
                    duration: 1500,
                    easing: 'easeOutQuart',
                    step: function(now, fx) {
                        // Calculate current percentage based on animation progress
                        const currentPercent = Math.round((now / fx.end) * targetProgress);

                        // Update percentage text if it exists
                        if ($percentText.length) {
                            $percentText.text(currentPercent + '%');
                        }

                        // Update progress text inside the bar if it exists
                        if ($progressText.length) {
                            $progressText.text(currentPercent + '%');
                        }

                        // If progress is complete, add a completion class
                        if (currentPercent >= 100) {
                            $container.addClass('farmfaucet-milestone-complete');

                            // Show the button if it exists and was hidden
                            if ($button.length && $button.is(':hidden')) {
                                $button.fadeIn(500);
                            }
                        }
                    },
                    complete: function() {
                        // Ensure final percentage is accurate
                        if ($percentText.length) {
                            $percentText.text(targetProgress + '%');
                        }

                        // If milestone is complete, add completion effects
                        if (targetProgress >= 100) {
                            $container.addClass('farmfaucet-milestone-complete');

                            // Show the button with a fade effect if it exists and was hidden
                            if ($button.length && $button.is(':hidden')) {
                                $button.fadeIn(500);
                            }
                        }
                    }
                }
            );
        });
    }

    // Add jQuery easing function if not available
    if ($.easing.easeOutQuart === undefined) {
        $.easing.easeOutQuart = function(x, t, b, c, d) {
            return -c * ((t=t/d-1)*t*t*t - 1) + b;
        };
    }

    // Initialize milestone progress bars if they exist on the page
    if ($('.farmfaucet-milestone-container').length || $('.farmfaucet-completion-container').length) {
        console.log('Initializing progress bars, found milestone containers:',
            $('.farmfaucet-milestone-container').length,
            'and completion containers:',
            $('.farmfaucet-completion-container').length
        );
        // Delay animation slightly to ensure DOM is fully loaded
        setTimeout(animateMilestoneProgressBars, 300);
    }
});
