<?php

/**
 * Advertising System for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.2
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Advertising
 *
 * Handles advertising functionality with voting system
 */
class Farmfaucet_Advertising
{

    /**
     * Singleton instance
     *
     * @var Farmfaucet_Advertising
     */
    private static $instance;

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Advertising
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Settings registration now handled by Farmfaucet_Settings_Manager

        // Register shortcodes
        add_shortcode('farmfaucet_ad_display', [$this, 'render_ad_display_shortcode']);
        add_shortcode('farmfaucet_ad_create', [$this, 'render_ad_create_shortcode']);
        add_shortcode('farmfaucet_ad_list', [$this, 'render_ad_list_shortcode']);

        // AJAX handlers
        add_action('wp_ajax_farmfaucet_create_ad', [$this, 'ajax_create_ad']);
        add_action('wp_ajax_farmfaucet_update_ad', [$this, 'ajax_update_ad']);
        add_action('wp_ajax_farmfaucet_delete_ad', [$this, 'ajax_delete_ad']);
        add_action('wp_ajax_farmfaucet_vote_ad', [$this, 'ajax_vote_ad']);
        add_action('wp_ajax_nopriv_farmfaucet_vote_ad', [$this, 'ajax_vote_ad']);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Create database tables on plugin activation
        add_action('farmfaucet_activate', [$this, 'create_tables']);
    }

    /**
     * Register settings for Advertising System
     */
    public function register_settings()
    {
        // Settings registration now handled by Farmfaucet_Settings_Manager
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets()
    {
        wp_enqueue_style(
            'farmfaucet-advertising',
            FARMFAUCET_URL . 'assets/css/advertising.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-advertising',
            FARMFAUCET_URL . 'assets/js/advertising.js',
            ['jquery'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-advertising', 'farmfaucetAdvertising', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-advertising-nonce'),
            'i18n' => [
                'createSuccess' => __('Advertisement created successfully. It will be reviewed by an admin.', 'farmfaucet'),
                'createFailed' => __('Failed to create advertisement. Please try again.', 'farmfaucet'),
                'voteSuccess' => __('Vote recorded successfully.', 'farmfaucet'),
                'voteFailed' => __('Failed to record vote. Please try again.', 'farmfaucet'),
                'alreadyVoted' => __('You have already voted for this advertisement.', 'farmfaucet'),
                'maxVotesReached' => __('You have reached the maximum number of votes allowed.', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook)
    {
        // Only load on Farm Faucet admin pages
        if (strpos($hook, 'farmfaucet') === false) {
            return;
        }

        wp_enqueue_style(
            'farmfaucet-advertising-admin',
            FARMFAUCET_URL . 'assets/css/advertising-admin.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-advertising-admin',
            FARMFAUCET_URL . 'assets/js/advertising-admin.js',
            ['jquery'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-advertising-admin', 'farmfaucetAdvertisingAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-advertising-admin-nonce'),
            'i18n' => [
                'approveSuccess' => __('Advertisement approved successfully.', 'farmfaucet'),
                'approveFailed' => __('Failed to approve advertisement. Please try again.', 'farmfaucet'),
                'rejectSuccess' => __('Advertisement rejected successfully.', 'farmfaucet'),
                'rejectFailed' => __('Failed to reject advertisement. Please try again.', 'farmfaucet'),
                'deleteSuccess' => __('Advertisement deleted successfully.', 'farmfaucet'),
                'deleteFailed' => __('Failed to delete advertisement. Please try again.', 'farmfaucet'),
                'confirmDelete' => __('Are you sure you want to delete this advertisement? This action cannot be undone.', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Create database tables
     */
    public function create_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Advertisements table
        $ads_table = $wpdb->prefix . 'farmfaucet_advertisements';

        $sql = "CREATE TABLE IF NOT EXISTS $ads_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            title varchar(100) NOT NULL,
            description text NOT NULL,
            url varchar(255) NOT NULL,
            image_url varchar(255) DEFAULT NULL,
            status varchar(20) NOT NULL DEFAULT 'pending',
            votes int(11) NOT NULL DEFAULT 0,
            cost decimal(18,8) NOT NULL,
            currency_id bigint(20) DEFAULT NULL,
            start_date datetime NOT NULL,
            end_date datetime NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY status (status),
            KEY end_date (end_date)
        ) $charset_collate;";

        // Ad votes table
        $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';

        $sql .= "CREATE TABLE IF NOT EXISTS $votes_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            ad_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            vote_type varchar(10) NOT NULL,
            reward_amount decimal(18,8) NOT NULL DEFAULT 0,
            currency_id bigint(20) DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY ad_user (ad_id, user_id),
            KEY ad_id (ad_id),
            KEY user_id (user_id)
        ) $charset_collate;";

        // Execute SQL
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get all advertisements
     *
     * @param string $status Filter by status (all, pending, approved, rejected)
     * @param int $limit Number of ads to return
     * @param int $offset Offset for pagination
     * @return array Array of advertisements
     */
    public function get_advertisements($status = 'all', $limit = 10, $offset = 0)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        $sql = "SELECT * FROM $table_name";

        if ($status !== 'all') {
            $sql .= $wpdb->prepare(" WHERE status = %s", $status);
        }

        $sql .= " ORDER BY created_at DESC";

        if ($limit > 0) {
            $sql .= $wpdb->prepare(" LIMIT %d OFFSET %d", $limit, $offset);
        }

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Get active advertisements
     *
     * @param int $limit Number of ads to return
     * @param int $offset Offset for pagination
     * @return array Array of active advertisements
     */
    public function get_active_advertisements($limit = 10, $offset = 0)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        $current_date = current_time('mysql');

        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE status = 'approved'
            AND start_date <= %s
            AND end_date >= %s
            ORDER BY votes DESC, RAND()
            LIMIT %d OFFSET %d",
            $current_date,
            $current_date,
            $limit,
            $offset
        );

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Get advertisement by ID
     *
     * @param int $ad_id Advertisement ID
     * @return array|null Advertisement data or null if not found
     */
    public function get_advertisement($ad_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $ad_id
            ),
            ARRAY_A
        );

        return $result;
    }

    /**
     * Get user advertisements
     *
     * @param int $user_id User ID
     * @param string $status Filter by status (all, pending, approved, rejected)
     * @return array Array of user advertisements
     */
    public function get_user_advertisements($user_id, $status = 'all')
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        $sql = $wpdb->prepare("SELECT * FROM $table_name WHERE user_id = %d", $user_id);

        if ($status !== 'all') {
            $sql .= $wpdb->prepare(" AND status = %s", $status);
        }

        $sql .= " ORDER BY created_at DESC";

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Create a new advertisement
     *
     * @param array $data Advertisement data
     * @return int|false Advertisement ID on success, false on failure
     */
    public function create_advertisement($data)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        // Validate required fields
        if (
            empty($data['user_id']) || empty($data['title']) || empty($data['description']) ||
            empty($data['url']) || !isset($data['cost'])
        ) {
            return false;
        }

        // Calculate start and end dates
        $start_date = current_time('mysql');
        $duration_days = get_option('farmfaucet_ad_duration_days', 7);
        $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + $duration_days days"));

        // Set initial status based on approval setting
        $status = get_option('farmfaucet_ad_approval_required', 1) ? 'pending' : 'approved';

        // Prepare data for insertion
        $insert_data = [
            'user_id' => intval($data['user_id']),
            'title' => sanitize_text_field($data['title']),
            'description' => wp_kses_post($data['description']),
            'url' => esc_url_raw($data['url']),
            'image_url' => isset($data['image_url']) ? esc_url_raw($data['image_url']) : null,
            'status' => $status,
            'votes' => 0,
            'cost' => floatval($data['cost']),
            'currency_id' => isset($data['currency_id']) ? intval($data['currency_id']) : null,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Insert advertisement
        $result = $wpdb->insert(
            $table_name,
            $insert_data,
            [
                '%d', // user_id
                '%s', // title
                '%s', // description
                '%s', // url
                '%s', // image_url
                '%s', // status
                '%d', // votes
                '%f', // cost
                '%d', // currency_id
                '%s', // start_date
                '%s', // end_date
                '%s', // created_at
                '%s'  // updated_at
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update an existing advertisement
     *
     * @param int $ad_id Advertisement ID
     * @param array $data Advertisement data
     * @return bool True on success, false on failure
     */
    public function update_advertisement($ad_id, $data)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        // Check if advertisement exists
        $ad = $this->get_advertisement($ad_id);
        if (!$ad) {
            return false;
        }

        // Prepare data for update
        $update_data = [];
        $update_format = [];

        if (isset($data['title'])) {
            $update_data['title'] = sanitize_text_field($data['title']);
            $update_format[] = '%s';
        }

        if (isset($data['description'])) {
            $update_data['description'] = wp_kses_post($data['description']);
            $update_format[] = '%s';
        }

        if (isset($data['url'])) {
            $update_data['url'] = esc_url_raw($data['url']);
            $update_format[] = '%s';
        }

        if (isset($data['image_url'])) {
            $update_data['image_url'] = esc_url_raw($data['image_url']);
            $update_format[] = '%s';
        }

        if (isset($data['status'])) {
            $update_data['status'] = sanitize_text_field($data['status']);
            $update_format[] = '%s';
        }

        if (isset($data['votes'])) {
            $update_data['votes'] = intval($data['votes']);
            $update_format[] = '%d';
        }

        if (isset($data['start_date'])) {
            $update_data['start_date'] = sanitize_text_field($data['start_date']);
            $update_format[] = '%s';
        }

        if (isset($data['end_date'])) {
            $update_data['end_date'] = sanitize_text_field($data['end_date']);
            $update_format[] = '%s';
        }

        // Update timestamp
        $update_data['updated_at'] = current_time('mysql');
        $update_format[] = '%s';

        // Update advertisement
        $result = $wpdb->update(
            $table_name,
            $update_data,
            ['id' => $ad_id],
            $update_format,
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Delete an advertisement
     *
     * @param int $ad_id Advertisement ID
     * @return bool True on success, false on failure
     */
    public function delete_advertisement($ad_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_advertisements';

        // Check if advertisement exists
        $ad = $this->get_advertisement($ad_id);
        if (!$ad) {
            return false;
        }

        // Delete advertisement
        $result = $wpdb->delete(
            $table_name,
            ['id' => $ad_id],
            ['%d']
        );

        if ($result) {
            // Also delete votes for this advertisement
            $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';
            $wpdb->delete(
                $votes_table,
                ['ad_id' => $ad_id],
                ['%d']
            );
        }

        return $result !== false;
    }

    /**
     * Approve an advertisement
     *
     * @param int $ad_id Advertisement ID
     * @return bool True on success, false on failure
     */
    public function approve_advertisement($ad_id)
    {
        return $this->update_advertisement($ad_id, ['status' => 'approved']);
    }

    /**
     * Reject an advertisement
     *
     * @param int $ad_id Advertisement ID
     * @return bool True on success, false on failure
     */
    public function reject_advertisement($ad_id)
    {
        return $this->update_advertisement($ad_id, ['status' => 'rejected']);
    }

    /**
     * Check if user has voted for an advertisement
     *
     * @param int $ad_id Advertisement ID
     * @param int $user_id User ID
     * @return bool True if user has voted, false otherwise
     */
    public function has_user_voted($ad_id, $user_id)
    {
        global $wpdb;

        $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';

        $result = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $votes_table WHERE ad_id = %d AND user_id = %d",
                $ad_id,
                $user_id
            )
        );

        return $result > 0;
    }

    /**
     * Count user votes for today
     *
     * @param int $user_id User ID
     * @return int Number of votes
     */
    public function count_user_votes_today($user_id)
    {
        global $wpdb;

        $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';

        $today_start = date('Y-m-d 00:00:00', current_time('timestamp'));
        $today_end = date('Y-m-d 23:59:59', current_time('timestamp'));

        $result = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $votes_table WHERE user_id = %d AND created_at BETWEEN %s AND %s",
                $user_id,
                $today_start,
                $today_end
            )
        );

        return intval($result);
    }

    /**
     * Vote for an advertisement
     *
     * @param int $ad_id Advertisement ID
     * @param int $user_id User ID
     * @param string $vote_type Vote type (up, down)
     * @return array|false Vote result on success, false on failure
     */
    public function vote_advertisement($ad_id, $user_id, $vote_type = 'up')
    {
        global $wpdb;

        // Check if advertisement exists and is approved
        $ad = $this->get_advertisement($ad_id);
        if (!$ad || $ad['status'] !== 'approved') {
            return false;
        }

        // Check if user has already voted for this ad
        if ($this->has_user_voted($ad_id, $user_id)) {
            return false;
        }

        // Check if user has reached the maximum number of votes for today
        $max_votes = get_option('farmfaucet_ad_max_votes', 3);
        $user_votes = $this->count_user_votes_today($user_id);

        if ($user_votes >= $max_votes) {
            return false;
        }

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            // Update advertisement votes
            $votes = intval($ad['votes']);
            $votes_change = $vote_type === 'up' ? 1 : -1;
            $new_votes = max(0, $votes + $votes_change);

            $result1 = $this->update_advertisement($ad_id, ['votes' => $new_votes]);

            // Calculate reward
            $reward_amount = get_option('farmfaucet_ad_vote_reward', 0.1);
            $currency_id = $ad['currency_id'];

            // Record vote
            $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';

            $result2 = $wpdb->insert(
                $votes_table,
                [
                    'ad_id' => $ad_id,
                    'user_id' => $user_id,
                    'vote_type' => $vote_type,
                    'reward_amount' => $reward_amount,
                    'currency_id' => $currency_id,
                    'created_at' => current_time('mysql')
                ],
                [
                    '%d', // ad_id
                    '%d', // user_id
                    '%s', // vote_type
                    '%f', // reward_amount
                    '%d', // currency_id
                    '%s'  // created_at
                ]
            );

            // Add reward to user balance if currency maker is available
            if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
                $currency_maker = Farmfaucet_Currency_Maker::init();
                $result3 = $currency_maker->update_user_currency_balance($user_id, $currency_id, $reward_amount);
            } else {
                $result3 = true;
            }

            if ($result1 && $result2 && $result3) {
                $wpdb->query('COMMIT');

                return [
                    'ad_id' => $ad_id,
                    'user_id' => $user_id,
                    'vote_type' => $vote_type,
                    'new_votes' => $new_votes,
                    'reward_amount' => $reward_amount,
                    'currency_id' => $currency_id,
                    'vote_id' => $wpdb->insert_id
                ];
            } else {
                $wpdb->query('ROLLBACK');
                return false;
            }
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * AJAX handler for creating an advertisement
     */
    public function ajax_create_ad()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-advertising-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('You must be logged in to create an advertisement.', 'farmfaucet')]);
        }

        // Check if advertising is enabled
        if (!get_option('farmfaucet_advertising_enabled', 1)) {
            wp_send_json_error(['message' => __('Advertising is currently disabled.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['title']) || !isset($_POST['description']) || !isset($_POST['url'])) {
            wp_send_json_error(['message' => __('Missing required fields.', 'farmfaucet')]);
        }

        $user_id = get_current_user_id();
        $title = sanitize_text_field($_POST['title']);
        $description = wp_kses_post($_POST['description']);
        $url = esc_url_raw($_POST['url']);
        $image_url = isset($_POST['image_url']) ? esc_url_raw($_POST['image_url']) : null;
        $currency_id = isset($_POST['currency_id']) ? intval($_POST['currency_id']) : null;

        // Get ad cost
        $cost = get_option('farmfaucet_ad_cost', 1.0);

        // Check if user has enough balance if currency maker is available
        if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $user_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);

            if ($user_balance < $cost) {
                wp_send_json_error(['message' => __('Insufficient balance to create an advertisement.', 'farmfaucet')]);
            }

            // Deduct cost from user balance
            $result = $currency_maker->update_user_currency_balance($user_id, $currency_id, -$cost);

            if (!$result) {
                wp_send_json_error(['message' => __('Failed to process payment. Please try again.', 'farmfaucet')]);
            }
        }

        // Create advertisement
        $ad_id = $this->create_advertisement([
            'user_id' => $user_id,
            'title' => $title,
            'description' => $description,
            'url' => $url,
            'image_url' => $image_url,
            'cost' => $cost,
            'currency_id' => $currency_id
        ]);

        if ($ad_id) {
            wp_send_json_success([
                'message' => __('Advertisement created successfully. It will be reviewed by an admin.', 'farmfaucet'),
                'ad_id' => $ad_id
            ]);
        } else {
            // Refund payment if advertisement creation failed
            if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
                $currency_maker->update_user_currency_balance($user_id, $currency_id, $cost);
            }

            wp_send_json_error(['message' => __('Failed to create advertisement. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for updating an advertisement
     */
    public function ajax_update_ad()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-advertising-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('You must be logged in to update an advertisement.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['ad_id'])) {
            wp_send_json_error(['message' => __('Missing advertisement ID.', 'farmfaucet')]);
        }

        $ad_id = intval($_POST['ad_id']);
        $user_id = get_current_user_id();

        // Get advertisement
        $ad = $this->get_advertisement($ad_id);

        if (!$ad) {
            wp_send_json_error(['message' => __('Advertisement not found.', 'farmfaucet')]);
        }

        // Check if user owns the advertisement or is an admin
        if ($ad['user_id'] != $user_id && !current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to update this advertisement.', 'farmfaucet')]);
        }

        // Prepare update data
        $update_data = [];

        if (isset($_POST['title'])) {
            $update_data['title'] = sanitize_text_field($_POST['title']);
        }

        if (isset($_POST['description'])) {
            $update_data['description'] = wp_kses_post($_POST['description']);
        }

        if (isset($_POST['url'])) {
            $update_data['url'] = esc_url_raw($_POST['url']);
        }

        if (isset($_POST['image_url'])) {
            $update_data['image_url'] = esc_url_raw($_POST['image_url']);
        }

        // Only admins can update status
        if (isset($_POST['status']) && current_user_can('manage_options')) {
            $update_data['status'] = sanitize_text_field($_POST['status']);
        }

        // Update advertisement
        $result = $this->update_advertisement($ad_id, $update_data);

        if ($result) {
            wp_send_json_success([
                'message' => __('Advertisement updated successfully.', 'farmfaucet'),
                'ad_id' => $ad_id
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to update advertisement. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for deleting an advertisement
     */
    public function ajax_delete_ad()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-advertising-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('You must be logged in to delete an advertisement.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['ad_id'])) {
            wp_send_json_error(['message' => __('Missing advertisement ID.', 'farmfaucet')]);
        }

        $ad_id = intval($_POST['ad_id']);
        $user_id = get_current_user_id();

        // Get advertisement
        $ad = $this->get_advertisement($ad_id);

        if (!$ad) {
            wp_send_json_error(['message' => __('Advertisement not found.', 'farmfaucet')]);
        }

        // Check if user owns the advertisement or is an admin
        if ($ad['user_id'] != $user_id && !current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to delete this advertisement.', 'farmfaucet')]);
        }

        // Delete advertisement
        $result = $this->delete_advertisement($ad_id);

        if ($result) {
            wp_send_json_success([
                'message' => __('Advertisement deleted successfully.', 'farmfaucet')
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete advertisement. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for voting on an advertisement
     */
    public function ajax_vote_ad()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-advertising-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('You must be logged in to vote.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['ad_id']) || !isset($_POST['vote_type'])) {
            wp_send_json_error(['message' => __('Missing required fields.', 'farmfaucet')]);
        }

        $ad_id = intval($_POST['ad_id']);
        $user_id = get_current_user_id();
        $vote_type = sanitize_text_field($_POST['vote_type']);

        // Validate vote type
        if (!in_array($vote_type, ['up', 'down'])) {
            wp_send_json_error(['message' => __('Invalid vote type.', 'farmfaucet')]);
        }

        // Check if user has already voted for this ad
        if ($this->has_user_voted($ad_id, $user_id)) {
            wp_send_json_error(['message' => __('You have already voted for this advertisement.', 'farmfaucet')]);
        }

        // Check if user has reached the maximum number of votes for today
        $max_votes = get_option('farmfaucet_ad_max_votes', 3);
        $user_votes = $this->count_user_votes_today($user_id);

        if ($user_votes >= $max_votes) {
            wp_send_json_error(['message' => __('You have reached the maximum number of votes allowed for today.', 'farmfaucet')]);
        }

        // Vote for advertisement
        $result = $this->vote_advertisement($ad_id, $user_id, $vote_type);

        if ($result) {
            wp_send_json_success([
                'message' => __('Vote recorded successfully.', 'farmfaucet'),
                'reward_amount' => $result['reward_amount'],
                'new_votes' => $result['new_votes']
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to record vote. Please try again.', 'farmfaucet')]);
        }
    }

    /**
     * Render the admin page
     */
    public static function render_admin_page()
    {
        $instance = self::init();
        $instance->render_admin_interface();
    }

    /**
     * Render the admin interface
     */
    private function render_admin_interface()
    {
        // Get advertisements
        $pending_ads = $this->get_advertisements('pending');
        $approved_ads = $this->get_advertisements('approved');
        $rejected_ads = $this->get_advertisements('rejected');

        // Get settings
        $advertising_enabled = get_option('farmfaucet_advertising_enabled', 1);
        $approval_required = get_option('farmfaucet_ad_approval_required', 1);
        $ad_cost = get_option('farmfaucet_ad_cost', 1.0);
        $ad_duration_days = get_option('farmfaucet_ad_duration_days', 7);
        $ad_max_votes = get_option('farmfaucet_ad_max_votes', 3);
        $ad_vote_reward = get_option('farmfaucet_ad_vote_reward', 0.1);

        // Get available currencies if Currency Maker is available
        $currencies = [];
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currencies = $currency_maker->get_currencies();
        }

        // Include admin template
        include(FARMFAUCET_DIR . 'templates/admin/advertising.php');
    }

    /**
     * Render advertisement display shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_ad_display_shortcode($atts)
    {
        // Check if advertising is enabled
        if (!get_option('farmfaucet_advertising_enabled', 1)) {
            return '<div class="farmfaucet-ad-notice">' . __('Advertising is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'limit' => 3,
            'layout' => 'grid', // grid, list, carousel
            'columns' => 3,
            'show_votes' => 1,
            'show_description' => 1,
            'description_length' => 100,
            'show_image' => 1,
            'random' => 0,
            'category' => '',
        ], $atts);

        // Get active advertisements
        $ads = $this->get_active_advertisements($atts['limit'], 0);

        if (empty($ads)) {
            return '<div class="farmfaucet-ad-notice">' . __('No advertisements available.', 'farmfaucet') . '</div>';
        }

        // Start output buffer
        ob_start();

        // Include template based on layout
        $layout = sanitize_key($atts['layout']);
        $template_file = FARMFAUCET_DIR . 'templates/advertising/display-' . $layout . '.php';

        // Fallback to grid layout if template doesn't exist
        if (!file_exists($template_file)) {
            $template_file = FARMFAUCET_DIR . 'templates/advertising/display-grid.php';
        }

        // Include template
        include($template_file);

        // Return buffered content
        return ob_get_clean();
    }

    /**
     * Render advertisement creation form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_ad_create_shortcode($atts)
    {
        // Check if advertising is enabled
        if (!get_option('farmfaucet_advertising_enabled', 1)) {
            return '<div class="farmfaucet-ad-notice">' . __('Advertising is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-ad-notice">' . __('You must be logged in to create an advertisement.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'title' => __('Create Advertisement', 'farmfaucet'),
            'button_text' => __('Submit Advertisement', 'farmfaucet'),
            'show_cost' => 1,
            'show_duration' => 1,
            'show_image_upload' => 1,
            'redirect_url' => '',
        ], $atts);

        // Get ad cost
        $ad_cost = get_option('farmfaucet_ad_cost', 1.0);
        $ad_duration_days = get_option('farmfaucet_ad_duration_days', 7);

        // Get available currencies if Currency Maker is available
        $currencies = [];
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currencies = $currency_maker->get_currencies();

            // Get user balances
            $user_id = get_current_user_id();
            $user_balances = [];

            foreach ($currencies as $currency) {
                $balance = $currency_maker->get_user_currency_balance($user_id, $currency['id']);
                $user_balances[$currency['id']] = $balance;
            }
        }

        // Start output buffer
        ob_start();

        // Include template
        include(FARMFAUCET_DIR . 'templates/advertising/create-form.php');

        // Return buffered content
        return ob_get_clean();
    }

    /**
     * Render user advertisements list shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_ad_list_shortcode($atts)
    {
        // Check if advertising is enabled
        if (!get_option('farmfaucet_advertising_enabled', 1)) {
            return '<div class="farmfaucet-ad-notice">' . __('Advertising is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-ad-notice">' . __('You must be logged in to view your advertisements.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'title' => __('My Advertisements', 'farmfaucet'),
            'show_status' => 1,
            'show_votes' => 1,
            'show_dates' => 1,
            'show_actions' => 1,
            'limit' => 10,
        ], $atts);

        // Get user advertisements
        $user_id = get_current_user_id();
        $ads = $this->get_user_advertisements($user_id);

        if (empty($ads)) {
            return '<div class="farmfaucet-ad-notice">' . __('You have not created any advertisements yet.', 'farmfaucet') . '</div>';
        }

        // Start output buffer
        ob_start();

        // Include template
        include(FARMFAUCET_DIR . 'templates/advertising/user-ads-list.php');

        // Return buffered content
        return ob_get_clean();
    }
}
