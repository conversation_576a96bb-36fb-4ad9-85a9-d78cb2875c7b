<?php
/**
 * Template for user advertisements list
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-user-ads-container">
    <h2 class="list-title"><?php echo esc_html($atts['title']); ?></h2>
    
    <div class="farmfaucet-user-ads-list">
        <table class="farmfaucet-ads-table">
            <thead>
                <tr>
                    <th><?php esc_html_e('Title', 'farmfaucet'); ?></th>
                    <?php if ($atts['show_status']) : ?>
                        <th><?php esc_html_e('Status', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                    <?php if ($atts['show_votes']) : ?>
                        <th><?php esc_html_e('Votes', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                    <?php if ($atts['show_dates']) : ?>
                        <th><?php esc_html_e('Created', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Expires', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                    <?php if ($atts['show_actions']) : ?>
                        <th><?php esc_html_e('Actions', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($ads as $ad) : ?>
                    <tr data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                        <td>
                            <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow">
                                <?php echo esc_html($ad['title']); ?>
                            </a>
                        </td>
                        
                        <?php if ($atts['show_status']) : ?>
                            <td class="ad-status status-<?php echo esc_attr($ad['status']); ?>">
                                <?php 
                                switch ($ad['status']) {
                                    case 'pending':
                                        esc_html_e('Pending', 'farmfaucet');
                                        break;
                                    case 'approved':
                                        esc_html_e('Approved', 'farmfaucet');
                                        break;
                                    case 'rejected':
                                        esc_html_e('Rejected', 'farmfaucet');
                                        break;
                                    default:
                                        echo esc_html($ad['status']);
                                }
                                ?>
                            </td>
                        <?php endif; ?>
                        
                        <?php if ($atts['show_votes']) : ?>
                            <td class="ad-votes">
                                <?php echo esc_html($ad['votes']); ?>
                            </td>
                        <?php endif; ?>
                        
                        <?php if ($atts['show_dates']) : ?>
                            <td class="ad-created">
                                <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($ad['created_at']))); ?>
                            </td>
                            <td class="ad-expires">
                                <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($ad['end_date']))); ?>
                            </td>
                        <?php endif; ?>
                        
                        <?php if ($atts['show_actions']) : ?>
                            <td class="ad-actions">
                                <?php if ($ad['status'] === 'pending' || $ad['status'] === 'rejected') : ?>
                                    <button class="ad-edit-button" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                                        <?php esc_html_e('Edit', 'farmfaucet'); ?>
                                    </button>
                                <?php endif; ?>
                                
                                <button class="ad-delete-button" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                                    <?php esc_html_e('Delete', 'farmfaucet'); ?>
                                </button>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Edit Advertisement Modal -->
<div id="farmfaucet-edit-ad-modal" class="farmfaucet-modal" style="display: none;">
    <div class="farmfaucet-modal-content">
        <div class="farmfaucet-modal-header">
            <h3 class="modal-title"><?php esc_html_e('Edit Advertisement', 'farmfaucet'); ?></h3>
            <span class="farmfaucet-modal-close">&times;</span>
        </div>
        <div class="farmfaucet-modal-body">
            <form id="farmfaucet-ad-edit-form" class="farmfaucet-form">
                <div class="form-group">
                    <label for="edit-ad-title"><?php esc_html_e('Title', 'farmfaucet'); ?> <span class="required">*</span></label>
                    <input type="text" id="edit-ad-title" name="title" required maxlength="100">
                </div>
                
                <div class="form-group">
                    <label for="edit-ad-description"><?php esc_html_e('Description', 'farmfaucet'); ?> <span class="required">*</span></label>
                    <textarea id="edit-ad-description" name="description" required rows="4"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit-ad-url"><?php esc_html_e('URL', 'farmfaucet'); ?> <span class="required">*</span></label>
                    <input type="url" id="edit-ad-url" name="url" required>
                </div>
                
                <div class="form-group">
                    <label for="edit-ad-image-url"><?php esc_html_e('Image URL', 'farmfaucet'); ?></label>
                    <input type="url" id="edit-ad-image-url" name="image_url">
                </div>
                
                <input type="hidden" id="edit-ad-id" name="ad_id">
                <input type="hidden" name="action" value="farmfaucet_update_ad">
                <input type="hidden" name="nonce" value="<?php echo esc_attr(wp_create_nonce('farmfaucet-advertising-nonce')); ?>">
            </form>
        </div>
        <div class="farmfaucet-modal-footer">
            <div class="form-message" style="display: none;"></div>
            <button type="button" id="edit-ad-submit" class="farmfaucet-button"><?php esc_html_e('Save Changes', 'farmfaucet'); ?></button>
            <button type="button" class="farmfaucet-button farmfaucet-modal-close"><?php esc_html_e('Cancel', 'farmfaucet'); ?></button>
        </div>
    </div>
</div>

<script>
    (function() {
        document.addEventListener('DOMContentLoaded', function() {
            // Edit button click handler
            const editButtons = document.querySelectorAll('.ad-edit-button');
            const editModal = document.getElementById('farmfaucet-edit-ad-modal');
            const editForm = document.getElementById('farmfaucet-ad-edit-form');
            const editSubmitButton = document.getElementById('edit-ad-submit');
            const editMessageContainer = editModal.querySelector('.form-message');
            const modalCloseButtons = document.querySelectorAll('.farmfaucet-modal-close');
            
            // Delete button click handler
            const deleteButtons = document.querySelectorAll('.ad-delete-button');
            
            // Edit advertisement
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const adId = this.dataset.adId;
                    const row = document.querySelector(`tr[data-ad-id="${adId}"]`);
                    
                    // Get advertisement data from the row
                    const title = row.querySelector('td:first-child a').textContent.trim();
                    const url = row.querySelector('td:first-child a').getAttribute('href');
                    
                    // Set form values
                    document.getElementById('edit-ad-id').value = adId;
                    document.getElementById('edit-ad-title').value = title;
                    document.getElementById('edit-ad-url').value = url;
                    
                    // Fetch additional data via AJAX
                    fetch(farmfaucetAdvertising.ajaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'farmfaucet_get_ad_details',
                            nonce: farmfaucetAdvertising.nonce,
                            ad_id: adId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('edit-ad-description').value = data.data.description;
                            document.getElementById('edit-ad-image-url').value = data.data.image_url || '';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                    
                    // Show modal
                    editModal.style.display = 'flex';
                });
            });
            
            // Close modal
            modalCloseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    editModal.style.display = 'none';
                    editMessageContainer.style.display = 'none';
                });
            });
            
            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === editModal) {
                    editModal.style.display = 'none';
                    editMessageContainer.style.display = 'none';
                }
            });
            
            // Submit edit form
            editSubmitButton.addEventListener('click', function() {
                // Validate form
                if (!editForm.checkValidity()) {
                    editForm.reportValidity();
                    return;
                }
                
                // Show loading state
                editSubmitButton.disabled = true;
                editSubmitButton.textContent = '<?php esc_html_e('Saving...', 'farmfaucet'); ?>';
                
                // Collect form data
                const formData = new FormData(editForm);
                
                // Send AJAX request
                fetch(farmfaucetAdvertising.ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        editMessageContainer.className = 'form-message success';
                        editMessageContainer.textContent = data.data.message;
                        editMessageContainer.style.display = 'block';
                        
                        // Reload page after a delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // Show error message
                        editMessageContainer.className = 'form-message error';
                        editMessageContainer.textContent = data.data.message;
                        editMessageContainer.style.display = 'block';
                        
                        // Reset button
                        editSubmitButton.disabled = false;
                        editSubmitButton.textContent = '<?php esc_html_e('Save Changes', 'farmfaucet'); ?>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    
                    // Show error message
                    editMessageContainer.className = 'form-message error';
                    editMessageContainer.textContent = farmfaucetAdvertising.i18n.error;
                    editMessageContainer.style.display = 'block';
                    
                    // Reset button
                    editSubmitButton.disabled = false;
                    editSubmitButton.textContent = '<?php esc_html_e('Save Changes', 'farmfaucet'); ?>';
                });
            });
            
            // Delete advertisement
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const adId = this.dataset.adId;
                    
                    if (confirm('<?php esc_html_e('Are you sure you want to delete this advertisement?', 'farmfaucet'); ?>')) {
                        // Send AJAX request
                        fetch(farmfaucetAdvertising.ajaxUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams({
                                action: 'farmfaucet_delete_ad',
                                nonce: farmfaucetAdvertising.nonce,
                                ad_id: adId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Remove row from table
                                const row = document.querySelector(`tr[data-ad-id="${adId}"]`);
                                row.remove();
                                
                                // Show alert
                                alert(data.data.message);
                                
                                // Reload if no ads left
                                const rows = document.querySelectorAll('.farmfaucet-ads-table tbody tr');
                                if (rows.length === 0) {
                                    window.location.reload();
                                }
                            } else {
                                alert(data.data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert(farmfaucetAdvertising.i18n.error);
                        });
                    }
                });
            });
        });
    })();
</script>
