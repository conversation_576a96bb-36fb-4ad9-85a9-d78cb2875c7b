<?php
/**
 * Admin template for Advertising System
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-admin-card">
    <div class="card-header">
        <h3><?php esc_html_e('Advertising Settings', 'farmfaucet'); ?></h3>
    </div>
    <div class="card-body">
        <form method="post" action="options.php" class="farmfaucet-form">
            <?php settings_fields('farmfaucet_settings'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_advertising_enabled"><?php esc_html_e('Enable Advertising', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" id="farmfaucet_advertising_enabled" name="farmfaucet_advertising_enabled" value="1" <?php checked(get_option('farmfaucet_advertising_enabled', 1), 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <p class="description"><?php esc_html_e('Enable or disable the advertising system.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_ad_approval_required"><?php esc_html_e('Require Approval', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" id="farmfaucet_ad_approval_required" name="farmfaucet_ad_approval_required" value="1" <?php checked(get_option('farmfaucet_ad_approval_required', 1), 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <p class="description"><?php esc_html_e('Require admin approval for new advertisements.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_ad_cost"><?php esc_html_e('Advertisement Cost', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="farmfaucet_ad_cost" name="farmfaucet_ad_cost" value="<?php echo esc_attr(get_option('farmfaucet_ad_cost', 1.0)); ?>" step="0.01" min="0">
                        <p class="description"><?php esc_html_e('Cost to create an advertisement.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_ad_duration_days"><?php esc_html_e('Advertisement Duration (Days)', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="farmfaucet_ad_duration_days" name="farmfaucet_ad_duration_days" value="<?php echo esc_attr(get_option('farmfaucet_ad_duration_days', 7)); ?>" min="1">
                        <p class="description"><?php esc_html_e('Number of days an advertisement will be active.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_ad_max_votes"><?php esc_html_e('Max Votes Per Day', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="farmfaucet_ad_max_votes" name="farmfaucet_ad_max_votes" value="<?php echo esc_attr(get_option('farmfaucet_ad_max_votes', 3)); ?>" min="1">
                        <p class="description"><?php esc_html_e('Maximum number of votes a user can cast per day.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="farmfaucet_ad_vote_reward"><?php esc_html_e('Vote Reward', 'farmfaucet'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="farmfaucet_ad_vote_reward" name="farmfaucet_ad_vote_reward" value="<?php echo esc_attr(get_option('farmfaucet_ad_vote_reward', 0.1)); ?>" step="0.01" min="0">
                        <p class="description"><?php esc_html_e('Reward amount for voting on an advertisement.', 'farmfaucet'); ?></p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
    </div>
</div>

<div class="farmfaucet-admin-card">
    <div class="card-header">
        <h3><?php esc_html_e('Pending Advertisements', 'farmfaucet'); ?></h3>
    </div>
    <div class="card-body">
        <?php if (empty($pending_ads)) : ?>
            <p class="farmfaucet-no-ads"><?php esc_html_e('No pending advertisements.', 'farmfaucet'); ?></p>
        <?php else : ?>
            <table class="widefat farmfaucet-ads-table">
                <thead>
                    <tr>
                        <th><?php esc_html_e('Title', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('User', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('URL', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Created', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Actions', 'farmfaucet'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pending_ads as $ad) : ?>
                        <tr data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                            <td>
                                <strong><?php echo esc_html($ad['title']); ?></strong>
                                <div class="row-actions">
                                    <span class="view"><a href="#" class="view-ad"><?php esc_html_e('View', 'farmfaucet'); ?></a> | </span>
                                    <span class="approve"><a href="#" class="approve-ad"><?php esc_html_e('Approve', 'farmfaucet'); ?></a> | </span>
                                    <span class="reject"><a href="#" class="reject-ad"><?php esc_html_e('Reject', 'farmfaucet'); ?></a></span>
                                </div>
                            </td>
                            <td>
                                <?php 
                                $user = get_userdata($ad['user_id']);
                                echo $user ? esc_html($user->display_name) : esc_html__('Unknown', 'farmfaucet');
                                ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url($ad['url']); ?>" target="_blank"><?php echo esc_url($ad['url']); ?></a>
                            </td>
                            <td>
                                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ad['created_at']))); ?>
                            </td>
                            <td>
                                <button class="button approve-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Approve', 'farmfaucet'); ?></button>
                                <button class="button reject-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Reject', 'farmfaucet'); ?></button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<div class="farmfaucet-admin-card">
    <div class="card-header">
        <h3><?php esc_html_e('Active Advertisements', 'farmfaucet'); ?></h3>
    </div>
    <div class="card-body">
        <?php if (empty($approved_ads)) : ?>
            <p class="farmfaucet-no-ads"><?php esc_html_e('No active advertisements.', 'farmfaucet'); ?></p>
        <?php else : ?>
            <table class="widefat farmfaucet-ads-table">
                <thead>
                    <tr>
                        <th><?php esc_html_e('Title', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('User', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('URL', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Votes', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Expires', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Actions', 'farmfaucet'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($approved_ads as $ad) : ?>
                        <tr data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                            <td>
                                <strong><?php echo esc_html($ad['title']); ?></strong>
                                <div class="row-actions">
                                    <span class="view"><a href="#" class="view-ad"><?php esc_html_e('View', 'farmfaucet'); ?></a> | </span>
                                    <span class="edit"><a href="#" class="edit-ad"><?php esc_html_e('Edit', 'farmfaucet'); ?></a> | </span>
                                    <span class="delete"><a href="#" class="delete-ad"><?php esc_html_e('Delete', 'farmfaucet'); ?></a></span>
                                </div>
                            </td>
                            <td>
                                <?php 
                                $user = get_userdata($ad['user_id']);
                                echo $user ? esc_html($user->display_name) : esc_html__('Unknown', 'farmfaucet');
                                ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url($ad['url']); ?>" target="_blank"><?php echo esc_url($ad['url']); ?></a>
                            </td>
                            <td>
                                <?php echo esc_html($ad['votes']); ?>
                            </td>
                            <td>
                                <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($ad['end_date']))); ?>
                            </td>
                            <td>
                                <button class="button edit-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Edit', 'farmfaucet'); ?></button>
                                <button class="button delete-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Delete', 'farmfaucet'); ?></button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<div class="farmfaucet-admin-card">
    <div class="card-header">
        <h3><?php esc_html_e('Rejected Advertisements', 'farmfaucet'); ?></h3>
    </div>
    <div class="card-body">
        <?php if (empty($rejected_ads)) : ?>
            <p class="farmfaucet-no-ads"><?php esc_html_e('No rejected advertisements.', 'farmfaucet'); ?></p>
        <?php else : ?>
            <table class="widefat farmfaucet-ads-table">
                <thead>
                    <tr>
                        <th><?php esc_html_e('Title', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('User', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('URL', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Created', 'farmfaucet'); ?></th>
                        <th><?php esc_html_e('Actions', 'farmfaucet'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rejected_ads as $ad) : ?>
                        <tr data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                            <td>
                                <strong><?php echo esc_html($ad['title']); ?></strong>
                                <div class="row-actions">
                                    <span class="view"><a href="#" class="view-ad"><?php esc_html_e('View', 'farmfaucet'); ?></a> | </span>
                                    <span class="approve"><a href="#" class="approve-ad"><?php esc_html_e('Approve', 'farmfaucet'); ?></a> | </span>
                                    <span class="delete"><a href="#" class="delete-ad"><?php esc_html_e('Delete', 'farmfaucet'); ?></a></span>
                                </div>
                            </td>
                            <td>
                                <?php 
                                $user = get_userdata($ad['user_id']);
                                echo $user ? esc_html($user->display_name) : esc_html__('Unknown', 'farmfaucet');
                                ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url($ad['url']); ?>" target="_blank"><?php echo esc_url($ad['url']); ?></a>
                            </td>
                            <td>
                                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ad['created_at']))); ?>
                            </td>
                            <td>
                                <button class="button approve-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Approve', 'farmfaucet'); ?></button>
                                <button class="button delete-ad" data-ad-id="<?php echo esc_attr($ad['id']); ?>"><?php esc_html_e('Delete', 'farmfaucet'); ?></button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<div class="farmfaucet-admin-card">
    <div class="card-header">
        <h3><?php esc_html_e('Shortcodes', 'farmfaucet'); ?></h3>
    </div>
    <div class="card-body">
        <div class="shortcode-group">
            <h4><?php esc_html_e('Display Advertisements', 'farmfaucet'); ?></h4>
            <div class="shortcode-item">
                <code>[farmfaucet_ad_display]</code>
                <span class="shortcode-description"><?php esc_html_e('Displays active advertisements.', 'farmfaucet'); ?></span>
            </div>
            <div class="shortcode-params">
                <p><strong><?php esc_html_e('Parameters:', 'farmfaucet'); ?></strong></p>
                <ul>
                    <li><code>limit</code> - <?php esc_html_e('Number of ads to display (default: 3)', 'farmfaucet'); ?></li>
                    <li><code>layout</code> - <?php esc_html_e('Layout style: grid, list, carousel (default: grid)', 'farmfaucet'); ?></li>
                    <li><code>columns</code> - <?php esc_html_e('Number of columns for grid layout (default: 3)', 'farmfaucet'); ?></li>
                    <li><code>show_votes</code> - <?php esc_html_e('Show vote count: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_description</code> - <?php esc_html_e('Show description: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>description_length</code> - <?php esc_html_e('Max description length (default: 100)', 'farmfaucet'); ?></li>
                    <li><code>show_image</code> - <?php esc_html_e('Show image: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>random</code> - <?php esc_html_e('Randomize order: 1 or 0 (default: 0)', 'farmfaucet'); ?></li>
                </ul>
                <p><strong><?php esc_html_e('Example:', 'farmfaucet'); ?></strong> <code>[farmfaucet_ad_display limit="5" layout="list" show_votes="1"]</code></p>
            </div>
        </div>
        
        <div class="shortcode-group">
            <h4><?php esc_html_e('Create Advertisement Form', 'farmfaucet'); ?></h4>
            <div class="shortcode-item">
                <code>[farmfaucet_ad_create]</code>
                <span class="shortcode-description"><?php esc_html_e('Displays a form for users to create advertisements.', 'farmfaucet'); ?></span>
            </div>
            <div class="shortcode-params">
                <p><strong><?php esc_html_e('Parameters:', 'farmfaucet'); ?></strong></p>
                <ul>
                    <li><code>title</code> - <?php esc_html_e('Form title (default: "Create Advertisement")', 'farmfaucet'); ?></li>
                    <li><code>button_text</code> - <?php esc_html_e('Submit button text (default: "Submit Advertisement")', 'farmfaucet'); ?></li>
                    <li><code>show_cost</code> - <?php esc_html_e('Show cost information: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_duration</code> - <?php esc_html_e('Show duration information: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_image_upload</code> - <?php esc_html_e('Show image upload field: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>redirect_url</code> - <?php esc_html_e('URL to redirect after submission (default: current page)', 'farmfaucet'); ?></li>
                </ul>
                <p><strong><?php esc_html_e('Example:', 'farmfaucet'); ?></strong> <code>[farmfaucet_ad_create title="Create Your Ad" button_text="Submit"]</code></p>
            </div>
        </div>
        
        <div class="shortcode-group">
            <h4><?php esc_html_e('User Advertisements List', 'farmfaucet'); ?></h4>
            <div class="shortcode-item">
                <code>[farmfaucet_ad_list]</code>
                <span class="shortcode-description"><?php esc_html_e('Displays a list of the current user\'s advertisements.', 'farmfaucet'); ?></span>
            </div>
            <div class="shortcode-params">
                <p><strong><?php esc_html_e('Parameters:', 'farmfaucet'); ?></strong></p>
                <ul>
                    <li><code>title</code> - <?php esc_html_e('List title (default: "My Advertisements")', 'farmfaucet'); ?></li>
                    <li><code>show_status</code> - <?php esc_html_e('Show status column: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_votes</code> - <?php esc_html_e('Show votes column: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_dates</code> - <?php esc_html_e('Show dates columns: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>show_actions</code> - <?php esc_html_e('Show action buttons: 1 or 0 (default: 1)', 'farmfaucet'); ?></li>
                    <li><code>limit</code> - <?php esc_html_e('Number of ads to display (default: 10)', 'farmfaucet'); ?></li>
                </ul>
                <p><strong><?php esc_html_e('Example:', 'farmfaucet'); ?></strong> <code>[farmfaucet_ad_list title="Your Advertisements" limit="5"]</code></p>
            </div>
        </div>
    </div>
</div>

<!-- Ad View Modal -->
<div id="farmfaucet-ad-modal" class="farmfaucet-modal" style="display: none;">
    <div class="farmfaucet-modal-content">
        <div class="farmfaucet-modal-header">
            <h3 class="modal-title"><?php esc_html_e('Advertisement Details', 'farmfaucet'); ?></h3>
            <span class="farmfaucet-modal-close">&times;</span>
        </div>
        <div class="farmfaucet-modal-body">
            <div id="ad-details-container">
                <!-- Ad details will be loaded here -->
            </div>
        </div>
        <div class="farmfaucet-modal-footer">
            <button class="button farmfaucet-modal-close"><?php esc_html_e('Close', 'farmfaucet'); ?></button>
        </div>
    </div>
</div>
