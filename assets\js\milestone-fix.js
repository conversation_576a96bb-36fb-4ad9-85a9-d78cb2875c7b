/**
 * Farm Faucet Milestone Display Fix
 * 
 * This script ensures that the milestone progress text is properly positioned
 * and styled in both card and compact views.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fix progress text positioning
    function fixProgressTextPositioning() {
        // Fix for card view
        const cardProgressTexts = document.querySelectorAll('.farmfaucet-milestone-progress-text');
        cardProgressTexts.forEach(function(text) {
            text.style.position = 'absolute';
            text.style.width = '100%';
            text.style.textAlign = 'center';
            text.style.color = '#fff';
            text.style.fontWeight = 'bold';
            text.style.lineHeight = '30px';
            text.style.fontSize = '1.1em';
            text.style.textShadow = '0 1px 2px rgba(0,0,0,0.5)';
            text.style.zIndex = '5';
            text.style.left = '0';
            text.style.top = '0';
            text.style.height = '100%';
            text.style.display = 'flex';
            text.style.alignItems = 'center';
            text.style.justifyContent = 'center';
            text.style.pointerEvents = 'none';
            text.style.margin = '0';
            text.style.padding = '0';
        });

        // Fix for compact view
        const compactProgressTexts = document.querySelectorAll('.compact-view .farmfaucet-milestone-progress-text');
        compactProgressTexts.forEach(function(text) {
            text.style.lineHeight = '24px';
            text.style.fontSize = '0.9em';
        });
    }

    // Run the fix immediately
    fixProgressTextPositioning();

    // Also run the fix after a short delay to ensure all elements are loaded
    setTimeout(fixProgressTextPositioning, 500);
});
