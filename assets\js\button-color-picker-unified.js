/**
 * Farm Faucet - Unified Button Color Picker
 *
 * This script provides a clean implementation of the button color picker
 * that works consistently across the admin panel.
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Unified Button Color Picker loaded');

        // Initialize color picker when the page loads
        initColorPicker();

        // Initialize color picker when the button dialog opens
        $(document).on('dialogopen', '#button-form-dialog', function() {
            setTimeout(initColorPicker, 100);
        });

        // Initialize color picker when editing a button
        $(document).on('click', '.edit-button', function() {
            setTimeout(initColorPicker, 500);
        });

        // Initialize color picker when adding a button
        $(document).on('click', '.add-button', function() {
            setTimeout(initColorPicker, 500);
        });

        // Fix button form submission to ensure color values are included
        $(document).on('submit', '#button-form', function() {
            // Make sure the color values are properly set
            var selectedColor = $('#button-color').val();
            var colorHex = $('#button-color-hex').val();

            console.log('Form submission - Color:', selectedColor, 'Hex:', colorHex);

            // If using a custom color but hex is empty, get it from the picker
            if (selectedColor === 'custom' && !colorHex) {
                colorHex = $('#button-custom-color').val();
                $('#button-color-hex').val(colorHex);
            }

            return true;
        });

        // Fix AJAX button saving
        $(document).ajaxSend(function(event, xhr, settings) {
            if (settings.data &&
                (settings.data.indexOf('farmfaucet_create_button') !== -1 ||
                 settings.data.indexOf('farmfaucet_update_button') !== -1)) {

                // Make sure the color values are properly set
                var selectedColor = $('#button-color').val();
                var colorHex = $('#button-color-hex').val();

                console.log('AJAX button save - Color:', selectedColor, 'Hex:', colorHex);

                // If using a custom color but hex is empty, get it from the picker
                if (selectedColor === 'custom' && !colorHex) {
                    colorHex = $('#button-custom-color').val();

                    // Append the hex color to the data
                    if (colorHex) {
                        settings.data += '&button_color_hex=' + encodeURIComponent(colorHex);
                    }
                }
            }
        });
    });

    /**
     * Initialize the color picker
     */
    function initColorPicker() {
        // Handle color swatch selection
        $(document).off('click', '.color-swatch-option').on('click', '.color-swatch-option', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $this = $(this);
            var value = $this.data('value');
            var name = $this.data('name');
            var color = $this.data('color');

            console.log('Color selected:', value, name, color);

            // Update hidden inputs
            $('#button-color').val(value);
            $('#button-color-hex').val('');

            // Update visual elements
            $('.color-swatch-option').removeClass('selected');
            $this.addClass('selected');
            $('.color-name-display').text(name);
            $('.color-preview').css('background-color', color);

            // Hide color grid
            $('.color-grid').removeClass('active').hide();

            // If this is the custom color option, show the custom color picker
            if (value === 'custom') {
                $('.custom-color-picker').show();

                // Initialize the custom color with the current preview color
                var currentColor = $('.color-preview').css('background-color');
                if (currentColor) {
                    // Convert RGB to hex if needed
                    if (currentColor.startsWith('rgb')) {
                        currentColor = rgbToHex(currentColor);
                    }
                    $('#button-custom-color').val(currentColor);
                    $('#button-color-hex').val(currentColor);
                }
            } else {
                $('.custom-color-picker').hide();
            }
        });

        // Handle custom color picker changes
        $(document).off('input change', '#button-custom-color').on('input change', '#button-custom-color', function() {
            var color = $(this).val();

            // Update hidden inputs
            $('#button-color').val('custom');
            $('#button-color-hex').val(color);

            // Update visual elements
            $('.color-swatch-option').removeClass('selected');
            $('.color-name-display').text('Custom');
            $('.color-preview').css('background-color', color);
        });

        // Toggle color grid on preview click
        $(document).off('click', '.color-preview, .color-select-wrapper').on('click', '.color-preview, .color-select-wrapper', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $grid = $(this).closest('.color-grid-container').find('.color-grid');

            // Close any other open color grids first
            $('.color-grid').not($grid).removeClass('active').hide();

            // Toggle this color grid
            $grid.toggleClass('active');

            if ($grid.hasClass('active')) {
                $grid.css('display', 'grid');
            } else {
                $grid.hide();
            }

            return false;
        });

        // Close color grid when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').removeClass('active').hide();
            }
        });

        // Fix color grid display
        $('.color-grid').each(function() {
            var $grid = $(this);
            $grid.css({
                'display': 'none',
                'grid-template-columns': 'repeat(8, 1fr)',
                'gap': '8px',
                'width': '320px',
                'z-index': '9999'
            });

            // Make sure active grids display as grid
            if ($grid.hasClass('active')) {
                $grid.css('display', 'grid');
            }
        });

        // Fix color preview display
        $('.color-preview').css({
            'width': '30px',
            'height': '30px',
            'min-width': '30px',
            'border-radius': '50%',
            'border': '1px solid rgba(0, 0, 0, 0.1)',
            'margin-left': '10px'
        });

        // Fix color name display
        $('.color-name-display').css({
            'flex': '1',
            'font-weight': '500',
            'min-width': '80px'
        });

        // Fix color select wrapper
        $('.color-select-wrapper').css({
            'display': 'flex',
            'align-items': 'center',
            'background': '#f0f0f0',
            'border': '1px solid #ddd',
            'border-radius': '4px',
            'padding': '8px 12px',
            'cursor': 'pointer'
        });

        // Make sure custom color picker is visible when needed
        if ($('#button-color').val() === 'custom') {
            $('.custom-color-picker').show();
        } else {
            $('.custom-color-picker').hide();
        }
    }

    /**
     * Convert RGB color to HEX
     *
     * @param {string} rgb RGB color string (e.g., "rgb(255, 0, 0)")
     * @return {string} HEX color string (e.g., "#ff0000")
     */
    function rgbToHex(rgb) {
        if (!rgb) {
            return '#4CAF50';
        }

        // Check if already a hex color
        if (rgb.charAt(0) === '#') {
            return rgb;
        }

        // Extract RGB values
        const rgbMatch = rgb.match(/^rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i);
        if (!rgbMatch) {
            return '#4CAF50';
        }

        const r = parseInt(rgbMatch[1], 10);
        const g = parseInt(rgbMatch[2], 10);
        const b = parseInt(rgbMatch[3], 10);

        // Convert to hex
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

})(jQuery);
