/**
 * Farm Faucet Milestone Compact View Fix
 * Fixes display issues with the milestone progress bar in compact view
 */

/* Progress bar container styles */
.farmfaucet-milestone-progress-container {
    position: relative;
    overflow: hidden;
    height: 30px;
    border-radius: 15px;
    background-color: #f0f0f0;
    margin: 10px 0;
    width: 100%;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

/* Compact view progress container */
.compact-view .farmfaucet-milestone-progress-container {
    height: 24px;
    border-radius: 12px;
}

/* Progress bar styles */
.farmfaucet-milestone-progress-bar {
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    transition: width 1.5s ease-in-out;
    min-width: 5px;
    z-index: 2;
}

/* Progress text styles */
.farmfaucet-milestone-progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    color: #fff;
    font-weight: bold;
    line-height: 30px;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    z-index: 5;
    left: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    margin: 0;
    padding: 0;
}

/* Compact view progress text */
.compact-view .farmfaucet-milestone-progress-text {
    line-height: 24px;
    font-size: 0.9em;
}

/* Transparent background styles */
.transparent-bg .farmfaucet-milestone-progress-container {
    background-color: rgba(240, 240, 240, 0.3);
    box-shadow: none;
}

/* Compact view specific styles */
.farmfaucet-milestone-container.compact-view {
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.compact-view .farmfaucet-milestone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.compact-view .farmfaucet-milestone-title {
    margin: 0;
    font-size: 1.1em;
}

/* Hide the percentage display in compact view since it's shown in the progress bar */
.compact-view .farmfaucet-milestone-percentage-display {
    display: none;
}

/* Milestone summary styles */
.compact-view .farmfaucet-milestone-summary {
    margin: 15px 0;
    padding: 10px;
    font-size: 0.95em;
    color: #555;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f9f9f9;
    border: 1px solid #eee;
}

/* Make summary background transparent when transparent background is enabled */
.transparent-bg.compact-view .farmfaucet-milestone-summary {
    background-color: transparent;
    border: none;
}

.compact-view .farmfaucet-milestone-summary:hover {
    background-color: #f0f0f0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Dropdown arrow styling */
.milestone-dropdown-arrow {
    font-size: 0.8em;
    opacity: 0.7;
    margin-left: 5px;
    display: inline-block;
    transition: transform 0.3s ease;
}

.show-tasks .milestone-dropdown-arrow {
    transform: rotate(180deg);
}

/* Task list styles */
.compact-view .farmfaucet-milestone-task-list {
    display: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.compact-view.show-tasks .farmfaucet-milestone-task-list {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
