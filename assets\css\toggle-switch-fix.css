/**
 * Farm Faucet - Toggle Switch Fix
 * Ensures consistent styling for all toggle switches in the admin panel
 */

/* Base toggle switch styling */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 10px;
    vertical-align: middle;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

/* Checked state - always green */
input:checked + .slider {
    background-color: #4CAF50 !important;
}

input:focus + .slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Toggle switch container */
.faucet-status-toggle {
    display: flex;
    align-items: center;
}

.faucet-status-label {
    font-weight: normal;
    margin-left: 5px;
}

/* Specific fixes for different toggle switches */
.farmfaucet-toggle-switch input:checked + .slider,
.farmfaucet-disable-toggle input:checked + .slider,
.farmfaucet-enable-toggle input:checked + .slider,
.faucet-enabled-toggle:checked + .slider {
    background-color: #4CAF50 !important;
}

/* Fix for task completion toggle switches */
.task-completion-toggle .switch input:checked + .slider {
    background-color: #4CAF50 !important;
}



/* Fix for any other toggle switches */
.toggle-switch input:checked + .slider,
.toggle-control input:checked + .slider {
    background-color: #4CAF50 !important;
}

/* Fix for faucet disable toggle */
.faucet-disable-toggle input:checked + .slider,
.faucet-enabled-toggle:checked + .slider {
    background-color: #4CAF50 !important;
}

/* Fix for specific toggle switches by ID */
#faucet-enabled-toggle:checked + .slider,
#task-completion-enabled-toggle:checked + .slider {
    background-color: #4CAF50 !important;
}

/* Fix for toggle switches in settings */
.settings-toggle input:checked + .slider {
    background-color: #4CAF50 !important;
}

/* Fix for all toggle switches with class */
.slider.round {
    border-radius: 34px;
}

input:checked + .slider.round {
    background-color: #4CAF50 !important;
}

/* Fix for disabled toggle switches */
.switch input:disabled + .slider {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Fix for toggle switch focus state */
.switch input:focus + .slider {
    box-shadow: 0 0 3px #4CAF50;
}

/* Fix for toggle switch hover state */
.slider:hover {
    background-color: #b3b3b3;
}

/* Fix for toggle switch active state */
.switch input:active + .slider:before {
    width: 18px;
}
