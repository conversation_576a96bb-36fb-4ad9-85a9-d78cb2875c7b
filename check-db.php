<?php
// Include WordPress core
require_once('wp-load.php');

// Get the database structure
global $wpdb;
$table_name = $wpdb->prefix . 'farmfaucet_faucets';

// Get table structure
$structure = $wpdb->get_results("DESCRIBE $table_name", ARRAY_A);

// Output the structure
echo "<h2>Table Structure: $table_name</h2>";
echo "<pre>";
print_r($structure);
echo "</pre>";

// Get a sample faucet
$faucet = $wpdb->get_row("SELECT * FROM $table_name LIMIT 1", ARRAY_A);

// Output the sample faucet
echo "<h2>Sample Faucet</h2>";
echo "<pre>";
print_r($faucet);
echo "</pre>";
