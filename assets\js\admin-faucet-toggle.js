/**
 * Farm Faucet - Admin Faucet Toggle
 * Handles the toggle switch functionality for enabling/disabling faucets
 */
jQuery(document).ready(function($) {
    // Handle toggle switch click
    $('.faucet-enabled-toggle').on('change', function() {
        const $toggle = $(this);
        const faucetId = $toggle.data('faucet-id');
        const isEnabled = $toggle.prop('checked') ? 1 : 0;
        const $statusLabel = $toggle.closest('.faucet-status-toggle').find('.faucet-status-label');
        
        // Show loading state
        $statusLabel.text(isEnabled ? 'Enabling...' : 'Disabling...');
        
        // Send AJAX request to toggle faucet status
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_toggle_faucet_status',
                faucet_id: faucetId,
                is_enabled: isEnabled,
                nonce: farmfaucet_admin_vars.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update status label
                    $statusLabel.text(isEnabled ? 'Enabled' : 'Disabled');
                    
                    // Show success message
                    farmfaucet_show_notification(response.data.message, 'success');
                } else {
                    // Revert toggle state
                    $toggle.prop('checked', !isEnabled);
                    $statusLabel.text(!isEnabled ? 'Enabled' : 'Disabled');
                    
                    // Show error message
                    farmfaucet_show_notification(response.data.message || 'An error occurred', 'error');
                }
            },
            error: function() {
                // Revert toggle state
                $toggle.prop('checked', !isEnabled);
                $statusLabel.text(!isEnabled ? 'Enabled' : 'Disabled');
                
                // Show error message
                farmfaucet_show_notification('Connection error', 'error');
            }
        });
    });
    
    // Helper function to show notifications
    function farmfaucet_show_notification(message, type) {
        const $notification = $('<div class="notice is-dismissible"></div>')
            .addClass(type === 'error' ? 'notice-error' : 'notice-success')
            .append($('<p></p>').text(message));
            
        // Add dismiss button
        const $button = $('<button type="button" class="notice-dismiss"></button>')
            .append($('<span class="screen-reader-text"></span>').text('Dismiss this notice.'));
            
        $notification.append($button);
        
        // Add to admin notices area
        $('.wrap > h1').after($notification);
        
        // Auto dismiss after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Handle dismiss button click
        $button.on('click', function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        });
    }
});
