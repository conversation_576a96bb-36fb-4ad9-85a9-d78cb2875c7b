/**
 * Farm Faucet Username Form Functionality (Improved Version)
 *
 * Handles username form display and submission with improved functionality
 */
(function($) {
    $(document).ready(function() {
        console.log('Username Form Improved loaded');

        // Initialize all username forms
        $('.farmfaucet-username-container').each(function() {
            initUsernameForm($(this));
        });

        /**
         * Initialize username form
         *
         * @param {jQuery} $container Form container
         */
        function initUsernameForm($container) {
            var $currentDisplay = $container.find('.farmfaucet-current-username');
            var $form = $container.find('.farmfaucet-username-form');
            var $editBtn = $container.find('.farmfaucet-username-edit-btn');
            var $cancelBtn = $container.find('.farmfaucet-username-cancel-link');
            var $input = $container.find('.farmfaucet-username-input');
            var $submitBtn = $container.find('.farmfaucet-username-submit');
            var $successMsg = $container.find('.farmfaucet-username-success');
            var $otpContainer = $container.find('.farmfaucet-otp-container');
            var $otpInput = $container.find('.farmfaucet-otp-input');
            var $sendOtpBtn = $container.find('.farmfaucet-send-otp-button');
            var $otpMessage = $container.find('.farmfaucet-otp-message');
            var currentName = $container.find('.farmfaucet-current-name-display').text().trim();

            // Store original name
            $container.data('original-name', currentName);

            // Initially hide the form and success message
            $form.hide();
            $successMsg.hide();

            // Handle OTP button click
            if ($sendOtpBtn.length) {
                $sendOtpBtn.on('click', function(e) {
                    e.preventDefault();

                    // Disable button during request
                    $sendOtpBtn.prop('disabled', true).text('Sending...');

                    // Send AJAX request to generate and send OTP
                    $.ajax({
                        url: farmfaucet_vars.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'farmfaucet_send_otp',
                            nonce: farmfaucet_vars.username_nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                $otpMessage.removeClass('error').addClass('success').text('OTP sent to your Telegram. Please check your messages.').fadeIn();
                                $otpInput.focus();

                                // Re-enable button after delay
                                setTimeout(function() {
                                    $sendOtpBtn.prop('disabled', false).text('Resend OTP');
                                }, 30000); // 30 seconds cooldown
                            } else {
                                $otpMessage.removeClass('success').addClass('error').text(response.data || 'Failed to send OTP').fadeIn();
                                $sendOtpBtn.prop('disabled', false).text('Send OTP');
                            }
                        },
                        error: function() {
                            $otpMessage.removeClass('success').addClass('error').text('Server error. Please try again.').fadeIn();
                            $sendOtpBtn.prop('disabled', false).text('Send OTP');
                        }
                    });
                });
            }

            // Show form when edit button is clicked
            $editBtn.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Edit button clicked');

                // Fade out current display and fade in form
                $currentDisplay.fadeOut(200, function() {
                    // Reset form state
                    $input.val(currentName);
                    $input.prop('disabled', false);
                    $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Save');
                    $successMsg.hide();

                    // Show form and focus input
                    $form.fadeIn(200, function() {
                        $input.focus();

                        // Select all text in the input
                        if ($input[0].setSelectionRange) {
                            $input[0].setSelectionRange(0, $input.val().length);
                        } else {
                            // Fallback for older browsers
                            $input.select();
                        }

                        console.log('Form shown, input focused');
                    });
                });
            });

            // Hide form when cancel link is clicked
            $cancelBtn.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Restore original value and hide form
                $input.val(currentName);
                $form.fadeOut(200, function() {
                    $currentDisplay.fadeIn(200);
                });
            });

            // Handle form submission
            $container.find('form').on('submit', function(e) {
                e.preventDefault();

                console.log('Form submitted');

                var newName = $input.val().trim();

                // Validate input
                if (newName === '') {
                    alert('Please enter a display name');
                    $input.focus();
                    return;
                }

                if (newName === currentName) {
                    // No change, just hide the form
                    $form.fadeOut(200, function() {
                        $currentDisplay.fadeIn(200);
                    });
                    return;
                }

                console.log('Updating display name from "' + currentName + '" to "' + newName + '"');

                // Store original button text
                if (!$submitBtn.data('original-text')) {
                    $submitBtn.data('original-text', $submitBtn.text());
                }

                // Disable form during submission
                $input.prop('disabled', true);
                $submitBtn.prop('disabled', true).text('Saving...');

                // Prepare data for AJAX request
                var requestData = {
                    action: 'farmfaucet_update_username',
                    nonce: farmfaucet_vars.username_nonce,
                    display_name: newName,
                    username: newName // Include both parameter names for compatibility
                };

                // Add OTP code if OTP field exists
                if ($otpInput.length) {
                    var otpCode = $otpInput.val().trim();
                    if (otpCode === '') {
                        alert('Please enter the OTP code sent to your Telegram');
                        $otpInput.focus();

                        // Re-enable form
                        $input.prop('disabled', false);
                        $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Save');
                        return;
                    }
                    requestData.otp_code = otpCode;
                }

                // Send AJAX request
                $.ajax({
                    url: farmfaucet_vars.ajaxurl,
                    type: 'POST',
                    data: requestData,
                    success: function(response) {
                        console.log('AJAX response:', response);

                        if (response.success) {
                            console.log('Display name updated successfully');

                            // Update current name display
                            currentName = newName;
                            $container.find('.farmfaucet-current-name-display').text(newName);
                            $container.data('original-name', newName);

                            // Update all other instances of the username on the page
                            $('.farmfaucet-username').text(newName);

                            // Show success message with improved visibility
                            $successMsg.css({
                                'display': 'block',
                                'opacity': 0
                            }).animate({
                                'opacity': 1
                            }, 300);
                            console.log('Success message shown');

                            // Hide form and show current display after a delay
                            setTimeout(function() {
                                $successMsg.animate({
                                    'opacity': 0
                                }, 300, function() {
                                    $(this).hide();
                                });

                                $form.fadeOut(200, function() {
                                    $currentDisplay.fadeIn(200);
                                    console.log('Form hidden, display shown');
                                });
                            }, 2000);
                        } else {
                            console.error('Error updating display name:', response.data);
                            alert(response.data || 'Error updating display name');

                            // Re-enable form
                            $input.prop('disabled', false);
                            $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Save');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', status, error);
                        alert('Error communicating with server: ' + error);

                        // Re-enable form
                        $input.prop('disabled', false);
                        $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Save');
                    }
                });
            });

            // Handle Escape key to cancel
            $input.on('keydown', function(e) {
                if (e.keyCode === 27) { // Escape key
                    e.preventDefault();
                    $cancelBtn.click();
                } else if (e.keyCode === 13) { // Enter key
                    e.preventDefault();
                    $submitBtn.click();
                }
            });
        }
    });
})(jQuery);
