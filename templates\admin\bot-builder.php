<?php
/**
 * Telegram Bot Builder Admin Template
 *
 * @package Farmfaucet
 * @since 2.5
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Get existing bots
require_once(FARMFAUCET_DIR . 'includes/class-farmfaucet-tg-bot-db.php');
$bots = Farmfaucet_Tg_Bot_DB::get_bots();
?>

<div class="farmfaucet-tg-bot-builder-container">
    <!-- Welcome Card -->
    <div class="farmfaucet-card welcome-card">
        <div class="card-header">
            <h3><?php esc_html_e('Telegram Bot Builder', 'farmfaucet'); ?></h3>
        </div>
        <div class="card-body">
            <p><?php esc_html_e('Create and manage Telegram bots for your faucet. Connect with your users through Telegram and provide automated responses, notifications, and more.', 'farmfaucet'); ?></p>
            
            <div class="tutorial-section">
                <h4><?php esc_html_e('Getting Started', 'farmfaucet'); ?></h4>
                <ol>
                    <li><?php esc_html_e('Create a new bot using BotFather on Telegram', 'farmfaucet'); ?></li>
                    <li><?php esc_html_e('Copy the bot token provided by BotFather', 'farmfaucet'); ?></li>
                    <li><?php esc_html_e('Click "Create New Bot" and paste the token', 'farmfaucet'); ?></li>
                    <li><?php esc_html_e('Configure your bot commands and responses', 'farmfaucet'); ?></li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Bot Management Section -->
    <div class="farmfaucet-card bot-management-card">
        <div class="card-header">
            <h3><?php esc_html_e('Bot Management', 'farmfaucet'); ?></h3>
            <button id="create-new-bot" class="button button-primary">
                <span class="dashicons dashicons-plus"></span> <?php esc_html_e('Create New Bot', 'farmfaucet'); ?>
            </button>
        </div>
        <div class="card-body">
            <?php if (empty($bots)) : ?>
                <div class="no-bots-message">
                    <p><?php esc_html_e('No bots found. Create your first bot to get started!', 'farmfaucet'); ?></p>
                </div>
            <?php else : ?>
                <div id="bots-tabs" class="bots-tabs-container">
                    <ul class="bots-tabs-nav">
                        <?php foreach ($bots as $bot) : ?>
                            <li>
                                <a href="#bot-<?php echo esc_attr($bot['id']); ?>">
                                    <?php echo esc_html($bot['bot_name']); ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>

                    <?php foreach ($bots as $bot) : ?>
                        <div id="bot-<?php echo esc_attr($bot['id']); ?>" class="bot-tab-content" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                            <div class="bot-details">
                                <h4><?php echo esc_html($bot['bot_name']); ?></h4>
                                
                                <div class="bot-info">
                                    <div class="bot-info-item">
                                        <strong><?php esc_html_e('Username:', 'farmfaucet'); ?></strong>
                                        <span>@<?php echo esc_html($bot['bot_username']); ?></span>
                                    </div>
                                    <div class="bot-info-item">
                                        <strong><?php esc_html_e('Type:', 'farmfaucet'); ?></strong>
                                        <span><?php echo esc_html(ucfirst($bot['bot_type'])); ?></span>
                                    </div>
                                    <div class="bot-info-item">
                                        <strong><?php esc_html_e('Token:', 'farmfaucet'); ?></strong>
                                        <span>••••••••<?php echo esc_html(substr($bot['bot_token'], -4)); ?></span>
                                    </div>
                                    <div class="bot-info-item bot-status-toggle">
                                        <strong><?php esc_html_e('Status:', 'farmfaucet'); ?></strong>
                                        <span>
                                            <label class="switch">
                                                <input type="checkbox" class="bot-enabled-toggle"
                                                    data-bot-id="<?php echo esc_attr($bot['id']); ?>"
                                                    <?php checked($bot['is_active']); ?>>
                                                <span class="slider round"></span>
                                            </label>
                                            <span class="bot-status-label">
                                                <?php echo $bot['is_active'] ? esc_html__('Enabled', 'farmfaucet') : esc_html__('Disabled', 'farmfaucet'); ?>
                                            </span>
                                        </span>
                                    </div>
                                </div>

                                <div class="bot-actions">
                                    <button class="button edit-bot" data-id="<?php echo esc_attr($bot['id']); ?>">
                                        <span class="dashicons dashicons-edit"></span> <?php esc_html_e('Edit', 'farmfaucet'); ?>
                                    </button>
                                    <button class="button button-link-delete delete-bot" data-id="<?php echo esc_attr($bot['id']); ?>">
                                        <span class="dashicons dashicons-trash"></span> <?php esc_html_e('Delete', 'farmfaucet'); ?>
                                    </button>
                                </div>
                            </div>

                            <!-- Bot Commands Section -->
                            <div class="bot-commands-section">
                                <div class="commands-header">
                                    <h4><?php esc_html_e('Bot Commands', 'farmfaucet'); ?></h4>
                                    <button class="button add-command" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                        <span class="dashicons dashicons-plus"></span> <?php esc_html_e('Add Command', 'farmfaucet'); ?>
                                    </button>
                                </div>
                                
                                <div class="commands-container">
                                    <?php
                                    $commands = Farmfaucet_Tg_Bot_DB::get_bot_commands($bot['id']);
                                    if (empty($commands)) :
                                    ?>
                                        <div class="no-commands-message">
                                            <p><?php esc_html_e('No commands found. Add your first command to get started!', 'farmfaucet'); ?></p>
                                        </div>
                                    <?php else : ?>
                                        <div class="commands-list">
                                            <?php foreach ($commands as $command) : ?>
                                                <div class="command-item" data-id="<?php echo esc_attr($command['id']); ?>">
                                                    <div class="command-header">
                                                        <h5>/<?php echo esc_html($command['command']); ?></h5>
                                                        <div class="command-actions">
                                                            <button class="button edit-command" data-id="<?php echo esc_attr($command['id']); ?>" title="<?php esc_attr_e('Edit Command', 'farmfaucet'); ?>">
                                                                <span class="dashicons dashicons-edit"></span>
                                                            </button>
                                                            <button class="button button-link-delete delete-command" data-id="<?php echo esc_attr($command['id']); ?>" title="<?php esc_attr_e('Delete Command', 'farmfaucet'); ?>">
                                                                <span class="dashicons dashicons-trash"></span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="command-description">
                                                        <?php echo esc_html($command['description']); ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Bot Flow Builder Section -->
                            <div class="bot-flow-builder-section">
                                <div class="flow-builder-header">
                                    <h4><?php esc_html_e('Flow Builder', 'farmfaucet'); ?></h4>
                                    <button class="button open-flow-builder" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                        <span class="dashicons dashicons-editor-kitchensink"></span> <?php esc_html_e('Open Flow Builder', 'farmfaucet'); ?>
                                    </button>
                                </div>
                            </div>

                            <!-- Bot Testing Section -->
                            <div class="bot-testing-section">
                                <div class="testing-header">
                                    <h4><?php esc_html_e('Bot Testing', 'farmfaucet'); ?></h4>
                                </div>
                                <div class="chat-simulator">
                                    <div class="chat-messages">
                                        <div class="bot-message">
                                            <div class="message-avatar">
                                                <img src="<?php echo esc_url(FARMFAUCET_URL . 'assets/images/bot-avatar.png'); ?>" alt="Bot">
                                            </div>
                                            <div class="message-content">
                                                <p><?php esc_html_e('Hello! I am your bot. Type a command to get started.', 'farmfaucet'); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-input">
                                        <input type="text" class="test-command-input" placeholder="<?php esc_attr_e('Type a command...', 'farmfaucet'); ?>">
                                        <button class="button send-test-command" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-arrow-right-alt2"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Bot Creation/Edit Dialog -->
<div id="bot-dialog" class="farmfaucet-dialog" title="<?php esc_attr_e('Bot Details', 'farmfaucet'); ?>" style="display:none;">
    <form id="bot-form">
        <input type="hidden" id="bot-id" name="bot_id" value="0">
        
        <div class="form-group">
            <label for="bot-name"><?php esc_html_e('Bot Name', 'farmfaucet'); ?></label>
            <input type="text" id="bot-name" name="bot_name" required>
        </div>
        
        <div class="form-group">
            <label for="bot-token"><?php esc_html_e('Bot Token', 'farmfaucet'); ?></label>
            <input type="text" id="bot-token" name="bot_token" required>
            <button type="button" id="test-token" class="button"><?php esc_html_e('Test Token', 'farmfaucet'); ?></button>
            <div id="token-status"></div>
        </div>
        
        <div class="form-group">
            <label for="bot-username"><?php esc_html_e('Bot Username', 'farmfaucet'); ?></label>
            <input type="text" id="bot-username" name="bot_username" required>
        </div>
        
        <div class="form-group">
            <label for="bot-type"><?php esc_html_e('Bot Type', 'farmfaucet'); ?></label>
            <select id="bot-type" name="bot_type" required>
                <option value="standard"><?php esc_html_e('Standard Bot', 'farmfaucet'); ?></option>
                <option value="login"><?php esc_html_e('Login Bot', 'farmfaucet'); ?></option>
                <option value="notification"><?php esc_html_e('Notification Bot', 'farmfaucet'); ?></option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="webhook-url"><?php esc_html_e('Webhook URL (Optional)', 'farmfaucet'); ?></label>
            <input type="url" id="webhook-url" name="webhook_url">
        </div>
        
        <div class="form-group">
            <label for="bot-active"><?php esc_html_e('Active', 'farmfaucet'); ?></label>
            <label class="switch">
                <input type="checkbox" id="bot-active" name="is_active" checked>
                <span class="slider round"></span>
            </label>
        </div>
    </form>
</div>

<!-- Command Creation/Edit Dialog -->
<div id="command-dialog" class="farmfaucet-dialog" title="<?php esc_attr_e('Command Details', 'farmfaucet'); ?>" style="display:none;">
    <form id="command-form">
        <input type="hidden" id="command-id" name="command_id" value="0">
        <input type="hidden" id="command-bot-id" name="bot_id" value="0">
        
        <div class="form-group">
            <label for="command-name"><?php esc_html_e('Command', 'farmfaucet'); ?></label>
            <div class="command-input-wrapper">
                <span class="command-prefix">/</span>
                <input type="text" id="command-name" name="command" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="command-description"><?php esc_html_e('Description', 'farmfaucet'); ?></label>
            <input type="text" id="command-description" name="description" required>
        </div>
        
        <div class="form-group">
            <label for="command-response"><?php esc_html_e('Response Text', 'farmfaucet'); ?></label>
            <textarea id="command-response" name="response" rows="5" required></textarea>
        </div>
        
        <div class="form-group">
            <label><?php esc_html_e('Response Type', 'farmfaucet'); ?></label>
            <div class="response-type-options">
                <label>
                    <input type="radio" name="response_type" value="text" checked>
                    <?php esc_html_e('Text', 'farmfaucet'); ?>
                </label>
                <label>
                    <input type="radio" name="response_type" value="buttons">
                    <?php esc_html_e('Buttons', 'farmfaucet'); ?>
                </label>
                <label>
                    <input type="radio" name="response_type" value="inline_buttons">
                    <?php esc_html_e('Inline Buttons', 'farmfaucet'); ?>
                </label>
            </div>
        </div>
        
        <div id="buttons-container" class="form-group" style="display:none;">
            <label><?php esc_html_e('Buttons', 'farmfaucet'); ?></label>
            <div class="buttons-list">
                <div class="button-item">
                    <input type="text" name="button_text[]" placeholder="<?php esc_attr_e('Button Text', 'farmfaucet'); ?>">
                    <input type="text" name="button_url[]" placeholder="<?php esc_attr_e('URL or Command', 'farmfaucet'); ?>">
                    <button type="button" class="remove-button button-link-delete">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </div>
            <button type="button" id="add-button" class="button">
                <span class="dashicons dashicons-plus"></span> <?php esc_html_e('Add Button', 'farmfaucet'); ?>
            </button>
        </div>
    </form>
</div>

<!-- Flow Builder Dialog -->
<div id="flow-builder-dialog" class="farmfaucet-dialog flow-builder-dialog" title="<?php esc_attr_e('Flow Builder', 'farmfaucet'); ?>" style="display:none;">
    <div class="flow-builder-container">
        <div class="flow-builder-sidebar">
            <h4><?php esc_html_e('Elements', 'farmfaucet'); ?></h4>
            <div class="flow-elements">
                <div class="flow-element" data-type="message">
                    <span class="dashicons dashicons-format-chat"></span>
                    <span><?php esc_html_e('Message', 'farmfaucet'); ?></span>
                </div>
                <div class="flow-element" data-type="command">
                    <span class="dashicons dashicons-editor-code"></span>
                    <span><?php esc_html_e('Command', 'farmfaucet'); ?></span>
                </div>
                <div class="flow-element" data-type="condition">
                    <span class="dashicons dashicons-randomize"></span>
                    <span><?php esc_html_e('Condition', 'farmfaucet'); ?></span>
                </div>
                <div class="flow-element" data-type="action">
                    <span class="dashicons dashicons-admin-generic"></span>
                    <span><?php esc_html_e('Action', 'farmfaucet'); ?></span>
                </div>
            </div>
        </div>
        <div class="flow-builder-canvas">
            <div class="flow-canvas" id="flow-canvas"></div>
        </div>
    </div>
    <div class="flow-builder-actions">
        <button type="button" id="save-flow" class="button button-primary"><?php esc_html_e('Save Flow', 'farmfaucet'); ?></button>
        <button type="button" id="cancel-flow" class="button"><?php esc_html_e('Cancel', 'farmfaucet'); ?></button>
    </div>
</div>
