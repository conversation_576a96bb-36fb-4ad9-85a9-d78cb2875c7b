<?php

/**
 * <PERSON>les database updates for new faucet types
 */
class Farmfaucet_DB_Updater_Faucet_Types
{
    /**
     * Initialize the updater
     */
    public static function init()
    {
        $instance = new self();
        $instance->update_database();
    }

    /**
     * Update database structure for new faucet types
     */
    public function update_database()
    {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Update faucets table to add new columns
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
        $this->add_column_if_not_exists($faucets_table, 'faucet_type', "VARCHAR(20) NOT NULL DEFAULT 'stage'");
        $this->add_column_if_not_exists($faucets_table, 'currency_id', "BIGINT(20) DEFAULT NULL");
        $this->add_column_if_not_exists($faucets_table, 'task_code', "TEXT DEFAULT NULL");
        $this->add_column_if_not_exists($faucets_table, 'min_withdrawal', "DECIMAL(18,8) DEFAULT 0");
        $this->add_column_if_not_exists($faucets_table, 'available_currencies', "TEXT DEFAULT NULL");
        $this->add_column_if_not_exists($faucets_table, 'view_style', "VARCHAR(20) DEFAULT 'default'");

        // Create withdrawals table if it doesn't exist
        $withdrawals_table = $wpdb->prefix . 'farmfaucet_withdrawals';
        $withdrawals_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$withdrawals_table}'") === $withdrawals_table;

        if (!$withdrawals_table_exists) {
            $sql = "CREATE TABLE {$withdrawals_table} (
                id BIGINT(20) NOT NULL AUTO_INCREMENT,
                user_id BIGINT(20) NOT NULL,
                faucet_id BIGINT(20) NOT NULL,
                currency_id BIGINT(20) NOT NULL,
                amount DECIMAL(18,8) NOT NULL,
                withdrawal_currency VARCHAR(20) NOT NULL,
                wallet_address VARCHAR(255) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                transaction_id VARCHAR(255) DEFAULT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY faucet_id (faucet_id),
                KEY currency_id (currency_id),
                KEY status (status)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }

        // Update existing faucets to set type as 'stage'
        $wpdb->query("UPDATE {$faucets_table} SET faucet_type = 'stage' WHERE faucet_type = '' OR faucet_type IS NULL");
    }

    /**
     * Helper function to add a column if it doesn't exist
     */
    private function add_column_if_not_exists($table, $column, $definition)
    {
        global $wpdb;
        
        $check_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = %s",
            DB_NAME,
            $table,
            $column
        ));
        
        if (empty($check_column)) {
            $wpdb->query("ALTER TABLE {$table} ADD COLUMN {$column} {$definition}");
            error_log("Added column {$column} to {$table}");
        }
    }
}
