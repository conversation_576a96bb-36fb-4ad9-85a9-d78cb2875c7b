/**
 * Telegram <PERSON>t Login Settings Fix
 * 
 * This script fixes issues with the Telegram Bot Login settings
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Telegram Bot Login Settings Fix loaded');
        initTgBotLoginSettingsFix();
    });

    /**
     * Initialize Telegram Bot Login Settings Fix
     */
    function initTgBotLoginSettingsFix() {
        // Toggle Telegram login fields
        $('#enable-tg-login').on('change', function() {
            if ($(this).is(':checked')) {
                $('.tg-login-field').fadeIn(300);
            } else {
                $('.tg-login-field').fadeOut(300);
            }
        });

        // Save login form settings
        $('.save-login-settings').on('click', function() {
            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update-alt spin"></span> Saving...');
            $button.prop('disabled', true);

            // Get form data
            const loginPageUrl = $('#login-page-url').val();
            const registerPageUrl = $('#register-page-url').val();
            const forgotPasswordUrl = $('#forgot-password-url').val();
            const profilePageUrl = $('#profile-page-url').val();
            const enableTgLogin = $('#enable-tg-login').is(':checked') ? 1 : 0;
            const tgLoginBot = $('#tg-login-bot').val();
            const tgOtpExpiration = $('#tg-otp-expiration').val() || 5;

            // Send AJAX request
            $.ajax({
                url: farmfaucetTgBotBuilder.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'farmfaucet_save_login_form_settings',
                    nonce: farmfaucetTgBotBuilder.nonce,
                    login_page_url: loginPageUrl,
                    register_page_url: registerPageUrl,
                    forgot_password_url: forgotPasswordUrl,
                    profile_page_url: profilePageUrl,
                    enable_tg_login: enableTgLogin,
                    tg_login_bot: tgLoginBot,
                    tg_otp_expiration: tgOtpExpiration
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        showNotification('Login form settings saved successfully.', 'success');
                    } else {
                        // Show error message
                        showNotification(response.data.message || 'Error saving login form settings.', 'error');
                    }
                },
                error: function() {
                    // Show error message
                    showNotification('Error saving login form settings. Please try again.', 'error');
                },
                complete: function() {
                    // Restore button
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Show notification message
     */
    function showNotification(message, type) {
        // Remove existing notifications
        $('.farmfaucet-notification').remove();

        // Create notification element
        const $notification = $('<div class="farmfaucet-notification ' + type + '">' +
            '<div class="notification-content">' + message + '</div>' +
            '<button class="notification-close">&times;</button>' +
            '</div>');

        // Add to body
        $('body').append($notification);

        // Show notification with animation
        setTimeout(() => {
            $notification.addClass('show');
        }, 10);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            $notification.removeClass('show');
            setTimeout(() => {
                $notification.remove();
            }, 300);
        }, 5000);

        // Close button
        $notification.find('.notification-close').on('click', function() {
            $notification.removeClass('show');
            setTimeout(() => {
                $notification.remove();
            }, 300);
        });
    }
})(jQuery);
