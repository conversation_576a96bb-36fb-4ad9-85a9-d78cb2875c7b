/**
 * Farm Faucet Milestone Page Selector
 * 
 * Handles the milestone page selection functionality in the button form
 */
jQuery(document).ready(function($) {
    // Initialize Select2 for multiple select fields if available
    if ($.fn.select2) {
        $('.farmfaucet-select2').select2({
            placeholder: 'Select pages',
            allowClear: true,
            width: '100%'
        });
    }

    // Toggle milestone type specific fields
    $('#milestone-type').on('change', function() {
        if ($(this).val() === 'page_specific') {
            $('.milestone-pages-field').slideDown();
            $('.milestone-specific-faucet-field').slideUp();
        } else if ($(this).val() === 'specific_faucet') {
            $('.milestone-pages-field').slideUp();
            $('.milestone-specific-faucet-field').slideDown();
        } else {
            $('.milestone-pages-field, .milestone-specific-faucet-field').slideUp();
        }
    });

    // Initialize milestone type fields on page load
    function initMilestoneFields() {
        var milestoneType = $('#milestone-type').val();
        
        if (milestoneType === 'page_specific') {
            $('.milestone-pages-field').show();
            $('.milestone-specific-faucet-field').hide();
        } else if (milestoneType === 'specific_faucet') {
            $('.milestone-pages-field').hide();
            $('.milestone-specific-faucet-field').show();
        } else {
            $('.milestone-pages-field, .milestone-specific-faucet-field').hide();
        }
    }

    // Add page to milestone pages
    $('#add-milestone-page').on('click', function(e) {
        e.preventDefault();
        
        var pageTemplate = $('.milestone-page-template').clone();
        pageTemplate.removeClass('milestone-page-template').addClass('milestone-page-item');
        pageTemplate.find('select').prop('disabled', false);
        pageTemplate.appendTo('.milestone-pages-list');
        pageTemplate.show();
        
        // Initialize Select2 on the new select element if available
        if ($.fn.select2) {
            pageTemplate.find('select').select2({
                placeholder: 'Select a page',
                allowClear: true,
                width: '100%'
            });
        }
    });

    // Remove page from milestone pages
    $(document).on('click', '.remove-milestone-page', function(e) {
        e.preventDefault();
        $(this).closest('.milestone-page-item').remove();
    });

    // Load website pages for milestone selection
    function loadWebsitePages() {
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_website_pages',
                nonce: farmfaucet_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    populatePageDropdowns(response.data);
                }
            }
        });
    }

    // Populate page dropdowns with website pages
    function populatePageDropdowns(pages) {
        var options = '<option value="">' + farmfaucet_admin.strings.select_page + '</option>';
        
        $.each(pages, function(index, page) {
            options += '<option value="' + page.id + '" data-url="' + page.url + '">' + page.title + '</option>';
        });
        
        $('.milestone-page-select').each(function() {
            var currentVal = $(this).val();
            $(this).html(options);
            
            if (currentVal) {
                $(this).val(currentVal);
            }
        });
        
        // Initialize Select2 on all page selects if available
        if ($.fn.select2) {
            $('.milestone-page-select').select2({
                placeholder: 'Select a page',
                allowClear: true,
                width: '100%'
            });
        }
    }

    // Initialize milestone fields and load website pages on page load
    initMilestoneFields();
    loadWebsitePages();
});
