<?php

/**
 * Telegram Bot Database Handler for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.3
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Tg_Bot_DB
 *
 * Handles database operations for Telegram Bot Builder
 */
class Farmfaucet_Tg_Bot_DB
{

    /**
     * Initialize database tables
     */
    public static function init_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Bots table
        $table_bots = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Flows table
        $table_flows = $wpdb->prefix . 'farmfaucet_tg_flows';

        // Commands table
        $table_commands = $wpdb->prefix . 'farmfaucet_tg_commands';

        // Analytics table
        $table_analytics = $wpdb->prefix . 'farmfaucet_tg_analytics';

        // Moderation logs table
        $table_moderation = $wpdb->prefix . 'farmfaucet_tg_moderation';

        // Create bots table
        $sql_bots = "CREATE TABLE $table_bots (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_name varchar(100) NOT NULL,
            bot_token varchar(255) NOT NULL,
            bot_username varchar(100) NOT NULL,
            bot_type varchar(20) NOT NULL DEFAULT 'text',
            webhook_url varchar(255) DEFAULT '',
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            settings longtext DEFAULT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            UNIQUE KEY bot_token (bot_token)
        ) $charset_collate;";

        // Create flows table
        $sql_flows = "CREATE TABLE $table_flows (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            flow_name varchar(100) NOT NULL,
            flow_data longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id)
        ) $charset_collate;";

        // Create commands table
        $sql_commands = "CREATE TABLE $table_commands (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            command_name varchar(100) NOT NULL,
            command_description varchar(255) DEFAULT '',
            command_response longtext NOT NULL,
            buttons longtext DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id)
        ) $charset_collate;";

        // Create analytics table
        $sql_analytics = "CREATE TABLE $table_analytics (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data longtext DEFAULT NULL,
            user_id varchar(50) DEFAULT NULL,
            chat_id varchar(50) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY chat_id (chat_id)
        ) $charset_collate;";

        // Create moderation logs table
        $sql_moderation = "CREATE TABLE $table_moderation (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            action_type varchar(50) NOT NULL,
            user_id varchar(50) NOT NULL,
            chat_id varchar(50) NOT NULL,
            reason text DEFAULT NULL,
            duration int(11) DEFAULT 0,
            admin_id varchar(50) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id),
            KEY action_type (action_type),
            KEY user_id (user_id),
            KEY chat_id (chat_id)
        ) $charset_collate;";

        // Include WordPress database upgrade functions
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Create the tables
        dbDelta($sql_bots);
        dbDelta($sql_flows);
        dbDelta($sql_commands);
        dbDelta($sql_analytics);
        dbDelta($sql_moderation);
    }

    /**
     * Get all bots
     *
     * @return array Array of bot data
     */
    public static function get_bots()
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_bots';

        $bots = $wpdb->get_results("SELECT * FROM $table ORDER BY id DESC", ARRAY_A);

        // Decrypt bot tokens
        if ($bots) {
            foreach ($bots as &$bot) {
                $bot['bot_token'] = Farmfaucet_Security::decrypt_api_key($bot['bot_token']);
            }
        }

        return $bots ?: [];
    }

    /**
     * Get a single bot by ID
     *
     * @param int $bot_id Bot ID
     * @return array|null Bot data or null if not found
     */
    public static function get_bot($bot_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_bots';

        $bot = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE id = %d", $bot_id),
            ARRAY_A
        );

        if ($bot) {
            $bot['bot_token'] = Farmfaucet_Security::decrypt_api_key($bot['bot_token']);
            return $bot;
        }

        return null;
    }

    /**
     * Add a new bot
     *
     * @param array $bot_data Bot data
     * @return int|false The bot ID on success, false on failure
     */
    public static function add_bot($bot_data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Encrypt bot token
        $bot_data['bot_token'] = Farmfaucet_Security::encrypt_api_key($bot_data['bot_token']);

        // Insert bot
        $result = $wpdb->insert(
            $table,
            $bot_data,
            [
                '%s', // bot_name
                '%s', // bot_token
                '%s', // bot_username
                '%s', // bot_type
                '%s', // webhook_url
                '%s', // created_at
                '%s', // updated_at
                '%s', // settings
                '%d'  // is_active
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update a bot
     *
     * @param int $bot_id Bot ID
     * @param array $bot_data Bot data
     * @return bool True on success, false on failure
     */
    public static function update_bot($bot_id, $bot_data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Encrypt bot token if provided
        if (isset($bot_data['bot_token'])) {
            $bot_data['bot_token'] = Farmfaucet_Security::encrypt_api_key($bot_data['bot_token']);
        }

        // Update bot
        $result = $wpdb->update(
            $table,
            $bot_data,
            ['id' => $bot_id],
            [
                '%s', // bot_name
                '%s', // bot_token
                '%s', // bot_username
                '%s', // bot_type
                '%s', // webhook_url
                '%s', // settings
                '%d'  // is_active
            ],
            ['%d'] // id
        );

        return $result !== false;
    }

    /**
     * Delete a bot
     *
     * @param int $bot_id Bot ID
     * @return bool True on success, false on failure
     */
    public static function delete_bot($bot_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Delete bot
        $result = $wpdb->delete(
            $table,
            ['id' => $bot_id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get all flows for a bot
     *
     * @param int $bot_id Bot ID
     * @return array Array of flow data
     */
    public static function get_flows($bot_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_flows';

        $flows = $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $table WHERE bot_id = %d ORDER BY id DESC", $bot_id),
            ARRAY_A
        );

        return $flows ?: [];
    }

    /**
     * Get a single flow by ID
     *
     * @param int $flow_id Flow ID
     * @return array|null Flow data or null if not found
     */
    public static function get_flow($flow_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_flows';

        $flow = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE id = %d", $flow_id),
            ARRAY_A
        );

        return $flow ?: null;
    }

    /**
     * Add a new flow
     *
     * @param array $flow_data Flow data
     * @return int|false The flow ID on success, false on failure
     */
    public static function add_flow($flow_data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_flows';

        // Insert flow
        $result = $wpdb->insert(
            $table,
            $flow_data,
            [
                '%d', // bot_id
                '%s', // flow_name
                '%s', // flow_data
                '%s', // created_at
                '%s', // updated_at
                '%d'  // is_active
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update a flow
     *
     * @param int $flow_id Flow ID
     * @param array $flow_data Flow data
     * @return bool True on success, false on failure
     */
    public static function update_flow($flow_id, $flow_data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_flows';

        // Update flow
        $result = $wpdb->update(
            $table,
            $flow_data,
            ['id' => $flow_id],
            [
                '%d', // bot_id
                '%s', // flow_name
                '%s', // flow_data
                '%d'  // is_active
            ],
            ['%d'] // id
        );

        return $result !== false;
    }

    /**
     * Delete a flow
     *
     * @param int $flow_id Flow ID
     * @return bool True on success, false on failure
     */
    public static function delete_flow($flow_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_flows';

        // Delete flow
        $result = $wpdb->delete(
            $table,
            ['id' => $flow_id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get all commands for a bot
     *
     * @param int $bot_id Bot ID
     * @return array Commands
     */
    public static function get_commands($bot_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_commands';

        $commands = $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $table WHERE bot_id = %d ORDER BY id DESC", $bot_id),
            ARRAY_A
        );

        return $commands ?: [];
    }

    /**
     * Get command
     *
     * @param int $command_id Command ID
     * @return array|null Command data or null if not found
     */
    public static function get_command($command_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_commands';

        $command = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE id = %d", $command_id),
            ARRAY_A
        );

        return $command ?: null;
    }

    /**
     * Add command
     *
     * @param array $data Command data
     * @return int|false Command ID or false on failure
     */
    public static function add_command($data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_commands';

        // Prepare data for insertion
        $insert_data = [
            'bot_id' => $data['bot_id'],
            'command_name' => $data['command_trigger'],
            'command_description' => $data['command_description'],
            'command_response' => $data['response_data'],
            'is_active' => $data['is_active']
        ];

        $result = $wpdb->insert(
            $table,
            $insert_data,
            [
                '%d', // bot_id
                '%s', // command_name
                '%s', // command_description
                '%s', // command_response
                '%d'  // is_active
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update command
     *
     * @param int $command_id Command ID
     * @param array $data Command data
     * @return bool True on success, false on failure
     */
    public static function update_command($command_id, $data)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_commands';

        // Prepare data for update
        $update_data = [
            'bot_id' => $data['bot_id'],
            'command_name' => $data['command_trigger'],
            'command_description' => $data['command_description'],
            'command_response' => $data['response_data'],
            'is_active' => $data['is_active']
        ];

        $result = $wpdb->update(
            $table,
            $update_data,
            ['id' => $command_id],
            [
                '%d', // bot_id
                '%s', // command_name
                '%s', // command_description
                '%s', // command_response
                '%d'  // is_active
            ],
            ['%d'] // id
        );

        return $result !== false;
    }

    /**
     * Delete command
     *
     * @param int $command_id Command ID
     * @return bool True on success, false on failure
     */
    public static function delete_command($command_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'farmfaucet_tg_commands';

        $result = $wpdb->delete(
            $table,
            ['id' => $command_id],
            ['%d']
        );

        return $result !== false;
    }
}
