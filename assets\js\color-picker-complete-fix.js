/**
 * Farm Faucet - Complete Color Picker Fix
 *
 * This script completely replaces all previous color picker implementations
 * to ensure consistent behavior across the admin panel.
 */
(function($) {
    'use strict';

    // Store the original button form HTML when the page loads
    var originalButtonFormHtml = '';

    $(document).ready(function() {
        console.log('Complete Color Picker Fix loaded');

        // Store the original button form HTML
        originalButtonFormHtml = $('#button-form-dialog').html();

        // Initialize color pickers
        initColorPickers();

        // Override button edit handler
        setupButtonEditHandler();

        // Override button add handler
        setupButtonAddHandler();

        // Override save button function
        overrideSaveButtonFunction();
    });

    /**
     * Initialize all color pickers on the page
     */
    function initColorPickers() {
        // Remove any existing event handlers to prevent conflicts
        $(document).off('click', '.color-preview, .color-select-wrapper');
        $(document).off('click', '.color-swatch-option');

        // Toggle color grid when clicking on the preview or wrapper
        $(document).on('click', '.color-preview, .color-select-wrapper', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $container = $(this).closest('.color-grid-container');
            var $grid = $container.find('.color-grid');

            // Close all other color grids
            $('.color-grid').not($grid).removeClass('active').hide();

            // Toggle this grid
            $grid.toggleClass('active');

            if ($grid.hasClass('active')) {
                $grid.css('display', 'grid');
            } else {
                $grid.hide();
            }
        });

        // Handle color swatch selection
        $(document).on('click', '.color-swatch-option', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $swatch = $(this);
            var value = $swatch.data('value');
            var name = $swatch.data('name');
            var color = $swatch.data('color');
            var $container = $swatch.closest('.color-grid-container');

            console.log('Color selected:', value, name, color);

            // Update hidden input
            $container.find('input[type="hidden"]').val(value);

            // Update visual elements
            $container.find('.color-swatch-option').removeClass('selected');
            $swatch.addClass('selected');
            $container.find('.color-name-display').text(name);
            $container.find('.color-preview').css('background-color', color);

            // Style selected swatch
            $container.find('.color-swatch-option').css('box-shadow', 'none');
            $swatch.css('box-shadow', '0 0 0 2px white, 0 0 0 4px #4CAF50');

            // Hide the grid
            $container.find('.color-grid').removeClass('active').hide();
        });

        // Close color grids when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').removeClass('active').hide();
            }
        });

        // Apply proper styling to color grids
        styleColorGrids();
    }

    /**
     * Apply proper styling to all color grids
     */
    function styleColorGrids() {
        $('.color-grid').each(function() {
            var $grid = $(this);

            // Apply grid styling
            $grid.css({
                'display': 'none',
                'position': 'absolute',
                'top': '100%',
                'left': '0',
                'background': 'white',
                'border': '1px solid #ddd',
                'border-radius': '4px',
                'padding': '10px',
                'z-index': '9999',
                'margin-top': '5px',
                'grid-template-columns': 'repeat(8, 1fr)',
                'gap': '8px',
                'width': '320px',
                'box-shadow': '0 4px 8px rgba(0,0,0,0.1)'
            });

            // Style color swatches
            $grid.find('.color-swatch-option').each(function() {
                var $swatch = $(this);

                $swatch.css({
                    'width': '32px',
                    'height': '32px',
                    'border-radius': '50%',
                    'cursor': 'pointer',
                    'border': '1px solid rgba(0, 0, 0, 0.1)',
                    'transition': 'all 0.2s ease',
                    'margin': '0 auto'
                });

                // Add hover effect
                $swatch.hover(
                    function() {
                        $(this).css({
                            'transform': 'scale(1.1)',
                            'box-shadow': '0 2px 5px rgba(0, 0, 0, 0.2)'
                        });
                    },
                    function() {
                        if (!$(this).hasClass('selected')) {
                            $(this).css({
                                'transform': 'scale(1)',
                                'box-shadow': 'none'
                            });
                        }
                    }
                );
            });

            // Style selected swatch
            $grid.find('.color-swatch-option.selected').css({
                'box-shadow': '0 0 0 2px white, 0 0 0 4px #4CAF50'
            });
        });

        // Style color select wrapper
        $('.color-select-wrapper').css({
            'display': 'flex',
            'align-items': 'center',
            'background': '#f0f0f0',
            'border': '1px solid #ddd',
            'border-radius': '4px',
            'padding': '8px 12px',
            'cursor': 'pointer'
        });

        // Style color name display
        $('.color-name-display').css({
            'flex': '1',
            'font-weight': '500'
        });

        // Style color preview
        $('.color-preview').css({
            'width': '24px',
            'height': '24px',
            'border-radius': '50%',
            'border': '1px solid rgba(0, 0, 0, 0.1)',
            'margin-left': '10px'
        });
    }

    /**
     * Setup the button edit handler
     */
    function setupButtonEditHandler() {
        $(document).off('click', '.edit-button').on('click', '.edit-button', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var buttonId = $(this).data('id');
            console.log('Editing button ID:', buttonId);

            // Reset the form to its original state
            $('#button-form-dialog').html(originalButtonFormHtml);

            // Show loading indicator
            $('#button-form-dialog').append('<div class="loading-overlay"><div class="loading-spinner">Loading...</div></div>');

            // Open the dialog
            $('#button-form-dialog').dialog('option', 'title', 'Edit Button');
            $('#button-form-dialog').dialog('open');

            // Set the button ID
            $('#button-id').val(buttonId);

            // Make an AJAX request to get button details
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'farmfaucet_get_button',
                    nonce: farmfaucet_admin.nonce,
                    button_id: buttonId
                },
                success: function(response) {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();

                    if (response.success && response.data) {
                        var button = response.data;
                        console.log('Button data loaded:', button);

                        // Populate form fields
                        $('#button-text').val(button.button_text || '');
                        $('#button-size').val(button.button_size || 'medium');
                        $('#button-color').val(button.button_color || 'blue');
                        $('#button-border').val(button.border_shape || 'rounded');
                        $('#button-redirect').val(button.redirect_url || '');

                        // Update color preview
                        updateColorPreview();

                        // Initialize color pickers
                        initColorPickers();

                        // Show/hide additional options based on button settings
                        if (button.is_locked) {
                            $('#button-is-locked').prop('checked', true);
                            $('.button-lock-options').show();

                            // Set required faucets
                            if (button.required_faucets) {
                                var requiredFaucets = button.required_faucets.split(',');
                                $('.required-faucet-checkbox').each(function() {
                                    if (requiredFaucets.indexOf($(this).val()) !== -1) {
                                        $(this).prop('checked', true);
                                    }
                                });
                            }

                            // Set reset minutes
                            $('#button-reset-minutes').val(button.reset_minutes || 0);

                            // Set lock faucet
                            $('#button-lock-faucet').prop('checked', button.lock_faucet == 1);
                        }

                        // Set milestone options
                        if (button.milestone_enabled) {
                            $('#button-milestone-enabled').prop('checked', true);
                            $('.milestone-options').show();

                            $('#button-milestone-type').val(button.milestone_type || 'card');

                            if (button.milestone_type === 'card') {
                                $('.milestone-card-options').show();
                                $('.milestone-compact-options').hide();
                            } else {
                                $('.milestone-card-options').hide();
                                $('.milestone-compact-options').show();
                            }

                            $('#button-milestone-title').val(button.milestone_title || '');
                            $('#button-milestone-description').val(button.milestone_description || '');
                            $('#button-milestone-bg-color').val(button.milestone_bg_color || '#ffffff');
                            $('#button-milestone-text-color').val(button.milestone_text_color || '#000000');
                            $('#button-milestone-progress-color').val(button.milestone_progress_color || '#4CAF50');
                            $('#button-milestone-transparent-bg').prop('checked', button.milestone_transparent_bg == 1);
                        }

                        // Set countdown options
                        if (button.countdown_enabled) {
                            $('#button-countdown-enabled').prop('checked', true);
                            $('.countdown-options').show();

                            $('#button-countdown-minutes').val(button.countdown_minutes || 5);
                            $('#button-countdown-text').val(button.countdown_text || '');

                            if (button.countdown_click_action) {
                                $('#button-countdown-click-action').prop('checked', true);
                                $('.countdown-click-options').show();
                                $('#button-countdown-click-url').val(button.countdown_click_url || '');
                            }
                        }
                    } else {
                        alert(response.data.message || 'Error loading button data');
                        $('#button-form-dialog').dialog('close');
                    }
                },
                error: function() {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();

                    alert('Error communicating with server');
                    $('#button-form-dialog').dialog('close');
                }
            });
        });
    }

    /**
     * Setup the button add handler
     */
    function setupButtonAddHandler() {
        $(document).off('click', '.add-button').on('click', '.add-button', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var faucetId = $(this).data('faucet-id');
            console.log('Adding button for faucet ID:', faucetId);

            // Reset the form to its original state
            $('#button-form-dialog').html(originalButtonFormHtml);

            // Reset form fields
            $('#button-id').val('0');
            $('#button-faucet-id').val(faucetId);
            $('#button-text').val('');
            $('#button-size').val('medium');
            $('#button-color').val('blue');
            $('#button-border').val('rounded');
            $('#button-redirect').val('');

            // Reset checkboxes
            $('#button-is-locked').prop('checked', false);
            $('#button-milestone-enabled').prop('checked', false);
            $('#button-countdown-enabled').prop('checked', false);

            // Hide option sections
            $('.button-lock-options').hide();
            $('.milestone-options').hide();
            $('.milestone-type-options').hide();
            $('.milestone-appearance-options').hide();
            $('.countdown-options').hide();
            $('.countdown-click-options').hide();

            // Update color preview
            updateColorPreview();

            // Initialize color pickers
            initColorPickers();

            // Open the dialog
            $('#button-form-dialog').dialog('option', 'title', 'Add New Button');
            $('#button-form-dialog').dialog('open');
        });
    }

    /**
     * Override the save button function
     */
    function overrideSaveButtonFunction() {
        window.saveButton = function() {
            // Get form data
            var buttonId = $('#button-id').val();
            var isNew = buttonId === '0';

            // Validate form
            var buttonText = $('#button-text').val().trim();
            if (!buttonText) {
                alert('Button text is required');
                return;
            }

            // Show loading indicator
            $('#button-form-dialog').append('<div class="loading-overlay"><div class="loading-spinner">Saving...</div></div>');

            // Prepare form data
            var formData = {
                action: isNew ? 'farmfaucet_create_button' : 'farmfaucet_update_button',
                nonce: farmfaucet_admin.nonce,
                button_id: buttonId,
                faucet_id: $('#button-faucet-id').val(),
                button_text: buttonText,
                button_size: $('#button-size').val(),
                button_color: $('#button-color').val(),
                border_shape: $('#button-border').val(),
                redirect_url: $('#button-redirect').val(),
                is_locked: $('#button-is-locked').is(':checked') ? 1 : 0,
                required_faucets: $('.required-faucet-checkbox:checked').map(function() { return $(this).val(); }).get(),
                reset_minutes: $('#button-reset-minutes').val(),
                lock_faucet: $('#button-lock-faucet').is(':checked') ? 1 : 0,
                milestone_enabled: $('#button-milestone-enabled').is(':checked') ? 1 : 0
            };

            // Add milestone options if enabled
            if (formData.milestone_enabled) {
                formData.milestone_type = $('#button-milestone-type').val();
                formData.milestone_title = $('#button-milestone-title').val();
                formData.milestone_description = $('#button-milestone-description').val();
                formData.milestone_bg_color = $('#button-milestone-bg-color').val();
                formData.milestone_text_color = $('#button-milestone-text-color').val();
                formData.milestone_progress_color = $('#button-milestone-progress-color').val();
                formData.milestone_transparent_bg = $('#button-milestone-transparent-bg').is(':checked') ? 1 : 0;
            }

            // Add countdown options if enabled
            if ($('#button-countdown-enabled').is(':checked')) {
                formData.countdown_enabled = 1;
                formData.countdown_minutes = $('#button-countdown-minutes').val();
                formData.countdown_text = $('#button-countdown-text').val();

                if ($('#button-countdown-click-action').is(':checked')) {
                    formData.countdown_click_action = 1;
                    formData.countdown_click_url = $('#button-countdown-click-url').val();
                }

                // Add standby mode option
                formData.countdown_standby = $('#button-countdown-standby').is(':checked') ? 1 : 0;
            }

            // Send AJAX request
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();

                    if (response.success) {
                        // Close the dialog
                        $('#button-form-dialog').dialog('close');

                        // Reload the page to show the updated button
                        location.reload();
                    } else {
                        alert(response.data.message || 'Error saving button');
                    }
                },
                error: function() {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();

                    alert('Error communicating with server');
                }
            });
        };
    }

    /**
     * Update the color preview based on the selected color
     */
    function updateColorPreview() {
        var selectedValue = $('#button-color').val();
        var selectedOption = $('.color-swatch-option[data-value="' + selectedValue + '"]');

        if (selectedOption.length) {
            // Update visual elements
            $('.color-swatch-option').removeClass('selected');
            selectedOption.addClass('selected');
            $('.color-name-display').text(selectedOption.data('name'));
            $('.color-preview').css('background-color', selectedOption.data('color'));

            // Style selected swatch
            $('.color-swatch-option').css('box-shadow', 'none');
            selectedOption.css('box-shadow', '0 0 0 2px white, 0 0 0 4px #4CAF50');
        }
    }

    // Add CSS for loading overlay
    $('head').append(`
        <style>
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }

            .loading-spinner {
                padding: 20px;
                background: white;
                border-radius: 4px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                font-weight: bold;
            }

            /* Fix for color grid display */
            .color-grid {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                z-index: 9999;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                margin-top: 5px;
                grid-template-columns: repeat(8, 1fr);
                gap: 8px;
                width: 320px;
            }

            .color-grid.active {
                display: grid !important;
            }

            .color-swatch-option {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                cursor: pointer;
                border: 1px solid rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                margin: 0 auto;
            }

            .color-swatch-option:hover {
                transform: scale(1.1);
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }

            .color-swatch-option.selected {
                box-shadow: 0 0 0 2px white, 0 0 0 4px #4CAF50;
            }

            .color-grid-container {
                position: relative;
            }

            .color-select-wrapper {
                display: flex;
                align-items: center;
                background: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                cursor: pointer;
            }

            .color-name-display {
                flex: 1;
                font-weight: 500;
            }

            .color-preview {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                border: 1px solid rgba(0, 0, 0, 0.1);
                margin-left: 10px;
            }
        </style>
    `);
})(jQuery);
