/**
 * Telegram Bot Builder Button Fix
 * 
 * This CSS file fixes the styling of the "Create New Bot" button in the Telegram Bot Builder.
 */

/* Create New Bot button styling */
#create-new-bot,
.farmfaucet-add-bot-button,
.farmfaucet-create-bot-button {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#create-new-bot:hover,
.farmfaucet-add-bot-button:hover,
.farmfaucet-create-bot-button:hover {
    background-color: #3d8b40 !important;
    border-color: #3d8b40 !important;
}

/* Dialog buttons styling */
.ui-dialog-buttonset button:first-child {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.ui-dialog-buttonset button:first-child:hover {
    background-color: #3d8b40 !important;
    border-color: #3d8b40 !important;
}

/* Center the text in the button */
#create-new-bot .dashicons,
.farmfaucet-add-bot-button .dashicons,
.farmfaucet-create-bot-button .dashicons {
    vertical-align: middle !important;
    margin-right: 5px !important;
}

/* Bot Builder UI Improvements */
.farmfaucet-admin-section.tg-bot-builder-section {
    margin-top: 20px;
}

.farmfaucet-admin-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.farmfaucet-admin-card .card-header {
    background-color: #4CAF50;
    border-bottom: 3px solid #388E3C;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.farmfaucet-admin-card .card-body {
    padding: 25px;
}

/* Bot List Styling */
.bot-list-container {
    margin-top: 20px;
}

.bot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.bot-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.bot-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.bot-card-header {
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    position: relative;
}

.bot-card-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.bot-card-body {
    padding: 15px;
}

.bot-card-footer {
    padding: 15px;
    background-color: #f5f5f5;
    display: flex;
    justify-content: space-between;
}

/* Bot Status Indicator */
.bot-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
}

.bot-status.active {
    background-color: #4CAF50;
    color: white;
}

.bot-status.inactive {
    background-color: #F44336;
    color: white;
}

/* Bot Action Buttons */
.bot-action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.bot-action-button.edit {
    background-color: #2196F3;
    color: white;
}

.bot-action-button.delete {
    background-color: #F44336;
    color: white;
}

.bot-action-button.manage {
    background-color: #4CAF50;
    color: white;
}

.bot-action-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

/* Tab Navigation */
.farmfaucet-tg-bot-tabs-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    border-bottom: 2px solid #e0e0e0;
}

.farmfaucet-tg-bot-tabs-nav li {
    margin-right: 5px;
}

.farmfaucet-tg-bot-tabs-nav li a {
    display: block;
    padding: 10px 20px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.farmfaucet-tg-bot-tabs-nav li.active a,
.farmfaucet-tg-bot-tabs-nav li a:hover {
    color: #4CAF50;
    border-bottom-color: #4CAF50;
}

/* Form Styling */
.farmfaucet-form-group {
    margin-bottom: 20px;
}

.farmfaucet-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.farmfaucet-form-group input[type="text"],
.farmfaucet-form-group input[type="url"],
.farmfaucet-form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.farmfaucet-form-group .description {
    margin-top: 5px;
    font-size: 13px;
    color: #666;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-right: 10px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.status-label {
    vertical-align: middle;
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
}
