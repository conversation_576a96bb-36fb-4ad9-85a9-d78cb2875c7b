<?php
/**
 * Farm Faucet Database Update Script
 * 
 * This script updates the database structure to ensure all required columns exist
 * and have the correct data types.
 */

// Load WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Check if user is logged in and is an admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to run this script.');
}

// Include the database updater class
require_once(dirname(__FILE__) . '/includes/class-farmfaucet-db-updater.php');

// Run the database update
Farmfaucet_DB_Updater::run_updates();

// Output success message
echo '<div style="background-color: #dff0d8; color: #3c763d; padding: 15px; border: 1px solid #d6e9c6; border-radius: 4px; margin: 20px 0;">';
echo '<h2>Database Update Complete</h2>';
echo '<p>The Farm Faucet database has been updated successfully.</p>';
echo '<p>All required columns now exist and have the correct data types.</p>';
echo '<p><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button button-primary">Return to Farm Faucet</a></p>';
echo '</div>';
