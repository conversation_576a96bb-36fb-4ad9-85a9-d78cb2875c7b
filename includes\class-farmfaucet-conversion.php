<?php

/**
 * Farm Faucet - Conversion Functionality
 *
 * @package FarmFaucet
 */

if (!defined('ABSPATH')) exit;

/**
 * Farmfaucet_Conversion class
 */
class Farmfaucet_Conversion
{
    /**
     * Singleton instance
     *
     * @var Farmfaucet_Conversion
     */
    private static $instance = null;

    /**
     * Get singleton instance
     *
     * @return Farmfaucet_Conversion
     */
    public static function init()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Register AJAX handlers
        add_action('wp_ajax_process_conversion', [$this, 'process_conversion']);
        add_action('wp_ajax_get_conversion_history', [$this, 'get_conversion_history']);

        // Create database tables on plugin activation
        add_action('farmfaucet_activate', [$this, 'create_tables']);
    }

    /**
     * Create database tables
     */
    public function create_tables()
    {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $table_name = $wpdb->prefix . 'farmfaucet_conversions';

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            faucet_id bigint(20) NOT NULL,
            source_currency_id bigint(20) NOT NULL,
            target_currency_id bigint(20) NOT NULL,
            amount decimal(18,8) NOT NULL,
            conversion_rate decimal(18,8) NOT NULL,
            converted_amount decimal(18,8) NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'pending',
            created_at datetime NOT NULL,
            ip_address varchar(45) NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY faucet_id (faucet_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Process a conversion request
     */
    public function process_conversion()
    {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_conversion_nonce')) {
            wp_send_json_error(__('Security check failed', 'farmfaucet'));
            return;
        }

        // Check if user is logged in
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            wp_send_json_error(['message' => __('You must be logged in to convert funds', 'farmfaucet')]);
            return;
        }

        // Verify captcha
        $captcha_verified = Farmfaucet_Frontend::init()->verify_captcha('farmfaucet_conversion_nonce');
        if (!$captcha_verified) {
            return;
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get faucet data
        $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        if (empty($faucet)) {
            wp_send_json_error(__('Faucet not found', 'farmfaucet'));
            return;
        }

        // Get currency ID
        $currency_id = isset($faucet['currency_id']) ? absint($faucet['currency_id']) : 0;
        if (empty($currency_id)) {
            wp_send_json_error(__('No currency configured for this faucet', 'farmfaucet'));
            return;
        }

        // Get target currency ID
        $target_currency_id = isset($_POST['target_currency_id']) ? absint($_POST['target_currency_id']) : 0;
        if (empty($target_currency_id)) {
            wp_send_json_error(__('Please select a currency to convert to', 'farmfaucet'));
            return;
        }

        // Get conversion amount
        $amount = isset($_POST['conversion_amount']) ? floatval($_POST['conversion_amount']) : 0;
        if ($amount <= 0) {
            wp_send_json_error(__('Please enter a valid amount', 'farmfaucet'));
            return;
        }

        // Get minimum conversion amount
        $min_conversion = isset($faucet['min_withdrawal']) ? floatval($faucet['min_withdrawal']) : 0;
        if ($amount < $min_conversion) {
            wp_send_json_error(__('Amount must be at least the minimum conversion amount', 'farmfaucet'));
            return;
        }

        // Check if user has enough balance
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $user_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);

            if ($amount > $user_balance) {
                wp_send_json_error(__('Amount cannot exceed your balance', 'farmfaucet'));
                return;
            }

            // Get target currency data
            $target_currency = $currency_maker->get_currency($target_currency_id);
            if (empty($target_currency)) {
                wp_send_json_error(__('Target currency not found', 'farmfaucet'));
                return;
            }

            // Get conversion rate
            $conversion_rate = isset($_POST['conversion_rate']) ? floatval($_POST['conversion_rate']) : 0;
            if ($conversion_rate <= 0) {
                // If no rate provided, use the default rate from the currency
                $conversion_rate = isset($target_currency['exchange_rate']) ? floatval($target_currency['exchange_rate']) : 0;
            }

            if ($conversion_rate <= 0) {
                wp_send_json_error(__('Invalid conversion rate', 'farmfaucet'));
                return;
            }

            // Calculate converted amount
            $converted_amount = $amount * $conversion_rate;

            // Process the conversion
            try {
                // Deduct from source currency
                $deducted = $currency_maker->update_user_currency_balance($user_id, $currency_id, -$amount);

                if (!$deducted) {
                    wp_send_json_error(__('Failed to deduct from your balance', 'farmfaucet'));
                    return;
                }

                // Add to target currency
                $added = $currency_maker->update_user_currency_balance($user_id, $target_currency_id, $converted_amount);

                if (!$added) {
                    // If adding fails, refund the deducted amount
                    $currency_maker->update_user_currency_balance($user_id, $currency_id, $amount);
                    wp_send_json_error(__('Failed to add to target currency', 'farmfaucet'));
                    return;
                }

                // Log the conversion
                global $wpdb;
                $table_name = $wpdb->prefix . 'farmfaucet_conversions';

                $wpdb->insert(
                    $table_name,
                    [
                        'user_id' => $user_id,
                        'faucet_id' => $faucet_id,
                        'source_currency_id' => $currency_id,
                        'target_currency_id' => $target_currency_id,
                        'amount' => $amount,
                        'conversion_rate' => $conversion_rate,
                        'converted_amount' => $converted_amount,
                        'status' => 'completed',
                        'created_at' => current_time('mysql'),
                        'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1'
                    ]
                );

                // Get updated balance
                $new_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);

                // Get currency symbols
                $source_currency = $currency_maker->get_currency($currency_id);
                $target_currency = $currency_maker->get_currency($target_currency_id);
                $source_symbol = isset($source_currency['symbol']) ? $source_currency['symbol'] : '';
                $target_symbol = isset($target_currency['symbol']) ? $target_currency['symbol'] : '';

                // Return success message
                wp_send_json_success([
                    'message' => sprintf(
                        __('Successfully converted %s %s to %s %s', 'farmfaucet'),
                        number_format($amount, 8),
                        $source_symbol,
                        number_format($converted_amount, 8),
                        $target_symbol
                    ),
                    'new_balance' => number_format($new_balance, 8)
                ]);
            } catch (Exception $e) {
                wp_send_json_error(__('Error processing conversion: ', 'farmfaucet') . $e->getMessage());
                return;
            }
        } else {
            wp_send_json_error(__('Currency system not available', 'farmfaucet'));
            return;
        }
    }

    /**
     * Get conversion history for a specific faucet
     */
    public function get_conversion_history()
    {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_conversion_nonce')) {
            wp_send_json_error(__('Security check failed', 'farmfaucet'));
            return;
        }

        // Check if user is logged in
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            wp_send_json_error(__('You must be logged in to view conversion history', 'farmfaucet'));
            return;
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get conversion history
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_conversions';

        $conversions = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT c.*,
                 sc.symbol as source_symbol, sc.name as source_name,
                 tc.symbol as target_symbol, tc.name as target_name
                 FROM $table_name c
                 LEFT JOIN {$wpdb->prefix}farmfaucet_currencies sc ON c.source_currency_id = sc.id
                 LEFT JOIN {$wpdb->prefix}farmfaucet_currencies tc ON c.target_currency_id = tc.id
                 WHERE c.user_id = %d AND c.faucet_id = %d
                 ORDER BY c.created_at DESC
                 LIMIT 10",
                $user_id,
                $faucet_id
            ),
            ARRAY_A
        );

        if (empty($conversions)) {
            wp_send_json_error(__('No conversion history found', 'farmfaucet'));
            return;
        }

        // Build HTML table
        $html = '<table class="farmfaucet-conversion-history-table">
            <thead>
                <tr>
                    <th>' . esc_html__('Date', 'farmfaucet') . '</th>
                    <th>' . esc_html__('From', 'farmfaucet') . '</th>
                    <th>' . esc_html__('To', 'farmfaucet') . '</th>
                    <th>' . esc_html__('Rate', 'farmfaucet') . '</th>
                    <th>' . esc_html__('Status', 'farmfaucet') . '</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($conversions as $conversion) {
            // Set status class
            $status_class = 'status-' . strtolower($conversion['status']);
            if ($conversion['status'] === 'completed') {
                $status_class .= ' status-success';
            } elseif ($conversion['status'] === 'failed') {
                $status_class .= ' status-error';
            } elseif ($conversion['status'] === 'pending') {
                $status_class .= ' status-pending';
            }

            $html .= '<tr>
                <td>' . date('Y-m-d H:i:s', strtotime($conversion['created_at'])) . '</td>
                <td>' . number_format($conversion['amount'], 8) . ' ' . esc_html($conversion['source_symbol']) . '</td>
                <td>' . number_format($conversion['converted_amount'], 8) . ' ' . esc_html($conversion['target_symbol']) . '</td>
                <td>' . number_format($conversion['conversion_rate'], 8) . '</td>
                <td><span class="conversion-status ' . $status_class . '">' . ucfirst(esc_html($conversion['status'])) . '</span></td>
            </tr>';
        }

        $html .= '</tbody></table>';

        wp_send_json_success($html);
    }
}
