<?php
if (!defined('ABSPATH')) exit;

// Get faucet data from the template variable
if (!isset($faucet)) {
  // Fallback to the first faucet if none specified
  $faucets = Farmfaucet_Logger::get_faucets();
  $faucet = !empty($faucets) ? $faucets[0] : null;

  if (empty($faucet)) {
    echo '<div class="farmfaucet-error">' . esc_html__('No faucets available', 'farmfaucet') . '</div>';
    return;
  }
}

// Check cooldown for this specific faucet
$user_ip = Farmfaucet_Security::get_user_ip();
$transient_key = 'farmfaucet_cooldown_' . $faucet['id'] . '_' . md5($user_ip);
$cooldown_active = get_transient($transient_key);
?>
<?php
// Generate background style based on faucet settings
$bg_style = '';
$text_style = '';

// Check if transparent background is enabled
if (isset($faucet['transparent_bg']) && $faucet['transparent_bg'] == 1) {
  $bg_style = 'background: transparent;';
} else {
  // Apply background style based on settings
  if (isset($faucet['bg_style']) && $faucet['bg_style'] == 'gradient') {
    $gradient_start = isset($faucet['bg_gradient_start']) ? $faucet['bg_gradient_start'] : '#f8fff8';
    $gradient_end = isset($faucet['bg_gradient_end']) ? $faucet['bg_gradient_end'] : '#e8f5e9';
    $bg_style = "background: linear-gradient(135deg, {$gradient_start}, {$gradient_end});";
  } else {
    $bg_color = isset($faucet['bg_color']) ? $faucet['bg_color'] : '#f8fff8';
    $bg_style = "background-color: {$bg_color};";
  }
}

// Apply text color and shadow
$text_color = isset($faucet['text_color']) ? $faucet['text_color'] : '#4CAF50';
$text_shadow = isset($faucet['text_shadow']) && $faucet['text_shadow'] != 'none' ? "text-shadow: {$faucet['text_shadow']};" : '';
$text_style = "color: {$text_color}; {$text_shadow}";

// Get button color
$button_color = isset($faucet['button_color']) ? $faucet['button_color'] : '#4CAF50';

// Get border color
$border_color = isset($faucet['border_color']) ? $faucet['border_color'] : '#4CAF50';

// Get border radius
$border_radius = isset($faucet['border_radius']) ? $faucet['border_radius'] : '8px';

// Get form background color
$form_bg_color = isset($faucet['form_bg_color']) ? $faucet['form_bg_color'] : '#ffffff';

// Check if form background should be transparent
$form_transparent = isset($faucet['form_transparent']) && $faucet['form_transparent'] ? 1 : 0;

// Add transparent class if needed
$transparent_class = isset($faucet['transparent_bg']) && $faucet['transparent_bg'] ? 'transparent-bg' : '';

// Add custom border class if needed
$border_class = isset($faucet['border_color']) && $faucet['border_color'] ? 'custom-border' : '';
?>
<div class="farmfaucet-container <?php echo esc_attr($transparent_class); ?> <?php echo esc_attr($border_class); ?>"
  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
  data-faucet-name="<?php echo esc_attr($faucet['name']); ?>"
  data-faucet-shortcode="<?php echo esc_attr($faucet['shortcode']); ?>"
  data-border-radius="<?php echo esc_attr($border_radius); ?>"
  data-form-bg-color="<?php echo esc_attr($form_bg_color); ?>"
  data-form-transparent="<?php echo esc_attr($form_transparent); ?>"
  role="region"
  aria-label="<?php esc_attr_e('Cryptocurrency Faucet Claim Form', 'farmfaucet'); ?>"
  style="<?php echo esc_attr($bg_style); ?> --button-color: <?php echo esc_attr($button_color); ?>; --border-color: <?php echo esc_attr($border_color); ?>; --form-bg-color: <?php echo esc_attr($form_bg_color); ?>;">

  <?php if ($cooldown_active) : ?>
    <div class="farmfaucet-cooldown" role="alert" aria-live="polite">
      <div class="cooldown-icon" aria-hidden="true">⏳</div>
      <h3 id="cooldown-heading" style="<?php echo esc_attr($text_style); ?>"><?php esc_html_e('Claim Cooldown Active', 'farmfaucet'); ?></h3>
      <p id="cooldown-description" style="<?php echo esc_attr($text_style); ?>"><?php esc_html_e('Next claim available in:', 'farmfaucet'); ?></p>
      <div class="cooldown-timer"
        data-end="<?php echo esc_attr(time() + $faucet['cooldown']); ?>"
        aria-labelledby="cooldown-heading cooldown-description"
        role="timer"
        style="<?php echo esc_attr($text_style); ?>">
        --:--:--
      </div>
    </div>
  <?php else : ?>
    <div class="farmfaucet-header">
      <h2 class="status-header" id="form-heading" style="<?php echo esc_attr($text_style); ?>">
        <span class="pre-captcha-status">📋 <?php esc_html_e('COMPLETE TASK', 'farmfaucet'); ?></span>
        <span class="post-captcha-status" style="display:none;">✅ <?php esc_html_e('TASK COMPLETE', 'farmfaucet'); ?></span>
      </h2>
      <div class="reward-notice" aria-live="polite" style="<?php echo esc_attr($text_style); ?>">
        <?php echo wp_kses_post(sprintf(
          __('You will receive ➔ %s %s', 'farmfaucet'),
          '<span class="amount">' . esc_html($faucet['amount']) . '</span>',
          '<span class="currency">' . esc_html($faucet['currency']) . '</span>'
        )); ?>
      </div>
    </div>

    <form id="farmfaucet-claim-form" method="post" class="farmfaucet-form" aria-labelledby="form-heading">
      <div class="form-group">
        <label for="user_email" class="sr-only"><?php esc_html_e('FaucetPay Email Address', 'farmfaucet'); ?></label>
        <input type="email"
          id="user_email"
          name="user_email"
          placeholder="<?php esc_attr_e('Enter FaucetPay Email Address', 'farmfaucet'); ?>"
          required
          class="farmfaucet-input"
          pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"
          aria-required="true"
          aria-describedby="email-format">
        <span id="email-format" class="sr-only"><?php esc_html_e('Please enter a valid email address in the format: <EMAIL>', 'farmfaucet'); ?></span>
      </div>

      <!-- Hidden field for faucet ID -->
      <input type="hidden" name="faucet_id" value="<?php echo esc_attr($faucet['id']); ?>">

      <div class="farmfaucet-captcha-container" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>" style="display: flex; visibility: visible; opacity: 1;">
        <?php
        try {
          // Get captcha type from faucet-specific setting or fallback to global settings
          $captcha_type = !empty($faucet['captcha_type']) ? $faucet['captcha_type'] : get_option('farmfaucet_captcha_type', 'hcaptcha');

          // Validate captcha type
          if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
          }

          // Debug information
          error_log('Faucet ID: ' . $faucet['id'] . ', Captcha Type: ' . $captcha_type);

          // Create a unique ID for this captcha instance based on faucet ID
          $captcha_id = 'captcha-widget-' . esc_attr($faucet['id']);

          // Get site keys for all captcha types
          $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
          $recaptcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
          $turnstile_sitekey = get_option('farmfaucet_turnstile_sitekey', '');

          // Check if the selected captcha type has a site key
          $has_sitekey = ($captcha_type === 'hcaptcha' && !empty($hcaptcha_sitekey)) ||
            ($captcha_type === 'recaptcha' && !empty($recaptcha_sitekey)) ||
            ($captcha_type === 'turnstile' && !empty($turnstile_sitekey));

          if (!$has_sitekey) {
            // If the selected captcha type doesn't have a site key, try the others in order
            if (!empty($hcaptcha_sitekey)) {
              $captcha_type = 'hcaptcha';
              $has_sitekey = true;
            } elseif (!empty($recaptcha_sitekey)) {
              $captcha_type = 'recaptcha';
              $has_sitekey = true;
            } elseif (!empty($turnstile_sitekey)) {
              $captcha_type = 'turnstile';
              $has_sitekey = true;
            }
          }

          // Add a loading indicator
          echo '<div class="farmfaucet-captcha-loading">' . esc_html__('Loading captcha...', 'farmfaucet') . '</div>';

          // Render the appropriate captcha
          if ($captcha_type === 'hcaptcha') {
            if (empty($hcaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('hCaptcha site key not configured', 'farmfaucet') . '</div>';
            } else {
        ?>
              <div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for hCaptcha -->
                <div id="<?php echo $captcha_id; ?>"
                  class="h-captcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($hcaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'recaptcha') {
            if (empty($recaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('reCAPTCHA site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for reCAPTCHA -->
                <div id="<?php echo $captcha_id; ?>"
                  class="g-recaptcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($recaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'turnstile') {
            if (empty($turnstile_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('Cloudflare Turnstile site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for Cloudflare Turnstile -->
                <div id="<?php echo $captcha_id; ?>"
                  class="cf-turnstile farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($turnstile_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
        <?php
            }
          }

          // Add hidden fields to store captcha information
          echo '<input type="hidden" name="captcha_type" value="' . esc_attr($captcha_type) . '" class="farmfaucet-captcha-type">';
          echo '<input type="hidden" name="captcha_id" value="' . esc_attr($captcha_id) . '" class="farmfaucet-captcha-id">';
        } catch (Exception $e) {
          // Log the error but don't break the form
          error_log('Farmfaucet captcha rendering error: ' . $e->getMessage());
          echo '<div class="farmfaucet-error">' . esc_html__('Error loading captcha', 'farmfaucet') . '</div>';
        }
        ?>
      </div>

      <button type="submit"
        class="farmfaucet-claim-btn farmfaucet-button custom-color"
        disabled
        aria-disabled="true"
        id="claim-button">
        <?php esc_html_e('CLAIM NOW', 'farmfaucet'); ?>
      </button>
    </form>
  <?php endif; ?>
  <div class="farmfaucet-notification" role="status" aria-live="assertive"></div>

  <!-- Captchas are initialized by the captcha-simple.js script -->
</div>