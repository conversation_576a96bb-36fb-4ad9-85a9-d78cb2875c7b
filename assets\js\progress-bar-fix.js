/**
 * Farm Faucet Progress Bar Fix
 * 
 * This script ensures that the progress bar is properly displayed in both card and compact views.
 */
(function($) {
    $(document).ready(function() {
        // Fix progress bar display
        function fixProgressBars() {
            // Initialize all progress bars
            $('.farmfaucet-milestone-progress-bar').each(function() {
                var $this = $(this);
                var progress = $this.data('progress');
                
                // Set the width with a slight delay for animation
                setTimeout(function() {
                    $this.css('width', progress + '%');
                }, 100);
            });
            
            // Ensure text is properly positioned
            $('.farmfaucet-milestone-progress-text, .farmfaucet-milestone-progress-bar-percentage').each(function() {
                var $this = $(this);
                $this.css({
                    'position': 'absolute',
                    'width': '100%',
                    'text-align': 'center',
                    'color': '#fff',
                    'font-weight': 'bold',
                    'line-height': $this.closest('.compact-view').length ? '24px' : '30px',
                    'font-size': $this.closest('.compact-view').length ? '0.9em' : '1.1em',
                    'text-shadow': '0 1px 2px rgba(0,0,0,0.5)',
                    'z-index': '5',
                    'left': '0',
                    'top': '0',
                    'height': '100%',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'pointer-events': 'none',
                    'margin': '0',
                    'padding': '0'
                });
            });
            
            // Add box shadow to progress containers
            $('.farmfaucet-milestone-progress-container').css({
                'box-shadow': 'inset 0 1px 3px rgba(0,0,0,0.1)',
                'position': 'relative',
                'overflow': 'hidden'
            });
        }
        
        // Run the fix immediately
        fixProgressBars();
        
        // Also run after a short delay to ensure all elements are loaded
        setTimeout(fixProgressBars, 500);
        
        // Run again after 1 second for good measure
        setTimeout(fixProgressBars, 1000);
    });
})(jQuery);
