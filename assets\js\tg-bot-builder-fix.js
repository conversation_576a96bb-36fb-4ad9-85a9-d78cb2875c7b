/**
 * Telegram Bot Builder Fix
 * 
 * This script fixes issues with the Telegram Bot Builder functionality
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Telegram Bot Builder Fix loaded');
        initTgBotBuilderFix();
    });

    /**
     * Initialize Telegram Bot Builder Fix
     */
    function initTgBotBuilderFix() {
        // Override the saveBot function
        if (typeof window.saveBot === 'function') {
            console.log('Overriding saveBot function');
            var originalSaveBot = window.saveBot;
            
            window.saveBot = function() {
                // Get form data
                const botId = $('#bot-id').val();
                const isUpdate = botId !== '';
                
                // Create a button to show saving state
                const $saveButton = $('#farmfaucet-save-bot');
                const originalButtonText = $saveButton.text();
                $saveButton.prop('disabled', true).text('Saving...');
                
                const formData = {
                    action: 'farmfaucet_save_bot',
                    nonce: farmfaucetTgBotBuilder.nonce,
                    bot_id: botId,
                    bot_name: $('#bot-name').val(),
                    bot_token: $('#bot-token').val(),
                    bot_username: $('#bot-username').val(),
                    bot_type: $('#bot-type').val(),
                    webhook_url: $('#webhook-url').val(),
                    is_active: $('#bot-active').is(':checked') ? 1 : 0,
                    settings: JSON.stringify({
                        welcome_message: $('#welcome-message').val(),
                        help_message: $('#help-message').val(),
                        error_message: $('#error-message').val()
                    })
                };
                
                console.log('Sending bot data:', formData);
                
                // Send AJAX request
                $.ajax({
                    url: farmfaucetTgBotBuilder.ajaxUrl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        console.log('Bot save response:', response);
                        if (response.success) {
                            alert(response.data.message || (isUpdate ? 'Bot updated successfully' : 'Bot created successfully'));
                            
                            // Reload the page to show the updated table
                            window.location.reload();
                        } else {
                            $saveButton.prop('disabled', false).text(originalButtonText);
                            alert(response.data.message || (isUpdate ? 'Failed to update bot' : 'Failed to create bot'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', xhr.responseText);
                        $saveButton.prop('disabled', false).text(originalButtonText);
                        
                        // Try to parse the error response
                        let errorMessage = error;
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            if (errorResponse.data && errorResponse.data.message) {
                                errorMessage = errorResponse.data.message;
                            }
                        } catch (e) {
                            // If parsing fails, use the original error
                        }
                        
                        alert('Error: ' + errorMessage);
                    }
                });
            };
        }
        
        // Add event listener for the save bot button
        $('#farmfaucet-save-bot').off('click').on('click', function(e) {
            e.preventDefault();
            saveBot();
        });
        
        // Add event listener for the test token button
        $('#test-bot-token').off('click').on('click', function(e) {
            e.preventDefault();
            testBotToken();
        });
    }
    
    /**
     * Test bot token
     */
    function testBotToken() {
        const token = $('#bot-token').val();
        
        if (!token) {
            alert('Please enter a bot token');
            return;
        }
        
        const $testButton = $('#test-bot-token');
        const originalButtonText = $testButton.text();
        $testButton.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_test_bot_token',
                nonce: farmfaucetTgBotBuilder.nonce,
                token: token
            },
            success: function(response) {
                $testButton.prop('disabled', false).text(originalButtonText);
                
                if (response.success) {
                    // Populate username field
                    $('#bot-username').val(response.data.username);
                    
                    alert('Bot token is valid. Bot username: ' + response.data.username);
                } else {
                    alert('Invalid bot token: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function() {
                $testButton.prop('disabled', false).text(originalButtonText);
                alert('Error testing bot token. Please try again.');
            }
        });
    }
})(jQuery);
