/**
 * Currency Maker Green Theme CSS
 * Provides consistent green styling for the Currency Maker tab
 */

/* Admin Cards */
.currency-maker-section .farmfaucet-admin-card {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.currency-maker-section .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #4CAF50;
    border-bottom: 3px solid #388E3C;
}

.currency-maker-section .card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: white;
}

.currency-maker-section .card-body {
    padding: 20px;
    background-color: #fff;
}

/* Welcome Card */
.currency-maker-welcome-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 20px;
    margin-bottom: 30px;
    border-radius: 4px;
}

.currency-maker-welcome-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

.currency-maker-welcome-card p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.currency-maker-tutorial {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.currency-maker-tutorial h4 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}

.currency-maker-tutorial ul {
    margin-left: 20px;
}

.currency-maker-tutorial li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.currency-maker-field-explanation {
    margin-top: 20px;
}

.currency-maker-field-explanation h4 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.currency-maker-field-explanation table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.currency-maker-field-explanation table th,
.currency-maker-field-explanation table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.currency-maker-field-explanation table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
}

/* Add Currency Button */
.currency-maker-section .farmfaucet-add-currency-button {
    margin-left: auto;
    background-color: #4CAF50;
    border-color: #43A047;
    color: white;
}

.currency-maker-section .farmfaucet-add-currency-button:hover {
    background-color: #43A047;
    border-color: #388E3C;
}

.currency-maker-section .farmfaucet-add-currency-button .dashicons {
    margin-right: 5px;
}

/* Currencies Table */
.farmfaucet-currencies-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.farmfaucet-currencies-table th,
.farmfaucet-currencies-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.farmfaucet-currencies-table th {
    background-color: #4CAF50;
    color: white;
    font-weight: bold;
}

.farmfaucet-currencies-table tr:hover {
    background-color: #f9f9f9;
}

.farmfaucet-currencies-table .currency-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}

.farmfaucet-currencies-table .color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-block;
    border: 1px solid #ddd;
}

.farmfaucet-currencies-table .status-active {
    color: #4CAF50;
    font-weight: bold;
}

.farmfaucet-currencies-table .status-inactive {
    color: #f44336;
    font-weight: bold;
}

/* Currency Type Badge */
.farmfaucet-currencies-table .currency-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    color: white;
}

.farmfaucet-currencies-table .currency-type-badge.earnings {
    background-color: #4CAF50;
}

.farmfaucet-currencies-table .currency-type-badge.advertisement {
    background-color: #2196F3;
}

.farmfaucet-currencies-table .farmfaucet-edit-currency-button {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
}

.farmfaucet-currencies-table .farmfaucet-delete-currency-button {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.farmfaucet-currencies-table .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    vertical-align: text-bottom;
}

.farmfaucet-no-currencies {
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
    color: #666;
}

/* Modal */
.farmfaucet-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.farmfaucet-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.farmfaucet-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #4CAF50;
    color: white;
}

.farmfaucet-modal-header h3 {
    margin: 0;
    font-size: 20px;
    color: white;
}

.farmfaucet-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: white;
}

.farmfaucet-modal-close:hover {
    color: #f0f0f0;
}

.farmfaucet-modal-body {
    padding: 20px;
}

.farmfaucet-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
    background-color: #f9f9f9;
}

.farmfaucet-modal-footer button {
    margin-left: 10px;
}

/* Form Styles */
.farmfaucet-form-group {
    margin-bottom: 20px;
}

.farmfaucet-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.farmfaucet-form-group input[type="text"],
.farmfaucet-form-group input[type="number"],
.farmfaucet-form-group input[type="url"],
.farmfaucet-form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.farmfaucet-form-group input[type="text"]:focus,
.farmfaucet-form-group input[type="number"]:focus,
.farmfaucet-form-group input[type="url"]:focus,
.farmfaucet-form-group select:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    outline: none;
}

.farmfaucet-form-group .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
    font-size: 13px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Save Button */
#farmfaucet-save-currency {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
}

#farmfaucet-save-currency:hover {
    background-color: #43A047;
}

/* Responsive Styles */
@media (max-width: 782px) {
    .farmfaucet-currencies-table th,
    .farmfaucet-currencies-table td {
        padding: 10px 5px;
    }

    .farmfaucet-currencies-table .farmfaucet-edit-currency-button,
    .farmfaucet-currencies-table .farmfaucet-delete-currency-button {
        padding: 4px 8px;
        font-size: 12px;
    }
}
