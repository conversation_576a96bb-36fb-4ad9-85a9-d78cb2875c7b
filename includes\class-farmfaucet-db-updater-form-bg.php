<?php

/**
 * Farmfaucet Database Updater for Form Background
 *
 * Handles database updates for the form background fields
 */
class Farmfaucet_DB_Updater_Form_Bg
{
    /**
     * Run the update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add form_bg_color column if it doesn't exist
        if (!in_array('form_bg_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius");
        }

        // Add form_transparent column if it doesn't exist
        if (!in_array('form_transparent', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color");
        }

        // Update existing faucets to set default values
        $wpdb->query("UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''");
    }

    /**
     * Initialize the updater
     *
     * @return void
     */
    public static function init()
    {
        // Run the update
        self::run_update();
    }
}
