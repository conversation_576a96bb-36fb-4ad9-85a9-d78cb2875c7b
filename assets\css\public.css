/* Farm Faucet Public Styles */

/* Claim Form */
.farmfaucet-form {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.farmfaucet-form h3 {
    margin-top: 0;
    color: #333;
}

.farmfaucet-form .form-field {
    margin-bottom: 15px;
}

.farmfaucet-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.farmfaucet-form input[type="email"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.farmfaucet-form button {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.farmfaucet-form button:hover {
    background-color: #135e96;
}

.farmfaucet-form .captcha-container {
    margin: 15px 0;
}

.farmfaucet-error {
    color: #d63638;
    padding: 10px;
    background-color: #ffeeee;
    border-left: 4px solid #d63638;
    margin-bottom: 15px;
}

.farmfaucet-success {
    color: #00a32a;
    padding: 10px;
    background-color: #eeffee;
    border-left: 4px solid #00a32a;
    margin-bottom: 15px;
}

/* Button Styles */
.farmfaucet-button-container {
    margin: 10px 0;
    text-align: center;
}

.farmfaucet-button {
    display: inline-block;
    text-decoration: none;
    color: white;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.farmfaucet-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.farmfaucet-button-disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.farmfaucet-button-locked {
    cursor: not-allowed;
    opacity: 0.7;
}

.farmfaucet-lock-icon {
    margin-right: 5px;
}

/* Completion Percentage Styles */
.farmfaucet-completion-container {
    margin: 20px 0;
    max-width: 500px;
}

.farmfaucet-completion-title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

/* Milestone Styles */
.farmfaucet-milestone-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    max-width: 500px;
}

.farmfaucet-milestone-title {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
    color: #333;
}

.farmfaucet-milestone-description {
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.farmfaucet-milestone-details {
    margin-top: 15px;
    font-size: 14px;
}

.farmfaucet-milestone-detail-item {
    margin-bottom: 5px;
}

.farmfaucet-milestone-label {
    font-weight: bold;
    color: #555;
}

.farmfaucet-milestone-value {
    color: #333;
}

/* Milestone Progress Bar Styles */
.farmfaucet-milestone-container {
    margin-bottom: 30px;
}

.farmfaucet-milestone-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.farmfaucet-milestone-description {
    margin-bottom: 15px;
    color: #555;
}

.farmfaucet-milestone-progress-container {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100% !important;
}

.farmfaucet-milestone-progress-text {
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);
}

.farmfaucet-milestone-details {
    margin-top: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.farmfaucet-milestone-detail-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.farmfaucet-milestone-detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.farmfaucet-milestone-label {
    font-weight: bold;
}

/* Username Form Styles */
.farmfaucet-username-form-container {
    margin: 15px 0;
    font-family: inherit;
    color: inherit;
    font-size: inherit;
}

.farmfaucet-username-toggle {
    transition: opacity 0.3s ease;
}

.farmfaucet-username-toggle-link {
    color: inherit;
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
}

.farmfaucet-username-form {
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-top: 10px;
}

.farmfaucet-username-form-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.farmfaucet-form-field {
    flex: 1;
    min-width: 200px;
}

.farmfaucet-form-field input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

.farmfaucet-username-submit {
    padding: 8px 16px;
    background-color: #2271b1;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    transition: background-color 0.3s ease;
}

.farmfaucet-username-submit:hover {
    background-color: #135e96;
}

.farmfaucet-username-submit:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.farmfaucet-username-success {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #d4edda;
    color: #155724;
    border-radius: 4px;
    font-size: 0.9em;
}

.farmfaucet-username {
    /* Inherit all styling from parent */
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
    line-height: inherit;
    text-decoration: inherit;
}

/* Page-specific Milestone Styles */
.farmfaucet-milestone-page {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.farmfaucet-milestone-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.farmfaucet-milestone-page-progress {
    margin: 8px 0;
}

.farmfaucet-milestone-page-link {
    display: inline-block;
    background-color: #2271b1;
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.farmfaucet-milestone-page-link:hover {
    background-color: #135e96;
    transform: translateY(-1px);
}

.farmfaucet-milestone-overall {
    font-size: 16px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

/* Shortcodes List Styles */
.farmfaucet-shortcodes-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.farmfaucet-shortcodes-title {
    margin-top: 0;
    color: #333;
}

.farmfaucet-shortcodes-list {
    margin-top: 15px;
}

.farmfaucet-shortcode-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.farmfaucet-shortcode-item:last-child {
    border-bottom: none;
}

.farmfaucet-shortcode-item code {
    background-color: #f0f0f0;
    padding: 3px 6px;
    border-radius: 3px;
    font-family: monospace;
    margin-right: 10px;
}

.farmfaucet-shortcode-description {
    color: #666;
    font-size: 14px;
    display: block;
    margin-top: 5px;
}
