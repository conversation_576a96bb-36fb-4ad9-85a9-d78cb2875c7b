<?php
class Farmfaucet_Admin
{
    // Singleton instance to ensure only one instance of the class is created
    private static $instance;

    // Define admin tabs for the settings page
    const ADMIN_TABS = [
        'faucets' => 'Faucets',
        'transactions' => 'Transactions Log',
        'task_completion' => 'Task Completion',
        'tg_bot_builder' => 'TG Bot Builder',
        'dashboard' => 'Dashboard',
        'currency_maker' => 'Currency Manager',
        'referral' => 'Referral System',
        'advertising' => 'Advertising',
        'settings' => 'Settings'
    ];

    // Initialize the class and return the singleton instance
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // Constructor to set up admin menu, settings, and assets
    private function __construct()
    {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // AJAX handlers for faucet management
        add_action('wp_ajax_farmfaucet_create_faucet', [$this, 'ajax_create_faucet']);
        add_action('wp_ajax_farmfaucet_update_faucet', [$this, 'ajax_update_faucet']);
        add_action('wp_ajax_farmfaucet_delete_faucet', [$this, 'ajax_delete_faucet']);
        add_action('wp_ajax_farmfaucet_toggle_faucet_status', [$this, 'ajax_toggle_faucet_status']);
        add_action('wp_ajax_farmfaucet_get_faucet', [$this, 'ajax_get_faucet']);

        // AJAX handlers for button management
        add_action('wp_ajax_farmfaucet_create_button', [$this, 'ajax_create_button']);
        add_action('wp_ajax_farmfaucet_update_button', [$this, 'ajax_update_button']);
        add_action('wp_ajax_farmfaucet_delete_button', [$this, 'ajax_delete_button']);
        add_action('wp_ajax_farmfaucet_get_button', [$this, 'ajax_get_button']);

        // AJAX handler for getting website pages
        add_action('wp_ajax_farmfaucet_get_website_pages', [$this, 'ajax_get_website_pages']);

        // AJAX handler for fixing database
        add_action('wp_ajax_farmfaucet_fix_database', [$this, 'ajax_fix_database']);
    }

    // Enqueue admin-specific styles and scripts
    public function enqueue_admin_assets($hook)
    {
        if ('toplevel_page_farmfaucet' !== $hook) return;

        wp_enqueue_style(
            'farmfaucet-admin-css',
            FARMFAUCET_URL . 'assets/css/admin.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Enqueue color grid fix CSS if it exists
        if (file_exists(FARMFAUCET_DIR . 'assets/css/color-grid-fix.css')) {
            wp_enqueue_style(
                'farmfaucet-color-grid-fix',
                FARMFAUCET_URL . 'assets/css/color-grid-fix.css',
                ['farmfaucet-admin-css'],
                FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
            );
        }

        // Add milestone admin styles
        wp_enqueue_style(
            'farmfaucet-admin-milestone-css',
            FARMFAUCET_URL . 'assets/css/admin-milestone.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add milestone admin fix styles
        wp_enqueue_style(
            'farmfaucet-admin-milestone-fix-css',
            FARMFAUCET_URL . 'assets/css/admin-milestone-fix.css',
            ['farmfaucet-admin-milestone-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add task completion styles
        wp_enqueue_style(
            'farmfaucet-task-completion-css',
            FARMFAUCET_URL . 'assets/css/task-completion.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add deposit/withdraw admin styles
        wp_enqueue_style(
            'farmfaucet-deposit-withdraw-admin-css',
            FARMFAUCET_URL . 'assets/css/deposit-withdraw-admin.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add currency maker admin styles
        wp_enqueue_style(
            'farmfaucet-currency-maker-admin-css',
            FARMFAUCET_URL . 'assets/css/currency-maker-admin.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add referral admin styles
        wp_enqueue_style(
            'farmfaucet-referral-admin-css',
            FARMFAUCET_URL . 'assets/css/referral-admin.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add fixed button color picker styles
        wp_enqueue_style(
            'farmfaucet-button-color-picker-fixed-css',
            FARMFAUCET_URL . 'assets/css/button-color-picker-fixed.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add toggle switch fix styles
        wp_enqueue_style(
            'farmfaucet-toggle-switch-fix-css',
            FARMFAUCET_URL . 'assets/css/toggle-switch-fix.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add toggle switch fix styles
        wp_enqueue_style(
            'farmfaucet-toggle-switch-fix-css',
            FARMFAUCET_URL . 'assets/css/toggle-switch-fix.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add faucet type selector styles
        wp_enqueue_style(
            'farmfaucet-faucet-type-selector-css',
            FARMFAUCET_URL . 'assets/css/faucet-type-selector.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add conversion faucet styles
        wp_enqueue_style(
            'farmfaucet-conversion-faucet-admin-css',
            FARMFAUCET_URL . 'assets/css/conversion-faucet-admin.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add dialog fixes CSS
        wp_enqueue_style(
            'farmfaucet-dialog-fixes-css',
            FARMFAUCET_URL . 'assets/css/dialog-fixes.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add improved faucet form CSS
        wp_enqueue_style(
            'farmfaucet-faucet-form-improved-css',
            FARMFAUCET_URL . 'assets/css/faucet-form-improved.css',
            ['farmfaucet-admin-css', 'farmfaucet-dialog-fixes-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add compact appearance settings CSS
        wp_enqueue_style(
            'farmfaucet-faucet-appearance-compact-css',
            FARMFAUCET_URL . 'assets/css/faucet-appearance-compact.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add Telegram Bot Login styles
        wp_enqueue_style(
            'farmfaucet-tg-bot-login-css',
            FARMFAUCET_URL . 'assets/css/tg-bot-login.css',
            ['farmfaucet-admin-css'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add jQuery UI for tabs and dialogs
        wp_enqueue_style('wp-jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-tabs');
        wp_enqueue_script('jquery-ui-dialog');

        wp_enqueue_script(
            'farmfaucet-admin-js',
            FARMFAUCET_URL . 'assets/js/admin.js',
            ['jquery', 'jquery-ui-tabs', 'jquery-ui-dialog'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Note: Removed milestone-display-fix script - functionality consolidated

        // Add master faucet form handler (replaces all faucet-related scripts)
        wp_enqueue_script(
            'farmfaucet-master-form-handler',
            FARMFAUCET_URL . 'assets/js/faucet-form-consolidated.js',
            ['jquery', 'farmfaucet-admin-js'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Note: Removed conflicting faucet-color-picker-fix script - functionality now in consolidated script

        // Add Telegram Bot Login script
        wp_enqueue_script(
            'farmfaucet-tg-bot-login',
            FARMFAUCET_URL . 'assets/js/tg-bot-login.js',
            ['jquery', 'farmfaucet-admin-js'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Add Telegram Bot Builder Fix CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-fix',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our simple fix CSS
        wp_enqueue_style(
            'farmfaucet-telegram-bot-builder-fix',
            FARMFAUCET_URL . 'assets/css/telegram-bot-builder-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our consolidated form transparency fix
        wp_enqueue_style(
            'farmfaucet-form-transparency-consolidated',
            FARMFAUCET_URL . 'assets/css/form-transparency-consolidated.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add inline CSS to fix the Create New Bot button
        wp_add_inline_style('farmfaucet-tg-bot-builder-fix', '
            #create-new-bot {
                background-color: #4CAF50 !important;
                border-color: #4CAF50 !important;
                color: white !important;
                text-align: center !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #create-new-bot:hover {
                background-color: #3d8b40 !important;
                border-color: #3d8b40 !important;
            }
        ');

        // Add consolidated database fix script
        wp_enqueue_script(
            'farmfaucet-db-fix-consolidated',
            FARMFAUCET_URL . 'assets/js/db-fix-consolidated.js',
            ['farmfaucet-admin-js'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Add referral admin script
        wp_enqueue_script(
            'farmfaucet-referral-admin',
            FARMFAUCET_URL . 'assets/js/referral-admin.js',
            ['jquery', 'jquery-ui-datepicker'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Note: Removed admin-faucet-toggle-fix script - functionality consolidated

        // Add consolidated button update script
        wp_enqueue_script(
            'farmfaucet-button-update-consolidated',
            FARMFAUCET_URL . 'assets/js/button-update-consolidated.js',
            ['jquery', 'farmfaucet-admin-js'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Add milestone page selector script
        wp_enqueue_script(
            'farmfaucet-milestone-page-selector',
            FARMFAUCET_URL . 'assets/js/milestone-page-selector.js',
            ['jquery', 'farmfaucet-admin-js'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Note: Removed conflicting color-picker-fix script - functionality now in consolidated script

        // Note: Removed create-bot-button-fix and admin-captcha-fix scripts - functionality consolidated

        // Add unified captcha script
        wp_enqueue_script(
            'farmfaucet-captcha-unified',
            FARMFAUCET_URL . 'assets/js/captcha-unified.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Note: Removed conflicting color-picker-fix CSS - functionality now in compact appearance CSS

        // Note: Removed form-style-fix script - functionality consolidated

        // Note: Removed view-style-fix CSS - functionality consolidated

        // Add localized script data for toggle switch
        wp_localize_script('farmfaucet-admin-faucet-toggle', 'farmfaucet_admin_vars', [
            'nonce' => wp_create_nonce('farmfaucet_admin_nonce'),
            'ajax_url' => admin_url('admin-ajax.php'),
            'strings' => [
                'enabling' => __('Enabling...', 'farmfaucet'),
                'disabling' => __('Disabling...', 'farmfaucet'),
                'enabled' => __('Enabled', 'farmfaucet'),
                'disabled' => __('Disabled', 'farmfaucet'),
                'error' => __('Error', 'farmfaucet'),
                'connection_error' => __('Connection error', 'farmfaucet')
            ]
        ]);

        // Add localized script data
        wp_localize_script('farmfaucet-admin-js', 'farmfaucet_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_admin_nonce'),
            'strings' => [
                'confirm_delete' => __('Are you sure you want to delete this faucet?', 'farmfaucet'),
                'create_success' => __('Faucet created successfully!', 'farmfaucet'),
                'update_success' => __('Faucet updated successfully!', 'farmfaucet'),
                'delete_success' => __('Faucet deleted successfully!', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    // Add Farmfaucet menu item with diamond pickaxe icon
    public function add_admin_menu()
    {
        // Use a single SVG that works on both desktop and mobile
        $icon_url = FARMFAUCET_URL . 'assets/images/diamond-pickaxe.svg';

        add_menu_page(
            __('Farmfaucet Settings', 'farmfaucet'),
            __('Farmfaucet', 'farmfaucet'),
            'manage_options',
            'farmfaucet',
            [$this, 'render_settings_page'],
            $icon_url, // Diamond pickaxe icon
            80
        );
    }

    // Register settings and fields for the Farmfaucet plugin
    public function register_settings()
    {
        register_setting('farmfaucet_settings', 'farmfaucet_captcha_type', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'hcaptcha'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_hcaptcha_sitekey', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_hcaptcha_secret', [
            'sanitize_callback' => [$this, 'encrypt_api_key_setting']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_recaptcha_sitekey', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_recaptcha_secret', [
            'sanitize_callback' => [$this, 'encrypt_api_key_setting']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_turnstile_sitekey', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_turnstile_secret', [
            'sanitize_callback' => [$this, 'encrypt_api_key_setting']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_faucetpay_api', [
            'sanitize_callback' => [$this, 'encrypt_api_key_setting']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_currency', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_amount', [
            'sanitize_callback' => ['Farmfaucet_Security', 'validate_amount']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_cooldown', [
            'sanitize_callback' => 'absint'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_redirect_url', [
            'sanitize_callback' => ['Farmfaucet_Security', 'validate_url']
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_daily_reset', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Avatar size setting moved to Dashboard tab

        register_setting('farmfaucet_settings', 'farmfaucet_leaderboard_reset_date', [
            'sanitize_callback' => 'sanitize_text_field'
        ]);



        add_settings_section(
            'farmfaucet_main',
            __('Main Settings', 'farmfaucet'),
            [$this, 'render_main_section'],
            'farmfaucet'
        );

        add_settings_field(
            'captcha_type',
            __('Captcha Type', 'farmfaucet'),
            [$this, 'render_captcha_type_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        // Captcha fields are now handled in the captcha_type_field

        add_settings_field(
            'faucetpay_api',
            __('FaucetPay API Key', 'farmfaucet'),
            [$this, 'render_faucetpay_api_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        add_settings_field(
            'currency',
            __('Payout Currency', 'farmfaucet'),
            [$this, 'render_currency_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        add_settings_field(
            'amount',
            __('Amount per Claim', 'farmfaucet'),
            [$this, 'render_amount_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        add_settings_field(
            'cooldown',
            __('Cooldown Period (seconds)', 'farmfaucet'),
            [$this, 'render_cooldown_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        add_settings_field(
            'redirect_url',
            __('Post-claim Redirect URL', 'farmfaucet'),
            [$this, 'render_redirect_url_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        add_settings_field(
            'daily_reset',
            __('Daily Reset Time', 'farmfaucet'),
            [$this, 'render_daily_reset_field'],
            'farmfaucet',
            'farmfaucet_main'
        );

        // Avatar size field moved to Dashboard tab

        add_settings_field(
            'leaderboard_reset_date',
            __('Leaderboard Reset Date', 'farmfaucet'),
            [$this, 'render_leaderboard_reset_field'],
            'farmfaucet',
            'farmfaucet_main'
        );
    }

    // Render the main settings section description
    public function render_main_section()
    {
        echo '<p class="description">' . esc_html__('Configure your faucet settings below:', 'farmfaucet') . '</p>';
    }

    // Render the settings page with tabs and content
    public function render_settings_page()
    {
        // Check if we need to run database updates
        if (isset($_GET['action']) && $_GET['action'] === 'update_db') {
            // Run database updates
            if (class_exists('Farmfaucet_DB_Updater_Consolidated')) {
                Farmfaucet_DB_Updater_Consolidated::run_updates();

                // Show success message
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>Database update completed successfully!</strong></p></div>';
                });
            }
        }

        $current_tab = isset($_GET['tab']) ? sanitize_key($_GET['tab']) : 'settings';
?>
        <div class="wrap farmfaucet-admin">
            <!-- Admin header section -->
            <div class="admin-header">
                <h1><?php esc_html_e('Farmfaucet Settings', 'farmfaucet'); ?></h1>
                <div class="version-author">
                    <span><?php echo esc_html__('Version', 'farmfaucet') . ' ' . FARMFAUCET_VERSION; ?></span>
                    <span><?php esc_html_e('By ZpromoterZ', 'farmfaucet'); ?></span>
                </div>
            </div>

            <!-- Main navigation tabs -->
            <nav class="nav-tab-wrapper">
                <?php foreach (self::ADMIN_TABS as $tab_key => $tab_label) : ?>
                    <a href="?page=farmfaucet&tab=<?php echo esc_attr($tab_key); ?>"
                        class="nav-tab <?php echo $current_tab === $tab_key ? 'nav-tab-active' : ''; ?>">
                        <?php echo esc_html($tab_label); ?>
                    </a>
                <?php endforeach; ?>
            </nav>

            <?php if ('settings' === $current_tab) : ?>
                <!-- Settings tab content -->
                <!-- Shortcodes section moved to Dashboard tab -->

                <form method="post" action="options.php">
                    <?php
                    settings_fields('farmfaucet_settings');
                    do_settings_sections('farmfaucet');
                    submit_button();
                    ?>
                </form>
            <?php elseif ('task_completion' === $current_tab) : ?>
                <!-- Task Completion tab content -->
                <div class="task-completion-section">
                    <?php Farmfaucet_Task_Completion::render_admin_page(); ?>
                </div>

            <?php elseif ('tg_bot_builder' === $current_tab) : ?>
                <!-- Telegram Bot Builder tab content -->
                <div class="tg-bot-builder-section">
                    <?php
                    // Use the clean Bot Builder class
                    if (class_exists('Farmfaucet_Tg_Bot_Builder_Clean')) {
                        Farmfaucet_Tg_Bot_Builder_Clean::render_admin_page();
                    } elseif (class_exists('Farmfaucet_Tg_Bot_Builder_New')) {
                        Farmfaucet_Tg_Bot_Builder_New::render_admin_page();
                    } elseif (class_exists('Farmfaucet_Tg_Bot_Builder')) {
                        Farmfaucet_Tg_Bot_Builder::render_admin_page();
                    } else {
                        echo '<div class="notice notice-error"><p>' . esc_html__('Telegram Bot Builder module is not available.', 'farmfaucet') . '</p></div>';
                    }
                    ?>
                </div>
            <?php elseif ('dashboard' === $current_tab) : ?>
                <!-- Dashboard tab content -->
                <div class="dashboard-section">
                    <?php
                    if (class_exists('Farmfaucet_Dashboard')) {
                        Farmfaucet_Dashboard::render_admin_page();
                    } else {
                        echo '<div class="notice notice-error"><p>' . esc_html__('Dashboard module is not available.', 'farmfaucet') . '</p></div>';
                    }
                    ?>
                </div>

            <?php elseif ('currency_maker' === $current_tab) : ?>
                <!-- Currency Maker tab content -->
                <div class="currency-maker-section">
                    <?php
                    if (class_exists('Farmfaucet_Currency_Maker')) {
                        Farmfaucet_Currency_Maker::render_admin_page();
                    } else {
                        echo '<div class="notice notice-error"><p>' . esc_html__('Currency Maker module is not available.', 'farmfaucet') . '</p></div>';
                    }
                    ?>
                </div>
            <?php elseif ('referral' === $current_tab) : ?>
                <!-- Referral System tab content -->
                <div class="referral-section">
                    <?php
                    if (class_exists('Farmfaucet_Referral')) {
                        Farmfaucet_Referral::render_admin_page();
                    } else {
                        echo '<div class="notice notice-error"><p>' . esc_html__('Referral System module is not available.', 'farmfaucet') . '</p></div>';
                    }
                    ?>
                </div>
            <?php elseif ('advertising' === $current_tab) : ?>
                <!-- Advertising tab content -->
                <div class="advertising-section">
                    <?php
                    if (class_exists('Farmfaucet_Advertising')) {
                        Farmfaucet_Advertising::render_admin_page();
                    } else {
                        echo '<div class="notice notice-error"><p>' . esc_html__('Advertising module is not available.', 'farmfaucet') . '</p></div>';
                    }
                    ?>
                </div>

            <?php elseif ('faucets' === $current_tab) : ?>
                <!-- Faucets tab content -->
                <div class="faucets-section">
                    <div class="faucets-header">
                        <h3><?php esc_html_e('Manage Faucets', 'farmfaucet'); ?></h3>
                        <button id="create-new-faucet" class="button button-primary">
                            <span class="dashicons dashicons-plus"></span> <?php esc_html_e('Create New Faucet', 'farmfaucet'); ?>
                        </button>
                    </div>

                    <div class="faucets-container">
                        <?php $faucets = Farmfaucet_Logger::get_faucets(); ?>

                        <?php if (empty($faucets)) : ?>
                            <div class="no-faucets">
                                <p><?php esc_html_e('No faucets found. Create your first faucet to get started!', 'farmfaucet'); ?></p>
                            </div>
                        <?php else : ?>
                            <div id="faucets-tabs" class="faucets-tabs-container">
                                <ul class="faucets-tabs-nav">
                                    <?php foreach ($faucets as $faucet) : ?>
                                        <li>
                                            <a href="#faucet-<?php echo esc_attr($faucet['id']); ?>">
                                                <?php echo esc_html($faucet['name']); ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                                <?php foreach ($faucets as $faucet) : ?>
                                    <div id="faucet-<?php echo esc_attr($faucet['id']); ?>" class="faucet-tab-content" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>">
                                        <div class="faucet-details">
                                            <h4><?php echo esc_html($faucet['name']); ?></h4>

                                            <div class="faucet-info">
                                                <div class="faucet-info-item faucet-status-toggle">
                                                    <strong><?php esc_html_e('Status:', 'farmfaucet'); ?></strong>
                                                    <span>
                                                        <label class="switch">
                                                            <input type="checkbox" class="faucet-enabled-toggle"
                                                                data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                                                                <?php checked(isset($faucet['is_enabled']) ? $faucet['is_enabled'] : 1); ?>>
                                                            <span class="slider round"></span>
                                                        </label>
                                                        <span class="faucet-status-label">
                                                            <?php echo isset($faucet['is_enabled']) && $faucet['is_enabled'] ? esc_html__('Enabled', 'farmfaucet') : esc_html__('Disabled', 'farmfaucet'); ?>
                                                        </span>
                                                    </span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('Currency:', 'farmfaucet'); ?></strong>
                                                    <span><?php echo esc_html($faucet['currency']); ?></span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('Amount:', 'farmfaucet'); ?></strong>
                                                    <span><?php echo esc_html($faucet['amount']); ?></span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('Cooldown:', 'farmfaucet'); ?></strong>
                                                    <span><?php echo esc_html($this->format_cooldown_time($faucet['cooldown'])); ?></span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('API Key:', 'farmfaucet'); ?></strong>
                                                    <span><?php echo !empty($faucet['api_key']) ? '••••••••' . substr($faucet['api_key'], -4) : esc_html__('Not set', 'farmfaucet'); ?></span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('Captcha:', 'farmfaucet'); ?></strong>
                                                    <span><?php echo !empty($faucet['captcha_type']) ? esc_html(ucfirst($faucet['captcha_type'])) : esc_html__('Default', 'farmfaucet'); ?></span>
                                                </div>
                                                <div class="faucet-info-item">
                                                    <strong><?php esc_html_e('Shortcode:', 'farmfaucet'); ?></strong>
                                                    <code>[<?php echo esc_html($faucet['shortcode']); ?>]</code>
                                                </div>
                                            </div>

                                            <div class="faucet-actions">
                                                <button class="button edit-faucet" data-id="<?php echo esc_attr($faucet['id']); ?>">
                                                    <span class="dashicons dashicons-edit"></span> <?php esc_html_e('Edit', 'farmfaucet'); ?>
                                                </button>
                                                <button class="button button-link-delete delete-faucet" data-id="<?php echo esc_attr($faucet['id']); ?>">
                                                    <span class="dashicons dashicons-trash"></span> <?php esc_html_e('Delete', 'farmfaucet'); ?>
                                                </button>
                                            </div>

                                            <!-- Faucet Buttons Section -->
                                            <div class="faucet-buttons-section">
                                                <h4><?php esc_html_e('Faucet Buttons', 'farmfaucet'); ?></h4>
                                                <?php
                                                $buttons = Farmfaucet_Logger::get_faucet_buttons($faucet['id']);
                                                $button_count = count($buttons);
                                                ?>

                                                <?php if (empty($buttons)) : ?>
                                                    <p class="no-buttons-message"><?php esc_html_e('No buttons created yet.', 'farmfaucet'); ?></p>
                                                <?php else : ?>
                                                    <div class="button-list">
                                                        <?php foreach ($buttons as $button) : ?>
                                                            <div class="button-item" data-id="<?php echo esc_attr($button['id']); ?>">
                                                                <div class="button-header">
                                                                    <h5><?php echo esc_html($button['button_text']); ?></h5>
                                                                    <div class="button-actions">
                                                                        <button class="button edit-button" data-id="<?php echo esc_attr($button['id']); ?>" title="<?php esc_attr_e('Edit Button', 'farmfaucet'); ?>">
                                                                            <span class="dashicons dashicons-edit"></span>
                                                                        </button>
                                                                        <button class="button button-link-delete delete-button" data-id="<?php echo esc_attr($button['id']); ?>" title="<?php esc_attr_e('Delete Button', 'farmfaucet'); ?>">
                                                                            <span class="dashicons dashicons-trash"></span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="button-preview">
                                                                    <a href="#" class="farmfaucet-button" style="
                                                                        background-color: <?php echo esc_attr($this->get_button_color($button['button_color'])); ?>;
                                                                        border-radius: <?php echo esc_attr($button['border_shape'] === 'rounded' ? '20px' : '4px'); ?>;
                                                                        padding: <?php echo esc_attr($this->get_button_padding($button['button_size'])); ?>;
                                                                        display: inline-block;
                                                                        color: white;
                                                                        font-weight: bold;
                                                                        text-align: center;
                                                                        text-decoration: none;
                                                                        min-width: 120px;
                                                                        box-shadow: 0 2px 5px rgba(0,0,0,0.15);
                                                                        transition: all 0.3s ease;
                                                                    ">
                                                                        <?php echo esc_html($button['button_text']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="button-details">
                                                                    <div class="button-detail-item">
                                                                        <strong><?php esc_html_e('Size:', 'farmfaucet'); ?></strong>
                                                                        <span><?php echo esc_html(ucfirst($button['button_size'])); ?></span>
                                                                    </div>
                                                                    <div class="button-detail-item color-detail">
                                                                        <strong><?php esc_html_e('Color:', 'farmfaucet'); ?></strong>
                                                                        <span>
                                                                            <span class="color-swatch" style="background-color: <?php echo esc_attr($this->get_button_color($button['button_color'])); ?>;"></span>
                                                                            <?php echo esc_html(ucfirst($button['button_color'])); ?>
                                                                        </span>
                                                                    </div>
                                                                    <div class="button-detail-item">
                                                                        <strong><?php esc_html_e('Shape:', 'farmfaucet'); ?></strong>
                                                                        <span><?php echo esc_html(ucfirst($button['border_shape'])); ?></span>
                                                                    </div>
                                                                </div>

                                                                <?php if (!empty($button['is_locked'])) : ?>
                                                                    <div class="button-lock-info">
                                                                        <div class="lock-icon"><span class="dashicons dashicons-lock"></span></div>
                                                                        <div class="lock-details">
                                                                            <strong><?php esc_html_e('Locked Button', 'farmfaucet'); ?></strong>
                                                                            <?php if (!empty($button['required_faucets'])) :
                                                                                $faucet_ids = explode(',', $button['required_faucets']);
                                                                                $faucet_names = [];
                                                                                foreach ($faucet_ids as $fid) {
                                                                                    $f = Farmfaucet_Logger::get_faucet($fid);
                                                                                    if ($f) {
                                                                                        $faucet_names[] = $f['name'];
                                                                                    }
                                                                                }
                                                                            ?>
                                                                                <div class="required-faucets">
                                                                                    <span><?php esc_html_e('Required Faucets:', 'farmfaucet'); ?></span>
                                                                                    <span><?php echo esc_html(implode(', ', $faucet_names)); ?></span>
                                                                                </div>
                                                                            <?php endif; ?>

                                                                            <?php if (!empty($button['reset_minutes'])) : ?>
                                                                                <div class="reset-time">
                                                                                    <span><?php esc_html_e('Reset Time:', 'farmfaucet'); ?></span>
                                                                                    <span><?php echo esc_html($button['reset_minutes'] . ' ' . _n('minute', 'minutes', $button['reset_minutes'], 'farmfaucet')); ?></span>
                                                                                </div>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <div class="button-shortcode">
                                                                    <strong><?php esc_html_e('Shortcode:', 'farmfaucet'); ?></strong>
                                                                    <code>[farmfaucet_button id="<?php echo esc_html($button['id']); ?>"]</code>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($button_count < 2) : ?>
                                                    <button class="button button-primary add-button" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>">
                                                        <?php esc_html_e('Add Button', 'farmfaucet'); ?>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Faucet Form Dialog -->
                <div id="faucet-form-dialog" title="<?php esc_attr_e('Faucet Form', 'farmfaucet'); ?>" style="display:none;">
                    <?php include(FARMFAUCET_DIR . 'templates/admin/faucet-form.php'); ?>
                </div>
        </div>
        </form>
        </div>

        <!-- Button Form Dialog -->
        <?php include(FARMFAUCET_DIR . 'templates/admin/button-form.php'); ?>
    <?php else : ?>
        <!-- Transactions tab content -->
        <div class="transaction-log-section">
            <h3><?php esc_html_e('Transaction History', 'farmfaucet'); ?></h3>

            <?php $faucets = Farmfaucet_Logger::get_faucets(); ?>

            <?php if (!empty($faucets)) : ?>
                <div id="transaction-tabs" class="transaction-tabs-container">
                    <ul class="transaction-tabs-nav">
                        <li><a href="#transactions-all"><?php esc_html_e('All Faucets', 'farmfaucet'); ?></a></li>
                        <?php foreach ($faucets as $faucet) : ?>
                            <li>
                                <a href="#transactions-<?php echo esc_attr($faucet['id']); ?>">
                                    <?php echo esc_html($faucet['name']); ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>

                    <div id="transactions-all" class="transaction-tab-content">
                        <div class="transaction-container">
                            <!-- Vertical filter tabs -->
                            <div class="transaction-subtabs">
                                <button class="transaction-subtab" data-type="all">
                                    <?php esc_html_e('All Transactions', 'farmfaucet'); ?>
                                </button>
                                <button class="transaction-subtab" data-type="success">
                                    <?php esc_html_e('Successful', 'farmfaucet'); ?> 💰
                                </button>
                                <button class="transaction-subtab error-tab" data-type="error">
                                    <?php esc_html_e('Failed', 'farmfaucet'); ?> ❌
                                </button>
                            </div>

                            <!-- Transaction messages container -->
                            <div class="transaction-chat-box">
                                <?php
                                $all_transactions = Farmfaucet_Logger::get_logs(100);
                                if (empty($all_transactions)) : ?>
                                    <div class="no-transactions">
                                        <?php esc_html_e('No transactions found', 'farmfaucet'); ?>
                                    </div>
                                    <?php else :
                                    foreach ($all_transactions as $txn) : ?>
                                        <div class="transaction-message <?php echo esc_attr($txn['type']); ?>"
                                            data-type="<?php echo esc_attr($txn['type']); ?>">
                                            <div class="transaction-message-header">
                                                <div class="txn-content">
                                                    <?php echo wp_kses_post($txn['message']); ?>
                                                    <div class="txn-meta">
                                                        <span class="txn-timestamp">
                                                            <?php echo esc_html(date_i18n('M j, Y H:i:s', strtotime($txn['timestamp']))); ?>
                                                        </span>
                                                        <span class="txn-status">
                                                            <?php echo ('error' === $txn['type']) ? '❌' : '💰'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <?php if ('error' === $txn['type']) : ?>
                                                <div class="error-details">
                                                    <?php if (strpos($txn['message'], 'error_code') !== false) : ?>
                                                        <div class="error-code">
                                                            <?php esc_html_e('Error Code:', 'farmfaucet'); ?>
                                                            <strong><?php echo esc_html($this->extract_error_code($txn['message'])); ?></strong>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                <?php endforeach;
                                endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php foreach ($faucets as $faucet) : ?>
                        <div id="transactions-<?php echo esc_attr($faucet['id']); ?>" class="transaction-tab-content">
                            <div class="transaction-container">
                                <!-- Vertical filter tabs -->
                                <div class="transaction-subtabs">
                                    <button class="transaction-subtab" data-type="all" data-faucet="<?php echo esc_attr($faucet['id']); ?>">
                                        <?php esc_html_e('All Transactions', 'farmfaucet'); ?>
                                    </button>
                                    <button class="transaction-subtab" data-type="success" data-faucet="<?php echo esc_attr($faucet['id']); ?>">
                                        <?php esc_html_e('Successful', 'farmfaucet'); ?> 💰
                                    </button>
                                    <button class="transaction-subtab error-tab" data-type="error" data-faucet="<?php echo esc_attr($faucet['id']); ?>">
                                        <?php esc_html_e('Failed', 'farmfaucet'); ?> ❌
                                    </button>
                                </div>

                                <!-- Transaction messages container -->
                                <div class="transaction-chat-box">
                                    <?php
                                    $faucet_transactions = Farmfaucet_Logger::get_logs(100, null, $faucet['id']);
                                    if (empty($faucet_transactions)) : ?>
                                        <div class="no-transactions">
                                            <?php esc_html_e('No transactions found for this faucet', 'farmfaucet'); ?>
                                        </div>
                                        <?php else :
                                        foreach ($faucet_transactions as $txn) : ?>
                                            <div class="transaction-message <?php echo esc_attr($txn['type']); ?>"
                                                data-type="<?php echo esc_attr($txn['type']); ?>">
                                                <div class="transaction-message-header">
                                                    <div class="txn-content">
                                                        <?php echo wp_kses_post($txn['message']); ?>
                                                        <div class="txn-meta">
                                                            <span class="txn-timestamp">
                                                                <?php echo esc_html(date_i18n('M j, Y H:i:s', strtotime($txn['timestamp']))); ?>
                                                            </span>
                                                            <span class="txn-status">
                                                                <?php echo ('error' === $txn['type']) ? '❌' : '💰'; ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <?php if ('error' === $txn['type']) : ?>
                                                    <div class="error-details">
                                                        <?php if (strpos($txn['message'], 'error_code') !== false) : ?>
                                                            <div class="error-code">
                                                                <?php esc_html_e('Error Code:', 'farmfaucet'); ?>
                                                                <strong><?php echo esc_html($this->extract_error_code($txn['message'])); ?></strong>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                    <?php endforeach;
                                    endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <div class="no-faucets-message">
                    <p><?php esc_html_e('No faucets found. Create a faucet first to see transaction logs.', 'farmfaucet'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    </div>
<?php
    }

    // Extract error code from error message
    private function extract_error_code($message)
    {
        preg_match('/error_code[\'"]? => (?<code>[^\s]+)/', $message, $matches);
        return $matches['code'] ?? 'UNKNOWN';
    }

    /**
     * Format cooldown time in a human-readable format
     *
     * @param int $seconds Cooldown time in seconds
     * @return string Formatted time
     */
    public function format_cooldown_time($seconds)
    {
        $seconds = absint($seconds);

        if ($seconds < 60) {
            return sprintf(_n('%d second', '%d seconds', $seconds, 'farmfaucet'), $seconds);
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            return sprintf(_n('%d minute', '%d minutes', $minutes, 'farmfaucet'), $minutes);
        } else {
            $hours = floor($seconds / 3600);
            return sprintf(_n('%d hour', '%d hours', $hours, 'farmfaucet'), $hours);
        }
    }

    /**
     * AJAX handler for creating a new faucet
     */
    public function ajax_create_faucet()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $faucet_type = isset($_POST['faucet_type']) ? sanitize_text_field($_POST['faucet_type']) : 'stage';

        // Ensure faucet_type is one of the allowed values
        if (!in_array($faucet_type, ['stage', 'dummy', 'withdrawal', 'conversion'])) {
            $faucet_type = 'stage';
        }

        $currency = isset($_POST['currency']) ? sanitize_text_field($_POST['currency']) : 'LTC';
        $amount = isset($_POST['amount']) ? sanitize_text_field($_POST['amount']) : '0.001';
        $cooldown = isset($_POST['cooldown']) ? absint($_POST['cooldown']) : 3600;
        $shortcode = isset($_POST['shortcode']) ? sanitize_text_field($_POST['shortcode']) : '';
        $captcha_type = isset($_POST['captcha_type']) ? sanitize_text_field($_POST['captcha_type']) : '';
        $faucet_color = isset($_POST['faucet_color']) ? sanitize_text_field($_POST['faucet_color']) : 'green';

        // Background styling options
        $transparent_bg = isset($_POST['transparent_bg']) ? absint($_POST['transparent_bg']) : 0;
        $bg_style = isset($_POST['bg_style']) ? sanitize_text_field($_POST['bg_style']) : 'solid';
        $bg_color = isset($_POST['bg_color']) ? sanitize_text_field($_POST['bg_color']) : '#f8fff8';
        $bg_gradient_start = isset($_POST['bg_gradient_start']) ? sanitize_text_field($_POST['bg_gradient_start']) : '#f8fff8';
        $bg_gradient_end = isset($_POST['bg_gradient_end']) ? sanitize_text_field($_POST['bg_gradient_end']) : '#e8f5e9';
        $text_color = isset($_POST['text_color']) ? sanitize_text_field($_POST['text_color']) : '#4CAF50';
        $text_shadow = isset($_POST['text_shadow']) ? sanitize_text_field($_POST['text_shadow']) : 'none';

        // Form background options
        $form_bg_color = isset($_POST['form_bg_color']) ? sanitize_text_field($_POST['form_bg_color']) : '#ffffff';
        $form_transparent = isset($_POST['form_transparent']) ? absint($_POST['form_transparent']) : 0;

        // Additional fields for dummy and withdrawal faucets
        $currency_id = isset($_POST['currency_id']) ? absint($_POST['currency_id']) : 0;
        $min_withdrawal = isset($_POST['min_withdrawal']) ? sanitize_text_field($_POST['min_withdrawal']) : '0';
        $view_style = isset($_POST['view_style']) ? sanitize_text_field($_POST['view_style']) : 'default';

        // Process available currencies for withdrawal faucets
        $available_currencies = [];
        if ($faucet_type === 'withdrawal' && isset($_POST['available_currencies']) && is_array($_POST['available_currencies'])) {
            foreach ($_POST['available_currencies'] as $curr) {
                $available_currencies[] = sanitize_text_field($curr);
            }
        }
        $available_currencies_json = !empty($available_currencies) ? json_encode($available_currencies) : '';

        // Validate required fields
        if (empty($name) || empty($shortcode)) {
            wp_send_json_error(['message' => __('Name and shortcode are required', 'farmfaucet')]);
        }

        // Check if shortcode already exists
        $existing_faucet = Farmfaucet_Logger::get_faucet_by_shortcode($shortcode);
        if ($existing_faucet) {
            wp_send_json_error(['message' => __('Shortcode already exists', 'farmfaucet')]);
        }

        // Get API key if provided and encrypt it
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        if (!empty($api_key)) {
            $api = new Farmfaucet_API();
            $api_key = $api->encrypt_api_key($api_key);
        }

        // Get button and border settings
        $button_color = isset($_POST['button_color']) ? sanitize_text_field($_POST['button_color']) : '#4CAF50';
        $border_color = isset($_POST['border_color']) ? sanitize_text_field($_POST['border_color']) : '#4CAF50';
        $border_radius = isset($_POST['border_radius']) ? sanitize_text_field($_POST['border_radius']) : '8px';

        // Create faucet data array with common fields
        $faucet_data = [
            'name' => $name,
            'faucet_type' => $faucet_type,
            'currency' => $currency,
            'amount' => $amount,
            'cooldown' => $cooldown,
            'shortcode' => $shortcode,
            'api_key' => $api_key,
            'captcha_type' => $captcha_type,
            'faucet_color' => $faucet_color,
            'transparent_bg' => $transparent_bg,
            'bg_style' => $bg_style,
            'bg_color' => $bg_color,
            'bg_gradient_start' => $bg_gradient_start,
            'bg_gradient_end' => $bg_gradient_end,
            'text_color' => $text_color,
            'text_shadow' => $text_shadow,
            'button_color' => $button_color,
            'border_color' => $border_color,
            'border_radius' => $border_radius,
            'form_bg_color' => $form_bg_color,
            'form_transparent' => $form_transparent
        ];

        // Add type-specific fields
        if ($faucet_type === 'dummy' || $faucet_type === 'withdrawal' || $faucet_type === 'conversion') {
            $faucet_data['currency_id'] = $currency_id;
            $faucet_data['view_style'] = $view_style;

            if ($faucet_type === 'withdrawal') {
                $faucet_data['min_withdrawal'] = $min_withdrawal;
                $faucet_data['available_currencies'] = $available_currencies_json;
            } else if ($faucet_type === 'conversion') {
                // For conversion faucets, store the minimum conversion amount in min_withdrawal field
                $faucet_data['min_withdrawal'] = $min_withdrawal;

                // Process conversion currencies
                $conversion_currencies = [];
                if (isset($_POST['conversion_currencies']) && is_array($_POST['conversion_currencies'])) {
                    foreach ($_POST['conversion_currencies'] as $curr) {
                        $conversion_currencies[] = sanitize_text_field($curr);
                    }
                }
                $conversion_currencies_json = !empty($conversion_currencies) ? json_encode($conversion_currencies) : '';
                $faucet_data['conversion_currencies'] = $conversion_currencies_json;
            }
        }

        // Create faucet
        $faucet_id = Farmfaucet_Logger::create_faucet($faucet_data);

        if ($faucet_id) {
            wp_send_json_success([
                'message' => __('Faucet created successfully', 'farmfaucet'),
                'faucet_id' => $faucet_id
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to create faucet', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for updating a faucet
     */
    public function ajax_update_faucet()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $faucet_type = isset($_POST['faucet_type']) ? sanitize_text_field($_POST['faucet_type']) : 'stage';

        // Ensure faucet_type is one of the allowed values
        if (!in_array($faucet_type, ['stage', 'dummy', 'withdrawal', 'conversion'])) {
            $faucet_type = 'stage';
        }

        $currency = isset($_POST['currency']) ? sanitize_text_field($_POST['currency']) : 'LTC';
        $amount = isset($_POST['amount']) ? sanitize_text_field($_POST['amount']) : '0.001';
        $cooldown = isset($_POST['cooldown']) ? absint($_POST['cooldown']) : 3600;
        $shortcode = isset($_POST['shortcode']) ? sanitize_text_field($_POST['shortcode']) : '';
        $captcha_type = isset($_POST['captcha_type']) ? sanitize_text_field($_POST['captcha_type']) : '';
        $faucet_color = isset($_POST['faucet_color']) ? sanitize_text_field($_POST['faucet_color']) : 'green';

        // Background styling options
        $transparent_bg = isset($_POST['transparent_bg']) ? absint($_POST['transparent_bg']) : 0;
        $bg_style = isset($_POST['bg_style']) ? sanitize_text_field($_POST['bg_style']) : 'solid';
        $bg_color = isset($_POST['bg_color']) ? sanitize_text_field($_POST['bg_color']) : '#f8fff8';
        $bg_gradient_start = isset($_POST['bg_gradient_start']) ? sanitize_text_field($_POST['bg_gradient_start']) : '#f8fff8';
        $bg_gradient_end = isset($_POST['bg_gradient_end']) ? sanitize_text_field($_POST['bg_gradient_end']) : '#e8f5e9';
        $text_color = isset($_POST['text_color']) ? sanitize_text_field($_POST['text_color']) : '#4CAF50';
        $text_shadow = isset($_POST['text_shadow']) ? sanitize_text_field($_POST['text_shadow']) : 'none';
        $button_color = isset($_POST['button_color']) ? sanitize_text_field($_POST['button_color']) : '#4CAF50';
        $border_color = isset($_POST['border_color']) ? sanitize_text_field($_POST['border_color']) : '#4CAF50';
        $border_radius = isset($_POST['border_radius']) ? sanitize_text_field($_POST['border_radius']) : '8px';

        // Form background options
        $form_bg_color = isset($_POST['form_bg_color']) ? sanitize_text_field($_POST['form_bg_color']) : '#ffffff';
        $form_transparent = isset($_POST['form_transparent']) ? absint($_POST['form_transparent']) : 0;

        // Additional fields for dummy and withdrawal faucets
        $currency_id = isset($_POST['currency_id']) ? absint($_POST['currency_id']) : 0;
        $min_withdrawal = isset($_POST['min_withdrawal']) ? sanitize_text_field($_POST['min_withdrawal']) : '0';
        $view_style = isset($_POST['view_style']) ? sanitize_text_field($_POST['view_style']) : 'default';

        // Process available currencies for withdrawal faucets
        $available_currencies = [];
        if (isset($_POST['available_currencies']) && is_array($_POST['available_currencies'])) {
            foreach ($_POST['available_currencies'] as $curr) {
                $available_currencies[] = sanitize_text_field($curr);
            }
        }
        $available_currencies_json = !empty($available_currencies) ? json_encode($available_currencies) : '';

        // Validate required fields
        if (empty($id) || empty($name) || empty($shortcode)) {
            wp_send_json_error(['message' => __('ID, name, and shortcode are required', 'farmfaucet')]);
        }

        // Check if faucet exists
        $faucet = Farmfaucet_Logger::get_faucet($id);
        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
        }

        // Check if shortcode already exists (for another faucet)
        $existing_faucet = Farmfaucet_Logger::get_faucet_by_shortcode($shortcode);
        if ($existing_faucet && $existing_faucet['id'] != $id) {
            wp_send_json_error(['message' => __('Shortcode already exists', 'farmfaucet')]);
        }

        // Get API key if provided and encrypt it
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        if (!empty($api_key)) {
            $api = new Farmfaucet_API();
            $api_key = $api->encrypt_api_key($api_key);
        }

        // Get faucet to determine its type
        $faucet = Farmfaucet_Logger::get_faucet($id);
        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
            return;
        }

        // Create faucet data array with common fields
        $faucet_data = [
            'name' => $name,
            'faucet_type' => $faucet_type, // Add faucet_type to the update data
            'currency' => $currency,
            'amount' => $amount,
            'cooldown' => $cooldown,
            'shortcode' => $shortcode,
            'api_key' => $api_key,
            'captcha_type' => $captcha_type,
            'faucet_color' => $faucet_color,
            'transparent_bg' => $transparent_bg,
            'bg_style' => $bg_style,
            'bg_color' => $bg_color,
            'bg_gradient_start' => $bg_gradient_start,
            'bg_gradient_end' => $bg_gradient_end,
            'text_color' => $text_color,
            'text_shadow' => $text_shadow,
            'button_color' => $button_color,
            'border_color' => $border_color,
            'border_radius' => $border_radius,
            'form_bg_color' => $form_bg_color,
            'form_transparent' => $form_transparent
        ];

        // Add type-specific fields based on the selected faucet type
        if ($faucet_type === 'dummy' || $faucet_type === 'withdrawal' || $faucet_type === 'conversion') {
            $faucet_data['currency_id'] = $currency_id;
            $faucet_data['view_style'] = $view_style;

            if ($faucet_type === 'withdrawal') {
                $faucet_data['min_withdrawal'] = $min_withdrawal;
                $faucet_data['available_currencies'] = $available_currencies_json;
                $faucet_data['ads_only_conversion'] = isset($_POST['ads_only_conversion']) ? (int)$_POST['ads_only_conversion'] : 0;
            } else if ($faucet_type === 'conversion') {
                // For conversion faucets, store the minimum conversion amount in min_withdrawal field
                $faucet_data['min_withdrawal'] = $min_withdrawal;

                // Process conversion currencies
                $conversion_currencies = [];
                if (isset($_POST['conversion_currencies']) && is_array($_POST['conversion_currencies'])) {
                    foreach ($_POST['conversion_currencies'] as $curr) {
                        $conversion_currencies[] = sanitize_text_field($curr);
                    }
                }
                $conversion_currencies_json = !empty($conversion_currencies) ? json_encode($conversion_currencies) : '';
                $faucet_data['conversion_currencies'] = $conversion_currencies_json;
            }
        }

        // Update faucet
        $result = Farmfaucet_Logger::update_faucet($id, $faucet_data);

        if ($result) {
            wp_send_json_success(['message' => __('Faucet updated successfully', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to update faucet', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for deleting a faucet
     */
    public function ajax_delete_faucet()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Validate required fields
        if (empty($id)) {
            wp_send_json_error(['message' => __('Faucet ID is required', 'farmfaucet')]);
        }

        // Check if faucet exists
        $faucet = Farmfaucet_Logger::get_faucet($id);
        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
        }

        // Delete faucet
        $result = Farmfaucet_Logger::delete_faucet($id);

        if ($result) {
            // Clear logs for this faucet
            Farmfaucet_Logger::clear_logs($id);

            wp_send_json_success(['message' => __('Faucet deleted successfully', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete faucet', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for toggling faucet enabled/disabled status
     */
    public function ajax_toggle_faucet_status()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
            return;
        }

        // Validate and sanitize data
        $id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;
        $is_enabled = isset($_POST['is_enabled']) ? (int)$_POST['is_enabled'] : 0;

        // Validate required fields
        if (empty($id)) {
            wp_send_json_error(['message' => __('Faucet ID is required', 'farmfaucet')]);
            return;
        }

        // Make sure the faucet exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';
        $faucet = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);

        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
            return;
        }

        // Toggle the faucet status directly with the database to ensure it works
        $result = $wpdb->update(
            $table_name,
            ['is_enabled' => $is_enabled ? 1 : 0, 'updated_at' => current_time('mysql')],
            ['id' => absint($id)],
            ['%d', '%s'],
            ['%d']
        );

        if ($result !== false) {
            // Log the action
            Farmfaucet_Logger::log(
                sprintf(
                    __('Faucet "%s" was %s by admin', 'farmfaucet'),
                    $faucet['name'],
                    $is_enabled ? __('enabled', 'farmfaucet') : __('disabled', 'farmfaucet')
                ),
                'admin',
                $id
            );

            wp_send_json_success([
                'message' => $is_enabled ? __('Faucet enabled successfully', 'farmfaucet') : __('Faucet disabled successfully', 'farmfaucet'),
                'status' => $is_enabled ? 'enabled' : 'disabled'
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to update faucet status', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for getting faucet data
     */
    public function ajax_get_faucet()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
            return;
        }

        // Validate and sanitize data
        $id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Validate required fields
        if (empty($id)) {
            wp_send_json_error(['message' => __('Faucet ID is required', 'farmfaucet')]);
            return;
        }

        // Get faucet data
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';
        $faucet = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id), ARRAY_A);

        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
            return;
        }

        // Return faucet data
        wp_send_json_success($faucet);
    }

    // Render the captcha type field
    public function render_captcha_type_field()
    {
        $value = get_option('farmfaucet_captcha_type', 'hcaptcha');

        // Get captcha configuration status
        $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
        $hcaptcha_secret = get_option('farmfaucet_hcaptcha_secret', '');
        $hcaptcha_configured = !empty($hcaptcha_sitekey) && !empty($hcaptcha_secret);

        $recaptcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
        $recaptcha_secret = get_option('farmfaucet_recaptcha_secret', '');
        $recaptcha_configured = !empty($recaptcha_sitekey) && !empty($recaptcha_secret);

        $turnstile_sitekey = get_option('farmfaucet_turnstile_sitekey', '');
        $turnstile_secret = get_option('farmfaucet_turnstile_secret', '');
        $turnstile_configured = !empty($turnstile_sitekey) && !empty($turnstile_secret);

        echo '<div class="captcha-settings-container">';

        // Default captcha selector
        echo '<div class="captcha-type-selector">';
        echo '<label class="settings-label">' . esc_html__('Default Captcha', 'farmfaucet') . '</label>';
        echo '<select name="farmfaucet_captcha_type" id="farmfaucet-captcha-type" class="regular-text">';
        echo '<option value="hcaptcha"' . ($value === 'hcaptcha' ? ' selected' : '') . '>' . esc_html__('hCaptcha', 'farmfaucet') . '</option>';
        echo '<option value="recaptcha"' . ($value === 'recaptcha' ? ' selected' : '') . '>' . esc_html__('reCAPTCHA v2', 'farmfaucet') . '</option>';
        echo '<option value="turnstile"' . ($value === 'turnstile' ? ' selected' : '') . '>' . esc_html__('Cloudflare Turnstile', 'farmfaucet') . '</option>';
        echo '</select>';
        echo '<p class="description">' . esc_html__('Select which captcha service to use as the default', 'farmfaucet') . '</p>';
        echo '</div>';

        // Captcha configuration section
        echo '<div class="captcha-config-section">';
        echo '<h3 class="settings-heading">' . esc_html__('Captcha Configuration', 'farmfaucet') . '</h3>';

        // hCaptcha settings
        echo '<div class="captcha-config-item" id="hcaptcha-config">';
        echo '<div class="captcha-header">';
        echo '<span class="captcha-name">' . esc_html__('hCaptcha', 'farmfaucet') . '</span>';
        echo '<span class="captcha-status ' . ($hcaptcha_configured ? 'configured' : 'not-configured') . '">';
        echo $hcaptcha_configured ? esc_html__('Configured', 'farmfaucet') : esc_html__('Not Configured', 'farmfaucet');
        echo '</span>';
        echo '</div>';

        echo '<div class="captcha-fields">';
        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Site Key', 'farmfaucet') . '</label>';
        echo '<input type="text" name="farmfaucet_hcaptcha_sitekey" value="' . esc_attr($hcaptcha_sitekey) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Get this from hCaptcha dashboard', 'farmfaucet') . '</p>';
        echo '</div>';

        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Secret Key', 'farmfaucet') . '</label>';
        echo '<input type="password" name="farmfaucet_hcaptcha_secret" value="' . esc_attr($hcaptcha_secret) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Keep this secret! Obtain from hCaptcha dashboard', 'farmfaucet') . '</p>';
        echo '</div>';
        echo '</div>'; // End hCaptcha fields
        echo '</div>'; // End hCaptcha item

        // reCAPTCHA settings
        echo '<div class="captcha-config-item" id="recaptcha-config">';
        echo '<div class="captcha-header">';
        echo '<span class="captcha-name">' . esc_html__('reCAPTCHA v2', 'farmfaucet') . '</span>';
        echo '<span class="captcha-status ' . ($recaptcha_configured ? 'configured' : 'not-configured') . '">';
        echo $recaptcha_configured ? esc_html__('Configured', 'farmfaucet') : esc_html__('Not Configured', 'farmfaucet');
        echo '</span>';
        echo '</div>';

        echo '<div class="captcha-fields">';
        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Site Key', 'farmfaucet') . '</label>';
        echo '<input type="text" name="farmfaucet_recaptcha_sitekey" value="' . esc_attr($recaptcha_sitekey) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Get this from Google reCAPTCHA dashboard', 'farmfaucet') . '</p>';
        echo '</div>';

        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Secret Key', 'farmfaucet') . '</label>';
        echo '<input type="password" name="farmfaucet_recaptcha_secret" value="' . esc_attr($recaptcha_secret) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Keep this secret! Obtain from Google reCAPTCHA dashboard', 'farmfaucet') . '</p>';
        echo '</div>';
        echo '</div>'; // End reCAPTCHA fields
        echo '</div>'; // End reCAPTCHA item

        // Cloudflare Turnstile settings
        echo '<div class="captcha-config-item" id="turnstile-config">';
        echo '<div class="captcha-header">';
        echo '<span class="captcha-name">' . esc_html__('Cloudflare Turnstile', 'farmfaucet') . '</span>';
        echo '<span class="captcha-status ' . ($turnstile_configured ? 'configured' : 'not-configured') . '">';
        echo $turnstile_configured ? esc_html__('Configured', 'farmfaucet') : esc_html__('Not Configured', 'farmfaucet');
        echo '</span>';
        echo '</div>';

        echo '<div class="captcha-fields">';
        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Site Key', 'farmfaucet') . '</label>';
        echo '<input type="text" name="farmfaucet_turnstile_sitekey" value="' . esc_attr($turnstile_sitekey) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Get this from Cloudflare Turnstile dashboard', 'farmfaucet') . '</p>';
        echo '</div>';

        echo '<div class="captcha-field-group">';
        echo '<label class="settings-label">' . esc_html__('Secret Key', 'farmfaucet') . '</label>';
        echo '<input type="password" name="farmfaucet_turnstile_secret" value="' . esc_attr($turnstile_secret) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Keep this secret! Obtain from Cloudflare Turnstile dashboard', 'farmfaucet') . '</p>';
        echo '</div>';
        echo '</div>'; // End Turnstile fields
        echo '</div>'; // End Turnstile item

        echo '</div>'; // End captcha-config-section
        echo '</div>'; // End container

        // Add JavaScript for captcha settings
        echo '<script>
            jQuery(document).ready(function($) {
                // Add spacing between captcha config items
                $(".captcha-config-item").not(:last-child).css("margin-bottom", "20px");

                // Make the captcha headers clickable to toggle their content
                $(".captcha-header").css("cursor", "pointer").on("click", function() {
                    $(this).next(".captcha-fields").slideToggle();
                    $(this).toggleClass("active");
                });

                // Initially hide all captcha fields
                $(".captcha-fields").hide();

                // Show the selected captcha type fields
                var selectedType = $("#farmfaucet-captcha-type").val();
                $("#" + selectedType + "-config .captcha-fields").show();
                $("#" + selectedType + "-config .captcha-header").addClass("active");

                // Update active section when captcha type changes
                $("#farmfaucet-captcha-type").on("change", function() {
                    var captchaType = $(this).val();
                    $(".captcha-header").removeClass("active");
                    $(".captcha-fields").slideUp();
                    $("#" + captchaType + "-config .captcha-header").addClass("active");
                    $("#" + captchaType + "-config .captcha-fields").slideDown();
                });
            });
        </script>';
    }

    // Render the hCaptcha site key field - now handled in captcha_type_field
    public function render_hcaptcha_sitekey_field()
    {
        // Empty as this is now handled in the captcha_type_field function
    }

    // Render the hCaptcha secret key field - now handled in captcha_type_field
    public function render_hcaptcha_secret_field()
    {
        // Empty as this is now handled in the captcha_type_field function
    }

    // Render the reCAPTCHA site key field - now handled in captcha_type_field
    public function render_recaptcha_sitekey_field()
    {
        // Empty as this is now handled in the captcha_type_field function
    }

    // Render the reCAPTCHA secret key field - now handled in captcha_type_field
    public function render_recaptcha_secret_field()
    {
        // Empty as this is now handled in the captcha_type_field function
    }

    // Render the FaucetPay API key field
    public function render_faucetpay_api_field()
    {
        $value = get_option('farmfaucet_faucetpay_api');
        echo '<input type="password" name="farmfaucet_faucetpay_api" value="' . esc_attr($value) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Get API key from FaucetPay account settings', 'farmfaucet') . '</p>';
    }

    // Render the currency selection field
    public function render_currency_field()
    {
        $value = get_option('farmfaucet_currency', 'LTC');
        $currencies = [
            'LTC' => 'Litecoin',
            'BTC' => 'Bitcoin',
            'DOGE' => 'Dogecoin',
            'ETH' => 'Ethereum',
            'BCH' => 'Bitcoin Cash',
            'TRX' => 'TRON'
        ];

        echo '<select name="farmfaucet_currency" class="regular-text">';
        foreach ($currencies as $code => $name) {
            echo '<option value="' . esc_attr($code) . '"' . ($value === $code ? ' selected' : '') . '>'
                . esc_html("$code - $name") . '</option>';
        }
        echo '</select>';
    }

    // Render the amount per claim field
    public function render_amount_field()
    {
        $value = get_option('farmfaucet_amount', '0.001');
        echo '<input type="text"
                     name="farmfaucet_amount"
                     value="' . esc_attr($value) . '"
                     class="regular-text"
                     pattern="^[0-9]+(\.[0-9]{1,8})?$"
                     title="' . esc_attr__('Enter decimal value (e.g. 0.00009775)', 'farmfaucet') . '">';
        echo '<p class="description">' . esc_html__('Amount must be in standard decimal format', 'farmfaucet') . '</p>';
    }

    // Render the cooldown period field
    public function render_cooldown_field()
    {
        $value = get_option('farmfaucet_cooldown', '3600');
        echo '<input type="number" name="farmfaucet_cooldown" value="' . esc_attr($value) . '" class="small-text">';
        echo '<p class="description">' . esc_html__('Time between claims in seconds (3600 = 1 hour)', 'farmfaucet') . '</p>';
    }

    // Render the redirect URL field
    public function render_redirect_url_field()
    {
        $value = get_option('farmfaucet_redirect_url');
        echo '<input type="url"
                     name="farmfaucet_redirect_url"
                     value="' . esc_url($value) . '"
                     class="regular-text"
                     placeholder="https://example.com/offer">';
        echo '<p class="description">' . esc_html__('Users will be redirected here after successful claim', 'farmfaucet') . '</p>';
    }

    // Render the daily reset time field
    public function render_daily_reset_field()
    {
        $value = get_option('farmfaucet_daily_reset', '');
        echo '<input type="time"
                     name="farmfaucet_daily_reset"
                     value="' . esc_attr($value) . '"
                     class="regular-text">';
        echo '<p class="description">' . esc_html__('Set a time for daily reset of all completed faucets (e.g., 00:00 for midnight). Leave empty to disable.', 'farmfaucet') . '</p>';
    }

    // Avatar size field moved to Dashboard tab

    // Render the leaderboard reset date field
    public function render_leaderboard_reset_field()
    {
        $value = get_option('farmfaucet_leaderboard_reset_date', '');

        echo '<div class="leaderboard-reset-field">';
        echo '<input type="date" name="farmfaucet_leaderboard_reset_date" value="' . esc_attr($value) . '" class="regular-text">';
        echo '<p class="description">' . esc_html__('Set a future date when the leaderboard will reset. Leave empty for no reset.', 'farmfaucet') . '</p>';

        // If a date is set, show days remaining
        if (!empty($value)) {
            $reset_date = new DateTime($value);
            $today = new DateTime();
            $days_remaining = $today->diff($reset_date)->days;

            if ($reset_date > $today) {
                echo '<div class="days-remaining">';
                echo '<strong>' . esc_html__('Days until reset:', 'farmfaucet') . '</strong> ';
                echo '<span>' . esc_html($days_remaining) . '</span>';
                echo '</div>';
            } else {
                echo '<div class="days-remaining past-date">';
                echo '<strong>' . esc_html__('Reset date has passed!', 'farmfaucet') . '</strong> ';
                echo '</div>';
            }
        }

        echo '</div>';
    }

    /**
     * Encrypt API key before saving to database
     *
     * @param string $api_key The API key to encrypt
     * @return string Encrypted API key
     */
    public function encrypt_api_key_setting($api_key)
    {
        // First sanitize the API key
        $api_key = Farmfaucet_Security::sanitize_api_key($api_key);

        // If empty, return as is
        if (empty($api_key)) {
            return $api_key;
        }

        // Encrypt the API key
        $api = new Farmfaucet_API();
        return $api->encrypt_api_key($api_key);
    }

    /**
     * Get button color based on color name
     *
     * @param string $color_name Color name
     * @return string CSS color value
     */
    private function get_button_color($color_name)
    {
        $colors = [
            'blue' => '#2271b1',
            'green' => '#00a32a',
            'red' => '#d63638',
            'orange' => '#f56e28',
            'purple' => '#8c3db9',
            'black' => '#1d2327',
            'pink' => '#e91e63',
            'violet' => '#9c27b0',
            'deep-purple' => '#673ab7',
            'indigo' => '#3f51b5',
            'light-blue' => '#03a9f4',
            'teal' => '#009688',
            'light-green' => '#4caf50',
            'lime' => '#8bc34a',
            'yellow-green' => '#cddc39',
            'yellow' => '#ffeb3b',
            'amber' => '#ffc107',
            'deep-orange' => '#ff9800'
        ];

        // If the color is a hex code, return it directly
        if (preg_match('/^#[0-9a-f]{3,6}$/i', $color_name)) {
            return $color_name;
        }

        return isset($colors[$color_name]) ? $colors[$color_name] : '#2271b1';
    }

    /**
     * Get button padding based on size
     *
     * @param string $size Button size
     * @return string CSS padding value
     */
    private function get_button_padding($size)
    {
        $sizes = [
            'small' => '4px 8px',
            'medium' => '8px 16px',
            'large' => '12px 24px'
        ];

        return isset($sizes[$size]) ? $sizes[$size] : '8px 16px';
    }

    /**
     * AJAX handler for creating a new button
     */
    public function ajax_create_button()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;
        $button_text = isset($_POST['button_text']) ? sanitize_text_field($_POST['button_text']) : '';
        $button_size = isset($_POST['button_size']) ? sanitize_text_field($_POST['button_size']) : 'medium';
        $button_color = isset($_POST['button_color']) ? sanitize_text_field($_POST['button_color']) : 'blue';

        // Use our custom color utils class for hex color validation
        require_once(FARMFAUCET_DIR . 'includes/class-farmfaucet-color-utils.php');
        $button_color_hex = isset($_POST['button_color_hex']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['button_color_hex']) : '';
        $border_shape = isset($_POST['border_shape']) ? sanitize_text_field($_POST['border_shape']) : 'rounded';
        $redirect_url = isset($_POST['redirect_url']) ? esc_url_raw($_POST['redirect_url']) : '';
        $is_locked = isset($_POST['is_locked']) ? absint($_POST['is_locked']) : 0;
        $required_faucets = isset($_POST['required_faucets']) ? $_POST['required_faucets'] : [];
        $reset_minutes = isset($_POST['reset_minutes']) ? absint($_POST['reset_minutes']) : 0;
        $lock_faucet = isset($_POST['lock_faucet']) ? absint($_POST['lock_faucet']) : 0;

        // Get milestone options
        $milestone_enabled = isset($_POST['milestone_enabled']) ? absint($_POST['milestone_enabled']) : 0;
        $milestone_type = isset($_POST['milestone_type']) ? sanitize_text_field($_POST['milestone_type']) : 'global';
        $milestone_count = isset($_POST['milestone_count']) ? absint($_POST['milestone_count']) : 5;
        $milestone_lock_faucet = isset($_POST['milestone_lock_faucet']) ? absint($_POST['milestone_lock_faucet']) : 0;
        $milestone_display_style = isset($_POST['milestone_display_style']) ? sanitize_text_field($_POST['milestone_display_style']) : 'card';

        // Get milestone card appearance options
        $milestone_transparent_bg = isset($_POST['milestone_transparent_bg']) ? absint($_POST['milestone_transparent_bg']) : 0;
        $milestone_card_bg_style = isset($_POST['milestone_card_bg_style']) ? sanitize_text_field($_POST['milestone_card_bg_style']) : 'solid';
        $milestone_card_bg_color = isset($_POST['milestone_card_bg_color']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_bg_color']) : '#FFFFFF';
        $milestone_card_gradient_start = isset($_POST['milestone_card_gradient_start']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_gradient_start']) : '#FFFFFF';
        $milestone_card_gradient_end = isset($_POST['milestone_card_gradient_end']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_gradient_end']) : '#F5F5F5';

        // Get milestone progress bar options
        $milestone_bar_style = isset($_POST['milestone_bar_style']) ? sanitize_text_field($_POST['milestone_bar_style']) : 'solid';
        $milestone_bar_color = isset($_POST['milestone_bar_color']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_bar_color']) : '#4CAF50';
        $milestone_gradient_start = isset($_POST['milestone_gradient_start']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_gradient_start']) : '#4CAF50';
        $milestone_gradient_end = isset($_POST['milestone_gradient_end']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_gradient_end']) : '#2196F3';

        // Process milestone pages if provided
        $milestone_pages = '';
        if ($milestone_enabled && $milestone_type === 'page_specific' && isset($_POST['milestone_pages'])) {
            // Handle both array and JSON string formats
            if (is_array($_POST['milestone_pages'])) {
                $milestone_pages = json_encode($_POST['milestone_pages']);
            } elseif (is_string($_POST['milestone_pages']) && !empty($_POST['milestone_pages'])) {
                // Validate that it's a valid JSON string
                $decoded = json_decode($_POST['milestone_pages'], true);
                if ($decoded !== null) {
                    $milestone_pages = $_POST['milestone_pages'];
                } else {
                    // If not valid JSON, create an empty JSON object
                    $milestone_pages = '{}';
                }
            } else {
                // Default to empty JSON object
                $milestone_pages = '{}';
            }
        }

        // Get countdown options
        $countdown_enabled = isset($_POST['countdown_enabled']) ? absint($_POST['countdown_enabled']) : 0;
        $countdown_seconds = isset($_POST['countdown_seconds']) ? absint($_POST['countdown_seconds']) : 60;
        $countdown_message = isset($_POST['countdown_message']) ? sanitize_text_field($_POST['countdown_message']) : '';
        $countdown_click_activation = isset($_POST['countdown_click_activation']) ? absint($_POST['countdown_click_activation']) : 0;
        $countdown_click_element_id = isset($_POST['countdown_click_element_id']) ? sanitize_text_field($_POST['countdown_click_element_id']) : '';
        $countdown_pre_click_message = isset($_POST['countdown_pre_click_message']) ? sanitize_text_field($_POST['countdown_pre_click_message']) : '';

        // Validate required fields
        if (empty($faucet_id) || empty($button_text)) {
            wp_send_json_error(['message' => __('Faucet ID and button text are required', 'farmfaucet')]);
        }

        // Check if faucet exists
        $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        if (!$faucet) {
            wp_send_json_error(['message' => __('Faucet not found', 'farmfaucet')]);
        }

        // Check if faucet already has 2 buttons
        $button_count = Farmfaucet_Logger::count_faucet_buttons($faucet_id);
        if ($button_count >= 2) {
            wp_send_json_error(['message' => __('Maximum of 2 buttons per faucet allowed', 'farmfaucet')]);
        }

        // Generate a unique shortcode for the button
        $shortcode = 'button_' . $faucet_id . '_' . time();

        // Create button
        $button_id = Farmfaucet_Logger::create_button([
            'faucet_id' => $faucet_id,
            'button_text' => $button_text,
            'button_size' => $button_size,
            'button_color' => $button_color,
            'button_color_hex' => $button_color_hex,
            'border_shape' => $border_shape,
            'redirect_url' => $redirect_url,
            'shortcode' => $shortcode,
            'is_locked' => $is_locked,
            'required_faucets' => $required_faucets,
            'reset_minutes' => $reset_minutes,
            'lock_faucet' => $lock_faucet,
            'milestone_enabled' => $milestone_enabled,
            'milestone_type' => $milestone_type,
            'milestone_count' => $milestone_count,
            'milestone_lock_faucet' => $milestone_lock_faucet,
            'milestone_display_style' => $milestone_display_style,
            'milestone_transparent_bg' => $milestone_transparent_bg,
            'milestone_card_bg_style' => $milestone_card_bg_style,
            'milestone_card_bg_color' => $milestone_card_bg_color,
            'milestone_card_gradient_start' => $milestone_card_gradient_start,
            'milestone_card_gradient_end' => $milestone_card_gradient_end,
            'milestone_pages' => $milestone_pages,
            'milestone_bar_style' => $milestone_bar_style,
            'milestone_bar_color' => $milestone_bar_color,
            'milestone_gradient_start' => $milestone_gradient_start,
            'milestone_gradient_end' => $milestone_gradient_end,
            'countdown_enabled' => $countdown_enabled,
            'countdown_seconds' => $countdown_seconds,
            'countdown_message' => $countdown_message,
            'countdown_click_activation' => $countdown_click_activation,
            'countdown_click_element_id' => $countdown_click_element_id,
            'countdown_pre_click_message' => $countdown_pre_click_message
        ]);

        if ($button_id) {
            wp_send_json_success([
                'message' => __('Button created successfully', 'farmfaucet'),
                'button_id' => $button_id,
                'button' => Farmfaucet_Logger::get_button($button_id)
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to create button', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for updating a button
     */
    public function ajax_update_button()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $button_id = isset($_POST['button_id']) ? absint($_POST['button_id']) : 0;
        $button_text = isset($_POST['button_text']) ? sanitize_text_field($_POST['button_text']) : '';
        $button_size = isset($_POST['button_size']) ? sanitize_text_field($_POST['button_size']) : 'medium';
        $button_color = isset($_POST['button_color']) ? sanitize_text_field($_POST['button_color']) : 'blue';

        // Use our custom color utils class for hex color validation
        require_once(FARMFAUCET_DIR . 'includes/class-farmfaucet-color-utils.php');
        $button_color_hex = isset($_POST['button_color_hex']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['button_color_hex']) : '';
        $border_shape = isset($_POST['border_shape']) ? sanitize_text_field($_POST['border_shape']) : 'rounded';
        $redirect_url = isset($_POST['redirect_url']) ? esc_url_raw($_POST['redirect_url']) : '';

        // Validate required fields
        if (empty($button_id) || empty($button_text)) {
            wp_send_json_error(['message' => __('Button ID and text are required', 'farmfaucet')]);
        }

        // Check if button exists
        $button = Farmfaucet_Logger::get_button($button_id);
        if (!$button) {
            wp_send_json_error(['message' => __('Button not found', 'farmfaucet')]);
        }

        // Get lock options
        $is_locked = isset($_POST['is_locked']) ? absint($_POST['is_locked']) : 0;
        $required_faucets = isset($_POST['required_faucets']) ? $_POST['required_faucets'] : [];
        $reset_minutes = isset($_POST['reset_minutes']) ? absint($_POST['reset_minutes']) : 0;
        $lock_faucet = isset($_POST['lock_faucet']) ? absint($_POST['lock_faucet']) : 0;

        // Get milestone options
        $milestone_enabled = isset($_POST['milestone_enabled']) ? absint($_POST['milestone_enabled']) : 0;
        $milestone_type = isset($_POST['milestone_type']) ? sanitize_text_field($_POST['milestone_type']) : 'global';
        $milestone_count = isset($_POST['milestone_count']) ? absint($_POST['milestone_count']) : 5;
        $milestone_lock_faucet = isset($_POST['milestone_lock_faucet']) ? absint($_POST['milestone_lock_faucet']) : 0;

        // Get milestone card appearance options
        $milestone_display_style = isset($_POST['milestone_display_style']) ? sanitize_text_field($_POST['milestone_display_style']) : 'card';
        $milestone_transparent_bg = isset($_POST['milestone_transparent_bg']) ? absint($_POST['milestone_transparent_bg']) : 0;
        $milestone_card_bg_style = isset($_POST['milestone_card_bg_style']) ? sanitize_text_field($_POST['milestone_card_bg_style']) : 'solid';
        $milestone_card_bg_color = isset($_POST['milestone_card_bg_color']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_bg_color']) : '#FFFFFF';
        $milestone_card_gradient_start = isset($_POST['milestone_card_gradient_start']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_gradient_start']) : '#FFFFFF';
        $milestone_card_gradient_end = isset($_POST['milestone_card_gradient_end']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_card_gradient_end']) : '#F5F5F5';

        // Get milestone progress bar options
        $milestone_bar_style = isset($_POST['milestone_bar_style']) ? sanitize_text_field($_POST['milestone_bar_style']) : 'solid';
        $milestone_bar_color = isset($_POST['milestone_bar_color']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_bar_color']) : '#4CAF50';
        $milestone_gradient_start = isset($_POST['milestone_gradient_start']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_gradient_start']) : '#4CAF50';
        $milestone_gradient_end = isset($_POST['milestone_gradient_end']) ? Farmfaucet_Color_Utils::sanitize_hex_color($_POST['milestone_gradient_end']) : '#2196F3';

        // Process milestone pages if provided
        $milestone_pages = '{}'; // Default to empty JSON object
        if ($milestone_enabled && $milestone_type === 'page_specific' && isset($_POST['milestone_pages'])) {
            // Handle both array and JSON string formats
            if (is_array($_POST['milestone_pages'])) {
                // Sanitize the array before encoding
                $sanitized_pages = [];
                foreach ($_POST['milestone_pages'] as $page_id => $page_data) {
                    $sanitized_pages[$page_id] = [
                        'name' => isset($page_data['name']) ? sanitize_text_field($page_data['name']) : '',
                        'url' => isset($page_data['url']) ? esc_url_raw($page_data['url']) : '',
                        'count' => isset($page_data['count']) ? absint($page_data['count']) : 1
                    ];
                }
                $milestone_pages = json_encode($sanitized_pages);
            } elseif (is_string($_POST['milestone_pages']) && !empty($_POST['milestone_pages'])) {
                // Validate that it's a valid JSON string
                $decoded = json_decode(stripslashes($_POST['milestone_pages']), true);
                if ($decoded !== null) {
                    // Sanitize the decoded data
                    $sanitized_pages = [];
                    foreach ($decoded as $page_id => $page_data) {
                        $sanitized_pages[$page_id] = [
                            'name' => isset($page_data['name']) ? sanitize_text_field($page_data['name']) : '',
                            'url' => isset($page_data['url']) ? esc_url_raw($page_data['url']) : '',
                            'count' => isset($page_data['count']) ? absint($page_data['count']) : 1
                        ];
                    }
                    $milestone_pages = json_encode($sanitized_pages);
                }
            }
        }

        // Get countdown options
        $countdown_enabled = isset($_POST['countdown_enabled']) ? absint($_POST['countdown_enabled']) : 0;
        $countdown_seconds = isset($_POST['countdown_seconds']) ? absint($_POST['countdown_seconds']) : 60;
        $countdown_message = isset($_POST['countdown_message']) ? sanitize_text_field($_POST['countdown_message']) : '';
        $countdown_click_activation = isset($_POST['countdown_click_activation']) ? absint($_POST['countdown_click_activation']) : 0;
        $countdown_click_element_id = isset($_POST['countdown_click_element_id']) ? sanitize_text_field($_POST['countdown_click_element_id']) : '';
        $countdown_pre_click_message = isset($_POST['countdown_pre_click_message']) ? sanitize_text_field($_POST['countdown_pre_click_message']) : '';

        // Create button data array
        $button_data = [
            'button_text' => $button_text,
            'button_size' => $button_size,
            'button_color' => $button_color,
            'button_color_hex' => $button_color_hex,
            'border_shape' => $border_shape,
            'redirect_url' => $redirect_url,
            'is_locked' => $is_locked,
            'required_faucets' => $required_faucets,
            'reset_minutes' => $reset_minutes,
            'lock_faucet' => $lock_faucet,
            'milestone_enabled' => $milestone_enabled,
            'milestone_type' => $milestone_type,
            'milestone_count' => $milestone_count,
            'milestone_lock_faucet' => $milestone_lock_faucet,
            'milestone_display_style' => $milestone_display_style,
            'milestone_transparent_bg' => $milestone_transparent_bg,
            'milestone_card_bg_style' => $milestone_card_bg_style,
            'milestone_card_bg_color' => $milestone_card_bg_color,
            'milestone_card_gradient_start' => $milestone_card_gradient_start,
            'milestone_card_gradient_end' => $milestone_card_gradient_end,
            'milestone_pages' => $milestone_pages,
            'milestone_bar_style' => $milestone_bar_style,
            'milestone_bar_color' => $milestone_bar_color,
            'milestone_gradient_start' => $milestone_gradient_start,
            'milestone_gradient_end' => $milestone_gradient_end,
            'countdown_enabled' => $countdown_enabled,
            'countdown_seconds' => $countdown_seconds,
            'countdown_message' => $countdown_message,
            'countdown_click_activation' => $countdown_click_activation,
            'countdown_click_element_id' => $countdown_click_element_id,
            'countdown_pre_click_message' => $countdown_pre_click_message
        ];

        // Log the button data for debugging
        Farmfaucet_Logger::log("Updating button ID: $button_id with data: " . json_encode($button_data), 'info');

        // Update button
        $result = Farmfaucet_Logger::update_button($button_id, $button_data);

        // Log the result
        Farmfaucet_Logger::log("Button update result: " . ($result ? "Success" : "Failed"), 'info');

        if ($result) {
            // Get the updated button data
            $updated_button = Farmfaucet_Logger::get_button($button_id);
            Farmfaucet_Logger::log("Updated button data: " . json_encode($updated_button), 'info');

            wp_send_json_success([
                'message' => __('Button updated successfully', 'farmfaucet'),
                'button' => $updated_button
            ]);
        } else {
            global $wpdb;
            Farmfaucet_Logger::log("Database error: " . $wpdb->last_error, 'error');
            wp_send_json_error([
                'message' => __('Failed to update button. Please check the error log.', 'farmfaucet'),
                'error' => $wpdb->last_error
            ]);
        }
    }

    /**
     * AJAX handler for deleting a button
     */
    public function ajax_delete_button()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $button_id = isset($_POST['button_id']) ? absint($_POST['button_id']) : 0;

        // Validate required fields
        if (empty($button_id)) {
            wp_send_json_error(['message' => __('Button ID is required', 'farmfaucet')]);
        }

        // Check if button exists
        $button = Farmfaucet_Logger::get_button($button_id);
        if (!$button) {
            wp_send_json_error(['message' => __('Button not found', 'farmfaucet')]);
        }

        // Delete button
        $result = Farmfaucet_Logger::delete_button($button_id);

        if ($result) {
            wp_send_json_success(['message' => __('Button deleted successfully', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete button', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for getting button details
     */
    public function ajax_get_button()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $button_id = isset($_POST['button_id']) ? absint($_POST['button_id']) : 0;

        // Validate required fields
        if (empty($button_id)) {
            wp_send_json_error(['message' => __('Button ID is required', 'farmfaucet')]);
        }

        // Get button data
        $button = Farmfaucet_Logger::get_button($button_id);
        if (!$button) {
            wp_send_json_error(['message' => __('Button not found', 'farmfaucet')]);
        }

        wp_send_json_success($button);
    }

    /**
     * AJAX handler for getting all website pages
     */
    public function ajax_get_website_pages()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Get all published pages
        $args = [
            'post_type' => 'page',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
        ];

        $pages = get_posts($args);
        $page_data = [];

        foreach ($pages as $page) {
            $page_data[] = [
                'id' => $page->ID,
                'title' => $page->post_title,
                'url' => get_permalink($page->ID),
            ];
        }

        wp_send_json_success($page_data);
    }

    // This method has been replaced by the implementation at line 1488

    // This method has been replaced by the implementation at line 1547

    /**
     * AJAX handler for fixing database structure
     */
    public function ajax_fix_database()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        try {
            global $wpdb;
            $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

            // Check if the buttons table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;

            if (!$table_exists) {
                wp_send_json_error(['message' => __('Buttons table does not exist', 'farmfaucet')]);
                return;
            }

            // Get column names
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}", ARRAY_A);
            $column_names = array_column($columns, 'Field');

            // Update column sizes for color fields
            $color_columns = [
                'button_color' => 'blue',
                'button_color_hex' => '',
                'milestone_card_bg_color' => '#FFFFFF',
                'milestone_card_gradient_start' => '#FFFFFF',
                'milestone_card_gradient_end' => '#F5F5F5',
                'milestone_bar_color' => '#4CAF50',
                'milestone_gradient_start' => '#4CAF50',
                'milestone_gradient_end' => '#2196F3'
            ];

            foreach ($color_columns as $column => $default) {
                if (in_array($column, $column_names)) {
                    $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN {$column} varchar(50) NOT NULL DEFAULT '{$default}'");
                }
            }

            // Add missing columns
            $missing_columns = [
                'milestone_lock_faucet' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_lock_faucet tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_specific_count",
                'milestone_transparent_bg' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_display_style",
                'milestone_card_bg_style' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_bg_style varchar(20) NOT NULL DEFAULT 'solid' AFTER milestone_transparent_bg",
                'milestone_display_style' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_display_style varchar(20) NOT NULL DEFAULT 'card' AFTER milestone_lock_faucet",
                'button_color_hex' => "ALTER TABLE {$buttons_table} ADD COLUMN button_color_hex varchar(50) NOT NULL DEFAULT '' AFTER button_color",
                'countdown_standby' => "ALTER TABLE {$buttons_table} ADD COLUMN countdown_standby tinyint(1) NOT NULL DEFAULT 0 AFTER countdown_pre_click_message",
            ];

            foreach ($missing_columns as $column => $query) {
                if (!in_array($column, $column_names)) {
                    $wpdb->query($query);
                }
            }

            wp_send_json_success(['message' => __('Database structure updated successfully', 'farmfaucet')]);
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }
}
