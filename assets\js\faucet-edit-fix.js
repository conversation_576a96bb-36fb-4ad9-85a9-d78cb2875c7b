/**
 * Farm Faucet Faucet Edit Fix
 * 
 * Fixes issues with faucet editing in the admin panel
 */
(function($) {
    $(document).ready(function() {
        // Store the original faucet form HTML
        var originalFaucetFormHtml = $('#faucet-form-dialog').html();

        // Override the original edit faucet click handler
        $(document).off('click', '.edit-faucet').on('click', '.edit-faucet', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var faucetId = $(this).data('id');
            console.log('Editing faucet ID:', faucetId);

            // Reset the form to its original state
            $('#faucet-form-dialog').html(originalFaucetFormHtml);

            // Show loading indicator
            $('#faucet-form-dialog').append('<div class="loading-overlay"><div class="loading-spinner">Loading...</div></div>');

            // Open the dialog
            $('#faucet-form-dialog').dialog('option', 'title', 'Edit Faucet');
            $('#faucet-form-dialog').dialog('open');

            // Set the faucet ID
            $('#faucet-id').val(faucetId);

            // Get faucet data directly from the DOM
            var $faucetTab = $('#faucet-' + faucetId);
            var faucetName = $faucetTab.find('h4').text().trim();
            console.log('Faucet name from DOM:', faucetName);

            // Set the name field
            $('#faucet-name').val(faucetName);

            // Extract other data from the info items
            var $infoItems = $faucetTab.find('.faucet-info-item');

            // Reset captcha selection
            $('input[name="captcha_type"]').prop('checked', false);

            $infoItems.each(function() {
                var $item = $(this);
                var label = $item.find('strong').text().trim().toLowerCase();
                var value = $item.find('span').text().trim();

                if (label.includes('currency')) {
                    $('#faucet-currency').val(value);
                } else if (label.includes('amount')) {
                    $('#faucet-amount').val(value);
                } else if (label.includes('cooldown')) {
                    // Extract seconds from human-readable format
                    var seconds = 3600; // Default to 1 hour
                    if (value.includes('second')) {
                        seconds = parseInt(value);
                    } else if (value.includes('minute')) {
                        seconds = parseInt(value) * 60;
                    } else if (value.includes('hour')) {
                        seconds = parseInt(value) * 3600;
                    }
                    $('#faucet-cooldown').val(seconds);
                } else if (label.includes('api key')) {
                    // If API key is set (not showing 'Not set'), set a placeholder value
                    // We don't want to expose the actual API key, but we want to indicate it's set
                    if (!value.includes('Not set')) {
                        $('#faucet-api-key').val('').attr('placeholder', 'API key is set. Leave empty to keep current key.');
                    } else {
                        $('#faucet-api-key').val('').attr('placeholder', '');
                    }
                } else if (label.includes('shortcode')) {
                    var shortcode = $item.find('code').text().replace(/[\[\]]/g, '');
                    $('#faucet-shortcode').val(shortcode).data('auto-generated', false);
                } else if (label.includes('captcha')) {
                    var captchaType = value.toLowerCase();
                    if (captchaType === 'hcaptcha' || captchaType === 'recaptcha' || captchaType === 'turnstile') {
                        // Check the corresponding radio button
                        $('input[name="captcha_type"][value="' + captchaType + '"]').prop('checked', true);
                    }
                }
            });

            // Remove loading overlay
            $('#faucet-form-dialog .loading-overlay').remove();
        });

        // Override the original saveFaucet function
        window.saveFaucet = function() {
            var faucetId = $('#faucet-id').val();
            var isNew = faucetId === '0';

            // Get selected captcha type
            var captchaType = $('input[name="captcha_type"]:checked').val() || '';

            var formData = {
                action: isNew ? 'farmfaucet_create_faucet' : 'farmfaucet_update_faucet',
                nonce: farmfaucet_admin.nonce,
                faucet_id: faucetId,
                name: $('#faucet-name').val(),
                currency: $('#faucet-currency').val(),
                amount: $('#faucet-amount').val(),
                cooldown: $('#faucet-cooldown').val(),
                api_key: $('#faucet-api-key').val(),
                shortcode: $('#faucet-shortcode').val(),
                captcha_type: captchaType
            };

            // Validate form
            if (!formData.name || !formData.shortcode) {
                alert('Name and shortcode are required');
                return;
            }

            // Log the data being sent
            console.log('Saving faucet with data:', formData);

            // Disable dialog buttons during save
            var $dialog = $('#faucet-form-dialog').parent();
            $dialog.find('.ui-dialog-buttonpane button').prop('disabled', true);

            // Add loading indicator
            var $buttonPane = $dialog.find('.ui-dialog-buttonpane');
            $buttonPane.append('<span class="saving-indicator">Saving...</span>');

            // Send AJAX request
            $.ajax({
                url: farmfaucet_admin.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    // Remove loading indicator
                    $buttonPane.find('.saving-indicator').remove();

                    // Re-enable dialog buttons
                    $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);

                    if (response.success) {
                        // Close dialog
                        $('#faucet-form-dialog').dialog('close');

                        // Show success message
                        alert(isNew ? farmfaucet_admin.strings.create_success : farmfaucet_admin.strings.update_success);

                        // Reload the page to show updated faucet
                        location.reload();
                    } else {
                        alert(response.data.message || farmfaucet_admin.strings.error);
                    }
                },
                error: function() {
                    // Remove loading indicator
                    $buttonPane.find('.saving-indicator').remove();

                    // Re-enable dialog buttons
                    $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);

                    alert(farmfaucet_admin.strings.error);
                }
            });
        };
    });
})(jQuery);

