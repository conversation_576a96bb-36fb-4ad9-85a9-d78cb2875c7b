<?php

/**
 * Handles all FaucetPay API interactions with enhanced currency handling
 */
class Farmfaucet_API
{
    /**
     * Currency to smallest unit multipliers (e.g., satoshis/litoshis)
     * @var array
     */
    const CURRENCY_UNITS = [
        'BTC' => 100000000,  // 1 BTC = 100,000,000 satoshis
        'LTC' => 100000000,  // 1 LTC = 100,000,000 litoshis
        'DOGE' => 100000000, // 1 DOGE = 100,000,000 koinu
        'BCH' => 100000000,  // 1 BCH = 100,000,000 satoshis
        'ETH' => 1000000000000000000, // 1 ETH = 10^18 wei
        'TRX' => 1000000,    // 1 TRX = 1,000,000 sun (6 decimal places)
        'DASH' => 100000000,
        'DGB' => 100000000,
    ];

    /**
     * Minimum allowed amounts in base currency
     * @var array
     */
    const MIN_AMOUNTS = [
        'BTC' => 0.00000010, // Minimum 10 satoshis
        'LTC' => 0.00000100, // Minimum 100 litoshis
        'DOGE' => 0.10000000, // Minimum 0.1 DOGE
        'BCH' => 0.00000010, // Minimum 10 satoshis
        'ETH' => 0.00000100, // Minimum 0.000001 ETH (gwei)
        'TRX' => 0.10000000, // Minimum 0.1 TRX
        'DASH' => 0.00000010,
        'DGB' => 0.00000010,
    ];

    /**
     * Process cryptocurrency payment through FaucetPay API
     * @param string $email User's FaucetPay email
     * @param float $amount Payment amount in base currency
     * @param string $currency Cryptocurrency ticker
     * @param int $faucet_id Optional faucet ID to use specific API key
     * @return array Payment result
     */
    public function send_payment($email, $amount, $currency, $faucet_id = null)
    {
        try {
            // Validate and normalize currency format
            $currency = $this->validate_currency($currency);

            // Convert amount to smallest currency unit
            $converted_amount = $this->convert_to_smallest_unit($amount, $currency);

            // Validate against minimum amount requirements
            $this->validate_minimum_amount($amount, $currency);

            // Execute API request with faucet ID for specific API key
            $response = $this->make_api_request($email, $converted_amount, $currency, $faucet_id);

            // Process and validate API response
            return $this->handle_api_response($response, $amount, $currency);
        } catch (Exception $e) {
            // Log and return detailed error
            Farmfaucet_Logger::log_error("Payment Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => __('Payment processing failed', 'farmfaucet'),
                'error_code' => 'VALIDATION_ERROR'
            ];
        }
    }

    /**
     * Validate currency format and support status
     */
    private function validate_currency($currency)
    {
        $currency = strtoupper($currency);
        if (!isset(self::CURRENCY_UNITS[$currency])) {
            throw new Exception("Unsupported currency: $currency");
        }
        return $currency;
    }

    /**
     * Convert base currency amount to smallest unit
     */
    private function convert_to_smallest_unit($amount, $currency)
    {
        $multiplier = self::CURRENCY_UNITS[$currency];
        $value = bcmul($amount, $multiplier, 0);

        if ($value <= 0) {
            throw new Exception("Invalid amount after conversion: $amount $currency");
        }

        return $value;
    }

    /**
     * Validate amount against currency minimums
     */
    private function validate_minimum_amount($amount, $currency)
    {
        $minimum = self::MIN_AMOUNTS[$currency];
        if ($amount < $minimum) {
            throw new Exception(sprintf(
                'Amount below minimum for %s (Min: %s)',
                $currency,
                $minimum
            ));
        }
    }

    /**
     * Make API request to FaucetPay send endpoint
     *
     * @param string $email User's email
     * @param int $converted_amount Amount in smallest unit
     * @param string $currency Currency code
     * @param int $faucet_id Optional faucet ID to use specific API key
     * @return array|WP_Error API response
     */
    private function make_api_request($email, $converted_amount, $currency, $faucet_id = null)
    {
        Farmfaucet_Logger::log("Initiating $currency payment to $email", 'info');

        // Get faucet-specific API key if available
        $api_key = '';

        if ($faucet_id) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'farmfaucet_faucets';
            $faucet = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $faucet_id), ARRAY_A);

            if ($faucet && !empty($faucet['api_key'])) {
                $api_key = $this->decrypt_api_key($faucet['api_key']);
                Farmfaucet_Logger::log("Using faucet-specific API key for faucet ID: $faucet_id", 'info');
            }
        }

        // If no faucet-specific API key, use the global one
        if (empty($api_key)) {
            $api_key = $this->decrypt_api_key(get_option('farmfaucet_faucetpay_api'));
            Farmfaucet_Logger::log("Using global API key", 'info');
        }

        return wp_remote_post('https://faucetpay.io/api/v1/send', [
            'timeout' => 20,
            'sslverify' => false,
            'body' => [
                'api_key' => sanitize_text_field($api_key),
                'to' => sanitize_email($email),
                'amount' => $converted_amount,
                'currency' => $currency,
                'is_email' => 1
            ]
        ]);
    }

    /**
     * Process and validate API response
     */
    private function handle_api_response($response, $original_amount, $currency)
    {
        // Handle connection errors
        if (is_wp_error($response)) {
            $error = $response->get_error_message();
            Farmfaucet_Logger::log_error("Connection Error: $error");
            throw new Exception("API connection failed: $error");
        }

        // Parse response data
        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);
        Farmfaucet_Logger::log("API Response ($status_code): " . print_r($body, true), 'info');

        // Validate response structure
        if ($status_code !== 200 || !isset($body['status'])) {
            throw new Exception("Invalid API response structure");
        }

        // Handle API errors
        if ($body['status'] !== 200) {
            $error_msg = $body['message'] ?? 'Unknown API error';
            throw new Exception(sprintf(
                'API Error %d: %s (Amount: %s %s)',
                $body['status'],
                $error_msg,
                $original_amount,
                $currency
            ));
        }

        // Log successful transaction
        Farmfaucet_Logger::log(
            "Successful payment: $original_amount $currency",
            'success'
        );

        return ['success' => true];
    }

    /**
     * Get current balance for specified currency
     *
     * @param string $currency Currency code
     * @param int $faucet_id Optional faucet ID to use specific API key
     * @return float Balance amount
     */
    /**
     * Encrypt API key for secure storage
     *
     * @param string $api_key Plain API key
     * @return string Encrypted API key
     */
    public function encrypt_api_key($api_key)
    {
        if (empty($api_key)) {
            return '';
        }

        // Get encryption key or generate one if it doesn't exist
        $encryption_key = get_option('farmfaucet_encryption_key');
        if (empty($encryption_key)) {
            $encryption_key = wp_generate_password(64, true, true);
            update_option('farmfaucet_encryption_key', $encryption_key);
        }

        // Generate an initialization vector
        $iv_size = openssl_cipher_iv_length('aes-256-cbc');
        $iv = openssl_random_pseudo_bytes($iv_size);

        // Encrypt the API key
        $encrypted = openssl_encrypt(
            $api_key,
            'aes-256-cbc',
            $encryption_key,
            0,
            $iv
        );

        // Combine IV and encrypted data for storage
        $combined = base64_encode($iv . $encrypted);

        return $combined;
    }

    /**
     * Decrypt API key for use
     *
     * @param string $encrypted_api_key Encrypted API key
     * @return string Decrypted API key
     */
    public function decrypt_api_key($encrypted_api_key)
    {
        if (empty($encrypted_api_key)) {
            return '';
        }

        // Get encryption key
        $encryption_key = get_option('farmfaucet_encryption_key');
        if (empty($encryption_key)) {
            // If no encryption key exists, the API key is not encrypted
            return $encrypted_api_key;
        }

        // Decode the combined string
        $decoded = base64_decode($encrypted_api_key);
        if ($decoded === false) {
            // If decoding fails, the API key is not encrypted
            return $encrypted_api_key;
        }

        // Extract IV and encrypted data
        $iv_size = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($decoded, 0, $iv_size);
        $encrypted = substr($decoded, $iv_size);

        // Decrypt the API key
        $decrypted = openssl_decrypt(
            $encrypted,
            'aes-256-cbc',
            $encryption_key,
            0,
            $iv
        );

        if ($decrypted === false) {
            // If decryption fails, return empty string
            return '';
        }

        return $decrypted;
    }

    public function get_balance($currency, $faucet_id = null)
    {
        $currency = strtoupper($currency);
        Farmfaucet_Logger::log("Checking $currency balance", 'info');

        // Get faucet-specific API key if available
        $api_key = '';

        if ($faucet_id) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'farmfaucet_faucets';
            $faucet = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $faucet_id), ARRAY_A);

            if ($faucet && !empty($faucet['api_key'])) {
                $api_key = $this->decrypt_api_key($faucet['api_key']);
                Farmfaucet_Logger::log("Using faucet-specific API key for balance check, faucet ID: $faucet_id", 'info');
            }
        }

        // If no faucet-specific API key, use the global one
        if (empty($api_key)) {
            $api_key = $this->decrypt_api_key(get_option('farmfaucet_faucetpay_api'));
            Farmfaucet_Logger::log("Using global API key for balance check", 'info');
        }

        $response = wp_remote_post('https://faucetpay.io/api/v1/balance', [
            'timeout' => 15,
            'sslverify' => false,
            'body' => [
                'api_key' => sanitize_text_field($api_key),
                'currency' => sanitize_text_field($currency)
            ]
        ]);

        // Handle connection errors
        if (is_wp_error($response)) {
            Farmfaucet_Logger::log_error("Balance check failed: " . $response->get_error_message());
            return 0;
        }

        // Parse response
        $body = json_decode(wp_remote_retrieve_body($response), true);
        Farmfaucet_Logger::log("Balance response: " . print_r($body, true), 'info');

        return $body['balance'] ?? 0;
    }
}
