<?php
if (!defined('ABSPATH')) exit;

// Get faucet data from the template variable
if (!isset($faucet)) {
  echo '<div class="farmfaucet-error">' . esc_html__('No faucet available', 'farmfaucet') . '</div>';
  return;
}

// Check if user is logged in
$user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
if ($user_id === 0) {
  echo '<div class="farmfaucet-error">' . esc_html__('Please log in to use the withdrawal system', 'farmfaucet') . '</div>';
  return;
}

// Generate background style based on faucet settings
$bg_style = '';
$text_style = '';

// Apply background style
if (isset($faucet['transparent_bg']) && $faucet['transparent_bg'] == 1) {
  $bg_style = 'background: transparent;';
} else {
  if (isset($faucet['bg_style']) && $faucet['bg_style'] == 'gradient' && isset($faucet['bg_gradient_start']) && isset($faucet['bg_gradient_end'])) {
    $bg_style = "background: linear-gradient(135deg, {$faucet['bg_gradient_start']}, {$faucet['bg_gradient_end']});";
  } else if (isset($faucet['bg_color'])) {
    $bg_style = "background-color: {$faucet['bg_color']};";
  }
}

// Apply text color and shadow
$text_color = isset($faucet['text_color']) ? $faucet['text_color'] : '#4CAF50';
$text_shadow = isset($faucet['text_shadow']) && $faucet['text_shadow'] != 'none' ? "text-shadow: {$faucet['text_shadow']};" : '';
$text_style = "color: {$text_color}; {$text_shadow}";

// Get currency data
$currency_id = isset($faucet['currency_id']) ? $faucet['currency_id'] : 0;
$currency_name = '';
$currency_symbol = '';
$currency_type = 'earnings'; // Default type
$user_balance = 0;
$min_withdrawal = isset($faucet['min_withdrawal']) ? floatval($faucet['min_withdrawal']) : 0;

if ($currency_id && class_exists('Farmfaucet_Currency_Maker')) {
  $currency_maker = Farmfaucet_Currency_Maker::init();
  $currency = $currency_maker->get_currency($currency_id);
  if ($currency) {
    $currency_name = $currency['name'];
    $currency_symbol = $currency['symbol'];
    $currency_type = isset($currency['currency_type']) ? $currency['currency_type'] : 'earnings';
    $user_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);
  }
}

// Get available withdrawal currencies
$available_currencies = ['BTC', 'LTC', 'DOGE', 'ETH', 'TRX', 'BCH']; // Default list
if (isset($faucet['available_currencies']) && !empty($faucet['available_currencies'])) {
  $available_currencies = json_decode($faucet['available_currencies'], true);
  if (!is_array($available_currencies)) {
    $available_currencies = ['BTC', 'LTC', 'DOGE', 'ETH', 'TRX', 'BCH'];
  }
}

// Check if this is an advertisement-only conversion faucet
$ads_only_conversion = isset($faucet['ads_only_conversion']) && $faucet['ads_only_conversion'] == 1;
?>
<?php
// Get button color
$button_color = isset($faucet['button_color']) ? $faucet['button_color'] : '#4CAF50';

// Get border color
$border_color = isset($faucet['border_color']) ? $faucet['border_color'] : '#4CAF50';

// Get border radius
$border_radius = isset($faucet['border_radius']) ? $faucet['border_radius'] : '8px';

// Get form background color
$form_bg_color = isset($faucet['form_bg_color']) ? $faucet['form_bg_color'] : '#ffffff';

// Check if form background should be transparent
$form_transparent = isset($faucet['form_transparent']) && $faucet['form_transparent'] ? 1 : 0;

// Add transparent class if needed
$transparent_class = isset($faucet['transparent_bg']) && $faucet['transparent_bg'] ? 'transparent-bg' : '';

// Add custom border class if needed
$border_class = isset($faucet['border_color']) && $faucet['border_color'] ? 'custom-border' : '';
?>
<div class="farmfaucet-container withdrawal-faucet <?php echo esc_attr($transparent_class); ?> <?php echo esc_attr($border_class); ?>"
  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
  data-faucet-name="<?php echo esc_attr($faucet['name']); ?>"
  data-faucet-shortcode="<?php echo esc_attr($faucet['shortcode']); ?>"
  data-border-radius="<?php echo esc_attr($border_radius); ?>"
  data-form-bg-color="<?php echo esc_attr($form_bg_color); ?>"
  data-form-transparent="<?php echo esc_attr($form_transparent); ?>"
  role="region"
  aria-label="<?php esc_attr_e('Cryptocurrency Withdrawal Form', 'farmfaucet'); ?>"
  style="<?php echo esc_attr($bg_style); ?> --button-color: <?php echo esc_attr($button_color); ?>; --border-color: <?php echo esc_attr($border_color); ?>; --form-bg-color: <?php echo esc_attr($form_bg_color); ?>">

  <div class="farmfaucet-header">
    <h2 class="status-header" id="form-heading" style="<?php echo esc_attr($text_style); ?>">
      💰 <?php esc_html_e('WITHDRAW FUNDS', 'farmfaucet'); ?>
    </h2>
    <div class="balance-notice" aria-live="polite" style="<?php echo esc_attr($text_style); ?>">
      <?php echo wp_kses_post(sprintf(
        __('Your Balance: %s %s', 'farmfaucet'),
        '<span class="balance">' . esc_html(number_format($user_balance, 8)) . '</span>',
        '<span class="currency">' . esc_html($currency_name) . ' ' . esc_html($currency_symbol) . '</span>'
      )); ?>
    </div>
    <div class="min-withdrawal-notice" style="<?php echo esc_attr($text_style); ?>">
      <?php
      // Format the minimum withdrawal amount with 8 decimal places
      $formatted_min_withdrawal = number_format($min_withdrawal, 8, '.', '');
      echo wp_kses_post(sprintf(
        __('Minimum Withdrawal: %s %s', 'farmfaucet'),
        '<span class="min-amount">' . esc_html($formatted_min_withdrawal) . '</span>',
        '<span class="currency">' . esc_html($currency_name) . ' ' . esc_html($currency_symbol) . '</span>'
      )); ?>
    </div>
    <!-- Removed the conversion toggle -->
  </div>

  <form id="farmfaucet-withdrawal-form" method="post" class="farmfaucet-form" aria-labelledby="form-heading">
    <div class="form-group">
      <label for="withdrawal-amount"><?php esc_html_e('Withdrawal Amount', 'farmfaucet'); ?></label>
      <input type="number"
        id="withdrawal-amount"
        name="withdrawal_amount"
        placeholder="<?php esc_attr_e('Enter amount to withdraw', 'farmfaucet'); ?>"
        required
        step="0.00000001"
        min="<?php echo esc_attr($min_withdrawal); ?>"
        max="<?php echo esc_attr($user_balance); ?>"
        class="farmfaucet-input"
        aria-required="true">
    </div>

    <!-- Withdrawal Mode Fields -->
    <div class="withdrawal-mode withdrawal-crypto">
      <div class="form-group">
        <label for="withdrawal-currency"><?php esc_html_e('Withdrawal Currency', 'farmfaucet'); ?></label>
        <div class="withdrawal-currency-list">
          <select id="withdrawal-currency" name="withdrawal_currency" required class="farmfaucet-select">
            <?php foreach ($available_currencies as $curr) : ?>
              <option value="<?php echo esc_attr($curr); ?>"><?php echo esc_html($curr); ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <p class="description"><?php esc_html_e('Select the cryptocurrency you want to withdraw to', 'farmfaucet'); ?></p>
      </div>
    </div>

    <!-- Removed conversion mode fields -->

    <div class="form-group wallet-address-container">
      <label for="wallet-address"><?php esc_html_e('FaucetPay Email or Wallet Address', 'farmfaucet'); ?></label>
      <input type="text"
        id="wallet-address"
        name="wallet_address"
        placeholder="<?php esc_attr_e('Enter your FaucetPay email or wallet address', 'farmfaucet'); ?>"
        required
        class="farmfaucet-input"
        aria-required="true">
      <p class="address-warning"><?php esc_html_e('Warning: Please double-check your address. Incorrect addresses may result in lost funds.', 'farmfaucet'); ?></p>
    </div>

    <!-- Hidden fields -->
    <input type="hidden" name="faucet_id" value="<?php echo esc_attr($faucet['id']); ?>">
    <input type="hidden" name="currency_id" value="<?php echo esc_attr($currency_id); ?>">
    <input type="hidden" name="withdrawal_mode" value="crypto" id="withdrawal-mode-field">

    <!-- Removed conversion fields -->

    <div class="farmfaucet-captcha-container" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>" style="display: flex; visibility: visible; opacity: 1;">
      <?php
      if (class_exists('Farmfaucet_Captcha_Handler')) {
        echo Farmfaucet_Captcha_Handler::render_captcha($faucet);
      } else {
        try {
          // Get captcha type from faucet-specific setting or fallback to global settings
          $captcha_type = !empty($faucet['captcha_type']) ? $faucet['captcha_type'] : get_option('farmfaucet_captcha_type', 'hcaptcha');

          // Validate captcha type
          if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
          }

          // Get captcha site keys - check both possible option names
          $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_site_key', '');
          if (empty($hcaptcha_sitekey)) {
            $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
          }

          $recaptcha_sitekey = get_option('farmfaucet_recaptcha_site_key', '');
          if (empty($recaptcha_sitekey)) {
            $recaptcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
          }

          $turnstile_sitekey = get_option('farmfaucet_turnstile_site_key', '');
          if (empty($turnstile_sitekey)) {
            $turnstile_sitekey = get_option('farmfaucet_turnstile_sitekey', '');
          }

          // Generate a unique ID for this captcha
          $captcha_id = 'farmfaucet-captcha-' . $faucet['id'] . '-' . uniqid();

          // Add hidden field for captcha type
          echo '<input type="hidden" name="captcha_type" value="' . esc_attr($captcha_type) . '">';

          // Render the appropriate captcha
          if ($captcha_type === 'hcaptcha') {
            if (empty($hcaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('hCaptcha site key not configured', 'farmfaucet') . '</div>';
            } else {
      ?>
              <div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for hCaptcha -->
                <div id="<?php echo $captcha_id; ?>"
                  class="h-captcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($hcaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableWithdrawalButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'recaptcha') {
            if (empty($recaptcha_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('reCAPTCHA site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for reCAPTCHA -->
                <div id="<?php echo $captcha_id; ?>"
                  class="g-recaptcha farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($recaptcha_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableWithdrawalButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
            <?php
            }
          } elseif ($captcha_type === 'turnstile') {
            if (empty($turnstile_sitekey)) {
              echo '<div class="farmfaucet-error">' . esc_html__('Cloudflare Turnstile site key not configured', 'farmfaucet') . '</div>';
            } else {
            ?>
              <div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                <!-- Simple container for Cloudflare Turnstile -->
                <div id="<?php echo $captcha_id; ?>"
                  class="cf-turnstile farmfaucet-captcha"
                  data-sitekey="<?php echo esc_attr($turnstile_sitekey); ?>"
                  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                  data-callback="enableWithdrawalButton"
                  style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
              </div>
      <?php
            }
          }
        } catch (Exception $e) {
          echo '<div class="farmfaucet-error">' . esc_html__('Error loading captcha', 'farmfaucet') . '</div>';
        }
      }
      ?>
    </div>

    <button type="submit"
      class="farmfaucet-withdraw-btn farmfaucet-button custom-color"
      disabled
      aria-disabled="true"
      id="withdraw-button">
      <span class="withdrawal-btn-text"><?php esc_html_e('WITHDRAW', 'farmfaucet'); ?></span>
    </button>
  </form>

  <div class="farmfaucet-notification" role="status" aria-live="assertive"></div>
  <div class="farmfaucet-error-message" style="display: none;"></div>
</div>

<!-- Withdrawal history is now available via the [farmfaucet_withdrawal_history faucet_id="X"] shortcode -->

<script>
  jQuery(document).ready(function($) {
    // Enable submit button when captcha is completed
    window.enableWithdrawalButton = function() {
      $('#withdraw-button').prop('disabled', false).attr('aria-disabled', 'false');
    };

    // Form submission handler
    $('#farmfaucet-withdrawal-form').on('submit', function(e) {
      // Validate form
      var amount = parseFloat($('#withdrawal-amount').val()) || 0;
      var minWithdrawal = parseFloat('<?php echo esc_attr($min_withdrawal); ?>') || 0;
      var userBalance = parseFloat('<?php echo esc_attr($user_balance); ?>') || 0;

      if (amount <= 0) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Please enter a valid amount', 'farmfaucet')); ?>');
        return false;
      }

      if (amount < minWithdrawal) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Amount must be at least the minimum withdrawal amount', 'farmfaucet')); ?>');
        return false;
      }

      if (amount > userBalance) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Amount cannot exceed your balance', 'farmfaucet')); ?>');
        return false;
      }

      // Validate wallet address
      var walletAddress = $('#wallet-address').val().trim();
      if (!walletAddress) {
        e.preventDefault();
        alert('<?php echo esc_attr(__('Please enter a wallet address or FaucetPay email', 'farmfaucet')); ?>');
        return false;
      }

      // If all validations pass, show loading state
      if (!e.isDefaultPrevented()) {
        $('#withdraw-button').prop('disabled', true).text('<?php echo esc_attr(__('Processing...', 'farmfaucet')); ?>');
      }
    });
  });
</script>