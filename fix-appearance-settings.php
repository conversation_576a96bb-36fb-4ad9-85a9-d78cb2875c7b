<?php
/**
 * Farm Faucet - Fix Appearance Settings
 * 
 * This script fixes all issues with the new appearance settings:
 * - Updates database schema
 * - Tests functionality
 * - Provides troubleshooting information
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Fix Appearance Settings</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .step { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        .step h3 { margin-top: 0; color: #333; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; }
        .btn:hover { background: #45a049; }
        .btn-secondary { background: #2196F3; }
        .btn-secondary:hover { background: #1976D2; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Fix Appearance Settings</h1>
    <p>This script will fix all issues with the new appearance settings and ensure everything works correctly.</p>

<?php

global $wpdb;

// Get the faucets table name
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

// Function to check if a column exists
function column_exists($table, $column) {
    global $wpdb;
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
    return !empty($columns);
}

// Function to execute a query safely
function execute_query($query, $description) {
    global $wpdb;
    $result = $wpdb->query($query);
    if ($result === false) {
        echo '<div class="error"><p>❌ ' . $description . ' - FAILED</p>';
        echo '<p><strong>Error:</strong> ' . $wpdb->last_error . '</p>';
        echo '<pre>' . $query . '</pre></div>';
        return false;
    } else {
        echo '<div class="success"><p>✅ ' . $description . ' - SUCCESS</p></div>';
        return true;
    }
}

echo '<div class="step">';
echo '<h3>Step 1: Database Schema Update</h3>';

// Check if the faucets table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if (!$table_exists) {
    echo '<div class="error"><p>❌ The faucets table does not exist. Please ensure the Farm Faucet plugin is properly installed.</p></div>';
} else {
    echo '<div class="info"><p>✅ Faucets table found: ' . $faucets_table . '</p></div>';
    
    // Add missing columns
    $new_fields = [
        'button_border_radius' => "varchar(20) NOT NULL DEFAULT '25px'",
        'input_label_color' => "varchar(20) NOT NULL DEFAULT '#333333'",
        'input_placeholder_color' => "varchar(20) NOT NULL DEFAULT '#999999'"
    ];
    
    foreach ($new_fields as $field => $definition) {
        if (!column_exists($faucets_table, $field)) {
            $query = "ALTER TABLE {$faucets_table} ADD COLUMN {$field} {$definition}";
            execute_query($query, "Adding {$field} column");
        } else {
            echo '<div class="info"><p>ℹ️ Column ' . $field . ' already exists</p></div>';
        }
    }
    
    // Update existing faucets with default values
    $update_query = "UPDATE {$faucets_table} SET 
        button_border_radius = COALESCE(NULLIF(button_border_radius, ''), '25px'),
        input_label_color = COALESCE(NULLIF(input_label_color, ''), '#333333'),
        input_placeholder_color = COALESCE(NULLIF(input_placeholder_color, ''), '#999999')
        WHERE button_border_radius IS NULL OR button_border_radius = '' 
        OR input_label_color IS NULL OR input_label_color = ''
        OR input_placeholder_color IS NULL OR input_placeholder_color = ''";
    
    execute_query($update_query, "Updating existing faucets with default values");
}

echo '</div>';

echo '<div class="step">';
echo '<h3>Step 2: Functionality Test</h3>';

if ($table_exists) {
    // Test creating a faucet with new appearance settings
    $test_data = [
        'name' => 'Appearance Test Faucet',
        'shortcode' => 'appearance_test_' . time(),
        'faucet_type' => 'stage',
        'currency' => 'LTC',
        'amount' => '0.001',
        'cooldown' => 3600,
        'button_color' => '#FF5722',
        'button_border_radius' => '15px',
        'input_label_color' => '#FFFFFF',
        'input_placeholder_color' => '#CCCCCC',
        'border_color' => '#4CAF50',
        'border_radius' => '8px',
        'form_bg_color' => '#ffffff',
        'form_transparent' => 0,
        'is_enabled' => 1,
        'created_at' => current_time('mysql')
    ];
    
    $insert_result = $wpdb->insert($faucets_table, $test_data);
    
    if ($insert_result === false) {
        echo '<div class="error"><p>❌ Test faucet creation failed</p>';
        echo '<p><strong>Error:</strong> ' . $wpdb->last_error . '</p></div>';
    } else {
        $test_id = $wpdb->insert_id;
        echo '<div class="success"><p>✅ Test faucet created successfully (ID: ' . $test_id . ')</p></div>';
        
        // Verify the data was saved correctly
        $saved_data = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$faucets_table} WHERE id = %d", $test_id), ARRAY_A);
        
        if ($saved_data) {
            echo '<div class="info"><p>✅ Test data verification:</p>';
            echo '<ul>';
            echo '<li><strong>Button Border Radius:</strong> ' . $saved_data['button_border_radius'] . '</li>';
            echo '<li><strong>Input Label Color:</strong> ' . $saved_data['input_label_color'] . '</li>';
            echo '<li><strong>Input Placeholder Color:</strong> ' . $saved_data['input_placeholder_color'] . '</li>';
            echo '</ul></div>';
        }
        
        // Clean up test data
        $wpdb->delete($faucets_table, ['id' => $test_id], ['%d']);
        echo '<div class="info"><p>🧹 Test faucet cleaned up</p></div>';
    }
}

echo '</div>';

echo '<div class="step">';
echo '<h3>Step 3: File Verification</h3>';

// Check if required files exist
$required_files = [
    'templates/admin/faucet-form.php' => 'Admin form template',
    'assets/js/admin.js' => 'Admin JavaScript',
    'assets/js/farmfaucet-notifications.js' => 'Notification system JavaScript',
    'assets/css/farmfaucet-notifications.css' => 'Notification system CSS',
    'includes/class-farmfaucet-admin.php' => 'Admin class'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo '<div class="success"><p>✅ ' . $description . ' - EXISTS</p></div>';
    } else {
        echo '<div class="error"><p>❌ ' . $description . ' - MISSING</p></div>';
    }
}

echo '</div>';

echo '<div class="step">';
echo '<h3>Step 4: Current Table Structure</h3>';

if ($table_exists) {
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    
    echo '<table>';
    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Default</th></tr>';
    
    foreach ($columns as $column) {
        $highlight = in_array($column->Field, ['button_border_radius', 'input_label_color', 'input_placeholder_color']) ? ' style="background-color: #e8f5e8;"' : '';
        echo '<tr' . $highlight . '>';
        echo '<td><strong>' . esc_html($column->Field) . '</strong></td>';
        echo '<td>' . esc_html($column->Type) . '</td>';
        echo '<td>' . esc_html($column->Null) . '</td>';
        echo '<td>' . esc_html($column->Default) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
    echo '<div class="info"><p>💡 <strong>Highlighted rows</strong> are the new appearance fields that were added.</p></div>';
}

echo '</div>';

echo '<div class="step">';
echo '<h3>Step 5: Next Steps</h3>';

echo '<div class="success">';
echo '<h4>✅ Setup Complete!</h4>';
echo '<p>The appearance settings have been successfully configured. You can now:</p>';
echo '<ol>';
echo '<li><strong>Create or edit faucets</strong> with the new appearance options</li>';
echo '<li><strong>Customize button border radius</strong> (0px to 50px)</li>';
echo '<li><strong>Set input label colors</strong> for better visibility on transparent backgrounds</li>';
echo '<li><strong>Customize placeholder text colors</strong> for input fields</li>';
echo '<li><strong>Use the interactive notification system</strong> for better user feedback</li>';
echo '</ol>';
echo '</div>';

echo '<div class="warning">';
echo '<h4>⚠️ If you still experience issues:</h4>';
echo '<ol>';
echo '<li><strong>Clear browser cache</strong> and refresh the admin page</li>';
echo '<li><strong>Check browser console</strong> for JavaScript errors</li>';
echo '<li><strong>Verify WordPress admin AJAX</strong> is working properly</li>';
echo '<li><strong>Test with a simple faucet first</strong> before using advanced features</li>';
echo '</ol>';
echo '</div>';

echo '</div>';

?>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet'); ?>" class="btn">🚀 Go to Farm Faucet Admin</a>
    <a href="debug-faucet-save.php" class="btn btn-secondary">🔍 Run Debug Script</a>
</div>

<div class="info">
    <h4>📋 Summary of New Features</h4>
    <ul>
        <li><strong>Button Border Radius:</strong> Separate setting for button border radius (independent of container border radius)</li>
        <li><strong>Input Label Color:</strong> Customizable color for text labels outside input fields</li>
        <li><strong>Input Placeholder Color:</strong> Customizable color for placeholder text inside input fields</li>
        <li><strong>Enhanced Dropdown Transparency:</strong> Dropdowns now respect transparent background settings</li>
        <li><strong>Interactive Notifications:</strong> Slide-in notifications for all faucet operations with real-time feedback</li>
        <li><strong>Improved Form Transparency:</strong> Complete form transparency with customizable text colors for better visibility</li>
    </ul>
</div>

</body>
</html>
