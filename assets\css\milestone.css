/* Base milestone container styles */
.farmfaucet-milestone-container {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
    max-width: 100%;
    overflow-y: auto;
    max-height: 500px;
    text-align: center;
}

.farmfaucet-milestone-container.transparent-bg {
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Title styles */
.farmfaucet-milestone-title {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2em;
    color: #333;
    padding: 0 10px;
}

.transparent-bg .farmfaucet-milestone-title {
    color: inherit;
}

/* Description styles */
.farmfaucet-milestone-description {
    margin: 15px auto;
    max-width: 90%;
    color: #555;
    font-size: 1em;
    line-height: 1.4;
}

/* Progress display */
.farmfaucet-milestone-percentage-display {
    text-align: center;
    margin: 15px auto;
}

.farmfaucet-milestone-percentage {
    font-size: 1.8em;
    font-weight: bold;
    color: #333;
}

/* Progress bar percentage for card view */
.farmfaucet-milestone-progress-bar-percentage,
.farmfaucet-milestone-progress-text {
    position: absolute !important;
    width: 100% !important;
    text-align: center !important;
    color: #fff !important;
    font-weight: bold !important;
    line-height: 30px !important;
    font-size: 1.1em !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
    z-index: 5 !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    pointer-events: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Fix for progress bar container */
.farmfaucet-milestone-progress-container {
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    height: 30px !important;
    border-radius: 15px !important;
    background-color: #f0f0f0 !important;
    margin: 10px 0 !important;
    width: 100% !important;
}

.transparent-bg .farmfaucet-milestone-percentage {
    color: inherit;
}

.farmfaucet-milestone-complete-text {
    display: none;
    font-size: 1.8em;
    font-weight: bold;
    color: #4CAF50;
}

.farmfaucet-milestone-complete .farmfaucet-milestone-percentage {
    display: none;
}

.farmfaucet-milestone-complete .farmfaucet-milestone-complete-text {
    display: inline-block;
}

/* Progress section */
.farmfaucet-milestone-progress-section {
    margin: 0 auto;
    max-width: 90%;
}

/* Task list styles */
.farmfaucet-milestone-task-list {
    margin: 20px auto 10px;
    border-top: 1px solid #eee;
    padding-top: 15px;
    max-width: 90%;
}

.transparent-bg .farmfaucet-milestone-task-list {
    border-top-color: rgba(0,0,0,0.1);
}

.farmfaucet-milestone-task-heading {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.05em;
    color: #555;
    text-align: left;
}

.transparent-bg .farmfaucet-milestone-task-heading {
    color: inherit;
}

.farmfaucet-milestone-tasks {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.farmfaucet-milestone-tasks li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 6px;
}

.farmfaucet-task-complete {
    background-color: rgba(76, 175, 80, 0.1);
}

.transparent-bg .farmfaucet-task-complete {
    background-color: transparent;
}

.farmfaucet-task-icon {
    margin-right: 12px;
    font-size: 1.2em;
    flex-shrink: 0;
}

.farmfaucet-task-complete .farmfaucet-task-icon {
    color: #4CAF50;
}

.farmfaucet-task-incomplete .farmfaucet-task-icon {
    color: #999;
}

.transparent-bg .farmfaucet-task-incomplete .farmfaucet-task-icon {
    color: inherit;
}

.farmfaucet-task-text {
    flex-grow: 1;
    padding: 0 5px;
}

.farmfaucet-task-progress {
    font-weight: bold;
    margin-left: 10px;
    flex-shrink: 0;
}

.farmfaucet-task-complete .farmfaucet-task-progress {
    color: #4CAF50;
}

/* Button styles */
.farmfaucet-milestone-button-container {
    margin: 25px auto 10px;
    text-align: center;
    display: none;
    max-width: 90%;
}

.farmfaucet-milestone-complete .farmfaucet-milestone-button-container {
    display: block;
}

.farmfaucet-milestone-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #4CAF50;
    color: #fff;
    text-decoration: none;
    border-radius: 20px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    min-width: 150px;
}

.farmfaucet-milestone-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Locked message styles */
.farmfaucet-milestone-locked-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin: 20px auto 10px;
    text-align: center;
    border: 1px solid #f5c6cb;
    max-width: 90%;
}

.transparent-bg .farmfaucet-milestone-locked-message {
    background-color: transparent;
    color: inherit;
    border: none;
}

.farmfaucet-milestone-locked-message p {
    margin: 0;
    font-weight: bold;
    font-size: 1.05em;
}

/* Compact view specific styles */
.farmfaucet-milestone-container.compact-view {
    padding: 20px;
}

.compact-view .farmfaucet-milestone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.compact-view .farmfaucet-milestone-title {
    margin: 0;
    font-size: 1.1em;
}

/* Hide the percentage display in compact view since it's shown in the progress bar */
.compact-view .farmfaucet-milestone-percentage-display {
    display: none;
}

.compact-view .farmfaucet-milestone-progress-container {
    height: 24px;
    border-radius: 12px;
    background-color: #f0f0f0;
    overflow: hidden;
    position: relative;
    margin: 15px auto;
    width: 100%;
    max-width: 90%;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

/* Make progress bar background transparent when transparent background is enabled */
.transparent-bg.compact-view .farmfaucet-milestone-progress-container {
    background-color: rgba(240, 240, 240, 0.3);
    box-shadow: none;
}

.compact-view .farmfaucet-milestone-progress-bar {
    height: 100% !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    transition: width 1.5s ease-in-out !important;
    width: 0% !important; /* Start at 0% width and animate to the target width */
}

/* Only show percentage inside the progress bar for compact view */
.compact-view .farmfaucet-milestone-progress-text {
    position: absolute !important;
    width: 100% !important;
    text-align: center !important;
    color: #fff !important;
    font-weight: bold !important;
    line-height: 24px !important;
    font-size: 0.9em !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    z-index: 10 !important;
    left: 0 !important;
    top: 0 !important;
    pointer-events: none !important;
}

/* Fix for compact view progress bar - additional styles */
.compact-view .farmfaucet-milestone-progress-bar {
    min-width: 5px !important; /* Ensure the progress bar is always visible */
    z-index: 2 !important;
    height: 100% !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    transition: width 1.5s ease-in-out !important;
}

/* Progress text inside the bar */
.farmfaucet-milestone-progress-text {
    position: absolute !important;
    width: 100% !important;
    text-align: center !important;
    color: #fff !important;
    font-weight: bold !important;
    z-index: 5 !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    pointer-events: none !important;
    margin: 0 !important;
    padding: 0 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
}

.compact-view .farmfaucet-milestone-progress-text {
    line-height: 24px !important;
    font-size: 0.9em !important;
}

.card-view .farmfaucet-milestone-progress-text {
    line-height: 30px !important;
    font-size: 1.1em !important;
}

.compact-view .farmfaucet-milestone-summary {
    margin: 15px 0;
    padding: 10px;
    font-size: 0.95em;
    color: #555;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f9f9f9;
    border: 1px solid #eee;
}

/* Make summary background transparent when transparent background is enabled */
.transparent-bg.compact-view .farmfaucet-milestone-summary {
    background-color: transparent;
    border: none;
}

.compact-view .farmfaucet-milestone-summary:hover {
    background-color: #f0f0f0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Dropdown arrow styling */
.milestone-dropdown-arrow {
    font-size: 0.8em;
    opacity: 0.7;
    margin-left: 5px;
    display: inline-block;
    transition: transform 0.3s ease;
}

.show-tasks .milestone-dropdown-arrow {
    transform: rotate(180deg);
}

/* Hide the separate dropdown toggle button */
.compact-view .farmfaucet-milestone-dropdown-toggle {
    display: none;
}

.compact-view .farmfaucet-milestone-task-list {
    display: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.compact-view.show-tasks .farmfaucet-milestone-task-list {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Task action button for page-specific milestones */
.farmfaucet-task-action {
    margin-left: 10px;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
}

.farmfaucet-task-action:hover {
    background: #e0e0e0;
    color: #000;
}
