<?php

/**
 * Database updater for adding is_enabled column to faucets table
 */
class Farmfaucet_DB_Updater_Disable
{
    /**
     * Run the database update
     */
    public static function run_update()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the column already exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'is_enabled'");

        if (empty($column_exists)) {
            // Add the is_enabled column with default value 1 (enabled)
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN is_enabled tinyint(1) NOT NULL DEFAULT 1 AFTER captcha_type");

            // Add index for is_enabled
            $wpdb->query("ALTER TABLE {$table_name} ADD INDEX is_enabled (is_enabled)");

            // Log the update
            error_log('Farmfaucet: Added is_enabled column to faucets table');
        }
    }

    /**
     * Initialize the class
     */
    public static function init()
    {
        // This class only handles database updates, not AJAX handlers
    }
}
