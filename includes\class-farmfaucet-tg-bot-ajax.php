<?php

/**
 * Telegram Bot AJAX Handler for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.5
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Tg_Bot_AJAX
 *
 * Handles AJAX operations for Telegram Bot Builder
 */
class Farmfaucet_Tg_Bot_AJAX
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        $instance = new self();
        $instance->setup_hooks();
    }

    /**
     * Setup hooks
     */
    private function setup_hooks()
    {
        // Bot AJAX actions
        add_action('wp_ajax_farmfaucet_save_bot', [$this, 'ajax_save_bot']);
        add_action('wp_ajax_farmfaucet_get_bot', [$this, 'ajax_get_bot']);
        add_action('wp_ajax_farmfaucet_delete_bot', [$this, 'ajax_delete_bot']);
        add_action('wp_ajax_farmfaucet_toggle_bot', [$this, 'ajax_toggle_bot']);

        // Command AJAX actions
        add_action('wp_ajax_farmfaucet_save_command', [$this, 'ajax_save_command']);
        add_action('wp_ajax_farmfaucet_get_command', [$this, 'ajax_get_command']);
        add_action('wp_ajax_farmfaucet_delete_command', [$this, 'ajax_delete_command']);
        add_action('wp_ajax_farmfaucet_get_commands', [$this, 'ajax_get_commands']);
    }

    /**
     * AJAX handler for saving a bot
     */
    public function ajax_save_bot()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $bot_id = isset($_POST['bot_id']) ? absint($_POST['bot_id']) : 0;
        $bot_name = isset($_POST['bot_name']) ? sanitize_text_field($_POST['bot_name']) : '';
        $bot_token = isset($_POST['bot_token']) ? sanitize_text_field($_POST['bot_token']) : '';
        $bot_username = isset($_POST['bot_username']) ? sanitize_text_field($_POST['bot_username']) : '';
        $bot_type = isset($_POST['bot_type']) ? sanitize_text_field($_POST['bot_type']) : 'text';
        $webhook_url = isset($_POST['webhook_url']) ? esc_url_raw($_POST['webhook_url']) : '';
        $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
        $settings = isset($_POST['settings']) ? sanitize_text_field($_POST['settings']) : '';

        // Validate required fields
        if (empty($bot_name) || empty($bot_token) || empty($bot_username)) {
            wp_send_json_error(['message' => __('Bot name, token, and username are required', 'farmfaucet')]);
        }

        // Prepare bot data
        $bot_data = [
            'bot_name' => $bot_name,
            'bot_token' => $bot_token,
            'bot_username' => $bot_username,
            'bot_type' => $bot_type,
            'webhook_url' => $webhook_url,
            'is_active' => $is_active,
            'settings' => $settings
        ];

        // Add or update bot
        if (empty($bot_id)) {
            // Add new bot
            $bot_data['created_at'] = current_time('mysql');
            $bot_data['updated_at'] = current_time('mysql');
            
            $result = Farmfaucet_Tg_Bot_DB::add_bot($bot_data);
            
            if ($result) {
                wp_send_json_success([
                    'message' => __('Bot created successfully', 'farmfaucet'),
                    'bot_id' => $result
                ]);
            } else {
                wp_send_json_error(['message' => __('Failed to create bot', 'farmfaucet')]);
            }
        } else {
            // Update existing bot
            $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, $bot_data);
            
            if ($result) {
                wp_send_json_success([
                    'message' => __('Bot updated successfully', 'farmfaucet'),
                    'bot_id' => $bot_id
                ]);
            } else {
                wp_send_json_error(['message' => __('Failed to update bot', 'farmfaucet')]);
            }
        }
    }

    /**
     * AJAX handler for getting a bot
     */
    public function ajax_get_bot()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $bot_id = isset($_POST['bot_id']) ? absint($_POST['bot_id']) : 0;

        // Validate required fields
        if (empty($bot_id)) {
            wp_send_json_error(['message' => __('Bot ID is required', 'farmfaucet')]);
        }

        // Get bot data
        $bot = Farmfaucet_Tg_Bot_DB::get_bot($bot_id);
        
        if ($bot) {
            wp_send_json_success($bot);
        } else {
            wp_send_json_error(['message' => __('Bot not found', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for deleting a bot
     */
    public function ajax_delete_bot()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $bot_id = isset($_POST['bot_id']) ? absint($_POST['bot_id']) : 0;

        // Validate required fields
        if (empty($bot_id)) {
            wp_send_json_error(['message' => __('Bot ID is required', 'farmfaucet')]);
        }

        // Delete bot
        $result = Farmfaucet_Tg_Bot_DB::delete_bot($bot_id);
        
        if ($result) {
            wp_send_json_success(['message' => __('Bot deleted successfully', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete bot', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for toggling bot status
     */
    public function ajax_toggle_bot()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $bot_id = isset($_POST['bot_id']) ? absint($_POST['bot_id']) : 0;
        $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 0;

        // Validate required fields
        if (empty($bot_id)) {
            wp_send_json_error(['message' => __('Bot ID is required', 'farmfaucet')]);
        }

        // Update bot status
        $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, ['is_active' => $is_active]);
        
        if ($result) {
            wp_send_json_success([
                'message' => $is_active ? __('Bot activated', 'farmfaucet') : __('Bot deactivated', 'farmfaucet'),
                'is_active' => $is_active
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to update bot status', 'farmfaucet')]);
        }
    }
}
