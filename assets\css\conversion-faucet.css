/**
 * Farm Faucet - Conversion Faucet CSS
 */

/* Conversion Faucet Container */
.conversion-faucet {
  max-width: 600px;
  margin: 0 auto 30px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.conversion-faucet .farmfaucet-header {
  text-align: center;
  margin-bottom: 20px;
}

.conversion-faucet .status-header {
  font-size: 24px;
  margin-bottom: 15px;
  font-weight: 700;
}

.conversion-faucet .balance-notice,
.conversion-faucet .min-conversion-notice {
  font-size: 16px;
  margin-bottom: 10px;
}

.conversion-faucet .balance-notice .balance,
.conversion-faucet .min-conversion-notice .min-amount {
  font-weight: 700;
}

/* Conversion Form */
.conversion-faucet .farmfaucet-form {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
}

.conversion-faucet .form-group {
  margin-bottom: 20px;
}

.conversion-faucet .form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.conversion-faucet .farmfaucet-input,
.conversion-faucet .farmfaucet-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  background-color: #fff;
}

.conversion-faucet .farmfaucet-input:focus,
.conversion-faucet .farmfaucet-select:focus {
  border-color: #4CAF50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Conversion Container */
.conversion-faucet .conversion-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.conversion-faucet .conversion-from,
.conversion-faucet .conversion-to {
  flex: 1;
}

.conversion-faucet .conversion-arrow {
  margin: 0 15px;
  font-size: 24px;
  color: #4CAF50;
}

.conversion-faucet .currency-label {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 600;
  color: #666;
}

.conversion-faucet .target-currency-wrapper {
  position: relative;
}

/* Conversion Result */
.conversion-faucet .conversion-result,
.conversion-faucet .conversion-rate {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

.conversion-faucet .receive-amount,
.conversion-faucet .conversion-rate-display {
  font-size: 18px;
  font-weight: 700;
  color: #4CAF50;
}

.conversion-faucet .currency-type-notice {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4CAF50;
  border-radius: 4px;
}

.conversion-faucet .ads-only-notice {
  font-size: 14px;
  color: #333;
}

/* Submit Button */
.conversion-faucet .farmfaucet-convert-btn {
  display: block;
  width: 100%;
  padding: 14px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 20px;
}

.conversion-faucet .farmfaucet-convert-btn:hover {
  background-color: #3e8e41;
}

.conversion-faucet .farmfaucet-convert-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Notification */
.conversion-faucet .farmfaucet-notification {
  margin-top: 20px;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  display: none;
}

.conversion-faucet .farmfaucet-notification.success {
  background-color: #dff0d8;
  color: #3c763d;
  border: 1px solid #d6e9c6;
  display: block;
}

.conversion-faucet .farmfaucet-notification.error {
  background-color: #f2dede;
  color: #a94442;
  border: 1px solid #ebccd1;
  display: block;
}

.conversion-faucet .farmfaucet-error-message {
  margin-top: 15px;
  color: #a94442;
  font-weight: 600;
}

/* Conversion History */
.farmfaucet-conversion-history {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.farmfaucet-conversion-history h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.conversion-history-container {
  max-height: 300px;
  overflow-y: auto;
}

.loading-history {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .conversion-faucet .conversion-container {
    flex-direction: column;
  }
  
  .conversion-faucet .conversion-arrow {
    transform: rotate(90deg);
    margin: 15px 0;
  }
  
  .conversion-faucet .conversion-from,
  .conversion-faucet .conversion-to {
    width: 100%;
  }
}
