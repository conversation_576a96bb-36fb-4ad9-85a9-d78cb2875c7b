<?php
/**
 * Telegram <PERSON>t Login Settings Template
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get all available bots
global $wpdb;
$bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';
$bots = $wpdb->get_results("SELECT id, bot_name, bot_username FROM {$bots_table} WHERE is_active = 1", ARRAY_A);

// Get all pages for redirect options
$pages = get_pages();
?>

<div class="farmfaucet-settings-section">
    <h2><?php esc_html_e('Telegram Bot Login Settings', 'farmfaucet'); ?></h2>
    
    <form method="post" action="options.php" class="farmfaucet-form">
        <?php settings_fields('farmfaucet_settings'); ?>
        
        <div class="form-group">
            <label for="farmfaucet_tg_login_enabled">
                <?php esc_html_e('Enable Telegram Login', 'farmfaucet'); ?>
            </label>
            <label class="switch">
                <input type="checkbox" id="farmfaucet_tg_login_enabled" name="farmfaucet_tg_login_enabled" value="1" <?php checked(get_option('farmfaucet_tg_login_enabled'), 1); ?>>
                <span class="slider round"></span>
            </label>
            <span class="status-label">
                <?php echo get_option('farmfaucet_tg_login_enabled') ? esc_html__('Enabled', 'farmfaucet') : esc_html__('Disabled', 'farmfaucet'); ?>
            </span>
        </div>
        
        <div class="form-group">
            <label for="farmfaucet_tg_login_bot">
                <?php esc_html_e('Select Telegram Bot for Login', 'farmfaucet'); ?>
            </label>
            <select id="farmfaucet_tg_login_bot" name="farmfaucet_tg_login_bot">
                <option value=""><?php esc_html_e('Select a bot', 'farmfaucet'); ?></option>
                <?php if (!empty($bots)): ?>
                    <?php foreach ($bots as $bot): ?>
                        <option value="<?php echo esc_attr($bot['id']); ?>" <?php selected(get_option('farmfaucet_tg_login_bot'), $bot['id']); ?>>
                            <?php echo esc_html($bot['bot_name']); ?> (@<?php echo esc_html($bot['bot_username']); ?>)
                        </option>
                    <?php endforeach; ?>
                <?php else: ?>
                    <option value="" disabled><?php esc_html_e('No bots available. Please create a bot first.', 'farmfaucet'); ?></option>
                <?php endif; ?>
            </select>
            <p class="description">
                <?php esc_html_e('Select which bot to use for Telegram login verification. This bot will be used to send OTP codes to users.', 'farmfaucet'); ?>
            </p>
        </div>
        
        <div class="form-group">
            <label for="farmfaucet_tg_otp_expiration">
                <?php esc_html_e('OTP Code Expiration (minutes)', 'farmfaucet'); ?>
            </label>
            <input type="number" id="farmfaucet_tg_otp_expiration" name="farmfaucet_tg_otp_expiration" min="1" max="60" value="<?php echo esc_attr(get_option('farmfaucet_tg_otp_expiration', 5)); ?>">
            <p class="description">
                <?php esc_html_e('How long OTP codes remain valid after being generated.', 'farmfaucet'); ?>
            </p>
        </div>
        
        <h3><?php esc_html_e('Login Form Settings', 'farmfaucet'); ?></h3>
        
        <div class="form-group">
            <label for="farmfaucet_login_form_title">
                <?php esc_html_e('Login Form Title', 'farmfaucet'); ?>
            </label>
            <input type="text" id="farmfaucet_login_form_title" name="farmfaucet_login_form_title" value="<?php echo esc_attr(get_option('farmfaucet_login_form_title', 'Login with Telegram')); ?>">
        </div>
        
        <div class="form-group">
            <label for="farmfaucet_login_button_text">
                <?php esc_html_e('Login Button Text', 'farmfaucet'); ?>
            </label>
            <input type="text" id="farmfaucet_login_button_text" name="farmfaucet_login_button_text" value="<?php echo esc_attr(get_option('farmfaucet_login_button_text', 'Login')); ?>">
        </div>
        
        <div class="form-group">
            <label for="farmfaucet_tg_login_redirect">
                <?php esc_html_e('Login Redirect Page', 'farmfaucet'); ?>
            </label>
            <select id="farmfaucet_tg_login_redirect" name="farmfaucet_tg_login_redirect">
                <option value="0"><?php esc_html_e('Default (Home Page)', 'farmfaucet'); ?></option>
                <?php foreach ($pages as $page): ?>
                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected(get_option('farmfaucet_tg_login_redirect'), $page->ID); ?>>
                        <?php echo esc_html($page->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <p class="description">
                <?php esc_html_e('Where to redirect users after successful login.', 'farmfaucet'); ?>
            </p>
        </div>
        
        <div class="form-group">
            <label for="farmfaucet_tg_signup_redirect">
                <?php esc_html_e('Signup Redirect Page', 'farmfaucet'); ?>
            </label>
            <select id="farmfaucet_tg_signup_redirect" name="farmfaucet_tg_signup_redirect">
                <option value="0"><?php esc_html_e('Default (Home Page)', 'farmfaucet'); ?></option>
                <?php foreach ($pages as $page): ?>
                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected(get_option('farmfaucet_tg_signup_redirect'), $page->ID); ?>>
                        <?php echo esc_html($page->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <p class="description">
                <?php esc_html_e('Where to redirect users after successful signup.', 'farmfaucet'); ?>
            </p>
        </div>
        
        <div class="form-actions">
            <?php submit_button(esc_html__('Save Settings', 'farmfaucet'), 'primary', 'submit', false); ?>
        </div>
    </form>
</div>

<div class="farmfaucet-settings-section">
    <h2><?php esc_html_e('Telegram Login Shortcodes', 'farmfaucet'); ?></h2>
    
    <table class="form-table">
        <tr>
            <th scope="row"><?php esc_html_e('Login Form', 'farmfaucet'); ?></th>
            <td>
                <code>[farmfaucet_tg_login]</code>
                <p class="description">
                    <?php esc_html_e('Displays a Telegram login form with OTP verification.', 'farmfaucet'); ?>
                </p>
                <p class="description">
                    <?php esc_html_e('Optional attributes:', 'farmfaucet'); ?>
                    <code>title="Login with Telegram"</code>
                    <code>button_text="Login"</code>
                    <code>redirect_url="https://example.com/dashboard"</code>
                </p>
            </td>
        </tr>
        <tr>
            <th scope="row"><?php esc_html_e('Signup Form', 'farmfaucet'); ?></th>
            <td>
                <code>[farmfaucet_tg_signup]</code>
                <p class="description">
                    <?php esc_html_e('Displays a Telegram signup form with OTP verification.', 'farmfaucet'); ?>
                </p>
                <p class="description">
                    <?php esc_html_e('Optional attributes:', 'farmfaucet'); ?>
                    <code>title="Sign Up with Telegram"</code>
                    <code>button_text="Sign Up"</code>
                    <code>redirect_url="https://example.com/welcome"</code>
                </p>
            </td>
        </tr>
    </table>
</div>
