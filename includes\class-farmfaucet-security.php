<?php
class Farmfaucet_Security
{
    /**
     * Plugin activation handler
     */
    public static function activate_plugin()
    {
        try {
            // Add default options if they don't exist
            if (function_exists('get_option') && function_exists('add_option')) {
                $default_options = [
                    'farmfaucet_captcha_type' => 'hcaptcha',
                    'farmfaucet_hcaptcha_sitekey' => '',
                    'farmfaucet_hcaptcha_secret' => '',
                    'farmfaucet_recaptcha_sitekey' => '',
                    'farmfaucet_recaptcha_secret' => '',
                    'farmfaucet_turnstile_sitekey' => '',
                    'farmfaucet_turnstile_secret' => '',
                    'farmfaucet_faucetpay_api' => '',
                    'farmfaucet_currency' => 'LTC',
                    'farmfaucet_amount' => '0.001',
                    'farmfaucet_cooldown' => '3600'
                ];

                foreach ($default_options as $option_name => $default_value) {
                    if (get_option($option_name) === false) {
                        add_option($option_name, $default_value);
                    }
                }
            } else {
                error_log('Farmfaucet activation: WordPress option functions not available');
            }

            // Create database tables with error handling for each step
            try {
                self::create_log_table();
            } catch (Exception $e) {
                error_log('Farmfaucet log table creation error: ' . $e->getMessage());
            }

            try {
                self::create_faucets_table();
            } catch (Exception $e) {
                error_log('Farmfaucet faucets table creation error: ' . $e->getMessage());
            }

            try {
                self::create_buttons_table();
            } catch (Exception $e) {
                error_log('Farmfaucet buttons table creation error: ' . $e->getMessage());
            }

            try {
                self::create_users_table();
            } catch (Exception $e) {
                error_log('Farmfaucet users table creation error: ' . $e->getMessage());
            }

            try {
                self::create_leaderboard_table();
            } catch (Exception $e) {
                error_log('Farmfaucet leaderboard table creation error: ' . $e->getMessage());
            }

            try {
                self::create_claims_table();
            } catch (Exception $e) {
                error_log('Farmfaucet claims table creation error: ' . $e->getMessage());
            }

            // Create default faucet if none exists
            try {
                self::create_default_faucet();
            } catch (Exception $e) {
                error_log('Farmfaucet default faucet creation error: ' . $e->getMessage());
            }

            // Run database updates using the consolidated updater class
            try {
                if (class_exists('Farmfaucet_DB_Updater_Consolidated')) {
                    // Run all updates
                    Farmfaucet_DB_Updater_Consolidated::run_updates();
                } else {
                    error_log('Farmfaucet_DB_Updater_Consolidated class not found');
                    // Fall back to the old update method
                    self::update_database_schema();
                }
            } catch (Exception $e) {
                error_log('Farmfaucet database update error: ' . $e->getMessage());
            }
        } catch (Exception $e) {
            // Log the error but don't break the activation
            error_log('Farmfaucet activation error: ' . $e->getMessage());

            // Check for database updates
            try {
                // Try the consolidated updater
                if (class_exists('Farmfaucet_DB_Updater_Consolidated')) {
                    // Run all updates
                    Farmfaucet_DB_Updater_Consolidated::run_updates();
                } else {
                    // Fall back to the old update method
                    self::update_database_schema();
                }
            } catch (Exception $e) {
                error_log('Farmfaucet database schema update error: ' . $e->getMessage());
            }
        }
    }

    /**
     * Update database schema for existing installations
     */
    private static function update_database_schema()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;

        if ($table_exists) {
            // Check if new columns exist
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Add lock_faucet column if it doesn't exist
            if (!in_array('lock_faucet', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN lock_faucet tinyint(1) NOT NULL DEFAULT 0 AFTER reset_minutes");
            }

            // Add milestone_enabled column if it doesn't exist
            if (!in_array('milestone_enabled', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_enabled tinyint(1) NOT NULL DEFAULT 0 AFTER lock_faucet");
            }

            // Add milestone_count column if it doesn't exist
            if (!in_array('milestone_count', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_count int(11) NOT NULL DEFAULT 0 AFTER milestone_enabled");
            }

            // Add milestone_specific_faucet column if it doesn't exist
            if (!in_array('milestone_specific_faucet', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_specific_faucet mediumint(9) NOT NULL DEFAULT 0 AFTER milestone_count");
            }

            // Add milestone_specific_count column if it doesn't exist
            if (!in_array('milestone_specific_count', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_specific_count int(11) NOT NULL DEFAULT 0 AFTER milestone_specific_faucet");
            }

            // Add milestone_type column if it doesn't exist
            if (!in_array('milestone_type', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_type varchar(20) NOT NULL DEFAULT 'global' AFTER milestone_enabled");
            }

            // Add milestone_lock_faucet column if it doesn't exist
            if (!in_array('milestone_lock_faucet', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_lock_faucet tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_specific_count");
            }

            // Add milestone_pages column if it doesn't exist
            if (!in_array('milestone_pages', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_pages longtext DEFAULT NULL AFTER milestone_lock_faucet");
            }

            // Add milestone appearance columns if they don't exist
            if (!in_array('milestone_display_style', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_display_style varchar(20) NOT NULL DEFAULT 'card' AFTER milestone_pages");
            }

            if (!in_array('milestone_transparent_bg', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_display_style");
            }

            if (!in_array('milestone_card_bg_style', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_bg_style varchar(20) NOT NULL DEFAULT 'solid' AFTER milestone_transparent_bg");
            }

            if (!in_array('milestone_card_bg_color', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_bg_color varchar(50) NOT NULL DEFAULT '#FFFFFF' AFTER milestone_card_bg_style");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_bg_color varchar(50) NOT NULL DEFAULT '#FFFFFF'");
            }

            if (!in_array('milestone_card_gradient_start', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_gradient_start varchar(50) NOT NULL DEFAULT '#FFFFFF' AFTER milestone_card_bg_color");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_gradient_start varchar(50) NOT NULL DEFAULT '#FFFFFF'");
            }

            if (!in_array('milestone_card_gradient_end', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_gradient_end varchar(50) NOT NULL DEFAULT '#F5F5F5' AFTER milestone_card_gradient_start");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_gradient_end varchar(50) NOT NULL DEFAULT '#F5F5F5'");
            }

            if (!in_array('milestone_bar_style', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_bar_style varchar(20) NOT NULL DEFAULT 'solid' AFTER milestone_card_gradient_end");
            }

            if (!in_array('milestone_bar_color', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_bar_color varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER milestone_bar_style");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_bar_color varchar(50) NOT NULL DEFAULT '#4CAF50'");
            }

            if (!in_array('milestone_gradient_start', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_gradient_start varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER milestone_bar_color");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_gradient_start varchar(50) NOT NULL DEFAULT '#4CAF50'");
            }

            if (!in_array('milestone_gradient_end', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_gradient_end varchar(50) NOT NULL DEFAULT '#2196F3' AFTER milestone_gradient_start");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_gradient_end varchar(50) NOT NULL DEFAULT '#2196F3'");
            }

            // Update button_color column size
            $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN button_color varchar(50) NOT NULL DEFAULT 'blue'");

            // Add countdown columns if they don't exist
            if (!in_array('countdown_enabled', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_enabled tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_gradient_end");
            }

            if (!in_array('countdown_seconds', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_seconds int(11) NOT NULL DEFAULT 60 AFTER countdown_enabled");
            }

            if (!in_array('countdown_message', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_message varchar(255) DEFAULT '' NOT NULL AFTER countdown_seconds");
            }

            if (!in_array('countdown_click_activation', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_click_activation tinyint(1) NOT NULL DEFAULT 0 AFTER countdown_message");
            }

            if (!in_array('countdown_click_element_id', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_click_element_id varchar(50) DEFAULT '' NOT NULL AFTER countdown_click_activation");
            }

            if (!in_array('countdown_pre_click_message', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_pre_click_message varchar(255) DEFAULT '' NOT NULL AFTER countdown_click_element_id");
            }

            if (!in_array('countdown_standby', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_standby tinyint(1) NOT NULL DEFAULT 0 AFTER countdown_pre_click_message");
            }

            // Add index for milestone_enabled if it doesn't exist
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$buttons_table} WHERE Key_name = 'milestone_enabled'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD INDEX milestone_enabled (milestone_enabled)");
            }

            // Add index for milestone_type if it doesn't exist
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$buttons_table} WHERE Key_name = 'milestone_type'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD INDEX milestone_type (milestone_type)");
            }

            // Add index for countdown_enabled if it doesn't exist
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$buttons_table} WHERE Key_name = 'countdown_enabled'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD INDEX countdown_enabled (countdown_enabled)");
            }
        }

        // Check if users table exists and create it if not
        $users_table = $wpdb->prefix . 'farmfaucet_users';
        $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$users_table}'") === $users_table;
        if (!$users_table_exists) {
            self::create_users_table();
        }

        // Check if leaderboard table exists and create it if not
        $leaderboard_table = $wpdb->prefix . 'farmfaucet_leaderboard';
        $leaderboard_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$leaderboard_table}'") === $leaderboard_table;
        if (!$leaderboard_table_exists) {
            self::create_leaderboard_table();
        }

        // Check if claims table exists and create it if not
        $claims_table = $wpdb->prefix . 'farmfaucet_claims';
        $claims_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$claims_table}'") === $claims_table;
        if (!$claims_table_exists) {
            self::create_claims_table();
        }
    }

    /**
     * Create database table for error logging
     */
    private static function create_log_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_logs';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            timestamp datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            message text NOT NULL,
            type varchar(20) NOT NULL,
            faucet_id int(11) DEFAULT 0 NOT NULL,
            PRIMARY KEY  (id),
            KEY type (type),
            KEY faucet_id (faucet_id),
            KEY timestamp (timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet table creation error: ' . $e->getMessage());
        }
    }

    /**
     * Create database table for multiple faucets
     */
    private static function create_faucets_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            currency varchar(20) NOT NULL,
            amount varchar(50) NOT NULL,
            cooldown int(11) NOT NULL,
            shortcode varchar(50) NOT NULL,
            api_key varchar(255) DEFAULT '' NOT NULL,
            captcha_type varchar(20) DEFAULT '' NOT NULL,
            created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY shortcode (shortcode),
            KEY currency (currency),
            KEY created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet table creation error: ' . $e->getMessage());
        }
    }

    /**
     * Create default faucet if none exists
     */
    private static function create_default_faucet()
    {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'farmfaucet_faucets';

            // Check if any faucets exist
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

            if ($count == 0) {
                // Get default values with fallbacks
                $currency = function_exists('get_option') ? get_option('farmfaucet_currency', 'LTC') : 'LTC';
                $amount = function_exists('get_option') ? get_option('farmfaucet_amount', '0.001') : '0.001';
                $cooldown = function_exists('get_option') ? get_option('farmfaucet_cooldown', '3600') : '3600';
                $api_key = function_exists('get_option') ? get_option('farmfaucet_faucetpay_api', '') : '';
                $created_at = function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s');
                $updated_at = $created_at;

                // Create default faucet with current settings
                $wpdb->insert(
                    $table_name,
                    [
                        'name' => 'Default Faucet',
                        'currency' => $currency,
                        'amount' => $amount,
                        'cooldown' => $cooldown,
                        'shortcode' => 'farmfaucet',
                        'api_key' => $api_key,
                        'captcha_type' => function_exists('get_option') ? get_option('farmfaucet_captcha_type', 'hcaptcha') : 'hcaptcha',
                        'created_at' => $created_at,
                        'updated_at' => $updated_at
                    ],
                    ['%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s']
                );
            }
        } catch (Exception $e) {
            error_log('Farmfaucet default faucet creation error: ' . $e->getMessage());
        }
    }

    /**
     * Plugin deactivation cleanup
     */
    public static function deactivate_plugin()
    {
        // Placeholder for future cleanup
    }

    /**
     * Sanitize API keys
     */
    public static function sanitize_api_key($input)
    {
        return sanitize_text_field(trim($input));
    }

    /**
     * Validate cryptocurrency amount (string-based)
     */
    public static function validate_amount($amount)
    {
        // Convert commas to dots for international formats
        $amount = str_replace(',', '.', sanitize_text_field($amount));

        // Validate decimal format (max 8 decimal places)
        if (!preg_match('/^[0-9]+(\.[0-9]{1,8})?$/', $amount)) {
            return '0.001'; // Default value if invalid
        }

        return $amount; // Return raw string value
    }

    /**
     * Get user IP address with enhanced validation
     *
     * @return string Validated IP address
     */
    public static function get_user_ip()
    {
        // Default to REMOTE_ADDR as it's the most reliable
        $ip = !empty($_SERVER['REMOTE_ADDR']) ? sanitize_text_field($_SERVER['REMOTE_ADDR']) : '';

        // Only use other headers if REMOTE_ADDR is a known proxy
        $is_known_proxy = in_array($ip, ['127.0.0.1', '::1']) || strpos($ip, '10.') === 0 || strpos($ip, '192.168.') === 0;

        if ($is_known_proxy) {
            // Try X-Forwarded-For first (common for proxies)
            if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                // X-Forwarded-For can contain multiple IPs - get the first one
                $forwarded_ips = explode(',', sanitize_text_field($_SERVER['HTTP_X_FORWARDED_FOR']));
                $client_ip = trim($forwarded_ips[0]);

                // Validate IP format
                if (self::is_valid_ip($client_ip)) {
                    return $client_ip;
                }
            }

            // Try other headers if X-Forwarded-For failed
            $headers = ['HTTP_CLIENT_IP', 'HTTP_X_REAL_IP', 'HTTP_X_FORWARDED'];

            foreach ($headers as $header) {
                if (!empty($_SERVER[$header])) {
                    $possible_ip = sanitize_text_field($_SERVER[$header]);
                    if (self::is_valid_ip($possible_ip)) {
                        return $possible_ip;
                    }
                }
            }
        }

        return $ip;
    }

    /**
     * Validate IP address format
     *
     * @param string $ip IP address to validate
     * @return bool True if valid IP format
     */
    private static function is_valid_ip($ip)
    {
        // Check for valid IPv4 format
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return true;
        }

        // Check for valid IPv6 format
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return true;
        }

        return false;
    }

    /**
     * Verify nonce
     */
    public static function verify_nonce($nonce, $action)
    {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Validate URLs
     */
    public static function validate_url($url)
    {
        return esc_url_raw(trim($url));
    }

    /**
     * Encrypt API key
     *
     * @param string $api_key The API key to encrypt
     * @return string The encrypted API key
     */
    public static function encrypt_api_key($api_key)
    {
        if (empty($api_key)) {
            return '';
        }

        // Get the encryption key
        $encryption_key = self::get_encryption_key();

        // If we can use Sodium for encryption, use it
        if (function_exists('sodium_crypto_secretbox') && function_exists('random_bytes')) {
            try {
                $nonce = random_bytes(SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
                $encrypted = sodium_crypto_secretbox($api_key, $nonce, $encryption_key);
                $encoded = base64_encode($nonce . $encrypted);
                return 'sodium:' . $encoded;
            } catch (Exception $e) {
                error_log('Farmfaucet encryption error: ' . $e->getMessage());
                // Fall back to simple encryption
            }
        }

        // Simple fallback encryption using XOR
        $encrypted = '';
        $key_length = strlen($encryption_key);
        for ($i = 0; $i < strlen($api_key); $i++) {
            $encrypted .= chr(ord($api_key[$i]) ^ ord($encryption_key[$i % $key_length]));
        }

        return 'simple:' . base64_encode($encrypted);
    }

    /**
     * Decrypt API key
     *
     * @param string $encrypted_api_key The encrypted API key
     * @return string The decrypted API key
     */
    public static function decrypt_api_key($encrypted_api_key)
    {
        if (empty($encrypted_api_key)) {
            return '';
        }

        // Get the encryption key
        $encryption_key = self::get_encryption_key();

        // Check if it's a Sodium encrypted key
        if (strpos($encrypted_api_key, 'sodium:') === 0) {
            $encoded = substr($encrypted_api_key, 7);
            $decoded = base64_decode($encoded);

            if (function_exists('sodium_crypto_secretbox_open') && strlen($decoded) > SODIUM_CRYPTO_SECRETBOX_NONCEBYTES) {
                try {
                    $nonce = substr($decoded, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
                    $ciphertext = substr($decoded, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
                    $decrypted = sodium_crypto_secretbox_open($ciphertext, $nonce, $encryption_key);

                    if ($decrypted !== false) {
                        return $decrypted;
                    }
                } catch (Exception $e) {
                    error_log('Farmfaucet decryption error: ' . $e->getMessage());
                    // Fall through to return the original value
                }
            }
        }

        // Check if it's a simple encrypted key
        if (strpos($encrypted_api_key, 'simple:') === 0) {
            $encoded = substr($encrypted_api_key, 7);
            $encrypted = base64_decode($encoded);

            $decrypted = '';
            $key_length = strlen($encryption_key);
            for ($i = 0; $i < strlen($encrypted); $i++) {
                $decrypted .= chr(ord($encrypted[$i]) ^ ord($encryption_key[$i % $key_length]));
            }

            return $decrypted;
        }

        // If it's not encrypted or we can't decrypt it, return as is
        return $encrypted_api_key;
    }

    /**
     * Get encryption key
     *
     * @return string The encryption key
     */
    private static function get_encryption_key()
    {
        // Try to use WordPress salt if available
        if (defined('AUTH_KEY')) {
            $key = AUTH_KEY;
        } else {
            // Fallback to a hardcoded key (not ideal but better than nothing)
            $key = 'farmfaucet_encryption_key_' . ABSPATH;
        }

        // For Sodium, we need a specific key length
        if (function_exists('sodium_crypto_secretbox_keygen')) {
            try {
                // Create a deterministic key from our string
                $hash = hash('sha256', $key, true);
                return substr($hash, 0, SODIUM_CRYPTO_SECRETBOX_KEYBYTES);
            } catch (Exception $e) {
                error_log('Farmfaucet key generation error: ' . $e->getMessage());
            }
        }

        // For simple encryption, just return the key
        return $key;
    }

    /**
     * Create database table for faucet buttons
     */
    private static function create_buttons_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            faucet_id mediumint(9) NOT NULL,
            button_text varchar(255) NOT NULL,
            button_size varchar(20) NOT NULL DEFAULT 'medium',
            button_color varchar(50) NOT NULL DEFAULT 'blue',
            border_shape varchar(20) NOT NULL DEFAULT 'rounded',
            redirect_url varchar(255) DEFAULT '' NOT NULL,
            shortcode varchar(50) NOT NULL,
            is_locked tinyint(1) NOT NULL DEFAULT 0,
            required_faucets text DEFAULT NULL,
            reset_minutes int(11) NOT NULL DEFAULT 0,
            lock_faucet tinyint(1) NOT NULL DEFAULT 0,
            milestone_enabled tinyint(1) NOT NULL DEFAULT 0,
            milestone_type varchar(20) NOT NULL DEFAULT 'global',
            milestone_count int(11) NOT NULL DEFAULT 0,
            milestone_specific_faucet mediumint(9) NOT NULL DEFAULT 0,
            milestone_specific_count int(11) NOT NULL DEFAULT 0,
            milestone_lock_faucet tinyint(1) NOT NULL DEFAULT 0,
            milestone_display_style varchar(20) NOT NULL DEFAULT 'card',
            milestone_transparent_bg tinyint(1) NOT NULL DEFAULT 0,
            milestone_card_bg_style varchar(20) NOT NULL DEFAULT 'solid',
            milestone_card_bg_color varchar(50) NOT NULL DEFAULT '#FFFFFF',
            milestone_card_gradient_start varchar(50) NOT NULL DEFAULT '#FFFFFF',
            milestone_card_gradient_end varchar(50) NOT NULL DEFAULT '#F5F5F5',
            milestone_pages longtext DEFAULT NULL,
            milestone_bar_style varchar(20) NOT NULL DEFAULT 'solid',
            milestone_bar_color varchar(50) NOT NULL DEFAULT '#4CAF50',
            milestone_gradient_start varchar(50) NOT NULL DEFAULT '#4CAF50',
            milestone_gradient_end varchar(50) NOT NULL DEFAULT '#2196F3',
            countdown_enabled tinyint(1) NOT NULL DEFAULT 0,
            countdown_seconds int(11) NOT NULL DEFAULT 60,
            countdown_message varchar(255) DEFAULT '' NOT NULL,
            countdown_click_activation tinyint(1) NOT NULL DEFAULT 0,
            countdown_click_element_id varchar(50) DEFAULT '' NOT NULL,
            countdown_pre_click_message varchar(255) DEFAULT '' NOT NULL,
            countdown_standby tinyint(1) NOT NULL DEFAULT 0,
            created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            PRIMARY KEY  (id),
            KEY faucet_id (faucet_id),
            UNIQUE KEY shortcode (shortcode),
            KEY is_locked (is_locked),
            KEY milestone_enabled (milestone_enabled),
            KEY milestone_type (milestone_type),
            KEY countdown_enabled (countdown_enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet table creation error: ' . $e->getMessage());
        }
    }

    /**
     * Create database table for user profiles
     */
    private static function create_users_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_users';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_hash varchar(64) NOT NULL,
            display_name varchar(50) DEFAULT NULL,
            profile_picture varchar(255) DEFAULT NULL,
            created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY user_hash (user_hash),
            KEY created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet users table creation error: ' . $e->getMessage());
        }
    }

    /**
     * Create database table for leaderboard tracking
     */
    private static function create_leaderboard_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_leaderboard';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_hash varchar(64) NOT NULL,
            total_completions int(11) NOT NULL DEFAULT 0,
            last_completion datetime DEFAULT NULL,
            created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY user_hash (user_hash),
            KEY total_completions (total_completions),
            KEY last_completion (last_completion)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet leaderboard table creation error: ' . $e->getMessage());
        }
    }

    /**
     * Create database table for claims tracking
     */
    private static function create_claims_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_claims';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_hash varchar(64) NOT NULL,
            faucet_id mediumint(9) NOT NULL,
            timestamp datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            amount varchar(50) NOT NULL,
            currency varchar(20) NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'success',
            PRIMARY KEY  (id),
            KEY user_hash (user_hash),
            KEY faucet_id (faucet_id),
            KEY timestamp (timestamp),
            KEY status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        try {
            if (file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                if (function_exists('dbDelta')) {
                    dbDelta($sql);
                } else {
                    // Fallback if dbDelta is not available
                    global $wpdb;
                    $wpdb->query($sql);
                }
            } else {
                // Fallback if upgrade.php is not available
                global $wpdb;
                $wpdb->query($sql);
            }
        } catch (Exception $e) {
            error_log('Farmfaucet claims table creation error: ' . $e->getMessage());
        }
    }
}
