/**
 * Farm Faucet Avatar Styles
 */

/* Basic avatar styles */
.farmfaucet-avatar {
    display: inline-block;
    position: relative;
}

.farmfaucet-avatar img {
    display: block;
    border-radius: 50%;
    object-fit: cover;
}

/* Editable avatar styles */
.farmfaucet-avatar-editable {
    display: inline-block;
    position: relative;
}

.farmfaucet-avatar-editable img {
    display: block;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.2s ease;
}

.farmfaucet-avatar-editable img:hover {
    filter: brightness(0.9);
}

.farmfaucet-avatar-editable input[type="file"] {
    display: none;
}

/* Avatar popup */
.farmfaucet-avatar-popup {
    position: absolute;
    z-index: 100;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    padding: 10px 0;
    min-width: 150px;
    display: none;
}

.farmfaucet-avatar-popup:before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.farmfaucet-avatar-popup ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.farmfaucet-avatar-popup li {
    padding: 8px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.farmfaucet-avatar-popup li:hover {
    background: #f5f5f5;
}

.farmfaucet-avatar-popup li i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Success message */
.farmfaucet-avatar-success {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(76, 175, 80, 0.8);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    opacity: 0;
    pointer-events: none;
}

/* Uploading state */
.farmfaucet-avatar-editable.uploading:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.farmfaucet-avatar-editable.uploading:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    z-index: 10;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Lightbox */
.farmfaucet-avatar-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    display: none;
}

.farmfaucet-avatar-lightbox .lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.farmfaucet-avatar-lightbox img {
    display: block;
    max-width: 100%;
    max-height: 80vh;
    border-radius: 4px;
}

.farmfaucet-avatar-lightbox .close-lightbox {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 30px;
    height: 30px;
    background: white;
    border-radius: 50%;
    border: none;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Username form */
.farmfaucet-username-container {
    margin: 15px 0;
}

.farmfaucet-current-username {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 10px;
}

.farmfaucet-current-name-display {
    font-size: 16px;
}

.farmfaucet-username-edit-btn {
    background: #2271b1;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.farmfaucet-username-edit-btn:hover {
    background: #135e96;
}

.farmfaucet-username-form {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
}

.farmfaucet-username-label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.farmfaucet-username-input-container {
    margin-bottom: 15px;
}

.farmfaucet-username-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
}

.farmfaucet-username-submit-container {
    text-align: right;
}

.farmfaucet-username-submit {
    background: #2271b1;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.farmfaucet-username-submit:hover {
    background: #135e96;
}

.farmfaucet-username-cancel-link {
    display: inline-block;
    margin-right: 10px;
    color: #666;
    text-decoration: none;
    cursor: pointer;
}

.farmfaucet-username-success {
    background: #dff0d8;
    color: #3c763d;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    display: none;
}
