<?php
/**
 * Farm Faucet Color Utilities
 *
 * Provides utility functions for handling colors in the Farm Faucet plugin
 */
class Farmfaucet_Color_Utils
{
    /**
     * Sanitize a hex color value
     *
     * @param string $color The color to sanitize
     * @return string The sanitized color
     */
    public static function sanitize_hex_color($color)
    {
        // If empty, return empty string
        if (empty($color)) {
            return '';
        }
        
        // Remove any spaces
        $color = trim($color);
        
        // If it's a named color, return as is
        if (in_array($color, ['blue', 'green', 'red', 'orange', 'purple', 'black', 'custom'])) {
            return $color;
        }
        
        // If it doesn't start with a #, add it
        if (substr($color, 0, 1) !== '#') {
            $color = '#' . $color;
        }
        
        // Validate hex color format
        if (preg_match('|^#([A-Fa-f0-9]{3}){1,2}$|', $color)) {
            return $color;
        }
        
        // Default to empty if invalid
        return '';
    }
    
    /**
     * Get the hex color value for a named color
     *
     * @param string $color_name The name of the color
     * @return string The hex color value
     */
    public static function get_color_hex_from_name($color_name)
    {
        $colors = [
            'blue' => '#2271b1',
            'green' => '#4CAF50',
            'red' => '#f44336',
            'orange' => '#ff9800',
            'purple' => '#9c27b0',
            'black' => '#000000',
            'custom' => '#4CAF50' // Default to green for custom
        ];
        
        return isset($colors[$color_name]) ? $colors[$color_name] : '#4CAF50';
    }
    
    /**
     * Get the color name from a hex value
     *
     * @param string $hex_color The hex color value
     * @return string The color name
     */
    public static function get_color_name_from_hex($hex_color)
    {
        $hex_color = strtolower($hex_color);
        
        $colors = [
            '#2271b1' => 'blue',
            '#4caf50' => 'green',
            '#f44336' => 'red',
            '#ff9800' => 'orange',
            '#9c27b0' => 'purple',
            '#000000' => 'black'
        ];
        
        return isset($colors[$hex_color]) ? $colors[$hex_color] : 'custom';
    }
}
