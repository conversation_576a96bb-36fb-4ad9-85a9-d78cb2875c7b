// Use the global jQuery object or fallback to the standard jQuery
var $ = window.farmfaucetJQuery || jQuery;

// Make sure we have a valid jQuery instance
if (typeof $ !== 'function') {
    console.error('Farmfaucet: jQuery not found!');
}

$(document).ready(function() {
    // Array of motivational progress messages
    const progressMessages = [
        // Early progress messages (0-25%)
        "Just getting started! Every journey begins with a single step.",
        "You're on your way! Keep going!",
        "Making progress! Small steps lead to big achievements.",
        "Keep going! You're building momentum!",
        "The first steps are always the hardest. You're doing great!",

        // Mid progress messages (25-50%)
        "You're making steady progress! Keep it up!",
        "Getting there! You've completed a quarter of your tasks!",
        "Keep the momentum going! You're doing well!",
        "Progress is progress, no matter how small!",
        "You're on the right track! Keep going!",

        // Good progress messages (50-75%)
        "Halfway there! You're making excellent progress!",
        "Keep pushing forward! The finish line is getting closer!",
        "You've accomplished so much already! Keep going!",
        "You're doing amazing! Don't stop now!",
        "The hard part is behind you! Keep up the great work!",

        // Almost complete messages (75-100%)
        "Almost there! Just a few more tasks to complete.",
        "You're in the home stretch! Keep going!",
        "So close to the finish line! You can do this!",
        "Just a little more effort and you'll be done!",
        "You've come so far already! Just a bit more to go!"
    ];

    // Function to update progress messages
    function updateProgressMessages() {
        $('.farmfaucet-milestone-container').each(function() {
            const $container = $(this);
            const $summary = $container.find('.farmfaucet-milestone-summary');

            if ($summary.length) {
                // Get current progress percentage
                const progress = parseInt($container.find('.farmfaucet-milestone-progress-bar').data('progress')) || 0;

                // Select a message based on progress
                let messagePool = [];

                if (progress < 25) {
                    // Early progress (0-25%)
                    messagePool = progressMessages.slice(0, 5);
                } else if (progress < 50) {
                    // Mid progress (25-50%)
                    messagePool = progressMessages.slice(5, 10);
                } else if (progress < 75) {
                    // Good progress (50-75%)
                    messagePool = progressMessages.slice(10, 15);
                } else {
                    // Almost complete (75-100%)
                    messagePool = progressMessages.slice(15, 20);
                }

                // Get a random message from the appropriate pool
                const randomIndex = Math.floor(Math.random() * messagePool.length);
                const message = messagePool[randomIndex];

                // Only update if the message is different
                if ($summary.data('current-message') !== message) {
                    // Store current message
                    $summary.data('current-message', message);

                    // Fade out, update text, fade in
                    $summary.fadeOut(500, function() {
                        // Save the arrow indicator element
                        const $arrow = $summary.find('.milestone-dropdown-arrow');

                        // Update text with new message
                        $summary.text(message);

                        // Re-append the arrow indicator if it exists
                        if ($arrow.length) {
                            $summary.append($arrow);
                        } else {
                            // Create a new arrow if it doesn't exist
                            $summary.append('<span class="milestone-dropdown-arrow">▼</span>');
                        }

                        $summary.fadeIn(500);
                    });
                }
            }
        });
    }

    // Initialize progress bars
    var progressBars = document.querySelectorAll(".farmfaucet-milestone-progress-bar");
    progressBars.forEach(function(bar) {
        var progress = bar.getAttribute("data-progress");
        setTimeout(function() {
            bar.style.width = progress + "%";
        }, 100);
    });

    // Initialize progress messages
    if ($('.farmfaucet-milestone-container').length) {
        // Initial update
        setTimeout(function() {
            updateProgressMessages();

            // Get the message change interval from data attribute or use default (3 minutes)
            const messageChangeInterval = parseInt($('.farmfaucet-milestone-container').data('message-interval')) || 180;
            const messageChangeIntervalMs = messageChangeInterval * 1000;

            // Set interval to update messages based on the configured interval
            setInterval(updateProgressMessages, messageChangeIntervalMs);

            console.log('Message change interval set to', messageChangeInterval, 'seconds');
        }, 2000); // Delay initial update to ensure progress bars are loaded
    }

    // Toggle milestone task list when clicking on the summary
    $(document).on('click', '.farmfaucet-milestone-summary', function() {
        const $container = $(this).closest('.farmfaucet-milestone-container');
        $container.toggleClass('show-tasks expanded');

        // Toggle arrow direction
        const $arrow = $(this).find('.milestone-dropdown-arrow');
        if ($container.hasClass('show-tasks')) {
            $arrow.css('transform', 'rotate(180deg)');
        } else {
            $arrow.css('transform', 'rotate(0deg)');
        }
    });

    // Initialize dropdown arrows if they don't exist
    $('.farmfaucet-milestone-summary').each(function() {
        if ($(this).find('.milestone-dropdown-arrow').length === 0) {
            $(this).append('<span class="milestone-dropdown-arrow">▼</span>');
        }
    });
});
