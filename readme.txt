=== FarmFaucet ===
Contributors: ZpromoterZ
Tags: crypto, faucet, cryptocurrency, FaucetPay, hCaptcha, instant faucet
Requires at least: 5.0
Tested up to: 6.2
Stable tag: 2.1
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

FarmFaucet is an instant cryptocurrency faucet plugin for WordPress that allows admins to send crypto (e.g., LTC) directly to users' FaucetPay accounts. The plugin includes hCaptcha verification, FaucetPay API integration, customizable cooldown timers, and a modern UI with shortcode support.

== Description ==
FarmFaucet provides the following features:
* **Instant Crypto Payouts:** Automatically sends crypto rewards to users' FaucetPay accounts.
* **hCaptcha Integration:** Protects against spam by requiring users to complete an hCaptcha challenge.
* **Admin Configurable Options:** Easily set hCaptcha keys, FaucetPay API credentials, currency type, crypto amount, and cooldown timers.
* **Shortcode Support:** Embed the faucet claim form anywhere using the `[farmfaucet_claim]` shortcode.
* **Modern UI Design:** A clean and modern interface for both the admin panel and the frontend.

== Installation ==
1. Upload the entire `farmfaucet` folder to your `/wp-content/plugins/` directory.
2. Activate the plugin through the WordPress Plugins menu.
3. Configure the plugin settings in the FarmFaucet admin panel.

== Frequently Asked Questions ==
= How do I configure hCaptcha? =
Enter your hCaptcha site key and secret key in the FarmFaucet admin panel.

= How do I set the cooldown timer? =
Set the desired cooldown time in the admin panel. The timer starts once the user clicks the CLAIM button.

== Changelog ==
= 2.1 =
* Added hCaptcha verification.
* Integrated FaucetPay API settings.
* Enhanced the admin panel with grid layout and input hints.
* Improved frontend design with dynamic headers and JavaScript notifications.
* Added cooldown timer functionality.

== Upgrade Notice ==
= 2.1 =
Update to this version to benefit from improved security and new features.



=== Farmfaucet ===
Contributors: ZpromoterZ
Donate link: https://example.com/donate
Tags: cryptocurrency, faucet, faucetpay
Requires at least: 5.0
Tested up to: 6.0
Stable tag: 2.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Farmfaucet is a cryptocurrency faucet plugin with instant payouts via FaucetPay.

== Description ==
This plugin allows you to run a cryptocurrency faucet. It supports instant payouts via FaucetPay and comes with several features including API endpoints, a dedicated admin dashboard, and a shortcode for a claim form.

== Installation ==
1. Upload the entire `farmfaucet` folder to the `/wp-content/plugins/` directory.
2. Activate the plugin through the 'Plugins' menu in WordPress.
3. Insert the `[farmfaucet_claim]` shortcode in any post or page to display the faucet claim form.

== Changelog ==
= 2.1 =
* Updated activation and deactivation hooks.
* Improved file inclusion methods.
* Added enhanced security and logging.

== Upgrade Notice ==
= 2.1 =
Please update to version 2.1 for improved functionality and security.











VERSION HISTORY


=== Farmfaucet ===
Contributors: ZpromoterZ
Donate link: https://example.com/donate
Tags: cryptocurrency, faucet, litecoin, bitcoin, rewards
Requires at least: 5.6
Tested up to: 6.5
Stable tag: 2.0
License: Proprietary (All rights reserved)
License URI: https://example.com/farmfaucet-license

== Description ==
Instant cryptocurrency faucet with direct FaucetPay payouts. Users claim crypto rewards after completing hCaptcha verification.

== Features ==
- One-click LTC/BTC payouts via FaucetPay API
- hCaptcha anti-bot protection
- Customizable cooldown timer
- Modern UI with animations
- Detailed error logging
- Admin dashboard with shortcode support

== Installation ==
1. Upload farmfaucet.zip via WordPress plugins page
2. Activate the plugin
3. Configure settings under Farmfaucet admin menu
4. Add [farmfaucet] shortcode to any page

== Frequently Asked Questions ==
= How do I get FaucetPay API keys? =
Register at faucetpay.io > Account Settings > API Keys

= Why is hCaptcha not working? =
1. Verify site/secret keys match your hCaptcha dashboard
2. Ensure your domain is whitelisted in hCaptcha settings

= How to change the payout amount? =
Navigate to Farmfaucet admin > Set "Amount per Claim"

== Changelog ==
= 2.0 =
- Added server-side cooldown validation
- Implemented enhanced security protocols
- Introduced error logging system
- Redesigned admin interface

= 1.1 =
- Fixed JavaScript compatibility issues
- Improved mobile responsiveness

= 1.0 =
- Initial release

== Copyright Notice ==
This plugin is free to use but protected under international copyright laws (Berne Convention). Redistribution or resale without explicit written permission from ZpromoterZ is strictly prohibited. Reverse-engineering or unauthorized modifications forbidden.

For licensing inquiries: <EMAIL>