<?php
/**
 * Farm Faucet - Debug Settings Save Issue
 * 
 * This script specifically debugs the settings save failure
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Debug Settings Save</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        .step { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        .step h3 { margin-top: 0; color: #333; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; }
        .btn:hover { background: #45a049; }
        .code-block { background: #2d2d2d; color: #f8f8f2; padding: 15px; border-radius: 4px; overflow-x: auto; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Debug Settings Save Issue</h1>
    <p>This script specifically debugs why settings fail to save with "website unable to handle request" error.</p>

<?php

echo '<div class="step">';
echo '<h3>🔍 Step 1: Check Settings Registration</h3>';

// Check if settings are properly registered
$registered_settings = get_registered_settings();
$farmfaucet_settings = [];

foreach ($registered_settings as $setting => $data) {
    if (strpos($setting, 'farmfaucet_') === 0) {
        $farmfaucet_settings[$setting] = $data;
    }
}

if (!empty($farmfaucet_settings)) {
    echo '<div class="success"><p>✅ Found ' . count($farmfaucet_settings) . ' registered Farm Faucet settings:</p>';
    echo '<ul>';
    foreach ($farmfaucet_settings as $setting => $data) {
        echo '<li><strong>' . esc_html($setting) . '</strong>';
        if (isset($data['sanitize_callback'])) {
            if (is_array($data['sanitize_callback'])) {
                echo ' - Callback: ' . esc_html($data['sanitize_callback'][0] . '::' . $data['sanitize_callback'][1]);
            } else {
                echo ' - Callback: ' . esc_html($data['sanitize_callback']);
            }
        }
        echo '</li>';
    }
    echo '</ul></div>';
} else {
    echo '<div class="error"><p>❌ No Farm Faucet settings found in registered settings!</p></div>';
}

echo '</div>';

// Step 2: Test encryption callback
echo '<div class="step">';
echo '<h3>🔐 Step 2: Test Encryption Callback</h3>';

if (class_exists('Farmfaucet_Admin')) {
    $admin_instance = Farmfaucet_Admin::get_instance();
    
    if (method_exists($admin_instance, 'encrypt_api_key_setting')) {
        try {
            $test_key = 'test_api_key_12345';
            $encrypted = $admin_instance->encrypt_api_key_setting($test_key);
            
            if (!empty($encrypted) && $encrypted !== $test_key) {
                echo '<div class="success"><p>✅ Encryption callback working correctly</p>';
                echo '<p><strong>Original:</strong> ' . esc_html($test_key) . '</p>';
                echo '<p><strong>Encrypted:</strong> ' . esc_html(substr($encrypted, 0, 50)) . '...</p></div>';
            } else {
                echo '<div class="warning"><p>⚠️ Encryption callback returned original value (may be empty key handling)</p></div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ Encryption callback failed: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    } else {
        echo '<div class="error"><p>❌ encrypt_api_key_setting method not found in Admin class</p></div>';
    }
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Admin class not found</p></div>';
}

echo '</div>';

// Step 3: Test Security class methods
echo '<div class="step">';
echo '<h3>🛡️ Step 3: Test Security Class Methods</h3>';

if (class_exists('Farmfaucet_Security')) {
    // Test sanitize_api_key
    if (method_exists('Farmfaucet_Security', 'sanitize_api_key')) {
        try {
            $test_key = '  test_api_key_with_spaces  ';
            $sanitized = Farmfaucet_Security::sanitize_api_key($test_key);
            echo '<div class="success"><p>✅ sanitize_api_key working: "' . esc_html($test_key) . '" → "' . esc_html($sanitized) . '"</p></div>';
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ sanitize_api_key failed: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    } else {
        echo '<div class="error"><p>❌ sanitize_api_key method not found</p></div>';
    }
    
    // Test encrypt_api_key
    if (method_exists('Farmfaucet_Security', 'encrypt_api_key')) {
        try {
            $test_key = 'test_encryption_key';
            $encrypted = Farmfaucet_Security::encrypt_api_key($test_key);
            echo '<div class="success"><p>✅ encrypt_api_key working: "' . esc_html($test_key) . '" → "' . esc_html(substr($encrypted, 0, 30)) . '..."</p></div>';
            
            // Test decryption
            if (method_exists('Farmfaucet_Security', 'decrypt_api_key')) {
                $decrypted = Farmfaucet_Security::decrypt_api_key($encrypted);
                if ($decrypted === $test_key) {
                    echo '<div class="success"><p>✅ decrypt_api_key working correctly</p></div>';
                } else {
                    echo '<div class="error"><p>❌ decrypt_api_key failed: "' . esc_html($decrypted) . '" ≠ "' . esc_html($test_key) . '"</p></div>';
                }
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ encrypt_api_key failed: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    } else {
        echo '<div class="error"><p>❌ encrypt_api_key method not found</p></div>';
    }
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Security class not found</p></div>';
}

echo '</div>';

// Step 4: Test settings save simulation
echo '<div class="step">';
echo '<h3>💾 Step 4: Test Settings Save Simulation</h3>';

if (isset($_POST['test_settings_save'])) {
    echo '<div class="info"><p>🧪 Testing settings save...</p></div>';
    
    // Test each setting individually
    $test_settings = [
        'farmfaucet_captcha_type' => 'hcaptcha',
        'farmfaucet_hcaptcha_sitekey' => 'test_site_key',
        'farmfaucet_hcaptcha_secret' => 'test_secret_key',
        'farmfaucet_faucetpay_api' => 'test_faucetpay_api'
    ];
    
    foreach ($test_settings as $setting => $value) {
        try {
            $old_value = get_option($setting);
            $result = update_option($setting, $value);
            
            if ($result || get_option($setting) === $value) {
                echo '<div class="success"><p>✅ ' . esc_html($setting) . ' saved successfully</p></div>';
                
                // Restore old value
                if ($old_value !== false) {
                    update_option($setting, $old_value);
                } else {
                    delete_option($setting);
                }
            } else {
                echo '<div class="error"><p>❌ ' . esc_html($setting) . ' failed to save</p></div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ ' . esc_html($setting) . ' error: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
} else {
    echo '<form method="post" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 20px 0;">';
    echo '<h4>Test Settings Save</h4>';
    echo '<p>This will test saving each setting individually to identify which one is causing the issue.</p>';
    echo '<p><input type="submit" name="test_settings_save" value="Test Settings Save" class="btn"></p>';
    echo '</form>';
}

echo '</div>';

// Step 5: Check for memory/execution issues
echo '<div class="step">';
echo '<h3>⚡ Step 5: Check System Resources</h3>';

$memory_limit = ini_get('memory_limit');
$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);
$execution_time = ini_get('max_execution_time');

echo '<div class="info">';
echo '<p><strong>Memory Limit:</strong> ' . esc_html($memory_limit) . '</p>';
echo '<p><strong>Memory Usage:</strong> ' . esc_html(round($memory_usage / 1024 / 1024, 2)) . ' MB</p>';
echo '<p><strong>Peak Memory:</strong> ' . esc_html(round($memory_peak / 1024 / 1024, 2)) . ' MB</p>';
echo '<p><strong>Max Execution Time:</strong> ' . esc_html($execution_time) . ' seconds</p>';
echo '</div>';

// Check if memory usage is high
$memory_limit_bytes = wp_convert_hr_to_bytes($memory_limit);
if ($memory_usage > ($memory_limit_bytes * 0.8)) {
    echo '<div class="warning"><p>⚠️ High memory usage detected. This could cause save failures.</p></div>';
} else {
    echo '<div class="success"><p>✅ Memory usage is within normal limits</p></div>';
}

echo '</div>';

// Step 6: Check for conflicting plugins
echo '<div class="step">';
echo '<h3>🔌 Step 6: Check for Plugin Conflicts</h3>';

$active_plugins = get_option('active_plugins');
$potential_conflicts = [];

foreach ($active_plugins as $plugin) {
    // Check for plugins that might interfere with settings
    if (strpos($plugin, 'security') !== false || 
        strpos($plugin, 'firewall') !== false || 
        strpos($plugin, 'cache') !== false ||
        strpos($plugin, 'optimization') !== false) {
        $potential_conflicts[] = $plugin;
    }
}

if (!empty($potential_conflicts)) {
    echo '<div class="warning"><p>⚠️ Found plugins that might interfere with settings save:</p>';
    echo '<ul>';
    foreach ($potential_conflicts as $plugin) {
        echo '<li>' . esc_html($plugin) . '</li>';
    }
    echo '</ul>';
    echo '<p>Try temporarily deactivating these plugins to test if they\'re causing the issue.</p></div>';
} else {
    echo '<div class="success"><p>✅ No obvious conflicting plugins detected</p></div>';
}

echo '</div>';

?>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Test Settings Tab</a>
    <a href="diagnostic.php" class="btn" style="background: #ff9800;">🔍 Run Main Diagnostic</a>
</div>

<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #2196F3;">
    <h3 style="color: #1976d2; margin-top: 0;">🔍 Debugging Results Summary</h3>
    <p style="color: #1976d2;">Based on the tests above, the most likely causes of settings save failure are:</p>
    <ol style="color: #1976d2;">
        <li><strong>Encryption Callback Issues:</strong> If encryption tests fail, the callback is causing the error</li>
        <li><strong>Memory Exhaustion:</strong> If memory usage is high, increase PHP memory limit</li>
        <li><strong>Plugin Conflicts:</strong> Security or caching plugins may be blocking the save</li>
        <li><strong>Missing Dependencies:</strong> Required classes or methods not properly loaded</li>
    </ol>
    <p style="color: #1976d2; margin-bottom: 0;"><strong>Check the test results above to identify the specific issue!</strong></p>
</div>

</body>
</html>
