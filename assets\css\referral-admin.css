/**
 * Referral System Admin CSS
 */

/* Admin Cards */
.referral-section .farmfaucet-admin-card {
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.referral-section .card-header {
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.referral-section .card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
}

.referral-section .card-body {
    padding: 20px;
}

/* Form Styles */
.referral-section .form-table th {
    width: 200px;
}

.referral-section .form-table td {
    vertical-align: top;
}

.referral-section .form-table input[type="text"],
.referral-section .form-table input[type="number"],
.referral-section .form-table select {
    width: 100%;
    max-width: 400px;
}

.referral-section .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Referral Stats */
.referral-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;
    margin-bottom: 20px;
}

.referral-stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.referral-stat-card .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.referral-stat-card .stat-label {
    font-size: 14px;
    color: #666;
}

/* Referrals Table */
.referrals-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.referrals-table th,
.referrals-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.referrals-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.referrals-table tr:hover {
    background-color: #f9f9f9;
}

.referrals-table .row-actions {
    font-size: 12px;
    color: #666;
    visibility: hidden;
    margin-top: 5px;
}

.referrals-table tr:hover .row-actions {
    visibility: visible;
}

.referrals-table .row-actions a {
    text-decoration: none;
}

.referrals-table .row-actions .activate a {
    color: #4CAF50;
}

.referrals-table .row-actions .deactivate a {
    color: #f44336;
}

.referrals-table .status-active {
    color: #4CAF50;
    font-weight: bold;
}

.referrals-table .status-inactive {
    color: #f44336;
    font-weight: bold;
}

.referrals-table .user-column {
    display: flex;
    align-items: center;
}

.referrals-table .user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

/* Filter Controls */
.referral-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.referral-filters .filter-group {
    display: flex;
    align-items: center;
}

.referral-filters .filter-label {
    margin-right: 10px;
    font-weight: bold;
}

.referral-filters select,
.referral-filters input[type="text"] {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
}

.referral-filters .filter-button {
    padding: 8px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.referral-filters .filter-button:hover {
    background-color: #45a049;
}

.referral-filters .reset-button {
    padding: 8px 15px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.referral-filters .reset-button:hover {
    background-color: #d32f2f;
}

/* Date Picker */
.ui-datepicker {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.ui-datepicker-header {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 5px;
    margin-bottom: 5px;
}

.ui-datepicker-title {
    text-align: center;
    font-weight: bold;
}

.ui-datepicker-prev,
.ui-datepicker-next {
    cursor: pointer;
    padding: 5px;
}

.ui-datepicker-prev {
    float: left;
}

.ui-datepicker-next {
    float: right;
}

.ui-datepicker-calendar {
    width: 100%;
    border-collapse: collapse;
}

.ui-datepicker-calendar th,
.ui-datepicker-calendar td {
    text-align: center;
    padding: 5px;
}

.ui-datepicker-calendar .ui-state-default {
    display: block;
    padding: 5px;
    text-decoration: none;
    color: #333;
}

.ui-datepicker-calendar .ui-state-active {
    background-color: #4CAF50;
    color: white;
    border-radius: 4px;
}

/* Shortcode Display */
.shortcode-group {
    margin-bottom: 30px;
}

.shortcode-group h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
}

.shortcode-item {
    margin-bottom: 10px;
}

.shortcode-item code {
    display: inline-block;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    margin-right: 10px;
}

.shortcode-description {
    color: #666;
}

.shortcode-params {
    margin-top: 10px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.shortcode-params p {
    margin-top: 0;
}

.shortcode-params ul {
    margin-bottom: 10px;
}

.shortcode-params li {
    margin-bottom: 5px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .referral-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 782px) {
    .referral-section .form-table th {
        width: 100%;
        display: block;
    }
    
    .referral-section .form-table td {
        display: block;
        padding-left: 0;
    }
    
    .referral-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .referral-filters .filter-group {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .referral-filters select,
    .referral-filters input[type="text"] {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .referral-stats-grid {
        grid-template-columns: 1fr;
    }
}
