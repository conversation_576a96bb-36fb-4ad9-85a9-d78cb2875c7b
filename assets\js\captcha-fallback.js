/**
 * Captcha fallback script
 * This script provides a direct fallback for captcha loading if the normal initialization fails
 */
(function() {
    'use strict';

    // Function to initialize captchas directly
    function initCaptchasDirect() {
        console.log('Attempting direct captcha initialization');
        
        // Check if farmfaucet_vars is available
        if (!window.farmfaucet_vars) {
            console.error('farmfaucet_vars not available for direct initialization');
            return;
        }
        
        // Get captcha type and sitekey
        const captchaType = window.farmfaucet_vars.captcha_type || 'hcaptcha';
        const sitekey = captchaType === 'hcaptcha' 
            ? window.farmfaucet_vars.hcaptcha_sitekey 
            : window.farmfaucet_vars.recaptcha_sitekey;
            
        if (!sitekey) {
            console.error('No sitekey available for ' + captchaType);
            return;
        }
        
        console.log('Direct initialization for ' + captchaType + ' with sitekey available');
        
        // Find all captcha containers
        const containers = document.querySelectorAll('.farmfaucet-captcha-container');
        if (containers.length === 0) {
            console.log('No captcha containers found for direct initialization');
            return;
        }
        
        console.log('Found ' + containers.length + ' captcha containers for direct initialization');
        
        // For each container, try to initialize the captcha directly
        containers.forEach(function(container) {
            try {
                // Get the captcha element
                const captchaElement = captchaType === 'hcaptcha' 
                    ? container.querySelector('.h-captcha') 
                    : container.querySelector('.g-recaptcha');
                    
                if (!captchaElement) {
                    console.log('No captcha element found in container');
                    return;
                }
                
                const captchaId = captchaElement.id;
                if (!captchaId) {
                    console.log('Captcha element has no ID');
                    return;
                }
                
                console.log('Attempting direct initialization for ' + captchaId);
                
                // Remove loading message
                const loadingMsg = container.querySelector('.farmfaucet-captcha-loading');
                if (loadingMsg) {
                    loadingMsg.style.display = 'none';
                }
                
                // Make sure the captcha element is visible
                captchaElement.style.display = 'block';
                captchaElement.style.visibility = 'visible';
                captchaElement.style.opacity = '1';
                
                // Try to initialize the captcha directly
                if (captchaType === 'hcaptcha' && typeof hcaptcha !== 'undefined') {
                    console.log('Directly rendering hCaptcha for ' + captchaId);
                    try {
                        hcaptcha.render(captchaId, {
                            sitekey: sitekey,
                            theme: 'light',
                            size: 'normal',
                            callback: function(token) {
                                console.log('Direct hCaptcha callback triggered');
                                // Enable the claim button
                                const formContainer = captchaElement.closest('.farmfaucet-container');
                                const formClaimBtn = formContainer.querySelector('.farmfaucet-claim-btn');
                                if (formClaimBtn) {
                                    formClaimBtn.disabled = false;
                                }
                            }
                        });
                        console.log('Direct hCaptcha rendering successful for ' + captchaId);
                    } catch (e) {
                        console.error('Direct hCaptcha rendering failed:', e);
                    }
                } else if (captchaType === 'recaptcha' && typeof grecaptcha !== 'undefined') {
                    console.log('Directly rendering reCAPTCHA for ' + captchaId);
                    try {
                        grecaptcha.render(captchaId, {
                            'sitekey': sitekey,
                            'theme': 'light',
                            'size': 'normal',
                            'callback': function(token) {
                                console.log('Direct reCAPTCHA callback triggered');
                                // Enable the claim button
                                const formContainer = captchaElement.closest('.farmfaucet-container');
                                const formClaimBtn = formContainer.querySelector('.farmfaucet-claim-btn');
                                if (formClaimBtn) {
                                    formClaimBtn.disabled = false;
                                }
                            }
                        });
                        console.log('Direct reCAPTCHA rendering successful for ' + captchaId);
                    } catch (e) {
                        console.error('Direct reCAPTCHA rendering failed:', e);
                    }
                } else {
                    console.log('Captcha API not available for direct initialization');
                }
            } catch (e) {
                console.error('Error during direct captcha initialization:', e);
            }
        });
    }
    
    // Wait for the page to be fully loaded
    window.addEventListener('load', function() {
        // Wait a bit to ensure the normal initialization has had a chance to run
        setTimeout(function() {
            // Check if any captcha containers still have loading messages
            const loadingMsgs = document.querySelectorAll('.farmfaucet-captcha-loading');
            if (loadingMsgs.length > 0) {
                console.log('Found ' + loadingMsgs.length + ' loading messages, attempting direct initialization');
                initCaptchasDirect();
            } else {
                console.log('No loading messages found, skipping direct initialization');
            }
        }, 5000); // Wait 5 seconds before trying direct initialization
    });
})();
