/**
 * <PERSON><PERSON><PERSON> Styles
 */

/* Card styling */
.farmfaucet-admin-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.farmfaucet-admin-card:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.farmfaucet-admin-card .card-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 15px 20px;
    font-weight: 500;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.farmfaucet-admin-card .card-body {
    padding: 25px;
    position: relative;
}

/* Form styling */
.farmfaucet-form .form-group {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.farmfaucet-form .form-group:hover {
    transform: translateX(3px);
}

.farmfaucet-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.farmfaucet-form input[type="text"],
.farmfaucet-form input[type="number"],
.farmfaucet-form input[type="url"],
.farmfaucet-form input[type="password"],
.farmfaucet-form select,
.farmfaucet-form textarea {
    width: 100%;
    max-width: 500px;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.farmfaucet-form input[type="text"]:focus,
.farmfaucet-form input[type="number"]:focus,
.farmfaucet-form input[type="url"]:focus,
.farmfaucet-form input[type="password"]:focus,
.farmfaucet-form select:focus,
.farmfaucet-form textarea:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
    outline: none;
}

.farmfaucet-form .description {
    margin-top: 8px;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
    font-style: italic;
}

/* Switch toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin-right: 15px;
    vertical-align: middle;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e0e0e0;
    transition: .4s;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:focus + .slider {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

input:checked + .slider:before {
    transform: translateX(30px);
}

.slider.round {
    border-radius: 30px;
}

.slider.round:before {
    border-radius: 50%;
}

.status-label {
    vertical-align: middle;
    font-weight: 600;
    font-size: 15px;
    margin-left: 5px;
    transition: all 0.3s ease;
}

input:checked ~ .status-label {
    color: #4CAF50;
}

/* Shortcode container */
.shortcode-container {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 15px 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.shortcode-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.shortcode-container code {
    flex: 1;
    font-size: 15px;
    background: none;
    padding: 0;
    color: #333;
    font-family: monospace;
}

.copy-shortcode {
    margin-left: 15px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(46, 125, 50, 0.2);
}

.copy-shortcode:hover {
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

/* Shortcode parameters table */
.shortcode-params {
    margin: 20px 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    max-width: 800px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.shortcode-params th,
.shortcode-params td {
    padding: 15px 20px;
    text-align: left;
    border: none;
    border-bottom: 1px solid #e0e0e0;
}

.shortcode-params th {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    font-weight: 600;
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shortcode-params tr:last-child td {
    border-bottom: none;
}

.shortcode-params tr:nth-child(even) {
    background-color: #f9f9f9;
}

.shortcode-params tr:hover {
    background-color: #f0f0f0;
}

.shortcode-params td code {
    background-color: #f5f5f5;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Shortcode example */
.shortcode-example {
    margin-top: 25px;
    padding: 20px;
    background-color: #f9f9f9;
    border-left: 5px solid #4CAF50;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.shortcode-example:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.shortcode-example p {
    margin-top: 0;
    color: #333;
    font-size: 15px;
    line-height: 1.6;
}

.shortcode-example code {
    display: block;
    margin-top: 15px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 14px;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Form actions */
.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 15px;
}

.form-actions .button {
    padding: 10px 20px;
    height: auto;
    font-size: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.form-actions .button-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-color: #2E7D32;
    box-shadow: 0 2px 5px rgba(46, 125, 50, 0.2);
}

.form-actions .button-primary:hover {
    background: linear-gradient(135deg, #43A047, #2E7D32);
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 782px) {
    .farmfaucet-admin-card .card-header {
        padding: 12px 15px;
    }

    .farmfaucet-admin-card .card-header h3 {
        font-size: 18px;
    }

    .farmfaucet-admin-card .card-body {
        padding: 15px;
    }

    .farmfaucet-form .form-group {
        margin-bottom: 20px;
    }

    .farmfaucet-form input[type="text"],
    .farmfaucet-form input[type="number"],
    .farmfaucet-form input[type="url"],
    .farmfaucet-form input[type="password"],
    .farmfaucet-form select,
    .farmfaucet-form textarea {
        padding: 10px 12px;
        font-size: 14px;
    }

    .shortcode-params th,
    .shortcode-params td {
        padding: 10px 12px;
        font-size: 13px;
    }

    .shortcode-params th {
        font-size: 14px;
    }

    .shortcode-container {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 15px;
    }

    .copy-shortcode {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .button {
        width: 100%;
    }
}
