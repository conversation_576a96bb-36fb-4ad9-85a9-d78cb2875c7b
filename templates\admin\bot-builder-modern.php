<?php

/**
 * Telegram Bot Builder Modern UI Template
 *
 * @package FarmFaucet
 */

// Get all bots
global $wpdb;
$bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';
$bots = $wpdb->get_results("SELECT * FROM $bots_table ORDER BY id DESC", ARRAY_A);
?>

<div class="wrap farmfaucet-admin-wrap">
    <h1 class="wp-heading-inline">
        <span class="dashicons dashicons-format-chat"></span>
        <?php esc_html_e('Telegram Bot Builder', 'farmfaucet'); ?>
    </h1>

    <div class="farmfaucet-admin-section tg-bot-builder-section">
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Your Telegram Bots', 'farmfaucet'); ?></h3>
                <button type="button" class="button button-primary add-new-bot">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php esc_html_e('Add New Bot', 'farmfaucet'); ?>
                </button>
            </div>

            <div class="card-body">
                <?php if (empty($bots)): ?>
                    <div class="no-bots-message">
                        <p><?php esc_html_e('No bots found. Create your first Telegram bot to get started.', 'farmfaucet'); ?></p>
                        <button type="button" class="button button-primary add-new-bot">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php esc_html_e('Create Your First Bot', 'farmfaucet'); ?>
                        </button>
                    </div>
                <?php else: ?>
                    <div class="bot-list">
                        <?php foreach ($bots as $bot): ?>
                            <div class="bot-card" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                <div class="bot-card-header">
                                    <h4><?php echo esc_html($bot['bot_name']); ?></h4>
                                </div>

                                <div class="bot-card-body">
                                    <p class="bot-username">@<?php echo esc_html($bot['bot_username']); ?></p>
                                    <p class="bot-type">
                                        <?php echo esc_html($bot['bot_type'] === 'text' ? __('Text Bot', 'farmfaucet') : __('Chat Bot', 'farmfaucet')); ?>
                                    </p>
                                    <div class="bot-status <?php echo $bot['is_active'] ? 'active' : 'inactive'; ?>">
                                        <?php echo $bot['is_active'] ? __('Active', 'farmfaucet') : __('Inactive', 'farmfaucet'); ?>
                                    </div>
                                </div>

                                <div class="bot-card-footer">
                                    <div class="bot-actions">
                                        <button type="button" class="bot-action-btn edit" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-edit"></span> <?php esc_html_e('Edit', 'farmfaucet'); ?>
                                        </button>

                                        <button type="button" class="bot-action-btn view" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-admin-tools"></span> <?php esc_html_e('Commands', 'farmfaucet'); ?>
                                        </button>

                                        <button type="button" class="bot-action-btn delete" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-trash"></span> <?php esc_html_e('Delete', 'farmfaucet'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Flow Builder Card -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Flow Builder', 'farmfaucet'); ?></h3>
                <button type="button" class="button button-primary open-flow-builder" <?php echo empty($bots) ? 'disabled' : ''; ?>>
                    <span class="dashicons dashicons-networking"></span>
                    <?php esc_html_e('Open Flow Builder', 'farmfaucet'); ?>
                </button>
            </div>

            <div class="card-body">
                <p><?php esc_html_e('Create advanced conversation flows for your Telegram bots with our visual flow builder.', 'farmfaucet'); ?></p>

                <?php if (empty($bots)): ?>
                    <div class="flow-builder-notice">
                        <p><?php esc_html_e('You need to create a bot first before using the flow builder.', 'farmfaucet'); ?></p>
                    </div>
                <?php else: ?>
                    <div class="flow-builder-preview">
                        <img src="<?php echo esc_url(FARMFAUCET_URL . 'assets/images/flow-builder-preview.png'); ?>" alt="Flow Builder Preview">
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Login Form Settings Card -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Login Form Settings', 'farmfaucet'); ?></h3>
                <button type="button" class="button button-primary save-login-settings">
                    <span class="dashicons dashicons-saved"></span>
                    <?php esc_html_e('Save Settings', 'farmfaucet'); ?>
                </button>
            </div>

            <div class="card-body">
                <form id="login-form-settings">
                    <div class="form-field">
                        <label for="login-page-url"><?php esc_html_e('Login Page URL', 'farmfaucet'); ?></label>
                        <input type="url" id="login-page-url" name="login_page_url" value="<?php echo esc_attr(get_option('farmfaucet_login_page_url', '')); ?>" placeholder="https://example.com/login">
                        <p class="description"><?php esc_html_e('The URL of your login page', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field">
                        <label for="register-page-url"><?php esc_html_e('Register Page URL', 'farmfaucet'); ?></label>
                        <input type="url" id="register-page-url" name="register_page_url" value="<?php echo esc_attr(get_option('farmfaucet_register_page_url', '')); ?>" placeholder="https://example.com/register">
                        <p class="description"><?php esc_html_e('The URL of your registration page', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field">
                        <label for="forgot-password-url"><?php esc_html_e('Forgot Password URL', 'farmfaucet'); ?></label>
                        <input type="url" id="forgot-password-url" name="forgot_password_url" value="<?php echo esc_attr(get_option('farmfaucet_forgot_password_url', '')); ?>" placeholder="https://example.com/forgot-password">
                        <p class="description"><?php esc_html_e('The URL of your forgot password page', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field">
                        <label for="profile-page-url"><?php esc_html_e('Profile Page URL', 'farmfaucet'); ?></label>
                        <input type="url" id="profile-page-url" name="profile_page_url" value="<?php echo esc_attr(get_option('farmfaucet_profile_page_url', '')); ?>" placeholder="https://example.com/profile">
                        <p class="description"><?php esc_html_e('The URL of your user profile page', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field">
                        <label>
                            <input type="checkbox" id="enable-tg-login" name="enable_tg_login" value="1" <?php echo get_option('farmfaucet_enable_tg_login', '0') === '1' ? 'checked' : ''; ?>>
                            <?php esc_html_e('Enable Telegram Login', 'farmfaucet'); ?>
                        </label>
                        <p class="description"><?php esc_html_e('Allow users to login using Telegram', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field tg-login-field" style="<?php echo get_option('farmfaucet_enable_tg_login', '0') === '1' ? '' : 'display: none;'; ?>">
                        <label for="tg-login-bot"><?php esc_html_e('Telegram Login Bot', 'farmfaucet'); ?></label>
                        <select id="tg-login-bot" name="tg_login_bot">
                            <option value=""><?php esc_html_e('Select a bot', 'farmfaucet'); ?></option>
                            <?php foreach ($bots as $bot): ?>
                                <option value="<?php echo esc_attr($bot['id']); ?>" <?php echo get_option('farmfaucet_tg_login_bot', '') == $bot['id'] ? 'selected' : ''; ?>><?php echo esc_html($bot['bot_name']); ?> (@<?php echo esc_html($bot['bot_username']); ?>)</option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php esc_html_e('Select which bot to use for Telegram login verification. This bot will be used to send OTP codes to users.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-field tg-login-field" style="<?php echo get_option('farmfaucet_enable_tg_login', '0') === '1' ? '' : 'display: none;'; ?>">
                        <label for="tg-otp-expiration"><?php esc_html_e('OTP Code Expiration (minutes)', 'farmfaucet'); ?></label>
                        <input type="number" id="tg-otp-expiration" name="tg_otp_expiration" min="1" max="60" value="<?php echo esc_attr(get_option('farmfaucet_tg_otp_expiration', 5)); ?>">
                        <p class="description"><?php esc_html_e('How long OTP codes remain valid after being generated.', 'farmfaucet'); ?></p>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bot Preview Card -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Bot Preview', 'farmfaucet'); ?></h3>
                <select id="preview-bot-selector" <?php echo empty($bots) ? 'disabled' : ''; ?>>
                    <option value=""><?php esc_html_e('Select a bot to preview', 'farmfaucet'); ?></option>
                    <?php foreach ($bots as $bot): ?>
                        <option value="<?php echo esc_attr($bot['id']); ?>"><?php echo esc_html($bot['bot_name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="card-body">
                <div class="telegram-preview">
                    <div class="telegram-header">
                        <span class="bot-name-preview"><?php esc_html_e('Bot Preview', 'farmfaucet'); ?></span>
                    </div>

                    <div class="telegram-chat">
                        <div class="bot-message">
                            <div class="message-content"><?php esc_html_e('Hello! I am your bot. How can I help you today?', 'farmfaucet'); ?></div>
                            <div class="message-time">12:00</div>
                        </div>

                        <div class="user-message">
                            <div class="message-content"><?php esc_html_e('/start', 'farmfaucet'); ?></div>
                            <div class="message-time">12:01</div>
                        </div>

                        <div class="bot-message">
                            <div class="message-content"><?php esc_html_e('Welcome to my bot! Please select an option below:', 'farmfaucet'); ?></div>
                            <div class="message-time">12:01</div>

                            <div class="telegram-keyboard">
                                <button class="telegram-button"><?php esc_html_e('Check Balance', 'farmfaucet'); ?></button>
                                <button class="telegram-button"><?php esc_html_e('Earn Rewards', 'farmfaucet'); ?></button>
                                <button class="telegram-button"><?php esc_html_e('Help', 'farmfaucet'); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bot Form Modal -->
<div id="bot-form-modal" class="farmfaucet-modal" style="display: none;">
    <div class="farmfaucet-modal-content">
        <span class="farmfaucet-modal-close">&times;</span>
        <h2 id="bot-form-title"><?php esc_html_e('Add New Bot', 'farmfaucet'); ?></h2>

        <form id="bot-form">
            <input type="hidden" id="bot-id" name="bot_id" value="0">

            <div class="form-field">
                <label for="bot-name"><?php esc_html_e('Bot Name', 'farmfaucet'); ?></label>
                <input type="text" id="bot-name" name="bot_name" required>
                <p class="description"><?php esc_html_e('A friendly name for your bot (for your reference only)', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="bot-username"><?php esc_html_e('Bot Username', 'farmfaucet'); ?></label>
                <input type="text" id="bot-username" name="bot_username" required>
                <p class="description"><?php esc_html_e('The username of your bot on Telegram (without @)', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="bot-token"><?php esc_html_e('Bot Token', 'farmfaucet'); ?></label>
                <input type="text" id="bot-token" name="bot_token" required>
                <p class="description"><?php esc_html_e('The token provided by BotFather when you created your bot', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="bot-type"><?php esc_html_e('Bot Type', 'farmfaucet'); ?></label>
                <select id="bot-type" name="bot_type">
                    <option value="text"><?php esc_html_e('Text Bot', 'farmfaucet'); ?></option>
                    <option value="chat"><?php esc_html_e('Chat Bot', 'farmfaucet'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('Text bots respond to specific commands, Chat bots can have conversations', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label>
                    <input type="checkbox" id="is-active" name="is_active" value="1" checked>
                    <?php esc_html_e('Active', 'farmfaucet'); ?>
                </label>
                <p class="description"><?php esc_html_e('Inactive bots will not respond to messages', 'farmfaucet'); ?></p>
            </div>

            <div class="form-actions">
                <button type="submit" class="button button-primary"><?php esc_html_e('Save Bot', 'farmfaucet'); ?></button>
                <button type="button" class="button farmfaucet-modal-cancel"><?php esc_html_e('Cancel', 'farmfaucet'); ?></button>
            </div>
        </form>
    </div>
</div>