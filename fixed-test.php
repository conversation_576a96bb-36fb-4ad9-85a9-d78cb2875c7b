<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting test...\n";

// Define WordPress constants that might be needed
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}

// Mock WordPress functions that might be needed
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        echo "Mock add_action called: $hook\n";
        return true;
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        echo "Mock register_activation_hook called\n";
        return true;
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        echo "Mock register_deactivation_hook called\n";
        return true;
    }
}

if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugins_url')) {
    function plugins_url($path, $plugin) {
        return 'http://example.com/wp-content/plugins/' . basename(dirname($plugin)) . '/' . $path;
    }
}

if (!function_exists('plugin_basename')) {
    function plugin_basename($file) {
        return basename(dirname($file)) . '/' . basename($file);
    }
}

if (!function_exists('wp_enqueue_script')) {
    function wp_enqueue_script($handle, $src = '', $deps = array(), $ver = false, $in_footer = false) {
        echo "Mock wp_enqueue_script called: $handle\n";
        return true;
    }
}

if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src = '', $deps = array(), $ver = false, $media = 'all') {
        echo "Mock wp_enqueue_style called: $handle\n";
        return true;
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        echo "Mock get_option called: $option\n";
        return $default;
    }
}

if (!function_exists('add_option')) {
    function add_option($option, $value = '', $deprecated = '', $autoload = 'yes') {
        echo "Mock add_option called: $option\n";
        return true;
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value, $autoload = null) {
        echo "Mock update_option called: $option\n";
        return true;
    }
}

if (!function_exists('wp_localize_script')) {
    function wp_localize_script($handle, $object_name, $l10n) {
        echo "Mock wp_localize_script called: $handle\n";
        return true;
    }
}

if (!function_exists('admin_url')) {
    function admin_url($path = '') {
        return 'http://example.com/wp-admin/' . $path;
    }
}

if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action = -1) {
        return 'mock-nonce-' . $action;
    }
}

if (!function_exists('esc_html__')) {
    function esc_html__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_attr__')) {
    function esc_attr__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_attr_e')) {
    function esc_attr_e($text, $domain = 'default') {
        echo $text;
    }
}

if (!function_exists('esc_html_e')) {
    function esc_html_e($text, $domain = 'default') {
        echo $text;
    }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('esc_url')) {
    function esc_url($url, $protocols = null, $_context = 'display') {
        return $url;
    }
}

if (!function_exists('esc_url_raw')) {
    function esc_url_raw($url, $protocols = null) {
        return $url;
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim($str);
    }
}

if (!function_exists('sanitize_email')) {
    function sanitize_email($email) {
        return $email;
    }
}

if (!function_exists('is_email')) {
    function is_email($email) {
        return (bool) filter_var($email, FILTER_VALIDATE_EMAIL);
    }
}

if (!function_exists('wp_send_json_error')) {
    function wp_send_json_error($data = null, $status_code = null) {
        echo "Mock wp_send_json_error called\n";
        return true;
    }
}

if (!function_exists('wp_send_json_success')) {
    function wp_send_json_success($data = null, $status_code = null) {
        echo "Mock wp_send_json_success called\n";
        return true;
    }
}

if (!function_exists('wp_remote_post')) {
    function wp_remote_post($url, $args = array()) {
        echo "Mock wp_remote_post called: $url\n";
        return array('body' => '{"success":true}');
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return isset($response['body']) ? $response['body'] : '';
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('current_time')) {
    function current_time($type, $gmt = 0) {
        return $type === 'mysql' ? date('Y-m-d H:i:s') : time();
    }
}

if (!function_exists('wp_verify_nonce')) {
    function wp_verify_nonce($nonce, $action = -1) {
        return true;
    }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) {
        return false;
    }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration = 0) {
        return true;
    }
}

if (!function_exists('wp_kses_post')) {
    function wp_kses_post($string) {
        return $string;
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('load_plugin_textdomain')) {
    function load_plugin_textdomain($domain, $deprecated = false, $plugin_rel_path = false) {
        return true;
    }
}

if (!function_exists('has_shortcode')) {
    function has_shortcode($content, $shortcode) {
        return false;
    }
}

if (!function_exists('is_singular')) {
    function is_singular() {
        return false;
    }
}

if (!function_exists('dbDelta')) {
    function dbDelta($queries, $execute = true) {
        echo "Mock dbDelta called\n";
        return array();
    }
}

// Mock global $wpdb
global $wpdb;
$wpdb = new class {
    public $prefix = 'wp_';
    
    public function get_var($query) {
        echo "Mock wpdb->get_var called\n";
        return null;
    }
    
    public function prepare($query, $args) {
        echo "Mock wpdb->prepare called\n";
        return $query;
    }
    
    public function get_results($query, $output = OBJECT) {
        echo "Mock wpdb->get_results called\n";
        return array();
    }
    
    public function get_row($query, $output = OBJECT, $row = 0) {
        echo "Mock wpdb->get_row called\n";
        return null;
    }
    
    public function insert($table, $data, $format = null) {
        echo "Mock wpdb->insert called: $table\n";
        return 1;
    }
    
    public function update($table, $data, $where, $format = null, $where_format = null) {
        echo "Mock wpdb->update called: $table\n";
        return 1;
    }
    
    public function delete($table, $where, $where_format = null) {
        echo "Mock wpdb->delete called: $table\n";
        return 1;
    }
    
    public function query($query) {
        echo "Mock wpdb->query called\n";
        return true;
    }
};

try {
    echo "Loading farmfaucet.php...\n";
    require_once 'farmfaucet.php';
    echo "farmfaucet.php loaded successfully!\n";
    
    echo "Test completed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
