<?php
// Include WordPress core
require_once('wp-load.php');

// Get the database structure
global $wpdb;
$table_name = $wpdb->prefix . 'farmfaucet_faucets';

// Get all faucets
$faucets = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

// Output the faucets
echo "<h2>Faucets and Their Types</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>Faucet Type</th><th>Currency ID</th><th>Currency</th><th>Shortcode</th></tr>";

foreach ($faucets as $faucet) {
    echo "<tr>";
    echo "<td>" . esc_html($faucet['id']) . "</td>";
    echo "<td>" . esc_html($faucet['name']) . "</td>";
    echo "<td>" . esc_html(isset($faucet['faucet_type']) ? $faucet['faucet_type'] : 'stage') . "</td>";
    echo "<td>" . esc_html(isset($faucet['currency_id']) ? $faucet['currency_id'] : 'N/A') . "</td>";
    echo "<td>" . esc_html($faucet['currency']) . "</td>";
    echo "<td>" . esc_html($faucet['shortcode']) . "</td>";
    echo "</tr>";
}

echo "</table>";

// Check if the faucet_type column exists
$columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name", ARRAY_A);
$has_faucet_type = false;

echo "<h2>Table Columns</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>" . esc_html($column['Field']) . "</td>";
    echo "<td>" . esc_html($column['Type']) . "</td>";
    echo "<td>" . esc_html($column['Null']) . "</td>";
    echo "<td>" . esc_html($column['Key']) . "</td>";
    echo "<td>" . esc_html($column['Default']) . "</td>";
    echo "<td>" . esc_html($column['Extra']) . "</td>";
    echo "</tr>";
    
    if ($column['Field'] === 'faucet_type') {
        $has_faucet_type = true;
    }
}

echo "</table>";

if (!$has_faucet_type) {
    echo "<p>The faucet_type column does not exist in the table. This could be why faucet types are not working.</p>";
}
