/**
 * Farm Faucet Button Color Picker Fix
 *
 * Fixes issues with the color picker in the button edit form
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Button Color Picker Fix loaded');

        // Override the color swatch selection handler for button form
        $(document).off('click', '.color-swatch-option').on('click', '.color-swatch-option', function() {
            var $this = $(this);
            var value = $this.data('value');
            var name = $this.data('name');
            var color = $this.data('color');
            var container = $this.closest('.color-grid-container');
            
            // Update hidden input
            container.find('input[type="hidden"]').val(value);
            
            // Update visual elements
            container.find('.color-swatch-option').removeClass('selected');
            $this.addClass('selected');
            container.find('.color-name-display').text(name);
            container.find('.color-preview').css('background-color', color);
            
            // Hide color grid
            container.find('.color-grid').removeClass('active');
            
            // Trigger change event on the hidden input
            container.find('input[type="hidden"]').trigger('change');
        });
        
        // Override the toggle color grid handler for button form
        $(document).off('click', '.color-preview, .color-select-wrapper').on('click', '.color-preview, .color-select-wrapper', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close any other open color grids first
            $('.color-grid').not($(this).closest('.color-grid-container').find('.color-grid')).removeClass('active');
            
            // Toggle this color grid
            $(this).closest('.color-grid-container').find('.color-grid').toggleClass('active');
            
            return false; // Prevent default dropdown
        });
        
        // Close color grid when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').removeClass('active');
            }
        });

        // Fix for color grid display in button form
        function fixColorGrids() {
            $('.color-grid').each(function() {
                var $grid = $(this);
                if (!$grid.hasClass('grid-fixed')) {
                    $grid.css({
                        'display': 'none',
                        'grid-template-columns': 'repeat(8, 1fr)',
                        'gap': '8px',
                        'width': '320px',
                        'z-index': '9999'
                    });
                    
                    $grid.addClass('grid-fixed');
                    
                    // Make sure active grids display as grid
                    if ($grid.hasClass('active')) {
                        $grid.css('display', 'grid');
                    }
                }
            });
        }

        // Run the fix immediately and after dialog opens
        fixColorGrids();
        
        // Fix color grids when button dialog opens
        $(document).on('dialogopen', '#button-form-dialog', function() {
            setTimeout(fixColorGrids, 100);
        });

        // Fix color grid when editing a button
        $(document).on('click', '.edit-button', function() {
            setTimeout(fixColorGrids, 500);
        });

        // Fix color grid when adding a button
        $(document).on('click', '.add-button', function() {
            setTimeout(fixColorGrids, 500);
        });

        // Update color preview when button data is loaded
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.data && settings.data.indexOf('farmfaucet_get_button') !== -1) {
                setTimeout(function() {
                    var selectedColor = $('#button-color').val();
                    var colorOption = $('.color-swatch-option[data-value="' + selectedColor + '"]');
                    
                    if (colorOption.length) {
                        var container = colorOption.closest('.color-grid-container');
                        
                        // Update visual elements
                        container.find('.color-swatch-option').removeClass('selected');
                        colorOption.addClass('selected');
                        container.find('.color-name-display').text(colorOption.data('name'));
                        container.find('.color-preview').css('background-color', colorOption.data('color'));
                    }
                    
                    fixColorGrids();
                }, 200);
            }
        });
    });

})(jQuery);
