/**
 * Advertising System Frontend JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initAdvertisingSystem();
    });

    /**
     * Initialize Advertising System functionality
     */
    function initAdvertisingSystem() {
        // Vote button click handler
        $('.ad-vote-button').on('click', function() {
            const adId = $(this).data('ad-id');
            voteForAd(adId, 'up');
        });
    }

    /**
     * Vote for an advertisement
     * 
     * @param {number} adId Advertisement ID
     * @param {string} voteType Vote type (up, down)
     */
    function voteForAd(adId, voteType) {
        // Disable all vote buttons
        $('.ad-vote-button').prop('disabled', true);
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetAdvertising.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_vote_ad',
                nonce: farmfaucetAdvertising.nonce,
                ad_id: adId,
                vote_type: voteType
            },
            success: function(response) {
                if (response.success) {
                    // Update votes count
                    const $adCard = $('[data-ad-id="' + adId + '"]');
                    $adCard.find('.votes-count').text(response.data.new_votes);
                    
                    // Replace vote button with "Voted" text
                    $adCard.find('.ad-vote-button').replaceWith(
                        '<span class="ad-voted">' + farmfaucetAdvertising.i18n.voteSuccess + '</span>'
                    );
                    
                    // Show success message
                    alert(farmfaucetAdvertising.i18n.voteSuccess + ' ' + 
                          farmfaucetAdvertising.i18n.rewardReceived.replace('{amount}', response.data.reward_amount));
                    
                    // Reload page after a delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    alert(response.data.message || farmfaucetAdvertising.i18n.voteFailed);
                    
                    // Re-enable vote buttons
                    $('.ad-vote-button').prop('disabled', false);
                }
            },
            error: function() {
                // Show error message
                alert(farmfaucetAdvertising.i18n.error);
                
                // Re-enable vote buttons
                $('.ad-vote-button').prop('disabled', false);
            }
        });
    }

})(jQuery);
