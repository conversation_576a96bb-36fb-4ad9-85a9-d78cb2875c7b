/**
 * Create Bot Button Fix
 * 
 * This is a very simple, targeted fix for the "Create New Bot" button in the Telegram Bot Builder.
 * It only focuses on making the button work properly and styling it correctly.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Create Bot Button Fix loaded');
        initCreateBotButton();
    });

    /**
     * Initialize Create Bot Button
     */
    function initCreateBotButton() {
        console.log('Initializing Create Bot Button Fix');
        
        // Style the Create New Bot button
        styleCreateBotButton();
        
        // Add event handler for the Create New Bot button
        $('#create-new-bot').off('click').on('click', function() {
            console.log('Create New Bot button clicked');
            openBotDialog();
        });
    }

    /**
     * Style the Create New Bot button
     */
    function styleCreateBotButton() {
        console.log('Styling Create New Bot button');
        
        // Make sure the button is green
        $('#create-new-bot').css({
            'background-color': '#4CAF50',
            'border-color': '#4CAF50',
            'color': 'white',
            'text-align': 'center',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });
        
        // Add hover effect
        $('#create-new-bot').hover(
            function() {
                $(this).css({
                    'background-color': '#3d8b40',
                    'border-color': '#3d8b40'
                });
            },
            function() {
                $(this).css({
                    'background-color': '#4CAF50',
                    'border-color': '#4CAF50'
                });
            }
        );
    }

    /**
     * Open the bot dialog
     */
    function openBotDialog() {
        console.log('Opening bot dialog');
        
        // Reset the form
        $('#bot-form')[0].reset();
        
        // Show the dialog
        $('#bot-dialog').dialog({
            title: 'Create New Bot',
            modal: true,
            width: 500,
            buttons: {
                'Create Bot': function() {
                    saveBot();
                },
                'Cancel': function() {
                    $(this).dialog('close');
                }
            }
        });
    }

    /**
     * Save the bot
     */
    function saveBot() {
        console.log('Saving bot');
        
        // Get form data
        const botToken = $('#bot-token').val();
        
        // Validate form data
        if (!botToken) {
            alert('Please enter a bot token');
            return;
        }
        
        // Create a loading indicator
        const $dialog = $('#bot-dialog');
        const $buttons = $dialog.dialog('widget').find('.ui-dialog-buttonpane button');
        $buttons.prop('disabled', true);
        $buttons.first().text('Creating...');
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_create_bot',
                nonce: farmfaucetTgBotBuilder.nonce,
                bot_token: botToken
            },
            success: function(response) {
                if (response.success) {
                    alert('Bot created successfully!');
                    window.location.reload();
                } else {
                    alert('Failed to create bot: ' + (response.data ? response.data.message : 'Unknown error'));
                    $buttons.prop('disabled', false);
                    $buttons.first().text('Create Bot');
                }
            },
            error: function() {
                alert('An error occurred while creating the bot');
                $buttons.prop('disabled', false);
                $buttons.first().text('Create Bot');
            }
        });
    }
})(jQuery);
