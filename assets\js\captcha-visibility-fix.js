/**
 * Captcha visibility fix
 * This script ensures captchas are visible by applying direct CSS fixes
 */
(function($) {
    'use strict';
    
    // Function to apply visibility fixes
    function applyCaptchaVisibilityFixes() {
        console.log('Applying captcha visibility fixes');
        
        // Make captcha containers visible
        $('.farmfaucet-captcha-container').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '100px'
        });
        
        // Make captcha wrappers visible
        $('.h-captcha-wrapper, .g-recaptcha-wrapper').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '78px',
            'width': '100%',
            'max-width': '300px',
            'margin': '0 auto',
            'justify-content': 'center',
            'align-items': 'center'
        });
        
        // Make captcha elements visible
        $('.h-captcha, .g-recaptcha, .farmfaucet-captcha').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '78px'
        });
        
        // Make captcha iframes visible
        $('iframe[src*="hcaptcha.com"], iframe[src*="google.com/recaptcha"]').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '70px',
            'margin': '0 auto'
        });
        
        // Remove any stale loading messages
        setTimeout(function() {
            if ($('.h-captcha iframe').length > 0 || $('.g-recaptcha iframe').length > 0) {
                $('.farmfaucet-captcha-loading').remove();
            }
        }, 2000);
    }
    
    // Apply fixes when the document is ready
    $(document).ready(function() {
        setTimeout(applyCaptchaVisibilityFixes, 2000);
    });
    
    // Apply fixes when the window is fully loaded
    $(window).on('load', function() {
        setTimeout(applyCaptchaVisibilityFixes, 2000);
        
        // Apply fixes again after a longer delay
        setTimeout(applyCaptchaVisibilityFixes, 5000);
    });
    
})(jQuery);
