/**
 * Farm Faucet - Dashboard Tab Improved Styling
 * Ensures consistent green styling for the dashboard tab with the rest of the plugin
 */

/* Dashboard tab container */
.farmfaucet-admin-container {
    margin: 20px 0;
}

.farmfaucet-admin-section {
    margin-bottom: 30px;
}

/* Dashboard card styling */
.farmfaucet-admin-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    overflow: hidden;
}

.farmfaucet-admin-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
    background: #f8f9fa;
    border-left: 4px solid #4CAF50;
    text-align: center; /* Center the header text */
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #4CAF50; /* Green color for consistency */
}

.farmfaucet-admin-card .card-body {
    padding: 20px;
    text-align: center; /* Center the card content */
}

/* Fix duplicate text by ensuring only one copy is visible */
.farmfaucet-admin-card .card-body p {
    margin-bottom: 15px;
}

.farmfaucet-admin-card .card-body h4,
.farmfaucet-admin-card .card-body h5 {
    margin-top: 20px;
    margin-bottom: 10px;
}

/* Ensure shortcode containers have proper spacing and alignment */
.shortcode-container {
    margin-bottom: 25px;
    text-align: left; /* Left-align the shortcode content for readability */
}

.shortcode-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

/* Section headings */
.farmfaucet-admin-section h2 {
    color: #4CAF50;
    font-size: 24px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-group .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Range slider */
.range-slider-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.range-slider {
    flex: 1;
    margin-right: 10px;
    accent-color: #4CAF50; /* Green color for slider */
    height: 6px;
}

#avatar-size-value {
    min-width: 50px;
    font-weight: bold;
    color: #4CAF50;
}

.avatar-preview {
    margin: 15px 0;
    display: flex;
    align-items: center;
}

.avatar-preview-label {
    margin-right: 15px;
    font-weight: 600;
}

/* Shortcodes styling */
.shortcode-container {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
}

.shortcode-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.shortcode-header h4 {
    margin: 0;
    color: #4CAF50;
    font-size: 16px;
}

.shortcode-info code {
    display: block;
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border-left: 3px solid #4CAF50;
    font-family: monospace;
    word-break: break-all;
}

.shortcode-params h5,
.shortcode-example h5 {
    margin: 0 0 8px 0;
    color: #4CAF50;
    font-size: 14px;
}

.shortcode-params ul {
    margin: 0;
    padding-left: 20px;
}

.shortcode-params li {
    margin-bottom: 5px;
}

.shortcode-example code {
    display: block;
    background: #f0f0f0;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #4CAF50;
    font-family: monospace;
    word-break: break-all;
}

/* Shortcodes grid */
.shortcodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.shortcode-item {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0;
}

/* Tutorial section */
.dashboard-tutorial {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.tutorial-steps {
    margin-left: 20px;
    margin-bottom: 15px;
}

.tutorial-steps li {
    margin-bottom: 10px;
}

.tutorial-note {
    background-color: #fffbea;
    border-left: 4px solid #f0c674;
    padding: 10px 15px;
    margin-top: 15px;
    border-radius: 4px;
}

/* Layout examples */
.layout-examples {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.layout-example {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0;
}

.layout-example h4 {
    margin-top: 0;
    color: #4CAF50;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.layout-code {
    background: #f0f0f0;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.layout-code pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 13px;
}

/* Buttons */
.copy-shortcode,
.copy-layout {
    background: #4CAF50 !important;
    border-color: #43A047 !important;
    color: white !important;
}

.copy-shortcode:hover,
.copy-layout:hover {
    background: #43A047 !important;
    border-color: #388E3C !important;
}

.submit .button-primary {
    background: #4CAF50 !important;
    border-color: #43A047 !important;
    color: white !important;
}

.submit .button-primary:hover {
    background: #43A047 !important;
    border-color: #388E3C !important;
}

/* Dashboard tab in the tab navigation */
.nav-tab-wrapper .nav-tab[href*="tab=dashboard"].nav-tab-active {
    border-top: 3px solid #4CAF50;
    color: #4CAF50;
}

/* Make all active tabs consistent */
.nav-tab-wrapper .nav-tab.nav-tab-active {
    border-top: 3px solid #4CAF50;
    color: #4CAF50;
}

/* Responsive styles */
@media (max-width: 782px) {
    .shortcodes-grid {
        grid-template-columns: 1fr;
    }

    .shortcode-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .shortcode-header button {
        margin-top: 10px;
    }
}
