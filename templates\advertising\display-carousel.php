<?php
/**
 * Template for displaying advertisements in carousel layout
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get current user ID
$current_user_id = get_current_user_id();

// Get max votes per day
$max_votes = get_option('farmfaucet_ad_max_votes', 3);

// Count user votes today
$user_votes_today = is_user_logged_in() ? $this->count_user_votes_today($current_user_id) : 0;

// Check if user can vote
$can_vote = is_user_logged_in() && $user_votes_today < $max_votes;

// Get vote reward
$vote_reward = get_option('farmfaucet_ad_vote_reward', 0.1);

// Generate unique ID for this carousel
$carousel_id = 'farmfaucet-ads-carousel-' . uniqid();
?>

<div class="farmfaucet-ads-container">
    <?php if ($can_vote) : ?>
        <div class="farmfaucet-ads-info">
            <p><?php printf(esc_html__('You have %1$d/%2$d votes remaining today. Earn %3$s for each vote!', 'farmfaucet'), 
                $max_votes - $user_votes_today, 
                $max_votes,
                '<strong>' . esc_html($vote_reward) . '</strong>'
            ); ?></p>
        </div>
    <?php elseif (is_user_logged_in()) : ?>
        <div class="farmfaucet-ads-info">
            <p><?php esc_html_e('You have used all your votes for today. Come back tomorrow!', 'farmfaucet'); ?></p>
        </div>
    <?php else : ?>
        <div class="farmfaucet-ads-info">
            <p><?php esc_html_e('Log in to vote on advertisements and earn rewards!', 'farmfaucet'); ?></p>
        </div>
    <?php endif; ?>

    <div id="<?php echo esc_attr($carousel_id); ?>" class="farmfaucet-ads-carousel">
        <div class="carousel-container">
            <div class="carousel-track">
                <?php foreach ($ads as $ad) : 
                    // Check if user has voted for this ad
                    $has_voted = is_user_logged_in() ? $this->has_user_voted($ad['id'], $current_user_id) : false;
                ?>
                    <div class="carousel-slide">
                        <div class="farmfaucet-ad-card" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                            <?php if ($atts['show_image'] && !empty($ad['image_url'])) : ?>
                                <div class="ad-image">
                                    <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow">
                                        <img src="<?php echo esc_url($ad['image_url']); ?>" alt="<?php echo esc_attr($ad['title']); ?>">
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="ad-content">
                                <h3 class="ad-title">
                                    <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow">
                                        <?php echo esc_html($ad['title']); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($atts['show_description']) : 
                                    $description = $ad['description'];
                                    $max_length = intval($atts['description_length']);
                                    
                                    if (strlen($description) > $max_length) {
                                        $description = substr($description, 0, $max_length) . '...';
                                    }
                                ?>
                                    <div class="ad-description">
                                        <?php echo wp_kses_post($description); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="ad-meta">
                                    <?php if ($atts['show_votes']) : ?>
                                        <div class="ad-votes">
                                            <span class="votes-count"><?php echo esc_html($ad['votes']); ?></span>
                                            <span class="votes-label"><?php echo _n('vote', 'votes', $ad['votes'], 'farmfaucet'); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="ad-actions">
                                        <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow" class="ad-visit-button">
                                            <?php esc_html_e('Visit', 'farmfaucet'); ?>
                                        </a>
                                        
                                        <?php if ($can_vote && !$has_voted) : ?>
                                            <button class="ad-vote-button" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                                                <?php esc_html_e('Vote', 'farmfaucet'); ?>
                                            </button>
                                        <?php elseif ($has_voted) : ?>
                                            <span class="ad-voted"><?php esc_html_e('Voted', 'farmfaucet'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <button class="carousel-control carousel-prev" aria-label="<?php esc_attr_e('Previous', 'farmfaucet'); ?>">
            <span class="dashicons dashicons-arrow-left-alt2"></span>
        </button>
        <button class="carousel-control carousel-next" aria-label="<?php esc_attr_e('Next', 'farmfaucet'); ?>">
            <span class="dashicons dashicons-arrow-right-alt2"></span>
        </button>
        
        <div class="carousel-indicators">
            <?php for ($i = 0; $i < count($ads); $i++) : ?>
                <button class="carousel-indicator <?php echo $i === 0 ? 'active' : ''; ?>" 
                        data-slide="<?php echo esc_attr($i); ?>" 
                        aria-label="<?php printf(esc_attr__('Go to slide %d', 'farmfaucet'), $i + 1); ?>">
                </button>
            <?php endfor; ?>
        </div>
    </div>
</div>

<script>
    (function() {
        // Initialize carousel when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            initCarousel('<?php echo esc_js($carousel_id); ?>');
        });
        
        function initCarousel(carouselId) {
            const carousel = document.getElementById(carouselId);
            if (!carousel) return;
            
            const track = carousel.querySelector('.carousel-track');
            const slides = carousel.querySelectorAll('.carousel-slide');
            const indicators = carousel.querySelectorAll('.carousel-indicator');
            const prevButton = carousel.querySelector('.carousel-prev');
            const nextButton = carousel.querySelector('.carousel-next');
            
            let currentSlide = 0;
            const slideCount = slides.length;
            
            // Set initial position
            updateCarousel();
            
            // Event listeners
            prevButton.addEventListener('click', () => {
                currentSlide = (currentSlide - 1 + slideCount) % slideCount;
                updateCarousel();
            });
            
            nextButton.addEventListener('click', () => {
                currentSlide = (currentSlide + 1) % slideCount;
                updateCarousel();
            });
            
            // Add click event to indicators
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    currentSlide = index;
                    updateCarousel();
                });
            });
            
            // Auto-advance carousel
            let interval = setInterval(() => {
                currentSlide = (currentSlide + 1) % slideCount;
                updateCarousel();
            }, 5000);
            
            // Pause auto-advance on hover
            carousel.addEventListener('mouseenter', () => {
                clearInterval(interval);
            });
            
            carousel.addEventListener('mouseleave', () => {
                interval = setInterval(() => {
                    currentSlide = (currentSlide + 1) % slideCount;
                    updateCarousel();
                }, 5000);
            });
            
            // Update carousel position and active indicator
            function updateCarousel() {
                // Update track position
                track.style.transform = `translateX(-${currentSlide * 100}%)`;
                
                // Update indicators
                indicators.forEach((indicator, index) => {
                    if (index === currentSlide) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }
                });
            }
        }
    })();
</script>
