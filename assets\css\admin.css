/* ============================================= */
/* FRONT-END STYLES */
/* ============================================= */

/* Main container styling */
.farmfaucet-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background: #f8fff8;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid #4CAF50;
    position: relative;
}

/* Header styling */
.farmfaucet-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.status-header {
    color: #4CAF50;
    margin: 0 0 1rem;
    font-size: 1.5rem;
}

.post-captcha { display: block; }
.pre-captcha { display: none; }

.reward-notice {
    background: #e8f5e9;
    padding: 0.8rem;
    border-radius: 8px;
    margin: 1rem 0;
    font-weight: 500;
    color: #2e7d32;
}

/* Form elements */
.farmfaucet-input {
    width: 100%;
    padding: 12px 15px;
    margin: 8px 0;
    border: 2px solid #9c27b0;
    border-radius: 25px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.farmfaucet-input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
}

.hcaptcha-container {
    margin: 1.5rem 0;
    min-height: 78px;
}

/* Claim button */
.farmfaucet-claim-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.farmfaucet-claim-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Cooldown styling */
.farmfaucet-cooldown {
    text-align: center;
    padding: 2rem;
}

.cooldown-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.cooldown-timer {
    font-size: 1.8rem;
    font-weight: bold;
    color: #9c27b0;
    margin: 1rem 0;
}

/* Notification popup */
.farmfaucet-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    background: #4CAF50;
    color: white;
    display: none;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@media (max-width: 600px) {
    .farmfaucet-container {
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* Loading spinner */
.farmfaucet-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: farmfaucet-spin 1s ease-in-out infinite;
    vertical-align: middle;
    margin-right: 8px;
}

@keyframes farmfaucet-spin {
    to { transform: rotate(360deg); }
}

/* Success popup */
.farmfaucet-success-popup {
    position: fixed;
    top: -100%;
    left: 50%;
    transform: translateX(-50%);
    background: #4CAF50;
    color: white;
    padding: 20px 35px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    z-index: 9999;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.farmfaucet-success-popup.visible {
    top: 20px;
    opacity: 1;
}

.popup-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.popup-icon {
    font-size: 32px;
    animation: popupBounce 0.8s ease;
}

@keyframes popupBounce {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.popup-message {
    font-size: 16px;
    font-weight: 500;
}

/* Amount Input Field */
input[name="farmfaucet_amount"] {
    min-width: 200px;
    width: auto;
    padding-right: 40px;
    transition: width 0.3s ease;
}

/* Cooldown Message */
.cooldown-message {
    text-align: center;
    color: #666;
    padding: 15px;
    background: #fff3cd;
    border-radius: 8px;
    margin: 15px 0;
}

.cooldown-message .cooldown-timer {
    font-size: 1.4em;
    color: #d35400;
    margin-top: 10px;
    font-weight: bold;
}

/* Error Message Styling */
.farmfaucet-notification.error {
    background: #e74c3c;
}


/* ============================================= */
/* ADMIN PANEL STYLES */
/* ============================================= */

/* Checkbox and radio button styling */
.form-field input[type="checkbox"],
.form-field input[type="radio"] {
    width: auto !important;
    height: auto !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
    position: relative !important;
    top: -1px !important;
    display: inline-block !important;
}

.form-field label {
    display: inline-block !important;
    margin-bottom: 5px !important;
    width: auto !important;
}

.radio-label {
    display: inline-block !important;
    margin-right: 15px !important;
    width: auto !important;
}

.radio-label input[type="radio"] {
    width: auto !important;
    height: auto !important;
    margin-right: 5px !important;
    vertical-align: middle !important;
    position: relative !important;
    top: -1px !important;
}

/* Background styling options */
.bg-style-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.faucet-bg-options {
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 15px;
}

.color-picker {
    width: 60px;
    height: 30px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    vertical-align: middle;
}

.gradient-color-start,
.gradient-color-end {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.gradient-color-start label,
.gradient-color-end label {
    min-width: 100px;
}

/* Admin header section */
.farmfaucet-admin .admin-header {
    background: #fff;
    padding: 20px;
    margin: -20px -20px 20px;
    border-bottom: 1px solid #ccd0d4;
}

/* Version and author information */
.farmfaucet-admin .version-author {
    margin-top: 10px;
    color: #666;
    font-size: 13px;
}

.farmfaucet-admin .version-author span {
    margin-right: 15px;
}

/* Shortcode notice */
.shortcode-notice {
    background: #f8f9fa;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 8px 8px 0;
}

.shortcode-notice h3 {
    margin-top: 0;
    color: #2e7d32;
    font-size: 18px;
}

.shortcode-group {
    margin-bottom: 20px;
}

.shortcode-group h4 {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e8f5e9;
    color: #4CAF50;
}

.shortcode-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.shortcode-item code {
    background: #e8f5e9;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    margin-right: 10px;
    min-width: 200px;
    display: inline-block;
}

.shortcode-description {
    color: #666;
    font-size: 13px;
}

/* ============================================= */
/* UPDATED TRANSACTION LOG STYLES */
/* ============================================= */

/* Main transaction log container */
.transaction-log-section {
    max-width: 100%;
    margin: 20px auto;
    background: #fff;
    border-radius: 12px;
    border: 2px solid #4CAF50; /* Added green border */
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
    padding: 20px;
}

/* Flex container for tabs and content */
.transaction-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

/* Left sidebar tabs */
.transaction-subtabs {
    flex: 0 0 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Individual tab styling */
.transaction-subtab {
    background: #f6f7f7;
    border: 1px solid #ccd0d4;
    border-radius: 8px; /* Rounded corners */
    padding: 12px 15px;
    cursor: pointer;
    text-align: left;
    transition: all 0.2s ease;
}

/* Active tab state */
.transaction-subtab.active {
    background: #4CAF50; /* Green background */
    color: white;
    border-color: #45a049;
}

/* Error tab specific styling */
.transaction-subtab.error-tab.active {
    background: #dc3545;
    border-color: #dc3545;
}

/* Main content area */
.transaction-chat-box {
    flex: 1;
    background: #f8f9fa;
    border: 2px solid #4CAF50; /* Green border */
    border-radius: 8px;
    padding: 15px;
    max-height: 600px;
    overflow-y: auto;
    min-height: 300px;
    display: block;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    position: relative;
}

/* Add clearfix to ensure container properly contains floated elements */
.transaction-chat-box::after {
    content: "";
    display: table;
    clear: both;
}

/* ============================================= */
/* TRANSACTION MESSAGE STYLES */
/* ============================================= */

/* Base message styling */
.transaction-message {
    background: #e3f2fd; /* Light blue default */
    border-radius: 15px;
    padding: 12px 18px;
    margin-bottom: 12px;
    position: relative;
    max-width: 80%;
    transition: all 0.2s ease;
    border: 1px solid rgba(0,0,0,0.05);
    box-sizing: border-box;
    width: auto;
    display: inline-block;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Error message variation */
.transaction-message.error {
    background: #ffebee; /* Light red */
    border-color: #ffcdd2;
    margin-left: auto; /* Right align errors */
    margin-right: 0;
    float: right;
    clear: both;
}

/* Success message variation */
.transaction-message.success {
    background: #e8f5e9; /* Light green */
    border-color: #c8e6c9;
    margin-right: auto; /* Left align successes */
    margin-left: 0;
    float: left;
    clear: both;
}

/* Message header section */
.transaction-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

/* Main content area */
.txn-content {
    flex: 1;
    color: #2c3e50;
}

/* Metadata styling */
.txn-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 0.85em;
}

/* Timestamp styling */
.txn-timestamp {
    color: #7f8c8d;
    font-style: italic;
}

/* Status indicator */
.txn-status {
    font-size: 1.2em;
    margin-left: 10px;
}

/* ============================================= */
/* ERROR DETAILS STYLING */
/* ============================================= */

.error-details {
    display: none;
    margin-top: 10px;
    padding: 10px;
    background: rgba(255,255,255,0.9);
    border-radius: 8px;
    font-size: 0.85em;
    border: 1px solid #ffcdd2;
}

.error-details.active {
    display: block;
}

.error-code strong {
    color: #e74c3c;
}

/* ============================================= */
/* FAUCET MANAGEMENT STYLES */
/* ============================================= */

/* Faucets section container */
.faucets-section {
    max-width: 100%;
    margin: 20px auto;
    background: #fff;
    border-radius: 12px;
    border: 2px solid #4CAF50;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* Toggle Switch Styling */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 10px;
    vertical-align: middle;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:focus + .slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

.faucet-status-toggle {
    display: flex;
    align-items: center;
}

.faucet-status-label {
    font-weight: normal;
    margin-left: 5px;
}

/* Faucets header with create button */
.faucets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* Create new faucet button */
#create-new-faucet {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #4CAF50;
    color: white;
    border-color: #45a049;
}

#create-new-faucet:hover {
    background: #45a049;
}

/* Faucets tabs container */
.faucets-tabs-container {
    margin-top: 20px;
}

/* Faucets tabs navigation */
.faucets-tabs-nav {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
    overflow-x: auto;
    padding-bottom: 0;
}

.faucets-tabs-nav li {
    margin-bottom: -1px;
    margin-right: 5px;
}

.faucets-tabs-nav a {
    display: block;
    padding: 10px 15px;
    background: #f6f7f7;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: all 0.2s ease;
}

.faucets-tabs-nav .ui-tabs-active a {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #4CAF50;
}

/* Faucet tab content */
.faucet-tab-content {
    background: #fff;
    border-radius: 0 0 8px 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

/* Faucet details */
.faucet-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

/* Captcha selection styles */
.captcha-selection {
    margin-bottom: 15px;
}

.captcha-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 8px;
}

.captcha-option {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.captcha-option:hover:not(.disabled) {
    border-color: #4CAF50;
    background: #f1f8e9;
}

.captcha-option.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f5f5f5;
}

.captcha-option label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.captcha-option.disabled label {
    cursor: not-allowed;
}

/* Style for checkboxes and radio buttons */
input[type="checkbox"],
input[type="radio"] {
    margin: 0;
    width: 16px;
    height: 16px;
    border: 1px solid #7e8993;
    background: #fff;
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    transition: .05s border-color ease-in-out;
    appearance: auto;
    -webkit-appearance: auto;
    -moz-appearance: auto;
}

input[type="checkbox"] {
    border-radius: 3px;
}

input[type="radio"] {
    border-radius: 50%;
}

/* Square input style for specific elements */
.square-input {
    border-radius: 3px !important;
}

.captcha-not-configured {
    font-size: 0.8em;
    color: #dc3545;
    margin-left: 10px;
    font-style: italic;
}

/* ============================================= */
/* BUTTON MANAGEMENT STYLES */
/* ============================================= */

/* Faucet buttons section */
.faucet-buttons-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px dashed #ccc;
}

.faucet-buttons-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #4CAF50;
    display: flex;
    align-items: center;
}

.faucet-buttons-section h4:before {
    content: '\f11c'; /* Button icon */
    font-family: dashicons;
    margin-right: 8px;
    font-size: 1.2em;
}

/* No buttons message */
.no-buttons-message {
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
    color: #666;
    text-align: center;
    font-style: italic;
}

/* Button list */
.button-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

/* Button item */
.button-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    width: calc(50% - 10px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.button-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: #4CAF50;
}

.button-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Button header */
.button-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.button-header h5 {
    margin: 0;
    font-size: 1.1em;
    color: #4CAF50;
    font-weight: 600;
}

/* Button preview */
.button-preview {
    text-align: center;
    min-width: 150px;
    display: inline-block;
    margin: 0 auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    font-size: 14px;
}

.button-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Button details */
.button-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    background: #f9f9f9;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #eee;
}

.button-detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 8px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.button-detail-item strong {
    font-size: 0.85em;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.button-detail-item span {
    font-weight: 500;
    color: #333;
}

/* Color swatch for button color */
.button-detail-item.color-detail span {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.color-swatch {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.1);
    vertical-align: middle;
    margin-right: 5px;
}

/* Button lock info */
.button-lock-info {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    background: #fff8e1;
    padding: 12px;
    border-radius: 6px;
    margin: 10px 0;
    border-left: 3px solid #ffc107;
}

.lock-icon {
    color: #ffa000;
    font-size: 1.2em;
}

.lock-details {
    flex: 1;
}

.lock-details strong {
    display: block;
    margin-bottom: 5px;
    color: #f57c00;
}

.required-faucets,
.reset-time {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Button shortcode */
.button-shortcode {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.9em;
    border-left: 3px solid #4CAF50;
}

.button-shortcode code {
    background: #e8f5e9;
    padding: 3px 6px;
    border-radius: 4px;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    font-family: monospace;
}

/* Button actions */
.button-actions {
    display: flex;
    gap: 5px;
}

/* Add button */
.add-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #2271b1;
    color: white;
    border-color: #135e96;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.add-button:hover {
    background: #135e96;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.add-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 113, 177, 0.5);
}

/* Button form dialog */
#button-form-dialog {
    padding: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.ui-dialog-titlebar {
    background: #4CAF50 !important;
    color: white !important;
    border: none !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    padding: 15px 20px !important;
    font-size: 16px !important;
}

.ui-dialog-titlebar-close {
    background: white !important;
    border-radius: 50% !important;
    color: #4CAF50 !important;
}

.ui-dialog .ui-dialog-buttonpane {
    border-top: 1px solid #eee !important;
    background: #f9f9f9 !important;
    padding: 15px 20px !important;
    margin-top: 0 !important;
    display: flex !important;
    justify-content: flex-end !important;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
}

.ui-dialog .ui-dialog-buttonpane button {
    background: #4CAF50 !important;
    color: white !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 8px !important;
    min-width: 80px !important;
    cursor: pointer !important;
}

.ui-dialog .ui-dialog-buttonpane button:hover {
    background: #45a049 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15) !important;
}

.ui-dialog .ui-dialog-buttonpane button.ui-button-cancel {
    background: #f5f5f5 !important;
    color: #333 !important;
}

.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: none !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

#button-form, #faucet-form {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

#button-form-dialog .form-field {
    margin-bottom: 20px;
    position: relative;
}

#button-form-dialog label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

#button-form-dialog input[type="text"],
#button-form-dialog input[type="url"],
#button-form-dialog select {
    width: 100%;
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

#button-form-dialog input[type="text"]:focus,
#button-form-dialog input[type="url"]:focus,
#button-form-dialog select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    background-color: #fff;
}

#button-form-dialog .description {
    font-size: 0.85em;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

/* Color selector with visual swatches */
.color-select-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: #fff;
    cursor: pointer;
}

.color-select-wrapper:hover {
    border-color: #999;
}

.color-name-display {
    flex-grow: 1;
    font-size: 14px;
    color: #333;
}

.color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-left: 10px;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    cursor: pointer;
}

/* Hide the dropdown arrow from the color select */
.color-select-wrapper select {
    position: absolute;
    opacity: 0;
    width: 1px;
    height: 1px;
    overflow: hidden;
    clip: rect(0 0 0 0);
}

/* Button lock options styling */
.button-lock-options {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #eee;
    margin-top: 10px;
}

.faucet-details h4 {
    margin-top: 0;
    color: #4CAF50;
    font-size: 1.2em;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Faucet info items */
.faucet-info {
    /* Flexbox fallback for browsers that don't support Grid */
    display: flex;
    flex-wrap: wrap;
    margin: 0 -7.5px 20px;
    /* Grid layout for modern browsers */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

/* Flexbox fallback item styling */
@supports not (display: grid) {
    .faucet-info-item {
        flex: 0 0 calc(50% - 15px);
        margin: 0 7.5px 15px;
    }
}

.faucet-info-item {
    background: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.faucet-info-item strong {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-size: 0.9em;
}

.faucet-info-item span,
.faucet-info-item code {
    font-size: 1.1em;
    color: #2c3e50;
}

.faucet-info-item code {
    background: #e8f5e9;
    padding: 3px 6px;
    border-radius: 4px;
    border: 1px solid #c8e6c9;
}

/* Faucet actions */
.faucet-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.edit-faucet {
    display: flex;
    align-items: center;
    gap: 5px;
}

.delete-faucet {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #dc3545;
}

/* Faucet form dialog */
#faucet-form-dialog, #button-form-dialog {
    padding: 0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

#faucet-form {
    padding: 20px;
}

.form-field {
    margin-bottom: 15px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-field input,
.form-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-field .description {
    font-size: 0.85em;
    color: #666;
    margin-top: 5px;
}

/* Transaction tabs styling */
.transaction-tabs-container {
    margin-top: 20px;
}

.transaction-tabs-nav {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
    overflow-x: auto;
    padding-bottom: 0;
}

.transaction-tabs-nav li {
    margin-bottom: -1px;
    margin-right: 5px;
}

.transaction-tabs-nav a {
    display: block;
    padding: 10px 15px;
    background: #f6f7f7;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: all 0.2s ease;
}

.transaction-tabs-nav .ui-tabs-active a {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #4CAF50;
}

/* ============================================= */
/* MISCELLANEOUS ADMIN STYLES */
/* ============================================= */

/* Empty state messages */
.no-transactions,
.no-faucets {
    text-align: center;
    color: #95a5a6;
    padding: 20px;
    font-style: italic;
}

.no-faucets {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    margin: 20px 0;
}

/* Admin menu icon fix */
#adminmenu .toplevel_page_farmfaucet div.wp-menu-image img {
    width: 20px;
    height: 20px;
    padding: 5px 0 0;
    opacity: 0.8;
}

/* Preserved existing admin styles */
.farmfaucet-admin .wrap,
.transaction-log-section,
.transaction-chat-box,
.transaction-message,
.error-details,
.faucets-section {
    border-radius: 12px;
}

.nav-tab-wrapper .nav-tab {
    border-radius: 0;
}

/* Color grid */
.color-grid {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    z-index: 9999; /* Higher z-index to ensure it appears above other elements */
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 5px;
    grid-template-columns: repeat(8, 1fr); /* 8 columns for horizontal layout */
    gap: 8px;
    width: 320px;
}

.color-grid.active,
.color-grid:hover,
.color-grid.show {
    display: grid; /* Use grid display for horizontal layout */
}

.color-swatch-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    border: 1px solid rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    margin: 0 auto;
}

.color-swatch-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.color-swatch-option.selected {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #4CAF50;
}

/* Additional colors */
.color-grid-container {
    position: relative;
}

/* Faucet checkbox list */
.faucet-checkbox-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background: #f9f9f9;
    margin-bottom: 10px;
}

/* Mobile responsiveness for admin panel */
@media (max-width: 782px) {
    .faucets-tabs-nav {
        flex-wrap: wrap;
    }

    .faucets-tabs-nav li {
        flex: 0 0 auto;
        margin-bottom: 5px;
    }

    .button-list {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .faucet-info {
        flex-direction: column;
    }

    .faucet-info-item {
        margin-bottom: 8px;
    }

    .button-detail-item {
        margin-bottom: 8px;
    }

    .ui-dialog {
        width: 95% !important;
        max-width: 500px;
    }

    .ui-dialog-buttonset {
        flex-direction: column;
        align-items: stretch;
    }

    .ui-dialog-buttonset button {
        margin: 5px 0 !important;
    }

    .transaction-container {
        flex-direction: column;
    }

    .transaction-subtabs {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 5px;
    }

    .transaction-subtab {
        flex: 1 1 auto;
        min-width: 100px;
        text-align: center;
    }
}

.faucet-checkbox-item {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.faucet-checkbox-item:hover {
    background-color: #f0f0f0;
}

.faucet-checkbox-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
    margin: 0;
}

.faucet-checkbox-item input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
    border: 1px solid #7e8993;
    border-radius: 3px;
    background: #fff;
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
}

.faucet-checkbox-item input[type="checkbox"]:checked + .faucet-name {
    font-weight: bold;
    color: #4CAF50;
}

.faucet-checkbox-item:has(input[type="checkbox"]:checked) {
    background-color: #e8f5e9;
    border-left: 3px solid #4CAF50;
}

/* Fix for stretched checkboxes in menus */
.square-input {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    min-width: 16px !important;
    min-height: 16px !important;
    max-width: 16px !important;
    max-height: 16px !important;
    display: inline-block !important;
    vertical-align: middle !important;
    position: static !important;
    float: none !important;
    left: auto !important;
    right: auto !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    border-radius: 3px !important;
}

/* Radio button styling fix */
input[type="radio"].milestone-type-radio,
input[type="radio"].milestone-bar-style-radio,
input[type="radio"].milestone-card-bg-style-radio,
input[type="radio"].milestone-display-style-radio,
input[type="radio"].captcha-radio {
    width: auto !important;
    height: auto !important;
    margin-right: 8px !important;
    min-width: 16px !important;
    min-height: 16px !important;
    max-width: 16px !important;
    max-height: 16px !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Radio label styling */
.radio-label {
    display: inline-flex !important;
    align-items: center !important;
    margin-right: 15px !important;
    cursor: pointer !important;
    font-weight: normal !important;
}

/* Captcha options styling fix */
.captcha-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
}

.captcha-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #eee;
}

.captcha-option label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
    font-weight: normal;
}

.captcha-not-configured {
    margin-left: auto;
    font-size: 0.85em;
    color: #dc3545;
    font-style: italic;
}

/* ============================================= */
/* MILESTONE DISPLAY STYLES */
/* ============================================= */

/* Milestone display style options */
.milestone-display-style-container {
    margin-top: 15px;
    margin-bottom: 15px;
}

.milestone-display-style-options {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.milestone-display-style-option {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    width: 200px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.milestone-display-style-option:hover {
    border-color: #aaa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.milestone-display-style-option.selected {
    border-color: #4CAF50;
    background-color: #f0fff0;
}

.milestone-display-style-option .style-preview {
    height: 120px;
    background: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.milestone-display-style-option .style-name {
    font-weight: bold;
    text-align: center;
    color: #333;
}

.milestone-display-style-option .style-description {
    font-size: 0.85em;
    color: #666;
    text-align: center;
    margin-top: 5px;
}

.milestone-display-style-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

/* Card view preview */
.card-view-preview {
    width: 90%;
    height: 90%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.card-view-preview .preview-title {
    font-weight: bold;
    font-size: 0.9em;
    margin-bottom: 5px;
    text-align: left;
}

.card-view-preview .preview-progress {
    height: 15px;
    background: #eee;
    border-radius: 10px;
    margin: 5px 0;
    position: relative;
    overflow: hidden;
}

.card-view-preview .preview-progress-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 70%;
    background: #4CAF50;
    border-radius: 10px;
}

.card-view-preview .preview-tasks {
    margin-top: 5px;
    border-top: 1px solid #eee;
    padding-top: 5px;
}

.card-view-preview .preview-task {
    display: flex;
    align-items: center;
    font-size: 0.7em;
    margin-bottom: 3px;
}

.card-view-preview .preview-task-icon {
    margin-right: 3px;
    color: #4CAF50;
}

/* Compact view preview */
.compact-view-preview {
    width: 90%;
    height: 90%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.compact-view-preview .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.compact-view-preview .preview-title {
    font-weight: bold;
    font-size: 0.8em;
}

.compact-view-preview .preview-percentage {
    font-size: 0.8em;
    font-weight: bold;
    color: #4CAF50;
}

.compact-view-preview .preview-progress {
    height: 20px;
    background: #eee;
    border-radius: 10px;
    margin: 5px 0;
    position: relative;
    overflow: hidden;
}

.compact-view-preview .preview-progress-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 70%;
    background: #4CAF50;
    border-radius: 10px;
}

.compact-view-preview .preview-progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 0.7em;
    font-weight: bold;
    color: white;
    line-height: 20px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.compact-view-preview .preview-summary {
    font-size: 0.7em;
    color: #666;
    margin-top: 5px;
    text-align: center;
}

.compact-view-preview .preview-dropdown {
    margin-top: 5px;
    font-size: 0.7em;
    color: #4CAF50;
    text-align: center;
    cursor: pointer;
}

/* ============================================= */
/* CAPTCHA SETTINGS STYLES */
/* ============================================= */

/* Captcha settings container */
.captcha-settings-container {
    margin-top: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Settings labels */
.settings-label {
    display: block;
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 14px;
    color: #23282d;
}

/* Settings headings */
.settings-heading {
    font-weight: 700;
    font-size: 18px;
    color: #23282d;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

/* Captcha type selector */
.captcha-type-selector {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

/* Captcha configuration section */
.captcha-config-section {
    margin-top: 25px;
}

/* Captcha config item */
.captcha-config-item {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
    transition: box-shadow 0.3s ease;
}

.captcha-config-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Captcha header */
.captcha-header {
    background-color: #f9f9f9;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    transition: background-color 0.3s ease;
}

.captcha-header:hover {
    background-color: #f0f0f0;
}

.captcha-header.active {
    background-color: #e9f7ef;
    border-bottom-color: #4CAF50;
}

.captcha-name {
    font-weight: 700;
    color: #23282d;
    font-size: 15px;
}

.captcha-status {
    font-size: 0.85em;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 500;
}

.captcha-status.configured {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.captcha-status.not-configured {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

/* Captcha fields */
.captcha-fields {
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

.captcha-field-group {
    margin-bottom: 20px;
}

.captcha-field-group:last-child {
    margin-bottom: 0;
}

.captcha-field-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #23282d;
}

/* Input fields */
.captcha-field-group input[type="text"],
.captcha-field-group input[type="password"] {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 100%;
    max-width: 400px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
}

.captcha-field-group input[type="text"]:focus,
.captcha-field-group input[type="password"]:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Description text */
.captcha-field-group .description {
    margin-top: 6px;
    color: #646970;
    font-style: italic;
}

/* ============================================= */
/* MILESTONE PAGE SPECIFIC STYLES */
/* ============================================= */

/* Milestone type selector */
.milestone-type-selector {
    margin-bottom: 15px;
}

.milestone-type-selector .radio-label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.milestone-type-selector input[type="radio"] {
    margin-right: 8px;
}

/* Milestone pages container */
.milestone-pages-container {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.milestone-pages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.milestone-pages-header h4 {
    margin: 0;
}

/* Milestone pages tabs */
.milestone-pages-tabs-container {
    margin-top: 20px;
}

.milestone-pages-tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 0;
    padding-left: 0;
    list-style: none;
}

.milestone-pages-tabs li {
    margin-bottom: -1px;
    margin-right: 5px;
}

.milestone-pages-tabs a {
    display: block;
    padding: 8px 12px;
    background: #f6f7f7;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    text-decoration: none;
    color: #555;
    font-weight: 500;
}

.milestone-pages-tabs .ui-tabs-active a {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #4CAF50;
}

/* Milestone page tab content */
.milestone-page-tab {
    background: #fff;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 5px 5px;
    padding: 15px;
}

.milestone-page-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.milestone-page-form .form-field:last-child {
    grid-column: 1 / -1;
    text-align: right;
}

.milestone-page-form input[type="url"],
.milestone-page-form input[type="text"] {
    width: 100%;
}

.remove-milestone-page {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.remove-milestone-page:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.add-milestone-page {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.add-milestone-page:hover {
    background-color: #45a049;
    border-color: #45a049;
}

/* ============================================= */
/* TOGGLE SWITCH STYLES */
/* ============================================= */

/* The switch - the box around the slider */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

/* Hide default HTML checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* The slider */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:focus + .slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

.faucet-status-toggle {
    display: flex;
    align-items: center;
}

.faucet-status-toggle span {
    display: flex;
    align-items: center;
    gap: 10px;
}

.faucet-status-label {
    font-weight: 500;
}

/* Milestone Appearance Styles */
.milestone-appearance-container {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.milestone-bar-style {
    margin-bottom: 15px;
}

.milestone-color-picker-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.milestone-color-option {
    flex: 1;
    min-width: 200px;
}

.milestone-color-picker {
    width: 100%;
    height: 40px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.milestone-gradient-colors {
    display: flex;
    gap: 15px;
}

.gradient-color-start,
.gradient-color-end {
    flex: 1;
}

.milestone-bar-preview {
    margin-top: 20px;
}

.milestone-preview-container {
    width: 100%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}