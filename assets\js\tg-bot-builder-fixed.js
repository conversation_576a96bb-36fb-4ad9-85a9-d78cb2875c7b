/**
 * Farm Faucet - Telegram Bot Builder
 * This script handles the Telegram bot builder functionality
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Telegram Bot Builder Fixed loaded');
        initTgBotBuilder();
        styleCreateBotButton();
    });

    /**
     * Initialize Telegram Bot Builder
     */
    function initTgBotBuilder() {
        // Remove any existing event handlers to prevent duplicates
        $('#create-new-bot').off('click');
        $('.edit-bot').off('click');
        $('.delete-bot').off('click');
        $('.bot-enabled-toggle').off('change');
        $('#test-token').off('click');
        $('#bot-dialog').off('dialogclose');
        
        // Add our clean event handlers
        $('#create-new-bot').on('click', function() {
            openBotDialog();
        });
        
        $('.edit-bot').on('click', function() {
            const botId = $(this).data('id');
            editBot(botId);
        });
        
        $('.delete-bot').on('click', function() {
            const botId = $(this).data('id');
            deleteBot(botId);
        });
        
        $('.bot-enabled-toggle').on('change', function() {
            const botId = $(this).data('bot-id');
            const isActive = $(this).is(':checked') ? 1 : 0;
            toggleBotStatus(botId, isActive);
        });
        
        $('#test-token').on('click', function() {
            testBotToken();
        });
        
        // Initialize jQuery UI dialog
        if ($.fn.dialog) {
            $('#bot-dialog').dialog({
                autoOpen: false,
                modal: true,
                width: 500,
                buttons: {
                    Save: function() {
                        saveBot();
                    },
                    Cancel: function() {
                        $(this).dialog('close');
                    }
                },
                close: function() {
                    resetBotForm();
                }
            });
        }
    }
    
    /**
     * Style the Create New Bot button to be green and center the text
     */
    function styleCreateBotButton() {
        $('#create-new-bot').css({
            'background-color': '#4CAF50',
            'border-color': '#4CAF50',
            'color': 'white',
            'text-align': 'center',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });
        
        // Also style the dialog buttons
        $('.ui-dialog-buttonset button:first-child').css({
            'background-color': '#4CAF50',
            'border-color': '#4CAF50',
            'color': 'white'
        });
    }
    
    /**
     * Open bot dialog
     */
    function openBotDialog() {
        resetBotForm();
        $('#bot-dialog').dialog('open');
    }
    
    /**
     * Reset bot form
     */
    function resetBotForm() {
        $('#bot-form')[0].reset();
        $('#bot-id').val('0');
        $('#token-status').empty();
    }
    
    /**
     * Edit bot
     */
    function editBot(botId) {
        // Get bot data
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_bot',
                nonce: farmfaucetTgBotBuilder.nonce,
                bot_id: botId
            },
            success: function(response) {
                if (response.success) {
                    const bot = response.data.bot;
                    
                    // Populate form
                    $('#bot-id').val(bot.id);
                    $('#bot-name').val(bot.bot_name);
                    $('#bot-token').val(bot.bot_token);
                    $('#bot-username').val(bot.bot_username);
                    $('#bot-type').val(bot.bot_type);
                    $('#webhook-url').val(bot.webhook_url);
                    $('#bot-active').prop('checked', bot.is_active == 1);
                    
                    // Open dialog
                    $('#bot-dialog').dialog('open');
                } else {
                    alert(response.data.message || 'Failed to get bot data');
                }
            },
            error: function() {
                alert('Failed to get bot data');
            }
        });
    }
    
    /**
     * Save bot
     */
    function saveBot() {
        // Validate form
        const $form = $('#bot-form');
        
        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }
        
        // Get form data
        const botId = $('#bot-id').val();
        const botName = $('#bot-name').val();
        const botToken = $('#bot-token').val();
        const botUsername = $('#bot-username').val();
        const botType = $('#bot-type').val();
        const webhookUrl = $('#webhook-url').val();
        const isActive = $('#bot-active').is(':checked') ? 1 : 0;
        
        // Validate required fields
        if (!botName) {
            alert('Please enter a bot name');
            return;
        }
        
        if (!botToken) {
            alert('Please enter a bot token');
            return;
        }
        
        if (!botUsername) {
            alert('Please enter a bot username');
            return;
        }
        
        // Disable dialog buttons
        const $dialog = $('#bot-dialog');
        $dialog.parent().find('.ui-dialog-buttonpane button').prop('disabled', true);
        
        // Prepare form data
        const formData = {
            action: 'farmfaucet_save_bot',
            nonce: farmfaucetTgBotBuilder.nonce,
            bot_id: botId,
            bot_name: botName,
            bot_token: botToken,
            bot_username: botUsername.replace('@', ''), // Remove @ if present
            bot_type: botType,
            webhook_url: webhookUrl,
            is_active: isActive
        };
        
        console.log('Sending bot data:', formData);
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Bot save response:', response);
                
                // Re-enable dialog buttons
                $dialog.parent().find('.ui-dialog-buttonpane button').prop('disabled', false);
                
                if (response.success) {
                    alert(response.data.message || (botId == '0' ? 'Bot created successfully' : 'Bot updated successfully'));
                    
                    // Close dialog
                    $dialog.dialog('close');
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    alert(response.data.message || (botId == '0' ? 'Failed to create bot' : 'Failed to update bot'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                
                // Re-enable dialog buttons
                $dialog.parent().find('.ui-dialog-buttonpane button').prop('disabled', false);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                alert('Error: ' + errorMessage);
            }
        });
    }
    
    /**
     * Test bot token
     */
    function testBotToken() {
        const token = $('#bot-token').val();
        
        if (!token) {
            alert('Please enter a bot token');
            return;
        }
        
        const $testButton = $('#test-token');
        const originalButtonText = $testButton.text();
        $testButton.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_test_bot_token',
                nonce: farmfaucetTgBotBuilder.nonce,
                token: token
            },
            success: function(response) {
                console.log('Test token response:', response);
                $testButton.prop('disabled', false).text(originalButtonText);
                
                if (response.success) {
                    // Populate username field
                    $('#bot-username').val(response.data.username);
                    
                    // Show success message
                    $('#token-status').html('<div class="token-valid">Bot token is valid. Bot username: ' + response.data.username + '</div>');
                } else {
                    // Show error message
                    $('#token-status').html('<div class="token-invalid">Invalid bot token: ' + (response.data.message || 'Unknown error') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $testButton.prop('disabled', false).text(originalButtonText);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                $('#token-status').html('<div class="token-invalid">Error testing bot token: ' + errorMessage + '</div>');
            }
        });
    }
    
    /**
     * Delete bot
     */
    function deleteBot(botId) {
        if (!confirm('Are you sure you want to delete this bot?')) {
            return;
        }
        
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_bot',
                nonce: farmfaucetTgBotBuilder.nonce,
                bot_id: botId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || 'Bot deleted successfully');
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    alert(response.data.message || 'Failed to delete bot');
                }
            },
            error: function() {
                alert('Failed to delete bot');
            }
        });
    }
    
    /**
     * Toggle bot status
     */
    function toggleBotStatus(botId, isActive) {
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_toggle_bot_status',
                nonce: farmfaucetTgBotBuilder.nonce,
                bot_id: botId,
                is_active: isActive
            },
            success: function(response) {
                if (response.success) {
                    // Update status label
                    const $statusLabel = $('.bot-status-toggle[data-bot-id="' + botId + '"] .bot-status-label');
                    $statusLabel.text(isActive ? 'Enabled' : 'Disabled');
                } else {
                    alert(response.data.message || 'Failed to update bot status');
                    
                    // Revert toggle
                    $('.bot-enabled-toggle[data-bot-id="' + botId + '"]').prop('checked', !isActive);
                }
            },
            error: function() {
                alert('Failed to update bot status');
                
                // Revert toggle
                $('.bot-enabled-toggle[data-bot-id="' + botId + '"]').prop('checked', !isActive);
            }
        });
    }
})(jQuery);
