/**
 * Farm Faucet Form Transparency Consolidated
 *
 * This CSS file consolidates all form transparency fixes into a single file.
 */

/* Base transparency for faucet container */
.farmfaucet-container.transparent-bg {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Transparent background based on faucet settings */
.farmfaucet-container[data-form-transparent="1"] {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-container[data-form-transparent="1"] .farmfaucet-header {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Make all inner elements transparent when form transparency is enabled */
.farmfaucet-container[data-form-transparent="1"] .form-group,
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-captcha-container,
.farmfaucet-container[data-form-transparent="1"] .reward-notice,
.farmfaucet-container[data-form-transparent="1"] .balance-notice,
.farmfaucet-container[data-form-transparent="1"] .min-withdrawal-notice,
.farmfaucet-container[data-form-transparent="1"] .min-conversion-notice,
.farmfaucet-container[data-form-transparent="1"] .conversion-info,
.farmfaucet-container[data-form-transparent="1"] .conversion-container,
.farmfaucet-container[data-form-transparent="1"] .conversion-result,
.farmfaucet-container[data-form-transparent="1"] .conversion-rate {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Ensure input fields remain visible with transparent backgrounds */
.farmfaucet-container[data-form-transparent="1"] input,
.farmfaucet-container[data-form-transparent="1"] select,
.farmfaucet-container[data-form-transparent="1"] textarea {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Keep buttons visible */
.farmfaucet-container[data-form-transparent="1"] button {
    background-color: var(--button-color, #4CAF50) !important;
    border: 1px solid var(--border-color, #4CAF50) !important;
}

/* Transparent form elements */
.farmfaucet-container.transparent-bg .farmfaucet-form,
.farmfaucet-container.transparent-bg .farmfaucet-header,
.farmfaucet-container.transparent-bg .farmfaucet-footer,
.farmfaucet-container.transparent-bg .farmfaucet-form-inner {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Form transparency when form_transparent is enabled */
.farmfaucet-container .farmfaucet-form.form-transparent,
.farmfaucet-form.form-transparent,
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

.farmfaucet-container .farmfaucet-form.form-transparent .farmfaucet-form-inner,
.farmfaucet-form.form-transparent .farmfaucet-form-inner,
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form .farmfaucet-form-inner {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Transparent form inputs */
.farmfaucet-container.transparent-bg input[type="text"],
.farmfaucet-container.transparent-bg input[type="number"],
.farmfaucet-container.transparent-bg input[type="email"],
.farmfaucet-container.transparent-bg input[type="password"],
.farmfaucet-container.transparent-bg input[type="tel"],
.farmfaucet-container.transparent-bg input[type="url"],
.farmfaucet-container.transparent-bg select,
.farmfaucet-container.transparent-bg textarea,
.farmfaucet-form.form-transparent input[type="text"],
.farmfaucet-form.form-transparent input[type="number"],
.farmfaucet-form.form-transparent input[type="email"],
.farmfaucet-form.form-transparent input[type="password"],
.farmfaucet-form.form-transparent input[type="tel"],
.farmfaucet-form.form-transparent input[type="url"],
.farmfaucet-form.form-transparent select,
.farmfaucet-form.form-transparent textarea {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: var(--input-label-color, inherit) !important;
}

/* Enhanced dropdown transparency */
.farmfaucet-container[data-form-transparent="1"] select,
.farmfaucet-form.form-transparent select {
    background-color: var(--form-bg-color, rgba(255, 255, 255, 0.1)) !important;
    border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2)) !important;
    color: var(--input-label-color, #333333) !important;
}

/* Dropdown options transparency */
.farmfaucet-container[data-form-transparent="1"] select option,
.farmfaucet-form.form-transparent select option {
    background-color: var(--form-bg-color, rgba(255, 255, 255, 0.9)) !important;
    color: var(--input-label-color, #333333) !important;
}

/* Placeholder text color */
.farmfaucet-container[data-form-transparent="1"] input::placeholder,
.farmfaucet-form.form-transparent input::placeholder {
    color: var(--input-placeholder-color, rgba(255, 255, 255, 0.7)) !important;
}

/* Specific fixes for dummy faucet */
.dummy-faucet .farmfaucet-form,
.dummy-faucet .farmfaucet-form-inner,
.farmfaucet-container.dummy-faucet .farmfaucet-form,
.farmfaucet-container.dummy-faucet .farmfaucet-form-inner {
    background-color: transparent !important;
}

.dummy-faucet.transparent-bg,
.farmfaucet-container.dummy-faucet.transparent-bg {
    background-color: transparent !important;
}

/* Specific fixes for withdrawal faucet */
.withdrawal-faucet .farmfaucet-form,
.withdrawal-faucet .farmfaucet-form-inner,
.farmfaucet-container.withdrawal-faucet .farmfaucet-form,
.farmfaucet-container.withdrawal-faucet .farmfaucet-form-inner {
    background-color: transparent !important;
}

.withdrawal-faucet.transparent-bg,
.farmfaucet-container.withdrawal-faucet.transparent-bg {
    background-color: transparent !important;
}

/* Specific fixes for conversion faucet */
.conversion-faucet .farmfaucet-form,
.conversion-faucet .farmfaucet-form-inner,
.farmfaucet-container.conversion-faucet .farmfaucet-form,
.farmfaucet-container.conversion-faucet .farmfaucet-form-inner {
    background-color: transparent !important;
}

.conversion-faucet.transparent-bg,
.farmfaucet-container.conversion-faucet.transparent-bg {
    background-color: transparent !important;
}

/* Force transparency for all faucet types when form_transparent is enabled */
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form,
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form-inner {
    background-color: transparent !important;
    background: transparent !important;
}

/* Fix for captcha container */
.farmfaucet-captcha-container {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1 !important;
    justify-content: center !important;
    margin: 15px 0 !important;
}

.farmfaucet-captcha-container iframe {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1 !important;
    min-height: 78px !important;
}

/* Fix for h-captcha */
.h-captcha {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 78px !important;
}

/* Fix for g-recaptcha */
.g-recaptcha {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 78px !important;
}

/* Fix for cf-turnstile */
.cf-turnstile {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 78px !important;
}

/* Fix for currency maker modal */
#farmfaucet-currency-modal {
    z-index: 99999 !important;
}

#farmfaucet-currency-modal .farmfaucet-modal-content {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

/* Fix for color picker in currency maker */
.wp-picker-container {
    z-index: 999999 !important;
}

.wp-picker-container .iris-picker {
    position: absolute !important;
    z-index: 999999 !important;
    display: block !important;
}

.wp-picker-container .wp-color-result {
    cursor: pointer !important;
}

/* Fix for conversion faucet */
.conversion-faucet .conversion-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.conversion-faucet .conversion-from,
.conversion-faucet .conversion-to {
    flex: 1;
    min-width: 200px;
}

.conversion-faucet .conversion-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
}

.conversion-faucet .target-currency-wrapper {
    position: relative;
}

.conversion-faucet .target-currency-wrapper select {
    width: 100%;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    text-align-last: center;
    -moz-text-align-last: center;
    -ms-text-align-last: center;
}

.conversion-faucet .currency-label {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.conversion-faucet .receive-amount {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: 5px;
}

/* Fix for minimum conversion amount display */
.conversion-faucet .min-conversion-notice,
.withdrawal-faucet .min-withdrawal-notice {
    font-size: 14px;
    margin-top: 5px;
    opacity: 0.8;
}

/* Fix for Telegram Bot Builder button */
#create-new-bot {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#create-new-bot:hover {
    background-color: #45a049 !important;
}

.ui-dialog-buttonset button:first-child {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.ui-dialog-buttonset button:first-child:hover {
    background-color: #45a049 !important;
}
