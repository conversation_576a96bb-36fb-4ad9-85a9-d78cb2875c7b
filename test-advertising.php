<?php
/**
 * Test file for the Advertising System
 */

// Include WordPress
require_once('wp-load.php');

// Check if Advertising class exists
if (class_exists('Farmfaucet_Advertising')) {
    echo "Farmfaucet_Advertising class exists.\n";
    
    // Initialize the class
    $advertising = Farmfaucet_Advertising::init();
    echo "Farmfaucet_Advertising initialized.\n";
    
    // Check if tables exist
    global $wpdb;
    $ads_table = $wpdb->prefix . 'farmfaucet_advertisements';
    $votes_table = $wpdb->prefix . 'farmfaucet_ad_votes';
    
    $ads_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$ads_table'") === $ads_table;
    $votes_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$votes_table'") === $votes_table;
    
    echo "Advertisements table exists: " . ($ads_table_exists ? 'Yes' : 'No') . "\n";
    echo "Ad votes table exists: " . ($votes_table_exists ? 'Yes' : 'No') . "\n";
    
    // Create tables if they don't exist
    if (!$ads_table_exists || !$votes_table_exists) {
        echo "Creating tables...\n";
        $advertising->create_tables();
        
        // Check again
        $ads_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$ads_table'") === $ads_table;
        $votes_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$votes_table'") === $votes_table;
        
        echo "Advertisements table exists: " . ($ads_table_exists ? 'Yes' : 'No') . "\n";
        echo "Ad votes table exists: " . ($votes_table_exists ? 'Yes' : 'No') . "\n";
    }
    
    // Check settings
    $advertising_enabled = get_option('farmfaucet_advertising_enabled', 1);
    $approval_required = get_option('farmfaucet_ad_approval_required', 1);
    $ad_cost = get_option('farmfaucet_ad_cost', 1.0);
    $ad_duration_days = get_option('farmfaucet_ad_duration_days', 7);
    $ad_max_votes = get_option('farmfaucet_ad_max_votes', 3);
    $ad_vote_reward = get_option('farmfaucet_ad_vote_reward', 0.1);
    
    echo "Advertising enabled: " . ($advertising_enabled ? 'Yes' : 'No') . "\n";
    echo "Approval required: " . ($approval_required ? 'Yes' : 'No') . "\n";
    echo "Ad cost: " . $ad_cost . "\n";
    echo "Ad duration days: " . $ad_duration_days . "\n";
    echo "Max votes per day: " . $ad_max_votes . "\n";
    echo "Vote reward: " . $ad_vote_reward . "\n";
    
    // Test shortcodes
    echo "Ad display shortcode: [farmfaucet_ad_display]\n";
    echo "Ad create shortcode: [farmfaucet_ad_create]\n";
    echo "Ad list shortcode: [farmfaucet_ad_list]\n";
} else {
    echo "Farmfaucet_Advertising class does not exist.\n";
}
