/**
 * Color Picker Fix for Farm Faucet
 *
 * This script fixes issues with the WordPress color picker in the Farm Faucet plugin.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Color Picker Fix loaded');
        initColorPickerFix();
    });

    /**
     * Initialize Color Picker Fix
     */
    function initColorPickerFix() {
        console.log('Initializing Color Picker Fix');

        // Ensure the color picker is properly initialized
        if ($.fn.wpColorPicker) {
            // Define color picker fields
            const colorPickerFields = [
                // Currency fields
                '#currency-color',

                // Faucet fields
                '#faucet-color',
                '#faucet-border-color',
                '#faucet-text-color',
                '#faucet-bg-color',
                '#faucet-bg-gradient-start',
                '#faucet-bg-gradient-end',
                '#faucet-form-bg-color',

                // Button fields
                '#button-color-hex',

                // Milestone fields
                '#milestone-card-bg-color',
                '#milestone-card-gradient-start',
                '#milestone-card-gradient-end',
                '#milestone-card-border-color',
                '#milestone-card-text-color',
                '#milestone-progress-color',
                '#milestone-progress-bg-color'
            ];

            // Initialize each color picker
            $.each(colorPickerFields, function(index, selector) {
                // First destroy any existing color pickers to avoid duplicates
                if ($(selector).hasClass('wp-color-picker')) {
                    $(selector).wpColorPicker('destroy');
                }

                // Only initialize if the element exists
                if ($(selector).length) {
                    // Get default color
                    const defaultColor = $(selector).data('default-color') || '#4CAF50';

                    // Initialize the color picker with proper event handling
                    $(selector).wpColorPicker({
                        defaultColor: defaultColor,
                        palettes: true, // Show the color palette
                        change: function(event, ui) {
                            // Update the input value when color changes
                            $(this).val(ui.color.toString());
                            $(this).attr('data-color', ui.color.toString());
                            console.log('Color picker changed to:', ui.color.toString());
                        },
                        clear: function() {
                            // Set default color when cleared
                            $(this).val(defaultColor);
                            $(this).attr('data-color', defaultColor);
                            console.log('Color picker cleared, set to default:', defaultColor);
                        }
                    });
                }
            });

            // Add a click handler to ensure the color picker opens
            $('.wp-color-result').off('click.colorpicker').on('click.colorpicker', function(e) {
                console.log('Color picker button clicked');
                // Ensure the color picker is visible
                setTimeout(function() {
                    $('.wp-picker-container').css('z-index', '999999');
                    $('.iris-picker').css({
                        'position': 'absolute',
                        'z-index': '999999',
                        'display': 'block'
                    });
                }, 10);
            });

            // Fix for modal z-index issues
            $('.ui-dialog').css('z-index', '99998');
        } else {
            console.error('WordPress color picker not available');
        }

        // Add event handler for all modals
        $('.farmfaucet-add-currency-button, .farmfaucet-edit-currency-button, #create-new-faucet, .edit-faucet, .add-button, .edit-button').off('click.colorpicker').on('click.colorpicker', function() {
            console.log('Modal button clicked');
            // Ensure color picker is initialized when modal opens
            setTimeout(function() {
                initColorPickerFix();
            }, 100);
        });

        // Fix for color picker in tabs
        $('#faucets-tabs').on('tabsactivate', function(event, ui) {
            console.log('Tab activated');
            // Reinitialize color pickers when tab changes
            setTimeout(function() {
                initColorPickerFix();
            }, 100);
        });
    }
})(jQuery);
