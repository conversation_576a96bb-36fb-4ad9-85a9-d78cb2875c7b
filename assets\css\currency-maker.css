/**
 * Currency Maker Frontend CSS
 */

/* Currency List */
.farmfaucet-currency-list {
    margin-bottom: 30px;
}

.farmfaucet-currency-list-title {
    margin-bottom: 20px;
    font-size: 24px;
    text-align: center;
}

/* Currency Grid */
.farmfaucet-currency-grid {
    display: grid;
    grid-gap: 20px;
    margin-bottom: 30px;
}

.farmfaucet-currency-grid.columns-1 {
    grid-template-columns: 1fr;
}

.farmfaucet-currency-grid.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.farmfaucet-currency-grid.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.farmfaucet-currency-grid.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* Currency Card */
.farmfaucet-currency-card {
    border-radius: 8px;
    border: 2px solid #4CAF50;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.farmfaucet-currency-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.farmfaucet-currency-header {
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    text-align: center;
    position: relative;
}

.farmfaucet-currency-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-bottom: 10px;
    background-color: white;
    padding: 5px;
}

.farmfaucet-currency-name {
    margin: 0 0 5px;
    font-size: 18px;
    font-weight: bold;
}

.farmfaucet-currency-code {
    font-size: 14px;
    opacity: 0.8;
}

.farmfaucet-currency-body {
    padding: 15px;
    background-color: white;
}

.farmfaucet-currency-balance {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.balance-value {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.balance-symbol {
    font-size: 16px;
    margin-left: 5px;
}

.farmfaucet-currency-info {
    font-size: 14px;
}

.info-row {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
}

.info-label {
    font-weight: bold;
    color: #666;
}

.info-value {
    text-align: right;
}

.farmfaucet-currency-footer {
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    text-align: center;
}

.farmfaucet-exchange-button {
    padding: 8px 15px;
    background-color: #0088cc;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-exchange-button:hover {
    background-color: #006699;
}

/* Exchange Form */
.farmfaucet-exchange-form {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.exchange-form-title {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
    color: #333;
    text-align: center;
}

.farmfaucet-form-group {
    margin-bottom: 15px;
}

.farmfaucet-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.farmfaucet-form-group select,
.farmfaucet-form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.farmfaucet-form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.amount-input-wrapper {
    position: relative;
}

.amount-symbol {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: bold;
}

.fee-info,
.receive-amount {
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
}

.farmfaucet-form-actions {
    margin-top: 20px;
    text-align: center;
}

.farmfaucet-exchange-submit,
.farmfaucet-exchange-cancel {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-exchange-submit {
    background-color: #4CAF50;
    color: white;
    margin-right: 10px;
}

.farmfaucet-exchange-submit:hover {
    background-color: #45a049;
}

.farmfaucet-exchange-cancel {
    background-color: #f44336;
    color: white;
}

.farmfaucet-exchange-cancel:hover {
    background-color: #d32f2f;
}

.farmfaucet-exchange-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

/* Currency Balance Display */
.farmfaucet-currency-balance-card {
    max-width: 200px;
    border-radius: 8px;
    border: 2px solid #4CAF50;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.farmfaucet-currency-balance-card .card-header {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    text-align: center;
}

.farmfaucet-currency-balance-card .card-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
    background-color: white;
    padding: 3px;
}

.farmfaucet-currency-balance-card .card-name {
    font-size: 16px;
    font-weight: bold;
    vertical-align: middle;
}

.farmfaucet-currency-balance-card .card-body {
    padding: 15px;
    background-color: white;
    text-align: center;
}

.farmfaucet-currency-balance-card .card-balance {
    font-size: 18px;
}

.farmfaucet-currency-balance-inline {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.farmfaucet-currency-balance-inline .balance-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 5px;
}

.farmfaucet-currency-balance-inline .balance-name {
    font-weight: bold;
    margin-right: 5px;
}

.farmfaucet-currency-balance-badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #4CAF50;
    color: white;
    border-radius: 20px;
    font-weight: bold;
}

.farmfaucet-currency-balance-badge .badge-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 5px;
    background-color: white;
    padding: 2px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .farmfaucet-currency-grid.columns-4,
    .farmfaucet-currency-grid.columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .farmfaucet-currency-grid.columns-4,
    .farmfaucet-currency-grid.columns-3,
    .farmfaucet-currency-grid.columns-2 {
        grid-template-columns: 1fr;
    }
    
    .farmfaucet-exchange-form {
        max-width: 100%;
        padding: 15px;
    }
}
