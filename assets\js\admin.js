jQuery(document).ready(function($) {
    // =============================================
    // TABS INITIALIZATION
    // =============================================

    // Initialize jQuery UI tabs
    $('#faucets-tabs').tabs();
    $('#transaction-tabs').tabs();

    // =============================================
    // TRANSACTION FILTERING SYSTEM
    // =============================================

    /**
     * Filter transactions by type
     * @param {string} type - Transaction type to filter (all|success|error)
     * @param {number|null} faucetId - Optional faucet ID to filter by
     */
    function filterTransactions(type, faucetId = null) {
        // Get the container based on faucet ID
        var container = '.transaction-chat-box';
        if (faucetId !== null) {
            container = '#transactions-' + faucetId + ' ' + container;
        }

        // Hide all messages and remove active states
        $(container + ' .transaction-message').hide().removeClass('active');

        // Show relevant transactions based on the selected type
        if (type === 'all') {
            $(container + ' .transaction-message').show();
        } else {
            $(container + ' .transaction-message[data-type="' + type + '"]').show();
        }

        // Update active tab styling
        $(container).closest('.transaction-container').find('.transaction-subtab').removeClass('active');
        $(container).closest('.transaction-container').find('.transaction-subtab[data-type="' + type + '"]').addClass('active');

        // Special handling for error tab
        if (type === 'error') {
            $(container).closest('.transaction-container').find('.transaction-subtab.error-tab').addClass('error-active');
        } else {
            $(container).closest('.transaction-container').find('.transaction-subtab').removeClass('error-active');
        }

        // Maintain scroll position
        var chatBox = $(container);
        setTimeout(function() {
            chatBox.scrollTop(chatBox[0].scrollHeight);
        }, 50);
    }

    // =============================================
    // INITIALIZATION
    // =============================================

    // Set default view to show all transactions
    $('.transaction-chat-box').each(function() {
        var $this = $(this);
        var $container = $this.closest('.transaction-container');
        var $firstTab = $container.find('.transaction-subtab').first();
        $firstTab.addClass('active');

        // Auto-scroll to bottom
        $this.scrollTop($this[0].scrollHeight);
    });

    // =============================================
    // FAUCET MANAGEMENT
    // =============================================

    // Initialize faucet form dialog
    var $faucetDialog = $('#faucet-form-dialog').dialog({
        autoOpen: false,
        modal: true,
        width: 500,
        buttons: {
            'Save': function() {
                saveFaucet();
            },
            'Cancel': function() {
                $(this).dialog('close');
            }
        },
        close: function() {
            resetFaucetForm();
        }
    });

    // Create new faucet button
    $('#create-new-faucet').on('click', function() {
        resetFaucetForm();
        $faucetDialog.dialog('option', 'title', 'Create New Faucet');
        $faucetDialog.dialog('open');
    });

    // Edit faucet button
    $(document).on('click', '.edit-faucet', function() {
        var faucetId = $(this).data('id');
        loadFaucetData(faucetId);
        $faucetDialog.dialog('option', 'title', 'Edit Faucet');
        $faucetDialog.dialog('open');
    });

    // Delete faucet button
    $(document).on('click', '.delete-faucet', function() {
        var faucetId = $(this).data('id');
        var faucetName = $(this).closest('.faucet-details').find('h4').text();

        if (confirm(farmfaucet_admin.strings.confirm_delete)) {
            deleteFaucet(faucetId);
        }
    });

    // Generate shortcode from name
    $('#faucet-name').on('input', function() {
        // Only auto-generate if the shortcode field is empty or hasn't been manually edited
        if ($('#faucet-shortcode').data('auto-generated') !== false) {
            var name = $(this).val();
            var shortcode = name.toLowerCase()
                .replace(/[^a-z0-9_\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '_')         // Replace spaces with underscores
                .replace(/-+/g, '_');         // Replace hyphens with underscores

            $('#faucet-shortcode').val(shortcode);
        }
    });

    // Mark shortcode as manually edited
    $('#faucet-shortcode').on('input', function() {
        $(this).data('auto-generated', false);
    });

    // Toggle background options based on transparent background setting
    $(document).on('change', '#faucet-transparent-bg', function() {
        updateFaucetBgOptions();
    });

    // Toggle form background options based on form transparent setting
    $(document).on('change', '#faucet-form-transparent', function() {
        updateFormBgOptions();
    });

    // Toggle between solid and gradient background options
    $(document).on('change', 'input[name="bg_style"]', function() {
        if ($(this).val() === 'solid') {
            $('.bg-solid-option').slideDown();
            $('.bg-gradient-options').slideUp();
        } else {
            $('.bg-solid-option').slideUp();
            $('.bg-gradient-options').slideDown();
        }
    });

    /**
     * Update faucet background options visibility based on transparent background setting
     */
    function updateFaucetBgOptions() {
        if ($('#faucet-transparent-bg').is(':checked')) {
            $('.faucet-bg-options').slideUp();
        } else {
            $('.faucet-bg-options').slideDown();
            // Also update form background options
            updateFormBgOptions();
        }
    }

    /**
     * Update form background options visibility based on form transparent setting
     */
    function updateFormBgOptions() {
        if ($('#faucet-form-transparent').is(':checked')) {
            $('#faucet-form-bg-color').closest('.form-field').slideUp();
        } else {
            if (!$('#faucet-transparent-bg').is(':checked')) {
                $('#faucet-form-bg-color').closest('.form-field').slideDown();
            }
        }
    }

    /**
     * Reset the faucet form
     */
    function resetFaucetForm() {
        $('#faucet-id').val('0');
        $('#faucet-name').val('');
        $('#faucet-currency').val('LTC');
        $('#faucet-amount').val('0.001');
        $('#faucet-cooldown').val('3600');
        $('#faucet-api-key').val('');
        $('#faucet-shortcode').val('').data('auto-generated', true);
        $('#faucet-is-enabled').prop('checked', true); // Default to enabled

        // Reset captcha selection
        $('input[name="captcha_type"]').prop('checked', false);

        // Reset background styling options
        $('#faucet-transparent-bg').prop('checked', false);
        $('input[name="bg_style"][value="solid"]').prop('checked', true);
        $('#faucet-bg-color').val('#f8fff8');
        $('#faucet-gradient-start').val('#f8fff8');
        $('#faucet-gradient-end').val('#e8f5e9');
        $('#faucet-text-color').val('#4CAF50');
        $('#faucet-text-shadow').val('none');
        $('#faucet-button-color').val('#4CAF50');
        $('#faucet-border-color').val('#4CAF50');
        $('#faucet-border-radius').val('8px');
        $('#faucet-form-bg-color').val('#ffffff');
        $('#faucet-form-transparent').prop('checked', false);

        // Show/hide appropriate background options
        $('.bg-solid-option').show();
        $('.bg-gradient-options').hide();
        updateFaucetBgOptions();
    }

    /**
     * Load faucet data into the form
     * @param {number} faucetId - The ID of the faucet to load
     */
    function loadFaucetData(faucetId) {
        var $faucetTab = $('#faucet-' + faucetId);

        $('#faucet-id').val(faucetId);
        // Get the faucet name from the h4 element, but ensure we're not getting "faucet buttons" text
        var faucetName = $faucetTab.find('h4').first().clone().children().remove().end().text().trim();
        $('#faucet-name').val(faucetName);

        // Get the enabled status from the toggle switch
        var isEnabled = $faucetTab.find('.faucet-enabled-toggle').is(':checked');
        $('#faucet-is-enabled').prop('checked', isEnabled);

        // Extract currency, amount, cooldown, and shortcode from the info items
        var $infoItems = $faucetTab.find('.faucet-info-item');

        // Reset captcha selection
        $('input[name="captcha_type"]').prop('checked', false);

        $infoItems.each(function() {
            var $item = $(this);
            var label = $item.find('strong').text().trim().toLowerCase();
            var value = $item.find('span').text().trim();

            if (label.includes('currency')) {
                $('#faucet-currency').val(value);
            } else if (label.includes('amount')) {
                // Don't set amount here - it will be set properly in the AJAX response based on faucet type
                // $('#faucet-amount').val(value);
            } else if (label.includes('cooldown')) {
                // Extract seconds from human-readable format
                var seconds = 3600; // Default to 1 hour
                if (value.includes('second')) {
                    seconds = parseInt(value);
                } else if (value.includes('minute')) {
                    seconds = parseInt(value) * 60;
                } else if (value.includes('hour')) {
                    seconds = parseInt(value) * 3600;
                }
                $('#faucet-cooldown').val(seconds);
            } else if (label.includes('api key')) {
                // If API key is set (not showing 'Not set'), set a placeholder value
                // We don't want to expose the actual API key, but we want to indicate it's set
                if (!value.includes('Not set')) {
                    $('#faucet-api-key').val('').attr('placeholder', 'API key is set. Leave empty to keep current key.');
                } else {
                    $('#faucet-api-key').val('').attr('placeholder', '');
                }
            } else if (label.includes('shortcode')) {
                var shortcode = $item.find('code').text().replace(/[\[\]]/g, '');
                $('#faucet-shortcode').val(shortcode).data('auto-generated', false);
            } else if (label.includes('captcha')) {
                var captchaType = value.toLowerCase();
                if (captchaType === 'hcaptcha' || captchaType === 'recaptcha' || captchaType === 'turnstile') {
                    // Check the corresponding radio button
                    $('input[name="captcha_type"][value="' + captchaType + '"]').prop('checked', true);
                }
            }
        });

        // Load background styling options via AJAX
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_faucet',
                nonce: farmfaucet_admin.nonce,
                faucet_id: faucetId
            },
            success: function(response) {
                if (response.success && response.data) {
                    var faucet = response.data;

                    // Set faucet type
                    var faucetType = faucet.faucet_type || 'stage';
                    $('#faucet-type').val(faucetType).trigger('change');

                    // Set amount based on faucet type
                    if (faucetType === 'stage') {
                        $('#faucet-amount').val(faucet.amount || '0.001');
                    } else if (faucetType === 'dummy') {
                        $('#dummy-amount').val(faucet.amount || '1');
                    }

                    // Set type-specific fields
                    if (faucetType === 'dummy') {
                        $('#dummy-currency-id').val(faucet.currency_id);
                        $('#dummy-view-style').val(faucet.view_style || 'default');
                    } else if (faucetType === 'withdrawal') {
                        $('#withdrawal-currency-id').val(faucet.currency_id);
                        $('#min-withdrawal').val(faucet.min_withdrawal || '1');
                        $('#ads-only-conversion').prop('checked', faucet.ads_only_conversion == 1);

                        // Set available currencies
                        if (faucet.available_currencies) {
                            try {
                                var availableCurrencies = JSON.parse(faucet.available_currencies);
                                if (Array.isArray(availableCurrencies)) {
                                    // Uncheck all first
                                    $('input[name="available_currencies[]"]').prop('checked', false);

                                    // Check the ones that are available
                                    availableCurrencies.forEach(function(currency) {
                                        $('input[name="available_currencies[]"][value="' + currency + '"]').prop('checked', true);
                                    });
                                }
                            } catch (e) {
                                console.error('Error parsing available currencies:', e);
                            }
                        }
                    } else if (faucetType === 'conversion') {
                        $('#conversion-source-currency-id').val(faucet.currency_id);
                        $('#min-conversion').val(faucet.min_withdrawal || '1');
                        $('#ads-only-conversion').prop('checked', faucet.ads_only_conversion == 1);

                        // Set conversion currencies
                        if (faucet.conversion_currencies) {
                            try {
                                var conversionCurrencies = JSON.parse(faucet.conversion_currencies);
                                if (Array.isArray(conversionCurrencies)) {
                                    // Uncheck all first
                                    $('input[name="conversion_currencies[]"]').prop('checked', false);

                                    // Check the ones that are available
                                    conversionCurrencies.forEach(function(currency) {
                                        $('input[name="conversion_currencies[]"][value="' + currency + '"]').prop('checked', true);
                                    });
                                }
                            } catch (e) {
                                console.error('Error parsing conversion currencies:', e);
                            }
                        }
                    }

                    // Set transparent background option
                    $('#faucet-transparent-bg').prop('checked', faucet.transparent_bg == 1);

                    // Set background style
                    $('input[name="bg_style"][value="' + faucet.bg_style + '"]').prop('checked', true);

                    // Set background colors
                    $('#faucet-bg-color').val(faucet.bg_color);
                    $('#faucet-gradient-start').val(faucet.bg_gradient_start);
                    $('#faucet-gradient-end').val(faucet.bg_gradient_end);

                    // Set text color and shadow
                    $('#faucet-text-color').val(faucet.text_color);
                    $('#faucet-text-shadow').val(faucet.text_shadow);

                    // Set button color, border color, and border radius
                    $('#faucet-button-color').val(faucet.button_color || '#4CAF50');
                    $('#faucet-border-color').val(faucet.border_color || '#4CAF50');
                    $('#faucet-border-radius').val(faucet.border_radius || '8px');

                    // Set form background color and transparency
                    $('#faucet-form-bg-color').val(faucet.form_bg_color || '#ffffff');
                    $('#faucet-form-transparent').prop('checked', faucet.form_transparent == 1);

                    // Set faucet color and update color circle
                    if (faucet.faucet_color) {
                        $('#faucet-color').val(faucet.faucet_color);
                        $('.color-circle').removeClass('active');
                        $('.color-circle[data-color="' + faucet.faucet_color + '"]').addClass('active');
                    }

                    // Show/hide appropriate background options
                    if (faucet.bg_style === 'gradient') {
                        $('.bg-solid-option').hide();
                        $('.bg-gradient-options').show();
                    } else {
                        $('.bg-solid-option').show();
                        $('.bg-gradient-options').hide();
                    }

                    // Update visibility based on transparent background setting
                    updateFaucetBgOptions();
                }
            }
        });
    }

    /**
     * Save faucet data
     */
    function saveFaucet() {
        var faucetId = $('#faucet-id').val();
        var isNew = faucetId === '0';

        // Get selected captcha type
        var captchaType = $('input[name="captcha_type"]:checked').val() || '';

        // Get is_enabled value
        var isEnabled = $('#faucet-is-enabled').is(':checked') ? 1 : 0;

        // Get faucet type to determine which amount field to use
        var faucetType = $('#faucet-type').val() || 'stage';

        // Get amount based on faucet type
        var amount = '0.001';
        if (faucetType === 'stage') {
            amount = $('#faucet-amount').val() || '0.001';
        } else if (faucetType === 'dummy') {
            amount = $('#dummy-amount').val() || '1';
        }

        var formData = {
            action: isNew ? 'farmfaucet_create_faucet' : 'farmfaucet_update_faucet',
            nonce: farmfaucet_admin.nonce,
            faucet_id: faucetId,
            name: $('#faucet-name').val(),
            faucet_type: faucetType,
            currency: $('#faucet-currency').val(),
            amount: amount,
            cooldown: $('#faucet-cooldown').val(),
            api_key: $('#faucet-api-key').val(),
            shortcode: $('#faucet-shortcode').val(),
            captcha_type: captchaType,
            is_enabled: isEnabled,
            transparent_bg: $('#faucet-transparent-bg').is(':checked') ? 1 : 0,
            bg_style: $('input[name="bg_style"]:checked').val(),
            bg_color: $('#faucet-bg-color').val(),
            bg_gradient_start: $('#faucet-gradient-start').val(),
            bg_gradient_end: $('#faucet-gradient-end').val(),
            text_color: $('#faucet-text-color').val(),
            text_shadow: $('#faucet-text-shadow').val(),
            button_color: $('#faucet-button-color').val(),
            border_color: $('#faucet-border-color').val(),
            border_radius: $('#faucet-border-radius').val(),
            form_bg_color: $('#faucet-form-bg-color').val() || '#ffffff',
            form_transparent: $('#faucet-form-transparent').is(':checked') ? 1 : 0,
            faucet_color: $('#faucet-color').val() || 'green'
        };

        // Add type-specific fields
        if (faucetType === 'dummy') {
            formData.currency_id = $('#dummy-currency-id').val() || 0;
            formData.view_style = $('#dummy-view-style').val() || 'default';
        } else if (faucetType === 'withdrawal') {
            formData.currency_id = $('#withdrawal-currency-id').val() || 0;
            formData.min_withdrawal = $('#min-withdrawal').val() || '1';

            // Get selected available currencies
            var availableCurrencies = [];
            $('input[name="available_currencies[]"]:checked').each(function() {
                availableCurrencies.push($(this).val());
            });
            formData.available_currencies = availableCurrencies;
        } else if (faucetType === 'conversion') {
            formData.currency_id = $('#conversion-source-currency-id').val() || 0;
            formData.min_withdrawal = $('#min-conversion').val() || '1'; // Store in min_withdrawal field
            formData.ads_only_conversion = $('#ads-only-conversion').is(':checked') ? 1 : 0;

            // Get selected conversion currencies
            var conversionCurrencies = [];
            $('input[name="conversion_currencies[]"]:checked').each(function() {
                conversionCurrencies.push($(this).val());
            });
            formData.conversion_currencies = conversionCurrencies;
        }

        // Validate form
        if (!formData.name || !formData.shortcode) {
            alert('Name and shortcode are required');
            return;
        }

        // Show loading state
        $faucetDialog.find('button').prop('disabled', true);

        // Send AJAX request
        $.post(farmfaucet_admin.ajax_url, formData, function(response) {
            if (response.success) {
                alert(isNew ? farmfaucet_admin.strings.create_success : farmfaucet_admin.strings.update_success);
                // Reload the page to show the updated faucet list
                window.location.reload();
            } else {
                alert(response.data.message || farmfaucet_admin.strings.error);
                $faucetDialog.find('button').prop('disabled', false);
            }
        }).fail(function() {
            alert(farmfaucet_admin.strings.error);
            $faucetDialog.find('button').prop('disabled', false);
        });
    }

    /**
     * Delete a faucet
     * @param {number} faucetId - The ID of the faucet to delete
     */
    function deleteFaucet(faucetId) {
        var formData = {
            action: 'farmfaucet_delete_faucet',
            nonce: farmfaucet_admin.nonce,
            faucet_id: faucetId
        };

        // Send AJAX request
        $.post(farmfaucet_admin.ajax_url, formData, function(response) {
            if (response.success) {
                alert(farmfaucet_admin.strings.delete_success);
                // Reload the page to update the faucet list
                window.location.reload();
            } else {
                alert(response.data.message || farmfaucet_admin.strings.error);
            }
        }).fail(function() {
            alert(farmfaucet_admin.strings.error);
        });
    }

    // =============================================
    // EVENT HANDLERS
    // =============================================

    // Transaction tab click handling
    $(document).on('click', '.transaction-subtab', function() {
        var type = $(this).data('type');
        var faucetId = $(this).data('faucet');
        filterTransactions(type, faucetId);
    });

    // Error details toggle
    $(document).on('click', '.transaction-message-header', function() {
        var $message = $(this).closest('.transaction-message');
        var $details = $message.find('.error-details');
        var $this = $(this);

        // Toggle error details with smooth animation
        $details.slideToggle(200, function() {
            // Maintain scroll position at bottom if at bottom
            var chatBox = $this.closest('.transaction-chat-box');
            if (chatBox.scrollTop() + chatBox.innerHeight() >= chatBox[0].scrollHeight - 50) {
                chatBox.scrollTop(chatBox[0].scrollHeight);
            }
        });

        // Toggle active state for the message
        $message.toggleClass('show-details');
    });

    // =============================================
    // BUTTON MANAGEMENT
    // =============================================

    // Initialize button form dialog
    var $buttonDialog = $('#button-form-dialog').dialog({
        autoOpen: false,
        modal: true,
        width: 500,
        buttons: {
            'Save': function() {
                saveButton();
            },
            'Cancel': function() {
                $(this).dialog('close');
            }
        },
        close: function() {
            resetButtonForm();
        }
    });

    // Add button click handler
    $(document).on('click', '.add-button', function() {
        var faucetId = $(this).data('faucet-id');
        resetButtonForm();
        $('#button-faucet-id').val(faucetId);
        $buttonDialog.dialog('option', 'title', 'Add New Button');
        $buttonDialog.dialog('open');
    });

    // Edit button click handler
    $(document).on('click', '.edit-button', function() {
        var buttonId = $(this).data('id');
        loadButtonData(buttonId);
        $buttonDialog.dialog('option', 'title', 'Edit Button');
        $buttonDialog.dialog('open');
    });

    // Delete button click handler
    $(document).on('click', '.delete-button', function() {
        var buttonId = $(this).data('id');
        if (confirm('Are you sure you want to delete this button?')) {
            deleteButton(buttonId);
        }
    });

    /**
     * Reset the button form
     */
    function resetButtonForm() {
        $('#button-id').val('0');
        $('#button-faucet-id').val('0');
        $('#button-text').val('');
        $('#button-size').val('medium');
        $('#button-color').val('blue');
        $('#button-color-hex').val('');
        $('#border-shape').val('rounded');
        $('#redirect-url').val('');
        $('#is-locked').prop('checked', false);
        $('.required-faucets-container input[type="checkbox"]').prop('checked', false);
        $('#reset-minutes').val(0);
        $('#lock-faucet').prop('checked', false);
        $('#milestone-enabled').prop('checked', false);

        // Reset countdown options
        $('#countdown-enabled').prop('checked', false);
        $('#countdown-seconds').val(60);
        $('#countdown-message').val('');
        $('#countdown-click-activation').prop('checked', false);
        $('#countdown-click-element-id').val('');
        $('#countdown-pre-click-message').val('');
        $('#countdown-standby').prop('checked', false);

        // Reset milestone type
        $('input[name="milestone_type"][value="global"]').prop('checked', true);

        // Reset global milestone options
        $('#button-milestone-count').val(5);
        $('#button-milestone-lock-faucet').prop('checked', false);

        // Reset page-specific milestone options
        $('.milestone-pages-tabs').empty();
        $('.milestone-pages-content').empty();

        // Reset milestone display style
        $('input[name="milestone_display_style"][value="card"]').prop('checked', true);
        $('.milestone-display-style-option').removeClass('selected');
        $('.milestone-display-style-option[data-style="card"]').addClass('selected');

        // Reset milestone appearance options
        $('input[name="milestone_bar_style"][value="solid"]').prop('checked', true);
        $('#milestone-bar-color').val('#4CAF50');
        $('#milestone-gradient-start').val('#4CAF50');
        $('#milestone-gradient-end').val('#2196F3');
        $('.milestone-solid-color').show();
        $('.milestone-gradient-colors').hide();

        // Update milestone preview
        updateMilestonePreview();

        // Hide all option containers
        $('.button-lock-options').hide();
        $('.milestone-options').hide();
        $('.milestone-type-options').hide();
        $('.milestone-specific-count-container').hide();
        $('.milestone-appearance-options').hide();
        $('.countdown-options').hide();
        $('.countdown-click-options').hide();

        // Initialize color preview
        var defaultColor = $('#button-color option:selected').data('color');
        $('.color-preview').css('background-color', defaultColor);
    }

    // Toggle button lock options visibility
    $(document).on('change', '#is-locked', function() {
        if ($(this).is(':checked')) {
            // Disable milestone and countdown checkboxes
            $('#milestone-enabled, #countdown-enabled').prop('checked', false).prop('disabled', true);

            // Hide milestone and countdown options
            $('.milestone-settings, .countdown-settings, .click-activation-settings').slideUp();

            // Show lock options
            $('.lock-settings').slideDown();

            // Show alert
            alert('Milestone and Countdown features have been disabled as they are not compatible with Lock Button.');
        } else {
            // Re-enable milestone and countdown checkboxes
            $('#milestone-enabled, #countdown-enabled').prop('disabled', false);

            // Hide lock options
            $('.lock-settings').slideUp();
        }
    });

    // Toggle milestone options visibility
    $(document).on('change', '#milestone-enabled', function() {
        if ($(this).is(':checked')) {
            // Disable lock and countdown checkboxes
            $('#is-locked, #countdown-enabled').prop('checked', false).prop('disabled', true);

            // Hide lock and countdown options
            $('.lock-settings').slideUp();
            $('.countdown-settings, .click-activation-settings').slideUp();

            // Show milestone options
            $('.milestone-settings').slideDown();

            // Show alert
            alert('Lock Button and Countdown features have been disabled as they are not compatible with Milestone.');
        } else {
            // Re-enable lock and countdown checkboxes
            $('#is-locked, #countdown-enabled').prop('disabled', false);

            // Hide milestone options
            $('.milestone-settings').slideUp();
        }
    });

    // Toggle countdown options visibility
    $(document).on('change', '#countdown-enabled', function() {
        if ($(this).is(':checked')) {
            $('.countdown-settings').slideDown();

            // If milestone or lock is enabled, disable them
            if ($('#milestone-enabled').is(':checked') || $('#is-locked').is(':checked')) {
                $('#milestone-enabled').prop('checked', false).trigger('change');
                $('#is-locked').prop('checked', false).trigger('change');
                alert('Milestone and Lock features have been disabled as they are not compatible with Countdown Timer.');
            }

            // Disable milestone and lock checkboxes
            $('#milestone-enabled, #is-locked').prop('disabled', true);
        } else {
            $('.countdown-settings').slideUp();
            $('.click-activation-settings').slideUp();

            // Re-enable milestone and lock checkboxes
            $('#milestone-enabled, #is-locked').prop('disabled', false);
        }
    });

    // Toggle click activation options visibility
    $(document).on('change', '#countdown-click-activation', function() {
        if ($(this).is(':checked')) {
            $('.click-activation-settings').slideDown();
        } else {
            $('.click-activation-settings').slideUp();
        }
    });

    // Toggle milestone type options visibility
    $(document).on('change', '#milestone-type', function() {
        var selectedType = $(this).val();
        if (selectedType === 'global') {
            // Show global options, hide page-specific options
            $('.milestone-page-options').hide();
        } else if (selectedType === 'page_specific') {
            // Show page-specific options, hide global options
            $('.milestone-page-options').show();
        }
    });

    // Toggle milestone bar style options
    $(document).on('change', '#milestone-bar-style', function() {
        var selectedStyle = $(this).val();
        if (selectedStyle === 'solid') {
            $('.milestone-bar-solid-option').show();
            $('.milestone-bar-gradient-options').hide();
        } else if (selectedStyle === 'gradient') {
            $('.milestone-bar-solid-option').hide();
            $('.milestone-bar-gradient-options').show();
        }
    });

    // Toggle milestone card background style options
    $(document).on('change', '#milestone-card-bg-style', function() {
        var selectedStyle = $(this).val();
        if (selectedStyle === 'solid') {
            $('.milestone-card-solid-option').show();
            $('.milestone-card-gradient-options').hide();
        } else if (selectedStyle === 'gradient') {
            $('.milestone-card-solid-option').hide();
            $('.milestone-card-gradient-options').show();
        }
    });

    // Handle milestone display style selection
    $(document).on('change', '#milestone-display-style', function() {
        // Log the selection for debugging
        console.log('Selected milestone display style:', $(this).val());
    });

    // Toggle milestone card background options based on transparent checkbox
    $(document).on('change', '#milestone-transparent-bg', function() {
        if ($(this).is(':checked')) {
            $('.milestone-card-bg-options').slideUp();
        } else {
            $('.milestone-card-bg-options').slideDown();
        }
    });

    // Update milestone preview when colors change
    $(document).on('input', 'input[type="color"]', function() {
        // This is a simplified version since we don't have a preview in the template
        console.log('Color changed:', $(this).val());
    });

    // Function to handle milestone page options
    function initMilestoneTabs() {
        // Initialize tabs for milestone pages if needed
        if ($('.milestone-pages-tabs-container').length) {
            $('.milestone-pages-tabs-container').tabs();
        }
    }

    // Fetch all website pages for milestone selection
    function fetchWebsitePages() {
        // Only fetch if we haven't already
        if (!window.websitePagesFetched) {
            $.post(farmfaucet_admin.ajax_url, {
                action: 'farmfaucet_get_website_pages',
                nonce: farmfaucet_admin.nonce
            }, function(response) {
                if (response.success && response.data) {
                    window.websitePages = response.data;
                    window.websitePagesFetched = true;

                    // Update any existing page selectors
                    updatePageSelectors();
                }
            }).fail(function() {
                console.error('Failed to fetch website pages');
            });
        }
    }

    // Update page selectors with available pages
    function updatePageSelectors() {
        if (window.websitePages && window.websitePages.length) {
            $('.milestone-page-url-select').each(function() {
                var $select = $(this);
                var currentValue = $select.val();

                // Clear existing options except the placeholder
                $select.find('option:not(:first)').remove();

                // Add options for each page
                $.each(window.websitePages, function(index, page) {
                    var selected = (page.url === currentValue) ? 'selected' : '';
                    $select.append('<option value="' + page.url + '" data-title="' + page.title + '" ' + selected + '>' + page.title + '</option>');
                });
            });
        }
    }

    // Handle page selection change
    $(document).on('change', '.milestone-page-url-select', function() {
        var $select = $(this);
        var $nameInput = $select.closest('.milestone-page-form').find('.milestone-page-name');
        var selectedOption = $select.find('option:selected');

        // Update the name field with the page title if it's empty or matches a previous selection
        if ($nameInput.val() === '' || $nameInput.data('auto-filled')) {
            $nameInput.val(selectedOption.data('title'));
            $nameInput.data('auto-filled', true);
        }

        // Update the hidden URL input
        $select.siblings('.milestone-page-url').val($select.val());
    });

    // Handle adding a new page requirement
    $(document).on('click', '.add-milestone-page', function(e) {
        e.preventDefault();

        // Fetch pages if we haven't already
        if (!window.websitePagesFetched) {
            fetchWebsitePages();
        }

        // Generate a unique ID for this page
        var pageId = 'page_' + Date.now();

        // Clone the template and replace the placeholder ID
        var template = $('.milestone-page-template').html();
        template = template.replace(/{id}/g, pageId);

        // Create a new tab
        var tabTitle = 'Page ' + ($('.milestone-pages-tabs li').length + 1);
        var tabId = 'milestone-page-tab-' + pageId;

        // Add the tab
        $('.milestone-pages-tabs').append('<li><a href="#' + tabId + '">' + tabTitle + '</a></li>');

        // Add the content
        $('.milestone-pages-content').append('<div id="' + tabId + '" class="milestone-page-tab">' + template + '</div>');

        // Initialize the tabs if this is the first page
        if ($('.milestone-pages-tabs li').length === 1) {
            initMilestoneTabs();
        } else {
            // Refresh the tabs
            $('.milestone-pages-tabs-container').tabs('refresh');
            // Activate the new tab
            $('.milestone-pages-tabs-container').tabs('option', 'active', $('.milestone-pages-tabs li').length - 1);
        }

        // Update the page selector in the new tab
        updatePageSelectors();
    });

    // Handle removing a page requirement
    $(document).on('click', '.remove-milestone-page', function(e) {
        e.preventDefault();

        // Get the tab ID
        var tabId = $(this).closest('.milestone-page-tab').attr('id');
        var tabIndex = $('#' + tabId).index();

        // Remove the tab and content
        $('a[href="#' + tabId + '"]').parent().remove();
        $('#' + tabId).remove();

        // Refresh the tabs
        if ($('.milestone-pages-tabs li').length > 0) {
            $('.milestone-pages-tabs-container').tabs('refresh');
            // Activate the previous tab or the first one
            var newIndex = Math.max(0, tabIndex - 1);
            $('.milestone-pages-tabs-container').tabs('option', 'active', newIndex);
        }

        // Rename the remaining tabs
        $('.milestone-pages-tabs li').each(function(index) {
            $(this).find('a').text('Page ' + (index + 1));
        });
    });

    // Initialize milestone tabs
    function initMilestoneTabs() {
        $('.milestone-pages-tabs-container').tabs();
    }

    // Handle checkbox changes for required faucets
    $(document).on('change', '.required-faucet-checkbox', function() {
        // Get all checked faucets
        var selectedFaucets = [];
        $('.required-faucet-checkbox:checked').each(function() {
            selectedFaucets.push($(this).val());
        });

        // Update the hidden input with the selected values
        $('#button-required-faucets').val(selectedFaucets);
    });

    // Toggle color grid when clicking on the color select wrapper or preview
    $(document).on('click', '.color-select-wrapper, .color-preview', function() {
        // Close any other open color grids first
        $('.color-grid').not($(this).closest('.color-grid-container').find('.color-grid')).removeClass('active');

        // Toggle this color grid
        $(this).closest('.color-grid-container').find('.color-grid').toggleClass('active');
        return false; // Prevent default dropdown
    });

    // Close color grid when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.color-grid-container').length) {
            $('.color-grid').removeClass('active');
        }
    });

    // Select color from grid
    $(document).on('click', '.color-swatch-option', function() {
        var color = $(this).data('color');
        var value = $(this).data('value');
        var name = $(this).data('name');
        var container = $(this).closest('.color-grid-container');

        // Update the hidden input value
        container.find('#button-color').val(value);

        // If this is the custom color option
        if (value === 'custom') {
            // Show the custom color picker
            $('.custom-color-picker').show();

            // If we have a previously selected custom color, use it
            var currentHex = $('#button-color-hex').val();
            if (currentHex) {
                $('#button-custom-color').val(currentHex);
                color = currentHex;
            } else {
                // Otherwise use the default color from the picker
                var defaultColor = $('#button-custom-color').val();
                $('#button-color-hex').val(defaultColor);
                color = defaultColor;
            }
        } else {
            // Hide the custom color picker for standard colors
            $('.custom-color-picker').hide();
            // Clear the custom color hex value
            $('#button-color-hex').val('');
        }

        // Update the color name display
        container.find('.color-name-display').text(name);

        // Update the color preview
        container.find('.color-preview').css('background-color', color);

        // Update selected state
        container.find('.color-swatch-option').removeClass('selected');
        $(this).addClass('selected');

        // Hide the grid
        container.find('.color-grid').removeClass('active');
    });

    // Handle custom color picker changes
    $(document).on('input change', '#button-custom-color', function() {
        var color = $(this).val();

        // Update the preview
        $('.color-preview').css('background-color', color);

        // Update the hidden hex input
        $('#button-color-hex').val(color);
    });

    /**
     * Load button data into the form
     * @param {number} buttonId - The ID of the button to load
     */
    function loadButtonData(buttonId) {
        var $buttonItem = $('.button-item[data-id="' + buttonId + '"]');
        var faucetId = $buttonItem.closest('.faucet-tab-content').data('faucet-id');

        // Set form values
        $('#button-id').val(buttonId);
        $('#button-faucet-id').val(faucetId);

        // Update the current faucet ID for the required faucets list
        if (typeof currentFaucetId !== 'undefined') {
            currentFaucetId = faucetId;

            // Hide the current faucet from the required faucets list
            $('.faucet-checkbox-item').show();
            $('.faucet-checkbox-item input[value="' + faucetId + '"]').closest('.faucet-checkbox-item').hide();
        }

        // Make an AJAX request to get button details
        $.post(farmfaucet_admin.ajax_url, {
            action: 'farmfaucet_get_button',
            nonce: farmfaucet_admin.nonce,
            button_id: buttonId
        }, function(response) {
            if (response.success && response.data) {
                var button = response.data;
                $('#button-text').val(button.button_text);
                $('#button-size').val(button.button_size);
                $('#button-color').val(button.button_color);
                $('#button-color-hex').val(button.button_color_hex || '');
                $('#button-border').val(button.border_shape);
                $('#button-redirect').val(button.redirect_url);

                // Update color preview and name
                var selectedValue = $('#button-color').val();
                var container = $('#button-color').closest('.color-grid-container');

                // Check if this is a custom color
                if (selectedValue === 'custom' && button.button_color_hex) {
                    // Use the custom color hex value
                    container.find('.color-preview').css('background-color', button.button_color_hex);
                    container.find('.color-name-display').text('Custom');

                    // No selected state in grid for custom colors
                    container.find('.color-swatch-option').removeClass('selected');
                } else {
                    // Handle predefined colors
                    var selectedOption = $('.color-swatch-option[data-value="' + selectedValue + '"]');

                    if (selectedOption.length) {
                        var selectedColor = selectedOption.data('color');
                        var selectedName = selectedOption.data('name');

                        container.find('.color-preview').css('background-color', selectedColor);
                        container.find('.color-name-display').text(selectedName);

                        // Update selected state in grid
                        container.find('.color-swatch-option').removeClass('selected');
                        selectedOption.addClass('selected');
                    } else {
                        // If the color value doesn't match any swatch, use the first one as default
                        var defaultOption = container.find('.color-swatch-option').first();
                        $('#button-color').val(defaultOption.data('value'));
                        container.find('.color-preview').css('background-color', defaultOption.data('color'));
                        container.find('.color-name-display').text(defaultOption.data('name'));
                        defaultOption.addClass('selected');
                    }
                }

                // Set button lock options
                if (button.is_locked == 1) {
                    $('#is-locked').prop('checked', true);
                    $('.lock-settings').show();

                    if (button.required_faucets) {
                        var requiredFaucets = button.required_faucets.split(',');

                        // Check the corresponding checkboxes
                        $('.required-faucets-container input[type="checkbox"]').prop('checked', false);
                        requiredFaucets.forEach(function(faucetId) {
                            $('.required-faucets-container input[value="' + faucetId + '"]').prop('checked', true);
                        });
                    }

                    // Set lock faucet option
                    if (button.lock_faucet == 1) {
                        $('#lock-faucet').prop('checked', true);
                    }

                    // Set reset minutes
                    $('#reset-minutes').val(button.reset_minutes || 0);
                }

                // Set milestone options
                if (button.milestone_enabled == 1) {
                    $('#milestone-enabled').prop('checked', true);
                    $('.milestone-settings').show();

                    // Set milestone type
                    var milestoneType = button.milestone_type || 'global';
                    $('#milestone-type').val(milestoneType);

                    // Show the appropriate milestone type options
                    if (milestoneType === 'global') {
                        // Set milestone count
                        $('#milestone-count').val(button.milestone_count || 5);

                        // Set milestone lock faucet option
                        if (button.milestone_lock_faucet == 1) {
                            $('#milestone-lock-faucet').prop('checked', true);
                        }
                    } else if (milestoneType === 'page_specific') {
                        // This would need additional implementation for page-specific options

                        // Load page requirements if they exist
                        if (button.milestone_pages) {
                            try {
                                var pages = JSON.parse(button.milestone_pages);

                                // Clear existing tabs
                                $('.milestone-pages-tabs').empty();
                                $('.milestone-pages-content').empty();

                                // Add each page as a tab
                                $.each(pages, function(pageId, page) {
                                    // Create a tab
                                    var tabTitle = page.name || 'Page ' + ($('.milestone-pages-tabs li').length + 1);
                                    var tabId = 'milestone-page-tab-' + pageId;

                                    // Clone the template and replace the placeholder ID
                                    var template = $('.milestone-page-template').html();
                                    template = template.replace(/{id}/g, pageId);

                                    // Add the tab
                                    $('.milestone-pages-tabs').append('<li><a href="#' + tabId + '">' + tabTitle + '</a></li>');

                                    // Add the content
                                    $('.milestone-pages-content').append('<div id="' + tabId + '" class="milestone-page-tab">' + template + '</div>');

                                    // Set the values
                                    $('#milestone-page-url-' + pageId).val(page.url || '');
                                    $('#milestone-page-name-' + pageId).val(page.name || '');
                                    $('#milestone-page-count-' + pageId).val(page.count || 1);
                                });

                                // Initialize tabs if there are pages
                                if ($('.milestone-pages-tabs li').length > 0) {
                                    initMilestoneTabs();
                                }
                            } catch (e) {
                                console.error('Error parsing milestone pages:', e);
                            }
                        }
                    }

                    // Set milestone display style
                    var displayStyle = button.milestone_display_style || 'card';
                    $('#milestone-display-style').val(displayStyle);

                    // Set milestone card appearance options
                    if (button.milestone_transparent_bg == 1) {
                        $('#milestone-transparent-bg').prop('checked', true);
                        $('.milestone-card-bg-options').hide();
                    } else {
                        $('#milestone-transparent-bg').prop('checked', false);
                        $('.milestone-card-bg-options').show();

                        // Set card background style
                        var cardBgStyle = button.milestone_card_bg_style || 'solid';
                        $('#milestone-card-bg-style').val(cardBgStyle);

                        // Set card color options based on style
                        if (cardBgStyle === 'solid') {
                            $('.milestone-card-solid-option').show();
                            $('.milestone-card-gradient-options').hide();

                            // Set card background color
                            if (button.milestone_card_bg_color) {
                                $('#milestone-card-bg-color').val(button.milestone_card_bg_color);
                            }
                        } else if (cardBgStyle === 'gradient') {
                            $('.milestone-card-solid-option').hide();
                            $('.milestone-card-gradient-options').show();

                            // Set card gradient colors
                            if (button.milestone_card_gradient_start) {
                                $('#milestone-card-gradient-start').val(button.milestone_card_gradient_start);
                            }
                            if (button.milestone_card_gradient_end) {
                                $('#milestone-card-gradient-end').val(button.milestone_card_gradient_end);
                            }
                        }
                    }

                    // Set milestone bar style
                    var barStyle = button.milestone_bar_style || 'solid';
                    $('#milestone-bar-style').val(barStyle);

                    // Set color options based on bar style
                    if (barStyle === 'solid') {
                        $('.milestone-bar-solid-option').show();
                        $('.milestone-bar-gradient-options').hide();

                        // Set bar color
                        if (button.milestone_bar_color) {
                            $('#milestone-bar-color').val(button.milestone_bar_color);
                        }
                    } else if (barStyle === 'gradient') {
                        $('.milestone-bar-solid-option').hide();
                        $('.milestone-bar-gradient-options').show();

                        // Set gradient colors
                        if (button.milestone_gradient_start) {
                            $('#milestone-gradient-start').val(button.milestone_gradient_start);
                        }
                        if (button.milestone_gradient_end) {
                            $('#milestone-gradient-end').val(button.milestone_gradient_end);
                        }
                    }
                }

                // Set countdown options
                if (button.countdown_enabled == 1) {
                    $('#countdown-enabled').prop('checked', true);
                    $('.countdown-settings').show();

                    // Disable milestone and lock checkboxes
                    $('#milestone-enabled, #is-locked').prop('disabled', true);

                    // Set countdown seconds
                    $('#countdown-seconds').val(button.countdown_seconds || 60);

                    // Set countdown message
                    $('#countdown-message').val(button.countdown_message || '');

                    // Set click activation options
                    if (button.countdown_click_activation == 1) {
                        $('#countdown-click-activation').prop('checked', true);
                        $('.click-activation-settings').show();

                        // Set click element ID
                        $('#countdown-click-element-id').val(button.countdown_click_element_id || '');

                        // Set pre-click message
                        $('#countdown-pre-click-message').val(button.countdown_pre_click_message || '');
                    }
                }
            } else {
                alert('Failed to load button data');
                $buttonDialog.dialog('close');
            }
        }).fail(function() {
            alert('Failed to load button data');
            $buttonDialog.dialog('close');
        });
    }

    /**
     * Save button data
     */
    function saveButton() {
        var buttonId = $('#button-id').val();
        var isNew = buttonId === '0';

        var formData = {
            action: isNew ? 'farmfaucet_create_button' : 'farmfaucet_update_button',
            nonce: farmfaucet_admin.nonce,
            button_id: buttonId,
            faucet_id: $('#button-faucet-id').val(),
            button_text: $('#button-text').val(),
            button_size: $('#button-size').val(),
            button_color: $('#button-color').val(),
            button_color_hex: $('#button-color-hex').val(),
            border_shape: $('#border-shape').val(),
            redirect_url: $('#redirect-url').val(),
            is_locked: $('#is-locked').is(':checked') ? 1 : 0,
            required_faucets: $('.required-faucets-container input:checked').map(function() { return $(this).val(); }).get(),
            reset_minutes: $('#reset-minutes').val(),
            lock_faucet: $('#lock-faucet').is(':checked') ? 1 : 0,
            milestone_enabled: $('#milestone-enabled').is(':checked') ? 1 : 0,
            milestone_type: $('#milestone-type').val(),
            milestone_count: $('#milestone-count').val(),
            milestone_lock_faucet: $('#milestone-lock-faucet').is(':checked') ? 1 : 0,
            // Milestone display style
            milestone_display_style: $('#milestone-display-style').val(),
            // Milestone card appearance options
            milestone_transparent_bg: $('#milestone-transparent-bg').is(':checked') ? 1 : 0,
            milestone_card_bg_style: $('#milestone-card-bg-style').val(),
            milestone_card_bg_color: $('#milestone-card-bg-color').val(),
            milestone_card_gradient_start: $('#milestone-card-gradient-start').val(),
            milestone_card_gradient_end: $('#milestone-card-gradient-end').val(),
            // Milestone progress bar options
            milestone_bar_style: $('#milestone-bar-style').val(),
            milestone_bar_color: $('#milestone-bar-color').val(),
            milestone_gradient_start: $('#milestone-gradient-start').val(),
            milestone_gradient_end: $('#milestone-gradient-end').val(),
            // Countdown options
            countdown_enabled: $('#countdown-enabled').is(':checked') ? 1 : 0,
            countdown_seconds: $('#countdown-seconds').val(),
            countdown_message: $('#countdown-message').val(),
            countdown_click_activation: $('#countdown-click-activation').is(':checked') ? 1 : 0,
            countdown_click_element_id: $('#countdown-click-element-id').val(),
            countdown_pre_click_message: $('#countdown-pre-click-message').val()
        };

        // Add page-specific milestone data if that type is selected
        if (formData.milestone_enabled && formData.milestone_type === 'page_specific') {
            var milestonePages = {};

            // Collect data from each page tab
            $('.milestone-page-tab').each(function() {
                var tabId = $(this).attr('id');
                var pageId = tabId.replace('milestone-page-tab-', '');

                var pageUrl = $('#milestone-page-url-' + pageId).val();
                var pageName = $('#milestone-page-name-' + pageId).val();
                var pageCount = $('#milestone-page-count-' + pageId).val();

                // Only add if URL is provided
                if (pageUrl) {
                    milestonePages[pageId] = {
                        url: pageUrl,
                        name: pageName || 'Page ' + ($('.milestone-pages-tabs li').length),
                        count: pageCount || 1
                    };
                }
            });

            // Add to form data
            formData.milestone_pages = milestonePages;
        }

        // Validate form
        if (!formData.button_text) {
            alert('Button text is required');
            return;
        }

        // Show loading state
        $buttonDialog.find('button').prop('disabled', true);

        // Send AJAX request
        $.post(farmfaucet_admin.ajax_url, formData, function(response) {
            if (response.success) {
                alert(isNew ? 'Button created successfully' : 'Button updated successfully');
                // Reload the page to show the updated button list
                window.location.reload();
            } else {
                alert(response.data.message || 'An error occurred');
                $buttonDialog.find('button').prop('disabled', false);
            }
        }).fail(function() {
            alert('An error occurred');
            $buttonDialog.find('button').prop('disabled', false);
        });
    }

    /**
     * Delete a button
     * @param {number} buttonId - The ID of the button to delete
     */
    function deleteButton(buttonId) {
        var formData = {
            action: 'farmfaucet_delete_button',
            nonce: farmfaucet_admin.nonce,
            button_id: buttonId
        };

        // Send AJAX request
        $.post(farmfaucet_admin.ajax_url, formData, function(response) {
            if (response.success) {
                alert('Button deleted successfully');
                // Reload the page to update the button list
                window.location.reload();
            } else {
                alert(response.data.message || 'An error occurred');
            }
        }).fail(function() {
            alert('An error occurred');
        });
    }

    // =============================================
    // UI ENHANCEMENTS
    // =============================================

    // Add hover effects to transaction messages
    $('.transaction-message').hover(
        function() {
            $(this).css('transform', 'translateY(-2px)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
        }
    );

    // Add hover effects to faucet tabs
    $('.faucet-tab-content').hover(
        function() {
            $(this).css('transform', 'translateY(-2px)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
        }
    );

    // =============================================
    // CAPTCHA SELECTION HANDLING
    // =============================================

    // Handle captcha radio selection
    $(document).on('change', '.captcha-radio', function() {
        if ($(this).is(':checked')) {
            // No need to uncheck others as radio buttons handle that automatically
            console.log('Selected captcha type:', $(this).val());
        }
    });

    /**
     * Update the milestone preview based on selected options
     */
    function updateMilestonePreview() {
        var barStyle = $('input[name="milestone_bar_style"]:checked').val();
        var previewBar = $('#milestone-preview-bar');

        if (barStyle === 'solid') {
            var barColor = $('#milestone-bar-color').val();
            previewBar.css('background', barColor);
        } else if (barStyle === 'gradient') {
            var startColor = $('#milestone-gradient-start').val();
            var endColor = $('#milestone-gradient-end').val();
            previewBar.css('background', 'linear-gradient(to right, ' + startColor + ', ' + endColor + ')');
        }
    }

    // Update milestone preview when color inputs change
    $(document).on('change', '#milestone-bar-color, #milestone-gradient-start, #milestone-gradient-end', function() {
        updateMilestonePreview();
    });

    // Update milestone preview when bar style changes
    $(document).on('change', 'input[name="milestone_bar_style"]', function() {
        var barStyle = $(this).val();

        if (barStyle === 'solid') {
            $('.milestone-solid-color').show();
            $('.milestone-gradient-colors').hide();
        } else if (barStyle === 'gradient') {
            $('.milestone-solid-color').hide();
            $('.milestone-gradient-colors').show();
        }

        updateMilestonePreview();
    });
});