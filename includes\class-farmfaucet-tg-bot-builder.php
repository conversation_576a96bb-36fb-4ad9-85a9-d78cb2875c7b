<?php

/**
 * Telegram Bot Builder functionality for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.3
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Tg_Bot_Builder
 *
 * Handles Telegram Bot Builder functionality
 */
class Farmfaucet_Tg_Bot_Builder
{

    /**
     * Singleton instance
     *
     * @var Farmfaucet_Tg_Bot_Builder
     */
    private static $instance;

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Tg_Bot_Builder
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);

        // Register AJAX handlers
        add_action('wp_ajax_farmfaucet_test_tg_bot_token', [$this, 'ajax_test_bot_token']);
        add_action('wp_ajax_farmfaucet_save_tg_bot', [$this, 'ajax_save_bot']);
        add_action('wp_ajax_farmfaucet_delete_tg_bot', [$this, 'ajax_delete_bot']);
        add_action('wp_ajax_farmfaucet_save_tg_flow', [$this, 'ajax_save_flow']);
        add_action('wp_ajax_farmfaucet_delete_tg_flow', [$this, 'ajax_delete_flow']);

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Initialize database tables
        add_action('plugins_loaded', [$this, 'init_db']);

        // Add admin body class
        add_filter('admin_body_class', [$this, 'add_admin_body_class']);
    }

    /**
     * Initialize database tables
     */
    public function init_db()
    {
        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        Farmfaucet_Tg_Bot_DB::init_tables();
    }

    /**
     * Register settings for Telegram Bot Builder
     */
    public function register_settings()
    {
        // Bot Builder settings - Use separate group to avoid conflicts
        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_builder_enabled', [
            'sanitize_callback' => 'intval',
            'default' => 0
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_webhook_url', [
            'sanitize_callback' => 'esc_url_raw',
            'default' => ''
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_default_type', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'text'
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_captcha_type', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'hcaptcha'
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_captcha_sitekey', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => ''
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_bot_captcha_secret', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => ''
        ]);

        // Bot Login settings
        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_login_enabled', [
            'sanitize_callback' => 'intval',
            'default' => 0
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_login_bot', [
            'sanitize_callback' => 'absint',
            'default' => 0
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_login_redirect', [
            'sanitize_callback' => 'absint',
            'default' => 0
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_signup_redirect', [
            'sanitize_callback' => 'absint',
            'default' => 0
        ]);

        register_setting('farmfaucet_tg_bot_settings', 'farmfaucet_tg_otp_expiration', [
            'sanitize_callback' => 'absint',
            'default' => 5
        ]);
    }

    /**
     * Sanitize captcha secret
     *
     * @param string $secret Captcha secret to sanitize
     * @return string Sanitized secret
     */
    public function sanitize_captcha_secret($secret)
    {
        return Farmfaucet_Security::encrypt_api_key($secret);
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook Current admin page
     */
    public function enqueue_admin_assets($hook)
    {
        // Only load on plugin settings page
        if (strpos($hook, 'farmfaucet-settings') === false) {
            return;
        }

        // Enqueue jQuery UI
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-effects-core');

        // Enqueue jQuery UI CSS
        wp_enqueue_style(
            'jquery-ui-css',
            'https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css',
            [],
            '1.13.2'
        );

        // Enqueue jsPlumb for the flow builder
        wp_enqueue_script(
            'jsplumb',
            'https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js',
            ['jquery'],
            '2.15.6',
            true
        );

        // Enqueue SortableJS for drag and drop
        wp_enqueue_script(
            'sortable',
            'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
            [],
            '1.15.0',
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-modern.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our simple fix CSS
        wp_enqueue_style(
            'farmfaucet-telegram-bot-builder-fix',
            FARMFAUCET_URL . 'assets/css/telegram-bot-builder-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our button fix CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-button-fix',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-button-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our Create Bot Button Fix JS
        wp_enqueue_script(
            'farmfaucet-create-bot-button-fix',
            FARMFAUCET_URL . 'assets/js/create-bot-button-fix.js',
            ['jquery', 'jquery-ui-dialog'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder JS
        if (isset($_GET['page']) && $_GET['page'] === 'farmfaucet-tg-bot-builder') {
            // Use the new Bot Builder JS for the dedicated page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder-new',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder-new.js',
                ['jquery'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );

            // Localize script for the new Bot Builder
            wp_localize_script('farmfaucet-tg-bot-builder-new', 'farmfaucetTgBot', [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('farmfaucet_tg_bot_nonce'),
                'i18n' => [
                    'confirmDelete' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                    'confirmDeleteCommand' => __('Are you sure you want to delete this command? This action cannot be undone.', 'farmfaucet'),
                    'errorOccurred' => __('An error occurred. Please try again.', 'farmfaucet'),
                    'savingChanges' => __('Saving changes...', 'farmfaucet'),
                    'changesSaved' => __('Changes saved successfully.', 'farmfaucet'),
                    'testingToken' => __('Testing token...', 'farmfaucet'),
                    'tokenValid' => __('Token is valid!', 'farmfaucet'),
                    'tokenInvalid' => __('Token is invalid. Please check and try again.', 'farmfaucet')
                ]
            ]);
        } else {
            // Use the original Bot Builder JS for the settings page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder.js',
                ['jquery', 'jquery-ui-core', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-sortable', 'wp-util', 'react-flow'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );
        }

        // Localize script
        wp_localize_script('farmfaucet-tg-bot-builder', 'farmfaucetTgBotBuilder', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-tg-bot-builder-nonce'),
            'pluginUrl' => FARMFAUCET_URL,
            'i18n' => [
                'testSuccess' => __('Bot token is valid! Bot name: ', 'farmfaucet'),
                'testError' => __('Invalid bot token. Please check and try again.', 'farmfaucet'),
                'saveSuccess' => __('Bot saved successfully.', 'farmfaucet'),
                'saveError' => __('Error saving bot. Please try again.', 'farmfaucet'),
                'deleteConfirm' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                'deleteSuccess' => __('Bot deleted successfully.', 'farmfaucet'),
                'deleteError' => __('Error deleting bot. Please try again.', 'farmfaucet'),
                'flowSaveSuccess' => __('Flow saved successfully.', 'farmfaucet'),
                'flowSaveError' => __('Error saving flow. Please try again.', 'farmfaucet'),
                'flowDeleteConfirm' => __('Are you sure you want to delete this flow? This action cannot be undone.', 'farmfaucet'),
                'flowDeleteSuccess' => __('Flow deleted successfully.', 'farmfaucet'),
                'flowDeleteError' => __('Error deleting flow. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Add admin body class
     *
     * @param string $classes Admin body classes
     * @return string Modified admin body classes
     */
    public function add_admin_body_class($classes)
    {
        // Check if we're on the plugin settings page
        if (isset($_GET['page']) && $_GET['page'] === 'farmfaucet-settings') {
            // Add class for TG Bot Builder tab
            if (isset($_GET['tab']) && $_GET['tab'] === 'tg_bot_builder') {
                $classes .= ' farmfaucet-tg-bot-builder-page';
            }
        }

        return $classes;
    }

    /**
     * AJAX handler for testing bot token
     */
    public function ajax_test_bot_token()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['token']) || empty($_POST['token'])) {
            wp_send_json_error(['message' => __('Bot token is required.', 'farmfaucet')]);
        }

        $token = sanitize_text_field($_POST['token']);

        // Test the token by making a request to the Telegram API
        $response = wp_remote_get("https://api.telegram.org/bot{$token}/getMe");

        if (is_wp_error($response)) {
            wp_send_json_error(['message' => $response->get_error_message()]);
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['ok']) || $data['ok'] !== true) {
            wp_send_json_error(['message' => __('Invalid bot token. Please check and try again.', 'farmfaucet')]);
        }

        // Return bot info
        wp_send_json_success([
            'message' => __('Bot token is valid!', 'farmfaucet'),
            'bot' => $data['result']
        ]);
    }

    /**
     * AJAX handler for saving bot
     */
    public function ajax_save_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (
            !isset($_POST['bot_name']) || empty($_POST['bot_name']) ||
            !isset($_POST['bot_token']) || empty($_POST['bot_token']) ||
            !isset($_POST['bot_username']) || empty($_POST['bot_username']) ||
            !isset($_POST['bot_type']) || empty($_POST['bot_type'])
        ) {
            wp_send_json_error(['message' => __('All fields are required.', 'farmfaucet')]);
        }

        // Sanitize input
        $bot_data = [
            'bot_name' => sanitize_text_field($_POST['bot_name']),
            'bot_token' => sanitize_text_field($_POST['bot_token']),
            'bot_username' => sanitize_text_field($_POST['bot_username']),
            'bot_type' => sanitize_text_field($_POST['bot_type']),
            'webhook_url' => isset($_POST['webhook_url']) ? esc_url_raw($_POST['webhook_url']) : '',
            'settings' => isset($_POST['settings']) ? wp_json_encode($_POST['settings']) : '',
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        ];

        // Check if we're updating or creating
        $bot_id = isset($_POST['bot_id']) ? intval($_POST['bot_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($bot_id > 0) {
            // Update existing bot
            $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, $bot_data);
            $message = __('Bot updated successfully.', 'farmfaucet');
        } else {
            // Create new bot
            $result = Farmfaucet_Tg_Bot_DB::add_bot($bot_data);
            $bot_id = $result;
            $message = __('Bot created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(['message' => __('Error saving bot. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => $message,
            'bot_id' => $bot_id
        ]);
    }

    /**
     * AJAX handler for deleting bot
     */
    public function ajax_delete_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(['message' => __('Bot ID is required.', 'farmfaucet')]);
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete bot
        $result = Farmfaucet_Tg_Bot_DB::delete_bot($bot_id);

        if (!$result) {
            wp_send_json_error(['message' => __('Error deleting bot. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => __('Bot deleted successfully.', 'farmfaucet')
        ]);
    }

    /**
     * AJAX handler for saving flow
     */
    public function ajax_save_flow()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (
            !isset($_POST['bot_id']) || empty($_POST['bot_id']) ||
            !isset($_POST['flow_name']) || empty($_POST['flow_name']) ||
            !isset($_POST['flow_data']) || empty($_POST['flow_data'])
        ) {
            wp_send_json_error(['message' => __('All fields are required.', 'farmfaucet')]);
        }

        // Sanitize input
        $flow_data = [
            'bot_id' => intval($_POST['bot_id']),
            'flow_name' => sanitize_text_field($_POST['flow_name']),
            'flow_data' => wp_kses_post($_POST['flow_data']),
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        ];

        // Check if we're updating or creating
        $flow_id = isset($_POST['flow_id']) ? intval($_POST['flow_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($flow_id > 0) {
            // Update existing flow
            $result = Farmfaucet_Tg_Bot_DB::update_flow($flow_id, $flow_data);
            $message = __('Flow updated successfully.', 'farmfaucet');
        } else {
            // Create new flow
            $result = Farmfaucet_Tg_Bot_DB::add_flow($flow_data);
            $flow_id = $result;
            $message = __('Flow created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(['message' => __('Error saving flow. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => $message,
            'flow_id' => $flow_id
        ]);
    }

    /**
     * AJAX handler for deleting flow
     */
    public function ajax_delete_flow()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['flow_id']) || empty($_POST['flow_id'])) {
            wp_send_json_error(['message' => __('Flow ID is required.', 'farmfaucet')]);
        }

        $flow_id = intval($_POST['flow_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete flow
        $result = Farmfaucet_Tg_Bot_DB::delete_flow($flow_id);

        if (!$result) {
            wp_send_json_error(['message' => __('Error deleting flow. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => __('Flow deleted successfully.', 'farmfaucet')
        ]);
    }

    /**
     * Render admin settings page
     */
    public static function render_admin_page()
    {
        // Include the modern bot builder template
        include(FARMFAUCET_DIR . 'templates/admin/bot-builder-modern.php');
    }




                                            <div class="form-group">
                                                <label for="farmfaucet_tg_login_bot">
                                                    <?php _e('Select Telegram Bot for Login', 'farmfaucet'); ?>
                                                </label>
                                                <select id="farmfaucet_tg_login_bot" name="farmfaucet_tg_login_bot">
                                                    <option value=""><?php _e('Select a bot', 'farmfaucet'); ?></option>
                                                    <?php foreach ($bots as $bot): ?>
                                                        <option value="<?php echo esc_attr($bot['id']); ?>" <?php selected(get_option('farmfaucet_tg_login_bot'), $bot['id']); ?>>
                                                            <?php echo esc_html($bot['bot_name']); ?> (@<?php echo esc_html($bot['bot_username']); ?>)
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <p class="description">
                                                    <?php _e('Select which Telegram bot to use for sending OTP codes during login and signup.', 'farmfaucet'); ?>
                                                </p>
                                            </div>

                                            <div class="form-group">
                                                <label for="farmfaucet_tg_login_redirect">
                                                    <?php _e('Login Redirect Page', 'farmfaucet'); ?>
                                                </label>
                                                <select id="farmfaucet_tg_login_redirect" name="farmfaucet_tg_login_redirect">
                                                    <option value=""><?php _e('Home Page', 'farmfaucet'); ?></option>
                                                    <?php
                                                    $pages = get_pages();
                                                    foreach ($pages as $page):
                                                    ?>
                                                        <option value="<?php echo esc_attr($page->ID); ?>" <?php selected(get_option('farmfaucet_tg_login_redirect'), $page->ID); ?>>
                                                            <?php echo esc_html($page->post_title); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <p class="description">
                                                    <?php _e('Select the page to redirect users to after successful login.', 'farmfaucet'); ?>
                                                </p>
                                            </div>

                                            <div class="form-group">
                                                <label for="farmfaucet_tg_signup_redirect">
                                                    <?php _e('Signup Redirect Page', 'farmfaucet'); ?>
                                                </label>
                                                <select id="farmfaucet_tg_signup_redirect" name="farmfaucet_tg_signup_redirect">
                                                    <option value=""><?php _e('Home Page', 'farmfaucet'); ?></option>
                                                    <?php
                                                    foreach ($pages as $page):
                                                    ?>
                                                        <option value="<?php echo esc_attr($page->ID); ?>" <?php selected(get_option('farmfaucet_tg_signup_redirect'), $page->ID); ?>>
                                                            <?php echo esc_html($page->post_title); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <p class="description">
                                                    <?php _e('Select the page to redirect users to after successful signup.', 'farmfaucet'); ?>
                                                </p>
                                            </div>

                                            <div class="form-group">
                                                <label for="farmfaucet_tg_otp_expiration">
                                                    <?php _e('OTP Code Expiration (minutes)', 'farmfaucet'); ?>
                                                </label>
                                                <input type="number" id="farmfaucet_tg_otp_expiration" name="farmfaucet_tg_otp_expiration" min="1" max="60" value="<?php echo esc_attr(get_option('farmfaucet_tg_otp_expiration', 5)); ?>">
                                                <p class="description">
                                                    <?php _e('Set how long the OTP verification code remains valid (in minutes).', 'farmfaucet'); ?>
                                                </p>
                                            </div>

                                            <div class="form-actions">
                                                <?php submit_button(__('Save Settings', 'farmfaucet'), 'primary', 'submit', false); ?>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="farmfaucet-admin-card">
                                    <div class="card-header">
                                        <h3><?php _e('Login Form Shortcode', 'farmfaucet'); ?></h3>
                                    </div>
                                    <div class="card-body">
                                        <p class="description">
                                            <?php _e('Use this shortcode to display a Telegram login form on your website.', 'farmfaucet'); ?>
                                        </p>

                                        <div class="shortcode-container">
                                            <code>[farmfaucet_tg_login]</code>
                                            <button class="copy-shortcode button" data-shortcode="[farmfaucet_tg_login]">
                                                <?php _e('Copy', 'farmfaucet'); ?>
                                            </button>
                                        </div>

                                        <p class="description">
                                            <?php _e('You can customize the login form with these parameters:', 'farmfaucet'); ?>
                                        </p>

                                        <table class="wp-list-table widefat fixed striped shortcode-params">
                                            <thead>
                                                <tr>
                                                    <th><?php _e('Parameter', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Description', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Default', 'farmfaucet'); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><code>button_text</code></td>
                                                    <td><?php _e('Custom text for the login button', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Login with Telegram', 'farmfaucet'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><code>redirect</code></td>
                                                    <td><?php _e('Custom redirect URL after login', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Global setting', 'farmfaucet'); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <div class="shortcode-example">
                                            <p><strong><?php _e('Example:', 'farmfaucet'); ?></strong></p>
                                            <code>[farmfaucet_tg_login button_text="Sign In" redirect="https://example.com/dashboard"]</code>
                                        </div>
                                    </div>
                                </div>

                                <div class="farmfaucet-admin-card">
                                    <div class="card-header">
                                        <h3><?php _e('Signup Form Shortcode', 'farmfaucet'); ?></h3>
                                    </div>
                                    <div class="card-body">
                                        <p class="description">
                                            <?php _e('Use this shortcode to display a Telegram signup form on your website.', 'farmfaucet'); ?>
                                        </p>

                                        <div class="shortcode-container">
                                            <code>[farmfaucet_tg_signup]</code>
                                            <button class="copy-shortcode button" data-shortcode="[farmfaucet_tg_signup]">
                                                <?php _e('Copy', 'farmfaucet'); ?>
                                            </button>
                                        </div>

                                        <p class="description">
                                            <?php _e('You can customize the signup form with these parameters:', 'farmfaucet'); ?>
                                        </p>

                                        <table class="wp-list-table widefat fixed striped shortcode-params">
                                            <thead>
                                                <tr>
                                                    <th><?php _e('Parameter', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Description', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Default', 'farmfaucet'); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><code>button_text</code></td>
                                                    <td><?php _e('Custom text for the signup button', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Sign Up with Telegram', 'farmfaucet'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><code>redirect</code></td>
                                                    <td><?php _e('Custom redirect URL after signup', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Global setting', 'farmfaucet'); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <div class="shortcode-example">
                                            <p><strong><?php _e('Example:', 'farmfaucet'); ?></strong></p>
                                            <code>[farmfaucet_tg_signup button_text="Create Account" redirect="https://example.com/welcome"]</code>
                                        </div>
                                    </div>
                                </div>

                                <div class="farmfaucet-admin-card">
                                    <div class="card-header">
                                        <h3><?php _e('Logout Button Shortcode', 'farmfaucet'); ?></h3>
                                    </div>
                                    <div class="card-body">
                                        <p class="description">
                                            <?php _e('Use this shortcode to display a logout button for logged-in users.', 'farmfaucet'); ?>
                                        </p>

                                        <div class="shortcode-container">
                                            <code>[farmfaucet_tg_logout]</code>
                                            <button class="copy-shortcode button" data-shortcode="[farmfaucet_tg_logout]">
                                                <?php _e('Copy', 'farmfaucet'); ?>
                                            </button>
                                        </div>

                                        <p class="description">
                                            <?php _e('You can customize the logout button with these parameters:', 'farmfaucet'); ?>
                                        </p>

                                        <table class="wp-list-table widefat fixed striped shortcode-params">
                                            <thead>
                                                <tr>
                                                    <th><?php _e('Parameter', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Description', 'farmfaucet'); ?></th>
                                                    <th><?php _e('Default', 'farmfaucet'); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><code>button_text</code></td>
                                                    <td><?php _e('Custom text for the logout button', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Logout', 'farmfaucet'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><code>redirect</code></td>
                                                    <td><?php _e('Custom redirect URL after logout', 'farmfaucet'); ?></td>
                                                    <td><?php _e('Home page', 'farmfaucet'); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <div class="shortcode-example">
                                            <p><strong><?php _e('Example:', 'farmfaucet'); ?></strong></p>
                                            <code>[farmfaucet_tg_logout button_text="Sign Out" redirect="https://example.com"]</code>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Global Settings Tab -->
                            <div id="tg-bot-settings" class="farmfaucet-tg-bot-tab">
                                <form method="post" action="options.php" class="farmfaucet-form">
                                    <?php settings_fields('farmfaucet_settings'); ?>

                                    <div class="form-group">
                                        <label for="farmfaucet_tg_bot_builder_enabled"><?php _e('Enable Bot Builder', 'farmfaucet'); ?></label>
                                        <label class="switch">
                                            <input type="checkbox" id="farmfaucet_tg_bot_builder_enabled" name="farmfaucet_tg_bot_builder_enabled" value="1" <?php checked(get_option('farmfaucet_tg_bot_builder_enabled'), 1); ?>>
                                            <span class="slider round"></span>
                                        </label>
                                        <span class="status-label"><?php echo get_option('farmfaucet_tg_bot_builder_enabled') ? __('Enabled', 'farmfaucet') : __('Disabled', 'farmfaucet'); ?></span>
                                        <p class="description"><?php _e('Enable or disable the Telegram Bot Builder functionality.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group">
                                        <label for="farmfaucet_tg_bot_default_type"><?php _e('Default Bot Type', 'farmfaucet'); ?></label>
                                        <select id="farmfaucet_tg_bot_default_type" name="farmfaucet_tg_bot_default_type">
                                            <option value="text" <?php selected(get_option('farmfaucet_tg_bot_default_type'), 'text'); ?>><?php _e('Text Bot', 'farmfaucet'); ?></option>
                                            <option value="chat" <?php selected(get_option('farmfaucet_tg_bot_default_type'), 'chat'); ?>><?php _e('Chat Bot', 'farmfaucet'); ?></option>
                                        </select>
                                        <p class="description"><?php _e('Select the default bot type for new bots.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group">
                                        <label for="farmfaucet_tg_bot_captcha_type"><?php _e('CAPTCHA Type', 'farmfaucet'); ?></label>
                                        <select id="farmfaucet_tg_bot_captcha_type" name="farmfaucet_tg_bot_captcha_type">
                                            <option value="hcaptcha" <?php selected(get_option('farmfaucet_tg_bot_captcha_type'), 'hcaptcha'); ?>><?php _e('hCaptcha', 'farmfaucet'); ?></option>
                                            <option value="recaptcha" <?php selected(get_option('farmfaucet_tg_bot_captcha_type'), 'recaptcha'); ?>><?php _e('reCAPTCHA', 'farmfaucet'); ?></option>
                                            <option value="custom" <?php selected(get_option('farmfaucet_tg_bot_captcha_type'), 'custom'); ?>><?php _e('Custom CAPTCHA', 'farmfaucet'); ?></option>
                                        </select>
                                        <p class="description"><?php _e('Select the CAPTCHA type to use for bot verification.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group captcha-settings hcaptcha-settings" <?php echo get_option('farmfaucet_tg_bot_captcha_type') !== 'hcaptcha' ? 'style="display: none;"' : ''; ?>>
                                        <label for="farmfaucet_tg_bot_captcha_sitekey"><?php _e('hCaptcha Site Key', 'farmfaucet'); ?></label>
                                        <input type="text" id="farmfaucet_tg_bot_captcha_sitekey" name="farmfaucet_tg_bot_captcha_sitekey" class="regular-text" value="<?php echo esc_attr(get_option('farmfaucet_tg_bot_captcha_sitekey')); ?>">
                                        <p class="description"><?php _e('Enter your hCaptcha site key.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group captcha-settings hcaptcha-settings" <?php echo get_option('farmfaucet_tg_bot_captcha_type') !== 'hcaptcha' ? 'style="display: none;"' : ''; ?>>
                                        <label for="farmfaucet_tg_bot_captcha_secret"><?php _e('hCaptcha Secret Key', 'farmfaucet'); ?></label>
                                        <input type="password" id="farmfaucet_tg_bot_captcha_secret" name="farmfaucet_tg_bot_captcha_secret" class="regular-text" value="<?php echo esc_attr(Farmfaucet_Security::decrypt_api_key(get_option('farmfaucet_tg_bot_captcha_secret'))); ?>">
                                        <p class="description"><?php _e('Enter your hCaptcha secret key.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group captcha-settings recaptcha-settings" <?php echo get_option('farmfaucet_tg_bot_captcha_type') !== 'recaptcha' ? 'style="display: none;"' : ''; ?>>
                                        <label for="farmfaucet_tg_bot_captcha_sitekey"><?php _e('reCAPTCHA Site Key', 'farmfaucet'); ?></label>
                                        <input type="text" id="farmfaucet_tg_bot_captcha_sitekey" name="farmfaucet_tg_bot_captcha_sitekey" class="regular-text" value="<?php echo esc_attr(get_option('farmfaucet_tg_bot_captcha_sitekey')); ?>">
                                        <p class="description"><?php _e('Enter your reCAPTCHA site key.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-group captcha-settings recaptcha-settings" <?php echo get_option('farmfaucet_tg_bot_captcha_type') !== 'recaptcha' ? 'style="display: none;"' : ''; ?>>
                                        <label for="farmfaucet_tg_bot_captcha_secret"><?php _e('reCAPTCHA Secret Key', 'farmfaucet'); ?></label>
                                        <input type="password" id="farmfaucet_tg_bot_captcha_secret" name="farmfaucet_tg_bot_captcha_secret" class="regular-text" value="<?php echo esc_attr(Farmfaucet_Security::decrypt_api_key(get_option('farmfaucet_tg_bot_captcha_secret'))); ?>">
                                        <p class="description"><?php _e('Enter your reCAPTCHA secret key.', 'farmfaucet'); ?></p>
                                    </div>

                                    <div class="form-actions">
                                        <?php submit_button(__('Save Settings', 'farmfaucet'), 'primary', 'submit', false); ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flow Builder Section -->
            <div class="farmfaucet-admin-card flow-builder-card" style="display: none;">
                <div class="card-header">
                    <h3><?php _e('Flow Builder', 'farmfaucet'); ?> - <span class="flow-builder-bot-name"></span></h3>
                    <button type="button" class="button close-flow-builder"><?php _e('Back to Bots', 'farmfaucet'); ?></button>
                </div>
                <div class="card-body">
                    <div class="farmfaucet-tg-bot-tabs">
                        <ul class="farmfaucet-tg-bot-tabs-nav">
                            <li class="active"><a href="#flow-list"><?php _e('Flow List', 'farmfaucet'); ?></a></li>
                            <li><a href="#flow-editor"><?php _e('Flow Editor', 'farmfaucet'); ?></a></li>
                            <li><a href="#command-editor"><?php _e('Commands', 'farmfaucet'); ?></a></li>
                            <li class="chat-bot-tab" style="display: none;"><a href="#moderation"><?php _e('Moderation', 'farmfaucet'); ?></a></li>
                        </ul>

                        <div class="farmfaucet-tg-bot-tabs-content">
                            <!-- Flow List Tab -->
                            <div id="flow-list" class="farmfaucet-tg-bot-tab active">
                                <div class="flow-list-container">
                                    <p class="description"><?php _e('Manage your bot flows. Flows define how your bot responds to user interactions.', 'farmfaucet'); ?></p>

                                    <div class="flow-list-actions">
                                        <button type="button" class="button button-primary create-flow"><?php _e('Create New Flow', 'farmfaucet'); ?></button>
                                        <button type="button" class="button use-template"><?php _e('Use Template', 'farmfaucet'); ?></button>
                                    </div>

                                    <div class="flow-list-table-container">
                                        <!-- Flow list will be loaded here via AJAX -->
                                    </div>
                                </div>

                                <!-- Template Library Section -->
                                <div class="template-library-section" style="display: none;">
                                    <div class="section-header">
                                        <h3><?php _e('Template Library', 'farmfaucet'); ?></h3>
                                        <button type="button" class="button close-template-library"><?php _e('Back to Flows', 'farmfaucet'); ?></button>
                                    </div>

                                    <p class="description"><?php _e('Choose a pre-built template to quickly create a flow for your bot.', 'farmfaucet'); ?></p>

                                    <div class="template-categories">
                                        <button type="button" class="template-category active" data-category="all"><?php _e('All Templates', 'farmfaucet'); ?></button>
                                        <button type="button" class="template-category" data-category="welcome"><?php _e('Welcome', 'farmfaucet'); ?></button>
                                        <button type="button" class="template-category" data-category="faucet"><?php _e('Faucet', 'farmfaucet'); ?></button>
                                        <button type="button" class="template-category" data-category="support"><?php _e('Support', 'farmfaucet'); ?></button>
                                        <button type="button" class="template-category" data-category="moderation"><?php _e('Moderation', 'farmfaucet'); ?></button>
                                    </div>

                                    <div class="template-grid">
                                        <!-- Welcome Templates -->
                                        <div class="template-card" data-category="welcome">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-format-chat"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Basic Welcome', 'farmfaucet'); ?></h4>
                                                <p><?php _e('A simple welcome message with basic instructions.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="welcome_basic"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="welcome_basic"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <div class="template-card" data-category="welcome">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-admin-users"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('User Registration', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Welcome flow with user registration and profile setup.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="welcome_registration"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="welcome_registration"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <!-- Faucet Templates -->
                                        <div class="template-card" data-category="faucet">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-money-alt"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Basic Faucet', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Simple faucet flow with CAPTCHA verification.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="faucet_basic"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="faucet_basic"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <div class="template-card" data-category="faucet">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-chart-area"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Advanced Faucet', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Faucet with user balance, referrals, and withdrawal options.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="faucet_advanced"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="faucet_advanced"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <!-- Support Templates -->
                                        <div class="template-card" data-category="support">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-editor-help"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('FAQ Bot', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Frequently asked questions with automated responses.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="support_faq"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="support_faq"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <div class="template-card" data-category="support">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-tickets-alt"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Support Ticket', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Support ticket system with admin notification.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="support_ticket"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="support_ticket"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <!-- Moderation Templates -->
                                        <div class="template-card" data-category="moderation">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-shield"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Basic Moderation', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Simple moderation with spam detection and user warnings.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="moderation_basic"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="moderation_basic"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>

                                        <div class="template-card" data-category="moderation">
                                            <div class="template-preview">
                                                <i class="dashicons dashicons-hammer"></i>
                                            </div>
                                            <div class="template-info">
                                                <h4><?php _e('Advanced Moderation', 'farmfaucet'); ?></h4>
                                                <p><?php _e('Advanced moderation with user levels, auto-moderation, and admin controls.', 'farmfaucet'); ?></p>
                                            </div>
                                            <div class="template-actions">
                                                <button type="button" class="button use-this-template" data-template="moderation_advanced"><?php _e('Use This Template', 'farmfaucet'); ?></button>
                                                <button type="button" class="button preview-template" data-template="moderation_advanced"><?php _e('Preview', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Flow Editor Tab -->
                            <div id="flow-editor" class="farmfaucet-tg-bot-tab">
                                <div class="flow-editor-container">
                                    <form id="farmfaucet-flow-form" class="farmfaucet-form">
                                        <input type="hidden" id="flow-id" name="flow_id" value="0">
                                        <input type="hidden" id="flow-bot-id" name="bot_id" value="0">

                                        <div class="form-group">
                                            <label for="flow-name"><?php _e('Flow Name', 'farmfaucet'); ?></label>
                                            <input type="text" id="flow-name" name="flow_name" class="regular-text" required>
                                            <p class="description"><?php _e('Enter a name for this flow.', 'farmfaucet'); ?></p>
                                        </div>

                                        <div class="form-group">
                                            <label for="flow-active"><?php _e('Status', 'farmfaucet'); ?></label>
                                            <label class="switch">
                                                <input type="checkbox" id="flow-active" name="is_active" value="1" checked>
                                                <span class="slider round"></span>
                                            </label>
                                            <span class="status-label"><?php _e('Active', 'farmfaucet'); ?></span>
                                            <p class="description"><?php _e('Enable or disable this flow.', 'farmfaucet'); ?></p>
                                        </div>

                                        <div class="flow-editor-canvas-container">
                                            <div class="flow-editor-sidebar">
                                                <h4><?php _e('Elements', 'farmfaucet'); ?></h4>
                                                <div class="flow-elements">
                                                    <div class="flow-element" data-type="message">
                                                        <i class="dashicons dashicons-format-chat"></i>
                                                        <span><?php _e('Message', 'farmfaucet'); ?></span>
                                                    </div>
                                                    <div class="flow-element" data-type="button">
                                                        <i class="dashicons dashicons-button"></i>
                                                        <span><?php _e('Button', 'farmfaucet'); ?></span>
                                                    </div>
                                                    <div class="flow-element" data-type="condition">
                                                        <i class="dashicons dashicons-randomize"></i>
                                                        <span><?php _e('Condition', 'farmfaucet'); ?></span>
                                                    </div>
                                                    <div class="flow-element" data-type="captcha">
                                                        <i class="dashicons dashicons-shield"></i>
                                                        <span><?php _e('CAPTCHA', 'farmfaucet'); ?></span>
                                                    </div>
                                                    <div class="flow-element" data-type="action">
                                                        <i class="dashicons dashicons-admin-generic"></i>
                                                        <span><?php _e('Action', 'farmfaucet'); ?></span>
                                                    </div>
                                                    <div class="flow-element chat-bot-element" data-type="moderation" style="display: none;">
                                                        <i class="dashicons dashicons-hammer"></i>
                                                        <span><?php _e('Moderation', 'farmfaucet'); ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flow-editor-canvas" id="flow-editor-canvas">
                                                <!-- Flow canvas will be rendered here -->
                                            </div>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" class="button button-primary"><?php _e('Save Flow', 'farmfaucet'); ?></button>
                                            <button type="button" id="cancel-edit-flow" class="button"><?php _e('Cancel', 'farmfaucet'); ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Command Editor Tab -->
                            <div id="command-editor" class="farmfaucet-tg-bot-tab">
                                <p class="description"><?php _e('Manage your bot commands. Commands are triggered when a user sends a specific message to your bot.', 'farmfaucet'); ?></p>

                                <div class="command-list-actions">
                                    <button type="button" class="button button-primary create-command"><?php _e('Add New Command', 'farmfaucet'); ?></button>
                                </div>

                                <div class="command-list-container">
                                    <table class="wp-list-table widefat fixed striped command-table">
                                        <thead>
                                            <tr>
                                                <th><?php _e('Command', 'farmfaucet'); ?></th>
                                                <th><?php _e('Description', 'farmfaucet'); ?></th>
                                                <th><?php _e('Response Type', 'farmfaucet'); ?></th>
                                                <th><?php _e('Status', 'farmfaucet'); ?></th>
                                                <th><?php _e('Actions', 'farmfaucet'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Command list will be populated dynamically -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Command Preview Section -->
                                <div class="command-preview-section">
                                    <h3><?php _e('Command Preview', 'farmfaucet'); ?></h3>
                                    <p class="description"><?php _e('See how your command will appear in Telegram.', 'farmfaucet'); ?></p>

                                    <div class="telegram-preview">
                                        <div class="telegram-header">
                                            <div class="telegram-avatar">
                                                <i class="dashicons dashicons-admin-users"></i>
                                            </div>
                                            <div class="telegram-info">
                                                <div class="telegram-name">Your Bot</div>
                                                <div class="telegram-status">online</div>
                                            </div>
                                        </div>
                                        <div class="telegram-chat">
                                            <div class="telegram-message user-message">
                                                <div class="message-content">/start</div>
                                                <div class="message-time">12:34 PM</div>
                                            </div>
                                            <div class="telegram-message bot-message">
                                                <div class="message-content">Welcome to the bot! How can I help you today?</div>
                                                <div class="message-time">12:34 PM</div>
                                            </div>
                                            <div class="telegram-keyboard">
                                                <div class="telegram-button">Help</div>
                                                <div class="telegram-button">About</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="preview-controls">
                                        <div class="form-group">
                                            <label for="preview-command"><?php _e('Test Command', 'farmfaucet'); ?></label>
                                            <div class="preview-input-container">
                                                <input type="text" id="preview-command" placeholder="/command">
                                                <button type="button" id="send-preview-command" class="button"><?php _e('Send', 'farmfaucet'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Moderation Tab (for Chat Bots) -->
                            <div id="moderation" class="farmfaucet-tg-bot-tab">
                                <p class="description"><?php _e('Configure moderation settings for your chat bot. These settings control how your bot moderates group chats.', 'farmfaucet'); ?></p>

                                <!-- Moderation settings will be implemented here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    }
}
