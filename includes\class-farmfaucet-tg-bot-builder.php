<?php

/**
 * Telegram Bot Builder functionality for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.3
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Tg_Bot_Builder
 *
 * Handles Telegram Bot Builder functionality
 */
class Farmfaucet_Tg_Bot_Builder
{

    /**
     * Singleton instance
     *
     * @var Farmfaucet_Tg_Bot_Builder
     */
    private static $instance;

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Tg_Bot_Builder
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Settings registration now handled by Farmfaucet_Settings_Manager

        // Register AJAX handlers
        add_action('wp_ajax_farmfaucet_test_tg_bot_token', [$this, 'ajax_test_bot_token']);
        add_action('wp_ajax_farmfaucet_save_tg_bot', [$this, 'ajax_save_bot']);
        add_action('wp_ajax_farmfaucet_delete_tg_bot', [$this, 'ajax_delete_bot']);
        add_action('wp_ajax_farmfaucet_save_tg_flow', [$this, 'ajax_save_flow']);
        add_action('wp_ajax_farmfaucet_delete_tg_flow', [$this, 'ajax_delete_flow']);

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Initialize database tables
        add_action('plugins_loaded', [$this, 'init_db']);

        // Add admin body class
        add_filter('admin_body_class', [$this, 'add_admin_body_class']);
    }

    /**
     * Initialize database tables
     */
    public function init_db()
    {
        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        Farmfaucet_Tg_Bot_DB::init_tables();
    }

    /**
     * Settings registration now handled by Farmfaucet_Settings_Manager
     */
    public function register_settings()
    {
        // All settings registration is now handled by Farmfaucet_Settings_Manager
        // to prevent conflicts and ensure consistent saving
    }

    /**
     * Sanitize captcha secret
     *
     * @param string $secret Captcha secret to sanitize
     * @return string Sanitized secret
     */
    public function sanitize_captcha_secret($secret)
    {
        return Farmfaucet_Security::encrypt_api_key($secret);
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook Current admin page
     */
    public function enqueue_admin_assets($hook)
    {
        // Only load on plugin settings page
        if (strpos($hook, 'farmfaucet-settings') === false) {
            return;
        }

        // Enqueue jQuery UI
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-effects-core');

        // Enqueue jQuery UI CSS
        wp_enqueue_style(
            'jquery-ui-css',
            'https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css',
            [],
            '1.13.2'
        );

        // Enqueue jsPlumb for the flow builder
        wp_enqueue_script(
            'jsplumb',
            'https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js',
            ['jquery'],
            '2.15.6',
            true
        );

        // Enqueue SortableJS for drag and drop
        wp_enqueue_script(
            'sortable',
            'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
            [],
            '1.15.0',
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-modern.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our simple fix CSS
        wp_enqueue_style(
            'farmfaucet-telegram-bot-builder-fix',
            FARMFAUCET_URL . 'assets/css/telegram-bot-builder-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our button fix CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-button-fix',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-button-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our Create Bot Button Fix JS
        wp_enqueue_script(
            'farmfaucet-create-bot-button-fix',
            FARMFAUCET_URL . 'assets/js/create-bot-button-fix.js',
            ['jquery', 'jquery-ui-dialog'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder JS
        if (isset($_GET['page']) && $_GET['page'] === 'farmfaucet-tg-bot-builder') {
            // Use the new Bot Builder JS for the dedicated page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder-new',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder-new.js',
                ['jquery'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );

            // Localize script for the new Bot Builder
            wp_localize_script('farmfaucet-tg-bot-builder-new', 'farmfaucetTgBot', [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('farmfaucet_tg_bot_nonce'),
                'i18n' => [
                    'confirmDelete' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                    'confirmDeleteCommand' => __('Are you sure you want to delete this command? This action cannot be undone.', 'farmfaucet'),
                    'errorOccurred' => __('An error occurred. Please try again.', 'farmfaucet'),
                    'savingChanges' => __('Saving changes...', 'farmfaucet'),
                    'changesSaved' => __('Changes saved successfully.', 'farmfaucet'),
                    'testingToken' => __('Testing token...', 'farmfaucet'),
                    'tokenValid' => __('Token is valid!', 'farmfaucet'),
                    'tokenInvalid' => __('Token is invalid. Please check and try again.', 'farmfaucet')
                ]
            ]);
        } else {
            // Use the original Bot Builder JS for the settings page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder.js',
                ['jquery', 'jquery-ui-core', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-sortable', 'wp-util', 'react-flow'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );
        }

        // Localize script
        wp_localize_script('farmfaucet-tg-bot-builder', 'farmfaucetTgBotBuilder', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-tg-bot-builder-nonce'),
            'pluginUrl' => FARMFAUCET_URL,
            'i18n' => [
                'testSuccess' => __('Bot token is valid! Bot name: ', 'farmfaucet'),
                'testError' => __('Invalid bot token. Please check and try again.', 'farmfaucet'),
                'saveSuccess' => __('Bot saved successfully.', 'farmfaucet'),
                'saveError' => __('Error saving bot. Please try again.', 'farmfaucet'),
                'deleteConfirm' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                'deleteSuccess' => __('Bot deleted successfully.', 'farmfaucet'),
                'deleteError' => __('Error deleting bot. Please try again.', 'farmfaucet'),
                'flowSaveSuccess' => __('Flow saved successfully.', 'farmfaucet'),
                'flowSaveError' => __('Error saving flow. Please try again.', 'farmfaucet'),
                'flowDeleteConfirm' => __('Are you sure you want to delete this flow? This action cannot be undone.', 'farmfaucet'),
                'flowDeleteSuccess' => __('Flow deleted successfully.', 'farmfaucet'),
                'flowDeleteError' => __('Error deleting flow. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Add admin body class
     *
     * @param string $classes Admin body classes
     * @return string Modified admin body classes
     */
    public function add_admin_body_class($classes)
    {
        // Check if we're on the plugin settings page
        if (isset($_GET['page']) && $_GET['page'] === 'farmfaucet-settings') {
            // Add class for TG Bot Builder tab
            if (isset($_GET['tab']) && $_GET['tab'] === 'tg_bot_builder') {
                $classes .= ' farmfaucet-tg-bot-builder-page';
            }
        }

        return $classes;
    }

    /**
     * AJAX handler for testing bot token
     */
    public function ajax_test_bot_token()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['token']) || empty($_POST['token'])) {
            wp_send_json_error(['message' => __('Bot token is required.', 'farmfaucet')]);
        }

        $token = sanitize_text_field($_POST['token']);

        // Test the token by making a request to the Telegram API
        $response = wp_remote_get("https://api.telegram.org/bot{$token}/getMe");

        if (is_wp_error($response)) {
            wp_send_json_error(['message' => $response->get_error_message()]);
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['ok']) || $data['ok'] !== true) {
            wp_send_json_error(['message' => __('Invalid bot token. Please check and try again.', 'farmfaucet')]);
        }

        // Return bot info
        wp_send_json_success([
            'message' => __('Bot token is valid!', 'farmfaucet'),
            'bot' => $data['result']
        ]);
    }

    /**
     * AJAX handler for saving bot
     */
    public function ajax_save_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (
            !isset($_POST['bot_name']) || empty($_POST['bot_name']) ||
            !isset($_POST['bot_token']) || empty($_POST['bot_token']) ||
            !isset($_POST['bot_username']) || empty($_POST['bot_username']) ||
            !isset($_POST['bot_type']) || empty($_POST['bot_type'])
        ) {
            wp_send_json_error(['message' => __('All fields are required.', 'farmfaucet')]);
        }

        // Sanitize input
        $bot_data = [
            'bot_name' => sanitize_text_field($_POST['bot_name']),
            'bot_token' => sanitize_text_field($_POST['bot_token']),
            'bot_username' => sanitize_text_field($_POST['bot_username']),
            'bot_type' => sanitize_text_field($_POST['bot_type']),
            'webhook_url' => isset($_POST['webhook_url']) ? esc_url_raw($_POST['webhook_url']) : '',
            'settings' => isset($_POST['settings']) ? wp_json_encode($_POST['settings']) : '',
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        ];

        // Check if we're updating or creating
        $bot_id = isset($_POST['bot_id']) ? intval($_POST['bot_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($bot_id > 0) {
            // Update existing bot
            $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, $bot_data);
            $message = __('Bot updated successfully.', 'farmfaucet');
        } else {
            // Create new bot
            $result = Farmfaucet_Tg_Bot_DB::add_bot($bot_data);
            $bot_id = $result;
            $message = __('Bot created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(['message' => __('Error saving bot. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => $message,
            'bot_id' => $bot_id
        ]);
    }

    /**
     * AJAX handler for deleting bot
     */
    public function ajax_delete_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(['message' => __('Bot ID is required.', 'farmfaucet')]);
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete bot
        $result = Farmfaucet_Tg_Bot_DB::delete_bot($bot_id);

        if (!$result) {
            wp_send_json_error(['message' => __('Error deleting bot. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => __('Bot deleted successfully.', 'farmfaucet')
        ]);
    }

    /**
     * AJAX handler for saving flow
     */
    public function ajax_save_flow()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (
            !isset($_POST['bot_id']) || empty($_POST['bot_id']) ||
            !isset($_POST['flow_name']) || empty($_POST['flow_name']) ||
            !isset($_POST['flow_data']) || empty($_POST['flow_data'])
        ) {
            wp_send_json_error(['message' => __('All fields are required.', 'farmfaucet')]);
        }

        // Sanitize input
        $flow_data = [
            'bot_id' => intval($_POST['bot_id']),
            'flow_name' => sanitize_text_field($_POST['flow_name']),
            'flow_data' => wp_kses_post($_POST['flow_data']),
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        ];

        // Check if we're updating or creating
        $flow_id = isset($_POST['flow_id']) ? intval($_POST['flow_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($flow_id > 0) {
            // Update existing flow
            $result = Farmfaucet_Tg_Bot_DB::update_flow($flow_id, $flow_data);
            $message = __('Flow updated successfully.', 'farmfaucet');
        } else {
            // Create new flow
            $result = Farmfaucet_Tg_Bot_DB::add_flow($flow_data);
            $flow_id = $result;
            $message = __('Flow created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(['message' => __('Error saving flow. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => $message,
            'flow_id' => $flow_id
        ]);
    }

    /**
     * AJAX handler for deleting flow
     */
    public function ajax_delete_flow()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-tg-bot-builder-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check required fields
        if (!isset($_POST['flow_id']) || empty($_POST['flow_id'])) {
            wp_send_json_error(['message' => __('Flow ID is required.', 'farmfaucet')]);
        }

        $flow_id = intval($_POST['flow_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete flow
        $result = Farmfaucet_Tg_Bot_DB::delete_flow($flow_id);

        if (!$result) {
            wp_send_json_error(['message' => __('Error deleting flow. Please try again.', 'farmfaucet')]);
        }

        wp_send_json_success([
            'message' => __('Flow deleted successfully.', 'farmfaucet')
        ]);
    }

    /**
     * Render admin settings page
     */
    public static function render_admin_page()
    {
        // Include the modern bot builder template
        include(FARMFAUCET_DIR . 'templates/admin/bot-builder-modern.php');
    }




    // HTML content removed - this should be in a proper render method

    // More HTML content removed - this should be in a proper render method

    // All HTML content removed - this should be in proper render methods
}
