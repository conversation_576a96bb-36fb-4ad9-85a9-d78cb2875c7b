/**
 * Farm Faucet - Dashboard Transparency Fix
 * Ensures all dashboard elements have transparent backgrounds
 */

/* Make all dashboard cards transparent */
.farmfaucet-dashboard-card,
.farmfaucet-username-container,
.farmfaucet-username-form,
.farmfaucet-balance-display,
.farmfaucet-leaderboard,
.farmfaucet-leaderboard-item {
    background-color: transparent !important;
    box-shadow: none !important;
}

.farmfaucet-dashboard-card .card-header,
.farmfaucet-username-container .card-header,
.farmfaucet-balance-display .card-header {
    background-color: transparent !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.farmfaucet-dashboard-card .card-body,
.farmfaucet-username-container .card-body,
.farmfaucet-balance-display .card-body {
    background-color: transparent !important;
}

/* Fix balance display */
.farmfaucet-balance-display {
    background-color: transparent !important;
}

.farmfaucet-total-earned .farmfaucet-total-earned-value {
    background-color: transparent !important;
}

/* Fix leaderboard transparency */
.farmfaucet-leaderboard {
    background-color: transparent !important;
}

.farmfaucet-leaderboard-item {
    background-color: transparent !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Fix username form */
.farmfaucet-username-container {
    background-color: transparent !important;
}

.farmfaucet-current-username {
    background-color: transparent !important;
}

.farmfaucet-username-form {
    background-color: transparent !important;
}

/* Fix alignment in user information cards */
.shortcode-item {
    overflow: hidden;
}

.shortcode-info code {
    word-break: break-all;
    white-space: normal;
}

.shortcode-params ul {
    padding-left: 20px;
}

.shortcode-params li {
    margin-bottom: 8px;
    line-height: 1.4;
}

/* Fix username form functionality */
.farmfaucet-username-input {
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 8px 12px !important;
}

.farmfaucet-username-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.farmfaucet-username-submit {
    background-color: #4CAF50 !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
}

.farmfaucet-username-cancel-link {
    color: #666 !important;
    text-decoration: none !important;
    cursor: pointer !important;
}

/* Leaderboard styling improvements */
.farmfaucet-leaderboard-title {
    color: inherit !important;
    text-align: center;
    margin-bottom: 20px;
}

.farmfaucet-leaderboard-item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.farmfaucet-leaderboard-item:hover {
    transform: translateY(-2px);
    background-color: rgba(0, 0, 0, 0.03) !important;
}

.farmfaucet-leaderboard-avatar {
    border: 2px solid #4CAF50 !important;
}

.farmfaucet-leaderboard-name {
    font-weight: 500;
}

.farmfaucet-leaderboard-completions-count {
    color: #4CAF50 !important;
    font-weight: bold;
}

/* Preview styling */
.leaderboard-preview {
    margin-top: 30px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.leaderboard-preview h4 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.preview-image {
    margin-top: 15px;
    text-align: center;
}
