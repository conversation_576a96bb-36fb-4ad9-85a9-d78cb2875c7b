<?php

/**
 * Farmfaucet Database Updater Class
 *
 * Handles database updates for the Farmfaucet plugin
 */
class Farmfaucet_DB_Updater
{
    /**
     * Run database updates
     *
     * @param bool $force Force updates even if version hasn't changed
     * @return void
     */
    public static function run_updates($force = false)
    {
        self::update_database_structure();
        self::update_task_completion_settings();
        self::update_currencies_table();
        self::update_tg_bots_table();
        self::update_faucets_table();

        // Run button color hex updater
        require_once(dirname(__FILE__) . '/class-farmfaucet-db-updater-button-color-hex.php');
        Farmfaucet_DB_Updater_Button_Color_Hex::run_update();

        // Run countdown captcha updater
        require_once(dirname(__FILE__) . '/class-farmfaucet-db-updater-countdown-captcha.php');
        Farmfaucet_DB_Updater_Countdown_Captcha::run_update();

        // Run form background updater
        require_once(dirname(__FILE__) . '/class-farmfaucet-db-updater-form-bg.php');
        Farmfaucet_DB_Updater_Form_Bg::run_update();

        // Run faucet appearance updater
        require_once(dirname(__FILE__) . '/class-farmfaucet-db-updater-faucet-appearance.php');
        Farmfaucet_DB_Updater_Faucet_Appearance::run_update();
    }

    /**
     * Update database structure
     *
     * @return void
     */
    public static function update_database_structure()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Update column sizes for color fields
        $color_columns = [
            'button_color' => 'blue',
            'milestone_card_bg_color' => '#FFFFFF',
            'milestone_card_gradient_start' => '#FFFFFF',
            'milestone_card_gradient_end' => '#F5F5F5',
            'milestone_bar_color' => '#4CAF50',
            'milestone_gradient_start' => '#4CAF50',
            'milestone_gradient_end' => '#2196F3'
        ];

        foreach ($color_columns as $column => $default) {
            if (in_array($column, $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN {$column} varchar(50) NOT NULL DEFAULT '{$default}'");
            }
        }

        // Add missing columns
        $missing_columns = [
            'milestone_lock_faucet' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_lock_faucet tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_specific_count",
            'milestone_transparent_bg' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_display_style",
            'milestone_card_bg_style' => "ALTER TABLE {$buttons_table} ADD COLUMN milestone_card_bg_style varchar(20) NOT NULL DEFAULT 'solid' AFTER milestone_transparent_bg",
        ];

        foreach ($missing_columns as $column => $query) {
            if (!in_array($column, $column_names)) {
                $wpdb->query($query);
            }
        }

        // Add task_completion_faucets table if it doesn't exist
        $task_completion_table = $wpdb->prefix . 'farmfaucet_task_completion';
        $task_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$task_completion_table}'") === $task_completion_table;

        if (!$task_table_exists) {
            $sql = "CREATE TABLE {$task_completion_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                faucet_id mediumint(9) NOT NULL,
                is_included tinyint(1) NOT NULL DEFAULT 1,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY  (id),
                UNIQUE KEY faucet_id (faucet_id),
                KEY is_included (is_included)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            $wpdb->query($sql);
        }
    }

    /**
     * Update task completion settings
     *
     * @return void
     */
    public static function update_task_completion_settings()
    {
        global $wpdb;
        $task_completion_table = $wpdb->prefix . 'farmfaucet_task_completion';
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the tables exist
        $task_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$task_completion_table}'") === $task_completion_table;
        $faucets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

        if (!$task_table_exists || !$faucets_table_exists) {
            return;
        }

        // Get all faucets
        $faucets = $wpdb->get_results("SELECT id FROM {$faucets_table}", ARRAY_A);

        // Add all faucets to task completion table if they don't exist
        foreach ($faucets as $faucet) {
            $faucet_id = $faucet['id'];
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$task_completion_table} WHERE faucet_id = %d",
                $faucet_id
            ));

            if (!$exists) {
                $wpdb->insert(
                    $task_completion_table,
                    [
                        'faucet_id' => $faucet_id,
                        'is_included' => 1,
                        'created_at' => current_time('mysql'),
                        'updated_at' => current_time('mysql')
                    ],
                    ['%d', '%d', '%s', '%s']
                );
            }
        }
    }

    /**
     * Update faucets table
     *
     * @return void
     */
    public static function update_faucets_table()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add form_bg_color column if it doesn't exist
        if (!in_array('form_bg_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius");
        }

        // Add form_transparent column if it doesn't exist
        if (!in_array('form_transparent', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color");
        }

        // Add button_border_radius column if it doesn't exist
        if (!in_array('button_border_radius', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN button_border_radius varchar(20) NOT NULL DEFAULT '25px' AFTER form_transparent");
        }

        // Add input_label_color column if it doesn't exist
        if (!in_array('input_label_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN input_label_color varchar(20) NOT NULL DEFAULT '#333333' AFTER button_border_radius");
        }

        // Add input_placeholder_color column if it doesn't exist
        if (!in_array('input_placeholder_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN input_placeholder_color varchar(20) NOT NULL DEFAULT '#999999' AFTER input_label_color");
        }

        // Update existing faucets to set default values
        $wpdb->query("UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''");
    }

    /**
     * Update currencies table
     *
     * @return void
     */
    public static function update_currencies_table()
    {
        global $wpdb;
        $currencies_table = $wpdb->prefix . 'farmfaucet_currencies';

        // Check if the currencies table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$currencies_table}'") === $currencies_table;

        if (!$table_exists) {
            // Create the currencies table
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$currencies_table} (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                code varchar(10) NOT NULL,
                symbol varchar(10) NOT NULL,
                base_currency varchar(10) NOT NULL,
                exchange_rate decimal(18,8) NOT NULL,
                color varchar(20) DEFAULT '#4CAF50',
                icon varchar(255) DEFAULT NULL,
                currency_type varchar(20) DEFAULT 'earnings',
                is_active tinyint(1) NOT NULL DEFAULT 1,
                created_by bigint(20) NOT NULL,
                created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY code (code),
                KEY base_currency (base_currency),
                KEY currency_type (currency_type),
                KEY is_active (is_active)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        } else {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$currencies_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Make sure the color column exists and has the right default
            if (!in_array('color', $column_names)) {
                $wpdb->query("ALTER TABLE {$currencies_table} ADD COLUMN color varchar(20) DEFAULT '#4CAF50' AFTER exchange_rate");
            } else {
                // Update the color column to ensure it has the right default
                $wpdb->query("ALTER TABLE {$currencies_table} MODIFY COLUMN color varchar(20) DEFAULT '#4CAF50'");
            }

            // Make sure the currency_type column exists
            if (!in_array('currency_type', $column_names)) {
                $wpdb->query("ALTER TABLE {$currencies_table} ADD COLUMN currency_type varchar(20) DEFAULT 'earnings' AFTER icon");
            }
        }
    }

    /**
     * Update Telegram bots table
     *
     * @return void
     */
    public static function update_tg_bots_table()
    {
        global $wpdb;
        $bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Check if the bots table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bots_table}'") === $bots_table;

        if (!$table_exists) {
            // Create the bots table
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$bots_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                bot_name varchar(100) NOT NULL,
                bot_token varchar(255) NOT NULL,
                bot_username varchar(100) NOT NULL,
                bot_type varchar(20) NOT NULL DEFAULT 'text',
                webhook_url varchar(255) DEFAULT '',
                created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
                settings longtext DEFAULT NULL,
                is_active tinyint(1) DEFAULT 1,
                PRIMARY KEY  (id),
                UNIQUE KEY bot_token (bot_token)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
