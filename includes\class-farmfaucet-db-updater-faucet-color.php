<?php

/**
 * Farmfaucet Database Updater for Faucet Color
 *
 * Adds the faucet_color column to the faucets table
 */
class Farmfaucet_DB_Updater_Faucet_Color
{
    /**
     * Run the update to add the faucet_color column
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add faucet_color column if it doesn't exist
        if (!in_array('faucet_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN faucet_color varchar(50) NOT NULL DEFAULT 'green' AFTER captcha_type");
        } else {
            // Update column size if it exists
            $wpdb->query("ALTER TABLE {$faucets_table} MODIFY COLUMN faucet_color varchar(50) NOT NULL DEFAULT 'green'");
        }

        // Add is_enabled column if it doesn't exist
        if (!in_array('is_enabled', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN is_enabled tinyint(1) NOT NULL DEFAULT 1 AFTER faucet_color");
            
            // Add index for is_enabled if it doesn't exist
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$faucets_table} WHERE Key_name = 'is_enabled'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$faucets_table} ADD INDEX is_enabled (is_enabled)");
            }
        }
    }
}
