/**
 * <PERSON><PERSON><PERSON><PERSON>gin Admin CSS
 */

/* Admin section */
.farmfaucet-admin-section.tg-bot-login-section {
    margin-top: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Bot status */
.bot-status {
    display: inline-block;
    margin-left: 15px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.bot-status.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.bot-status.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

/* Test token result */
#test-token-result {
    display: inline-block;
    margin-left: 15px;
    font-weight: 600;
    padding: 8px 15px;
    border-radius: 6px;
    animation: fadeIn 0.5s ease;
}

#test-token-result.success {
    color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
}

#test-token-result.error {
    color: #F44336;
    background-color: rgba(244, 67, 54, 0.1);
}

/* Shortcode container */
.shortcode-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.shortcode-container code {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 13px;
    margin-right: 10px;
}

.copy-shortcode {
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.copy-shortcode .dashicons {
    font-size: 16px;
}

.copy-shortcode.copied {
    background-color: #4CAF50;
    color: white;
}

/* Preview tabs */
.preview-tabs {
    margin-top: 20px;
}

.preview-tabs-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #ddd;
}

.preview-tabs-nav li {
    margin: 0;
    padding: 0;
}

.preview-tabs-nav a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: #555;
    font-weight: 500;
    border: 1px solid transparent;
    border-bottom: none;
    margin-bottom: -1px;
    transition: all 0.3s ease;
}

.preview-tabs-nav li.active a {
    border-color: #ddd;
    border-bottom-color: #fff;
    background-color: #fff;
    color: #0073aa;
}

.preview-tabs-content {
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
    background-color: #fff;
}

.preview-tab {
    display: none;
}

.preview-tab.active {
    display: block;
}

/* Preview forms */
.preview-tab .farmfaucet-tg-login-container,
.preview-tab .farmfaucet-tg-signup-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
