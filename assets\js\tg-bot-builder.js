/**
 * Telegram Bot Builder JavaScript
 * Enhanced with modern UI interactions
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initTelegramBotBuilder();
    });

    /**
     * Initialize Telegram Bot Builder functionality
     */
    function initTelegramBotBuilder() {
        // Add animation class to cards
        $('.farmfaucet-admin-card').addClass('animated fadeIn');

        // Tab navigation with smooth transitions
        $('.farmfaucet-tg-bot-tabs-nav a').on('click', function(e) {
            e.preventDefault();

            const tabId = $(this).attr('href');
            const $tabContent = $(tabId);

            // Update active tab
            $(this).parent().addClass('active').siblings().removeClass('active');

            // Animate tab transition
            $('.farmfaucet-tg-bot-tab.active').fadeOut(200, function() {
                $(this).removeClass('active');
                $tabContent.fadeIn(200).addClass('active');
            });
        });

        // Test bot token with enhanced UI feedback
        $('#test-bot-token').on('click', function() {
            const token = $('#bot-token').val();

            if (!token) {
                showTokenResult('Please enter a bot token.', 'error');
                $('#bot-token').addClass('error-highlight').focus();
                setTimeout(() => {
                    $('#bot-token').removeClass('error-highlight');
                }, 2000);
                return;
            }

            // Show loading state with animation
            const $button = $(this);
            $button.prop('disabled', true)
                .html('<span class="spinner"></span> Testing...')
                .addClass('loading');

            // Clear previous result
            $('#test-token-result').fadeOut(200);

            // Send AJAX request
            $.ajax({
                url: farmfaucetTgBotBuilder.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'farmfaucet_test_tg_bot_token',
                    nonce: farmfaucetTgBotBuilder.nonce,
                    token: token
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message with animation
                        showTokenResult(farmfaucetTgBotBuilder.i18n.testSuccess + response.data.bot.first_name, 'success');

                        // Auto-fill bot username with highlight effect
                        $('#bot-username').val(response.data.bot.username)
                            .addClass('success-highlight');

                        // Add bot name if empty
                        if (!$('#bot-name').val()) {
                            $('#bot-name').val(response.data.bot.first_name)
                                .addClass('success-highlight');
                        }

                        setTimeout(() => {
                            $('.success-highlight').removeClass('success-highlight');
                        }, 2000);
                    } else {
                        // Show error message with animation
                        showTokenResult(response.data.message, 'error');
                    }
                },
                error: function() {
                    showTokenResult(farmfaucetTgBotBuilder.i18n.testError, 'error');
                },
                complete: function() {
                    // Reset button state with animation
                    $button.removeClass('loading')
                        .prop('disabled', false)
                        .text('Test Token');
                }
            });
        });

        // Create/edit bot form submission with enhanced UI
        $('#farmfaucet-create-bot-form').on('submit', function(e) {
            e.preventDefault();

            // Validate form
            let isValid = true;
            $(this).find('input[required], select[required]').each(function() {
                if (!$(this).val()) {
                    $(this).addClass('error-highlight').focus();
                    isValid = false;
                    return false; // Break the loop
                }
            });

            if (!isValid) {
                showNotification('Please fill in all required fields.', 'error');
                setTimeout(() => {
                    $('.error-highlight').removeClass('error-highlight');
                }, 2000);
                return;
            }

            // Get form data
            const formData = $(this).serialize();

            // Show loading state with animation
            const submitButton = $(this).find('button[type="submit"]');
            submitButton.prop('disabled', true)
                .html('<span class="spinner"></span> Saving...')
                .addClass('loading');

            // Add overlay to form
            const $form = $(this);
            $form.addClass('form-loading');
            $form.append('<div class="form-overlay"><div class="spinner-large"></div></div>');

            // Send AJAX request
            $.ajax({
                url: farmfaucetTgBotBuilder.ajaxUrl,
                type: 'POST',
                data: formData + '&action=farmfaucet_save_tg_bot&nonce=' + farmfaucetTgBotBuilder.nonce,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        showNotification(response.data.message, 'success');

                        // Add success animation to form
                        $form.addClass('form-success');

                        // Reload page after delay to update bot list
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        // Show error message
                        showNotification(response.data.message, 'error');
                        $form.removeClass('form-loading');
                        $form.find('.form-overlay').remove();
                    }
                },
                error: function() {
                    showNotification(farmfaucetTgBotBuilder.i18n.saveError, 'error');
                    $form.removeClass('form-loading');
                    $form.find('.form-overlay').remove();
                },
                complete: function() {
                    // Reset button state
                    submitButton.removeClass('loading')
                        .prop('disabled', false)
                        .text('Save Bot');
                }
            });
        });

        // Edit bot button click with enhanced UI
        $('.edit-bot').on('click', function(e) {
            e.preventDefault();

            const botId = $(this).data('bot-id');

            // Highlight the selected row
            const $row = $(this).closest('tr');
            $row.addClass('row-highlight');

            // Get bot data from the table row
            const botName = $row.find('td:eq(0)').text();
            const botUsername = $row.find('td:eq(1)').text().replace('@', '');
            const botType = $row.find('td:eq(2)').text() === 'Text Bot' ? 'text' : 'chat';
            const isActive = $row.find('td:eq(3)').find('.status-active').length > 0;

            // Switch to create bot tab with animation
            $('.farmfaucet-tg-bot-tabs-nav a[href="#tg-bot-create"]').click();

            // Add loading animation to form
            const $form = $('#farmfaucet-create-bot-form');
            $form.addClass('form-loading');
            $form.append('<div class="form-overlay"><div class="spinner-large"></div><div class="loading-text">Loading Bot Data...</div></div>');

            // Simulate loading for better UX (remove in production if not needed)
            setTimeout(() => {
                // Fill the form with bot data with highlight effect
                $('#bot-id').val(botId);
                $('#bot-name').val(botName).addClass('highlight-field');
                $('#bot-username').val(botUsername).addClass('highlight-field');
                $('#bot-type').val(botType).addClass('highlight-field');
                $('#bot-active').prop('checked', isActive);

                if (isActive) {
                    $('.status-label').text('Active').addClass('highlight-text');
                } else {
                    $('.status-label').text('Inactive').addClass('highlight-text');
                }

                // Show cancel button with animation
                $('#cancel-edit-bot').fadeIn(300);

                // Update form title with animation
                if ($('#tg-bot-create .farmfaucet-form h3').length === 0) {
                    $('<h3 class="edit-title">Edit Bot: <span>' + botName + '</span></h3>')
                        .hide()
                        .prependTo('#tg-bot-create .farmfaucet-form')
                        .fadeIn(300);
                } else {
                    $('#tg-bot-create .farmfaucet-form h3 span').text(botName);
                }

                // Remove loading overlay
                $form.removeClass('form-loading');
                $form.find('.form-overlay').fadeOut(300, function() {
                    $(this).remove();
                });

                // Remove highlight after delay
                setTimeout(() => {
                    $('.highlight-field, .highlight-text').removeClass('highlight-field highlight-text');
                    $row.removeClass('row-highlight');
                }, 2000);
            }, 600);
        });

        // Cancel edit bot button click with enhanced UI
        $('#cancel-edit-bot').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            // Fade out form title
            $('#tg-bot-create .farmfaucet-form h3').fadeOut(300, function() {
                $(this).remove();
            });

            // Reset form with animation
            const $form = $('#farmfaucet-create-bot-form');
            $form.addClass('form-resetting');

            setTimeout(() => {
                // Reset form
                $form[0].reset();
                $('#bot-id').val(0);

                // Reset status label
                $('.status-label').text('Active');

                // Hide cancel button
                $('#cancel-edit-bot').hide().removeClass('button-clicked');

                // Remove animation class
                $form.removeClass('form-resetting');

                // Switch to bot list tab
                $('.farmfaucet-tg-bot-tabs-nav a[href="#tg-bot-list"]').click();
            }, 300);
        });

        // Delete bot button click with enhanced UI
        $('.delete-bot').on('click', function(e) {
            e.preventDefault();

            const botId = $(this).data('bot-id');
            const $row = $(this).closest('tr');
            const botName = $row.find('td:eq(0)').text();

            // Create and show custom confirmation dialog
            const $dialog = $('<div class="confirmation-dialog">' +
                '<div class="dialog-content">' +
                '<h3>Delete Bot</h3>' +
                '<p>Are you sure you want to delete the bot "<strong>' + botName + '</strong>"?</p>' +
                '<p class="warning">This action cannot be undone.</p>' +
                '<div class="dialog-buttons">' +
                '<button class="button button-cancel">Cancel</button>' +
                '<button class="button button-delete">Delete</button>' +
                '</div>' +
                '</div>' +
                '</div>');

            $('body').append($dialog);
            $dialog.fadeIn(300);

            // Handle cancel button
            $dialog.find('.button-cancel').on('click', function() {
                $dialog.fadeOut(300, function() {
                    $(this).remove();
                });
            });

            // Handle delete button
            $dialog.find('.button-delete').on('click', function() {
                // Show loading state
                $(this).prop('disabled', true)
                    .html('<span class="spinner"></span> Deleting...')
                    .addClass('loading');

                $dialog.find('.button-cancel').prop('disabled', true);

                // Highlight the row to be deleted
                $row.addClass('row-deleting');

                // Send AJAX request
                $.ajax({
                    url: farmfaucetTgBotBuilder.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_delete_tg_bot',
                        nonce: farmfaucetTgBotBuilder.nonce,
                        bot_id: botId
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            showNotification(response.data.message, 'success');

                            // Animate row removal
                            $row.fadeOut(500, function() {
                                $(this).remove();

                                // Check if table is empty
                                if ($('#tg-bot-list table tbody tr').length === 0) {
                                    $('#tg-bot-list table').replaceWith(
                                        '<div class="empty-state">' +
                                        '<p>No bots found. Create your first bot using the "Create Bot" tab.</p>' +
                                        '<button class="button create-first-bot">Create Bot</button>' +
                                        '</div>'
                                    );

                                    // Add click handler for the create button
                                    $('.create-first-bot').on('click', function() {
                                        $('.farmfaucet-tg-bot-tabs-nav a[href="#tg-bot-create"]').click();
                                    });
                                }
                            });

                            // Close dialog after delay
                            setTimeout(() => {
                                $dialog.fadeOut(300, function() {
                                    $(this).remove();
                                });
                            }, 1000);
                        } else {
                            // Show error message
                            showNotification(response.data.message, 'error');

                            // Reset dialog
                            $dialog.find('.button-delete').prop('disabled', false)
                                .removeClass('loading')
                                .text('Delete');
                            $dialog.find('.button-cancel').prop('disabled', false);

                            // Remove highlight
                            $row.removeClass('row-deleting');
                        }
                    },
                    error: function() {
                        // Show error message
                        showNotification(farmfaucetTgBotBuilder.i18n.deleteError, 'error');

                        // Reset dialog
                        $dialog.find('.button-delete').prop('disabled', false)
                            .removeClass('loading')
                            .text('Delete');
                        $dialog.find('.button-cancel').prop('disabled', false);

                        // Remove highlight
                        $row.removeClass('row-deleting');
                    }
                });
            });
        });

        // Manage flows button click with enhanced UI
        $('.manage-flows').on('click', function(e) {
            e.preventDefault();

            const botId = $(this).data('bot-id');
            const $row = $(this).closest('tr');
            const botName = $row.find('td:eq(0)').text();
            const botType = $row.find('td:eq(2)').text() === 'Text Bot' ? 'text' : 'chat';

            // Highlight the selected row
            $row.addClass('row-highlight');

            // Set bot ID for flow editor
            $('#flow-bot-id').val(botId);

            // Set bot name in flow builder header with animation
            $('.flow-builder-bot-name').text(botName);

            // Show/hide chat bot specific elements
            if (botType === 'chat') {
                $('.chat-bot-tab, .chat-bot-element').show();
            } else {
                $('.chat-bot-tab, .chat-bot-element').hide();
            }

            // Hide bot settings section with animation
            $('.farmfaucet-admin-card:not(.flow-builder-card)').fadeOut(300);

            // Show flow builder section with animation
            setTimeout(() => {
                $('.flow-builder-card').fadeIn(300);

                // Remove highlight after delay
                setTimeout(() => {
                    $row.removeClass('row-highlight');
                }, 1000);

                // Load flows for this bot with loading animation
                loadFlows(botId);
            }, 300);
        });

        // Close flow builder button click with enhanced UI
        $('.close-flow-builder').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            // Hide flow builder section with animation
            $('.flow-builder-card').fadeOut(300);

            // Show bot settings section with animation
            setTimeout(() => {
                $('.farmfaucet-admin-card:not(.flow-builder-card)').fadeIn(300);

                // Remove button animation
                $('.close-flow-builder').removeClass('button-clicked');
            }, 300);
        });

        // Create new flow button click with enhanced UI
        $('.create-flow').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            // Reset flow form with animation
            const $form = $('#farmfaucet-flow-form');
            $form.addClass('form-resetting');

            setTimeout(() => {
                // Reset form
                $form[0].reset();
                $('#flow-id').val(0);

                // Remove animation class
                $form.removeClass('form-resetting');
                $(this).removeClass('button-clicked');

                // Switch to flow editor tab with animation
                $('.farmfaucet-tg-bot-tabs-nav a[href="#flow-editor"]').click();

                // Initialize empty flow canvas
                initEmptyFlowCanvas();
            }, 300);
        });

        // Template library
        $('.use-template').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            setTimeout(() => {
                $(this).removeClass('button-clicked');

                // Show template library
                $('.flow-list-container').fadeOut(300, function() {
                    $('.template-library-section').fadeIn(300);
                });
            }, 300);
        });

        $('.close-template-library').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            setTimeout(() => {
                $(this).removeClass('button-clicked');

                // Hide template library
                $('.template-library-section').fadeOut(300, function() {
                    $('.flow-list-container').fadeIn(300);
                });
            }, 300);
        });

        // Template category filter
        $('.template-category').on('click', function() {
            const category = $(this).data('category');

            // Update active class
            $('.template-category').removeClass('active');
            $(this).addClass('active');

            // Filter templates
            if (category === 'all') {
                $('.template-card').fadeIn(300);
            } else {
                $('.template-card').hide();
                $('.template-card[data-category="' + category + '"]').fadeIn(300);
            }
        });

        // Use template button
        $('.use-this-template').on('click', function() {
            const template = $(this).data('template');

            // Add animation
            $(this).addClass('button-clicked');

            setTimeout(() => {
                $(this).removeClass('button-clicked');

                // Show flow editor tab with the selected template
                $('.farmfaucet-tg-bot-tabs-nav a[href="#flow-editor"]').click();

                // Load template data
                loadTemplateData(template);

                // Show success notification
                showNotification('Template loaded successfully. You can now customize it.', 'success');
            }, 300);
        });

        // Preview template button
        $('.preview-template').on('click', function() {
            const template = $(this).data('template');

            // Add animation
            $(this).addClass('button-clicked');

            setTimeout(() => {
                $(this).removeClass('button-clicked');

                // Show template preview modal
                showTemplatePreviewModal(template);
            }, 300);
        });

        // Cancel edit flow button click with enhanced UI
        $('#cancel-edit-flow').on('click', function() {
            // Add animation
            $(this).addClass('button-clicked');

            setTimeout(() => {
                // Remove animation class
                $(this).removeClass('button-clicked');

                // Switch to flow list tab
                $('.farmfaucet-tg-bot-tabs-nav a[href="#flow-list"]').click();
            }, 300);
        });

        // CAPTCHA type change with enhanced UI
        $('#farmfaucet_tg_bot_captcha_type').on('change', function() {
            const captchaType = $(this).val();

            // Hide all captcha settings with animation
            $('.captcha-settings').fadeOut(200);

            // Show settings for selected captcha type with animation
            setTimeout(() => {
                $('.' + captchaType + '-settings').fadeIn(200);
            }, 200);
        });

        // Command management
        $('.create-command').on('click', function() {
            showCommandModal();
        });

        // Command preview functionality
        $('#send-preview-command').on('click', function() {
            const command = $('#preview-command').val();

            if (!command) {
                showNotification('Please enter a command to test.', 'error');
                return;
            }

            // Add user message to chat
            addMessageToPreview(command, 'user');

            // Clear input
            $('#preview-command').val('');

            // Simulate bot response after a short delay
            setTimeout(() => {
                // Find a matching command in the list
                let responseFound = false;

                // For demo purposes, handle some default commands
                if (command.toLowerCase() === '/start') {
                    addMessageToPreview('Welcome to the bot! How can I help you today?', 'bot');
                    addKeyboardToPreview(['Help', 'About']);
                    responseFound = true;
                } else if (command.toLowerCase() === '/help') {
                    addMessageToPreview('Here are the available commands:\n/start - Start the bot\n/help - Show this help message\n/about - About this bot', 'bot');
                    responseFound = true;
                } else if (command.toLowerCase() === '/about') {
                    addMessageToPreview('This is a Telegram bot created with Farm Faucet\'s Telegram Bot Builder.', 'bot');
                    responseFound = true;
                }

                // If no response found, show default message
                if (!responseFound) {
                    addMessageToPreview('I don\'t understand that command. Try /help to see available commands.', 'bot');
                }
            }, 1000);
        });

        // Allow pressing Enter to send command
        $('#preview-command').on('keypress', function(e) {
            if (e.which === 13) {
                $('#send-preview-command').click();
                return false;
            }
        });

        // Initialize flow editor
        initFlowEditor();

        // Add animation to status toggle
        $('#bot-active, #flow-active, #farmfaucet_tg_bot_builder_enabled').on('change', function() {
            const $label = $(this).siblings('.status-label');

            if ($(this).is(':checked')) {
                $label.text('Active').addClass('highlight-text');
            } else {
                $label.text('Inactive').addClass('highlight-text');
            }

            setTimeout(() => {
                $label.removeClass('highlight-text');
            }, 1000);
        });
    }

    /**
     * Show token test result with animation
     */
    function showTokenResult(message, type) {
        const $result = $('#test-token-result');

        // Hide result if visible
        if ($result.is(':visible')) {
            $result.fadeOut(200, function() {
                // Update result
                $(this).text(message).removeClass('success error').addClass(type);

                // Show result with animation
                $(this).fadeIn(200);
            });
        } else {
            // Update result
            $result.text(message).removeClass('success error').addClass(type);

            // Show result with animation
            $result.fadeIn(200);
        }
    }

    /**
     * Show notification message
     */
    function showNotification(message, type) {
        // Remove existing notifications
        $('.farmfaucet-notification').remove();

        // Create notification element
        const $notification = $('<div class="farmfaucet-notification ' + type + '">' +
            '<div class="notification-content">' + message + '</div>' +
            '<button class="notification-close">&times;</button>' +
            '</div>');

        // Add to body
        $('body').append($notification);

        // Show notification with animation
        setTimeout(() => {
            $notification.addClass('show');
        }, 10);

        // Add close button handler
        $notification.find('.notification-close').on('click', function() {
            $notification.removeClass('show');

            setTimeout(() => {
                $notification.remove();
            }, 300);
        });

        // Auto-hide after delay
        setTimeout(() => {
            $notification.removeClass('show');

            setTimeout(() => {
                $notification.remove();
            }, 300);
        }, 5000);
    }

    /**
     * Load flows for a bot with enhanced UI
     */
    function loadFlows(botId) {
        // Show loading animation
        $('.flow-list-table-container').html(
            '<div class="loading-container">' +
            '<div class="spinner-large"></div>' +
            '<p>Loading flows...</p>' +
            '</div>'
        );

        // TODO: Replace with actual AJAX call in production
        // Simulate AJAX request for demo purposes
        setTimeout(() => {
            // For demo, show empty state
            $('.flow-list-table-container').html(
                '<div class="empty-state">' +
                '<p>No flows found for this bot. Create your first flow using the "Create New Flow" button.</p>' +
                '<button class="button create-first-flow">Create Flow</button>' +
                '</div>'
            );

            // Add click handler for the create button
            $('.create-first-flow').on('click', function() {
                $('.create-flow').click();
            });
        }, 1000);
    }

    /**
     * Initialize empty flow canvas
     */
    function initEmptyFlowCanvas() {
        // Show empty state in canvas
        $('#flow-editor-canvas').html(
            '<div class="flow-empty-state">' +
            '<div class="flow-empty-icon"><i class="dashicons dashicons-randomize"></i></div>' +
            '<h3>Start Building Your Flow</h3>' +
            '<p>Drag elements from the sidebar to create your bot flow.</p>' +
            '</div>'
        );
    }

    /**
     * Initialize flow editor with jsPlumb for connections
     */
    function initFlowEditor() {
        // Initialize jsPlumb instance
        const jsPlumbInstance = jsPlumb.getInstance({
            Endpoint: ["Dot", { radius: 4 }],
            Connector: ["Bezier", { curviness: 70 }],
            HoverPaintStyle: { stroke: "#4CAF50", strokeWidth: 3 },
            ConnectionOverlays: [
                ["Arrow", {
                    location: 1,
                    id: "arrow",
                    length: 12,
                    width: 12
                }],
                ["Label", {
                    label: "connect",
                    id: "label",
                    cssClass: "connection-label"
                }]
            ],
            Container: "flow-editor-canvas"
        });

        // Store jsPlumb instance in window for access in other functions
        window.flowJsPlumb = jsPlumbInstance;

        // Initialize empty canvas
        initEmptyFlowCanvas();

        // Initialize Sortable for drag and drop
        const flowCanvas = document.getElementById('flow-editor-canvas');

        // Initialize Sortable
        new Sortable(flowCanvas, {
            group: {
                name: 'flow-canvas',
                pull: false,
                put: true
            },
            animation: 150,
            onAdd: function(evt) {
                const item = evt.item;
                const elementType = $(item).data('type');

                // Replace the dragged element with a proper node
                const nodeId = 'node-' + Date.now();
                const nodeHtml = createNodeHtml(nodeId, elementType);

                // Replace the dragged element with the node
                $(item).replaceWith(nodeHtml);

                // Initialize the new node with jsPlumb
                initNode(nodeId, elementType);

                // Show node configuration modal
                showNodeConfigModal(nodeId, elementType);
            }
        });

        // Make flow elements draggable
        $('.flow-element').each(function() {
            const element = this;

            // Initialize Sortable
            new Sortable(element, {
                group: {
                    name: 'flow-elements',
                    pull: 'clone',
                    put: false
                },
                sort: false,
                animation: 150
            });
        });

        // Add double-click handler for nodes
        $(document).on('dblclick', '.flow-node', function() {
            const nodeId = $(this).attr('id');
            const nodeType = $(this).data('type');
            showNodeConfigModal(nodeId, nodeType);
        });

        // Add context menu for nodes
        $(document).on('contextmenu', '.flow-node', function(e) {
            e.preventDefault();

            const nodeId = $(this).attr('id');

            // Show context menu
            showNodeContextMenu(nodeId, e.pageX, e.pageY);
        });

        // Add click handler to close context menu
        $(document).on('click', function() {
            $('.node-context-menu').remove();
        });
    }

    /**
     * Create HTML for a flow node
     */
    function createNodeHtml(nodeId, type) {
        let icon, title;

        switch(type) {
            case 'message':
                icon = 'dashicons-format-chat';
                title = 'Message';
                break;
            case 'button':
                icon = 'dashicons-button';
                title = 'Button';
                break;
            case 'condition':
                icon = 'dashicons-randomize';
                title = 'Condition';
                break;
            case 'captcha':
                icon = 'dashicons-shield';
                title = 'CAPTCHA';
                break;
            case 'action':
                icon = 'dashicons-admin-generic';
                title = 'Action';
                break;
            case 'moderation':
                icon = 'dashicons-hammer';
                title = 'Moderation';
                break;
            default:
                icon = 'dashicons-admin-generic';
                title = 'Node';
        }

        return '<div id="' + nodeId + '" class="flow-node flow-node-' + type + '" data-type="' + type + '">' +
            '<div class="flow-node-header">' +
            '<i class="dashicons ' + icon + '"></i>' +
            '<span class="flow-node-title">' + title + '</span>' +
            '</div>' +
            '<div class="flow-node-content">' +
            '<p class="flow-node-description">Double-click to configure</p>' +
            '</div>' +
            '<div class="flow-node-footer">' +
            '<div class="flow-node-source"></div>' +
            '<div class="flow-node-target"></div>' +
            '</div>' +
            '</div>';
    }

    /**
     * Initialize a node with jsPlumb
     */
    function initNode(nodeId, type) {
        const jsPlumbInstance = window.flowJsPlumb;

        // Make the node draggable
        jsPlumbInstance.draggable(nodeId, {
            grid: [10, 10],
            containment: 'parent'
        });

        // Add source endpoint (output)
        jsPlumbInstance.addEndpoint(nodeId, {
            anchor: 'Right',
            isSource: true,
            maxConnections: -1,
            connectorStyle: {
                stroke: '#4CAF50',
                strokeWidth: 2
            }
        });

        // Add target endpoint (input)
        jsPlumbInstance.addEndpoint(nodeId, {
            anchor: 'Left',
            isTarget: true,
            maxConnections: -1,
            connectorStyle: {
                stroke: '#4CAF50',
                strokeWidth: 2
            }
        });

        // Special handling for condition nodes (add Yes/No outputs)
        if (type === 'condition') {
            jsPlumbInstance.addEndpoint(nodeId, {
                anchor: [1, 0.25, 1, 0],
                isSource: true,
                maxConnections: 1,
                connectorStyle: {
                    stroke: '#4CAF50',
                    strokeWidth: 2
                },
                overlays: [
                    ["Label", { label: "Yes", location: [0.5, -1.5], cssClass: "endpoint-label yes-label" }]
                ]
            });

            jsPlumbInstance.addEndpoint(nodeId, {
                anchor: [1, 0.75, 1, 0],
                isSource: true,
                maxConnections: 1,
                connectorStyle: {
                    stroke: '#f44336',
                    strokeWidth: 2
                },
                connectorHoverStyle: {
                    stroke: '#f44336',
                    strokeWidth: 3
                },
                overlays: [
                    ["Label", { label: "No", location: [0.5, 1.5], cssClass: "endpoint-label no-label" }]
                ]
            });
        }
    }

    /**
     * Show node configuration modal
     */
    function showNodeConfigModal(nodeId, type) {
        // Remove any existing modal
        $('.node-config-modal').remove();

        // Create modal HTML based on node type
        let modalHtml = '<div class="node-config-modal">' +
            '<div class="node-config-content">' +
            '<div class="node-config-header">' +
            '<h3>Configure ' + type.charAt(0).toUpperCase() + type.slice(1) + ' Node</h3>' +
            '<button class="close-modal">&times;</button>' +
            '</div>' +
            '<div class="node-config-body">';

        // Add form fields based on node type
        switch(type) {
            case 'message':
                modalHtml += '<div class="form-group">' +
                    '<label for="message-text">Message Text</label>' +
                    '<textarea id="message-text" rows="4" placeholder="Enter message text"></textarea>' +
                    '<p class="description">The text message that will be sent to the user.</p>' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label for="message-format">Format</label>' +
                    '<select id="message-format">' +
                    '<option value="text">Plain Text</option>' +
                    '<option value="html">HTML</option>' +
                    '<option value="markdown">Markdown</option>' +
                    '</select>' +
                    '<p class="description">The format of the message.</p>' +
                    '</div>';
                break;

            case 'button':
                modalHtml += '<div class="form-group">' +
                    '<label for="button-text">Button Text</label>' +
                    '<input type="text" id="button-text" placeholder="Enter button text">' +
                    '<p class="description">The text displayed on the button.</p>' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label for="button-type">Button Type</label>' +
                    '<select id="button-type">' +
                    '<option value="url">URL</option>' +
                    '<option value="callback">Callback Data</option>' +
                    '<option value="switch_inline">Switch Inline Query</option>' +
                    '</select>' +
                    '<p class="description">The type of button action.</p>' +
                    '</div>' +
                    '<div class="form-group button-url-group">' +
                    '<label for="button-url">URL</label>' +
                    '<input type="text" id="button-url" placeholder="https://">' +
                    '<p class="description">The URL to open when the button is clicked.</p>' +
                    '</div>' +
                    '<div class="form-group button-callback-group" style="display:none;">' +
                    '<label for="button-callback">Callback Data</label>' +
                    '<input type="text" id="button-callback" placeholder="callback_data">' +
                    '<p class="description">The callback data to send when the button is clicked.</p>' +
                    '</div>';
                break;

            case 'condition':
                modalHtml += '<div class="form-group">' +
                    '<label for="condition-type">Condition Type</label>' +
                    '<select id="condition-type">' +
                    '<option value="text">Text Contains</option>' +
                    '<option value="command">Command</option>' +
                    '<option value="user">User Property</option>' +
                    '<option value="custom">Custom Expression</option>' +
                    '</select>' +
                    '<p class="description">The type of condition to check.</p>' +
                    '</div>' +
                    '<div class="form-group condition-text-group">' +
                    '<label for="condition-text">Text Contains</label>' +
                    '<input type="text" id="condition-text" placeholder="Enter text to match">' +
                    '<p class="description">Check if user message contains this text.</p>' +
                    '</div>' +
                    '<div class="form-group condition-command-group" style="display:none;">' +
                    '<label for="condition-command">Command</label>' +
                    '<input type="text" id="condition-command" placeholder="/command">' +
                    '<p class="description">Check if user sent this command.</p>' +
                    '</div>';
                break;

            case 'captcha':
                modalHtml += '<div class="form-group">' +
                    '<label for="captcha-type">CAPTCHA Type</label>' +
                    '<select id="captcha-type">' +
                    '<option value="hcaptcha">hCaptcha</option>' +
                    '<option value="recaptcha">reCAPTCHA</option>' +
                    '<option value="custom">Custom CAPTCHA</option>' +
                    '</select>' +
                    '<p class="description">The type of CAPTCHA to use.</p>' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label for="captcha-message">Message</label>' +
                    '<textarea id="captcha-message" rows="3" placeholder="Please complete the CAPTCHA to continue"></textarea>' +
                    '<p class="description">Message to show with the CAPTCHA.</p>' +
                    '</div>';
                break;

            case 'action':
                modalHtml += '<div class="form-group">' +
                    '<label for="action-type">Action Type</label>' +
                    '<select id="action-type">' +
                    '<option value="set_variable">Set Variable</option>' +
                    '<option value="increment">Increment Counter</option>' +
                    '<option value="api_call">API Call</option>' +
                    '<option value="send_notification">Send Notification</option>' +
                    '</select>' +
                    '<p class="description">The type of action to perform.</p>' +
                    '</div>' +
                    '<div class="form-group action-variable-group">' +
                    '<label for="action-variable-name">Variable Name</label>' +
                    '<input type="text" id="action-variable-name" placeholder="variable_name">' +
                    '<p class="description">The name of the variable to set.</p>' +
                    '</div>' +
                    '<div class="form-group action-variable-group">' +
                    '<label for="action-variable-value">Variable Value</label>' +
                    '<input type="text" id="action-variable-value" placeholder="value">' +
                    '<p class="description">The value to set for the variable.</p>' +
                    '</div>';
                break;

            case 'moderation':
                modalHtml += '<div class="form-group">' +
                    '<label for="moderation-action">Moderation Action</label>' +
                    '<select id="moderation-action">' +
                    '<option value="ban">Ban User</option>' +
                    '<option value="kick">Kick User</option>' +
                    '<option value="restrict">Restrict User</option>' +
                    '<option value="warn">Warn User</option>' +
                    '</select>' +
                    '<p class="description">The moderation action to take.</p>' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label for="moderation-reason">Reason</label>' +
                    '<textarea id="moderation-reason" rows="3" placeholder="Reason for moderation action"></textarea>' +
                    '<p class="description">The reason for the moderation action.</p>' +
                    '</div>';
                break;
        }

        // Add save button
        modalHtml += '</div>' +
            '<div class="node-config-footer">' +
            '<button class="save-node-config" data-node-id="' + nodeId + '" data-node-type="' + type + '">Save Configuration</button>' +
            '</div>' +
            '</div>' +
            '</div>';

        // Add modal to the page
        $('body').append(modalHtml);

        // Show modal with animation
        setTimeout(() => {
            $('.node-config-modal').addClass('show');
        }, 10);

        // Add event handlers
        $('.close-modal').on('click', function() {
            $('.node-config-modal').removeClass('show');
            setTimeout(() => {
                $('.node-config-modal').remove();
            }, 300);
        });

        // Handle dynamic form changes
        $('#button-type').on('change', function() {
            const buttonType = $(this).val();

            $('.button-url-group, .button-callback-group').hide();

            if (buttonType === 'url') {
                $('.button-url-group').show();
            } else if (buttonType === 'callback') {
                $('.button-callback-group').show();
            }
        });

        $('#condition-type').on('change', function() {
            const conditionType = $(this).val();

            $('.condition-text-group, .condition-command-group').hide();

            if (conditionType === 'text') {
                $('.condition-text-group').show();
            } else if (conditionType === 'command') {
                $('.condition-command-group').show();
            }
        });

        // Handle save button click
        $('.save-node-config').on('click', function() {
            const nodeId = $(this).data('node-id');
            const nodeType = $(this).data('node-type');

            // Get form values based on node type
            let config = {};

            switch(nodeType) {
                case 'message':
                    config.text = $('#message-text').val();
                    config.format = $('#message-format').val();
                    break;

                case 'button':
                    config.text = $('#button-text').val();
                    config.type = $('#button-type').val();

                    if (config.type === 'url') {
                        config.url = $('#button-url').val();
                    } else if (config.type === 'callback') {
                        config.callback_data = $('#button-callback').val();
                    }
                    break;

                case 'condition':
                    config.type = $('#condition-type').val();

                    if (config.type === 'text') {
                        config.text = $('#condition-text').val();
                    } else if (config.type === 'command') {
                        config.command = $('#condition-command').val();
                    }
                    break;

                case 'captcha':
                    config.type = $('#captcha-type').val();
                    config.message = $('#captcha-message').val();
                    break;

                case 'action':
                    config.type = $('#action-type').val();

                    if (config.type === 'set_variable' || config.type === 'increment') {
                        config.variable_name = $('#action-variable-name').val();
                        config.variable_value = $('#action-variable-value').val();
                    }
                    break;

                case 'moderation':
                    config.action = $('#moderation-action').val();
                    config.reason = $('#moderation-reason').val();
                    break;
            }

            // Store configuration in node data attribute
            $('#' + nodeId).data('config', config);

            // Update node display
            updateNodeDisplay(nodeId, nodeType, config);

            // Close modal
            $('.node-config-modal').removeClass('show');
            setTimeout(() => {
                $('.node-config-modal').remove();
            }, 300);
        });
    }

    /**
     * Update node display based on configuration
     */
    function updateNodeDisplay(nodeId, type, config) {
        const $node = $('#' + nodeId);
        const $content = $node.find('.flow-node-content');

        // Clear existing content
        $content.empty();

        // Add content based on node type and configuration
        switch(type) {
            case 'message':
                if (config.text) {
                    $content.html('<p class="flow-node-text">' + truncateText(config.text, 50) + '</p>');
                    $node.find('.flow-node-title').text('Message (' + config.format + ')');
                }
                break;

            case 'button':
                if (config.text) {
                    $content.html('<p class="flow-node-text">Button: ' + config.text + '</p>');
                    $node.find('.flow-node-title').text('Button (' + config.type + ')');
                }
                break;

            case 'condition':
                if (config.type === 'text' && config.text) {
                    $content.html('<p class="flow-node-text">If text contains: ' + truncateText(config.text, 30) + '</p>');
                } else if (config.type === 'command' && config.command) {
                    $content.html('<p class="flow-node-text">If command: ' + config.command + '</p>');
                }
                break;

            case 'captcha':
                $content.html('<p class="flow-node-text">CAPTCHA: ' + config.type + '</p>');
                break;

            case 'action':
                if (config.type === 'set_variable' && config.variable_name) {
                    $content.html('<p class="flow-node-text">Set: ' + config.variable_name + ' = ' + config.variable_value + '</p>');
                } else if (config.type === 'increment' && config.variable_name) {
                    $content.html('<p class="flow-node-text">Increment: ' + config.variable_name + '</p>');
                } else {
                    $content.html('<p class="flow-node-text">Action: ' + config.type + '</p>');
                }
                break;

            case 'moderation':
                if (config.action) {
                    $content.html('<p class="flow-node-text">Moderation: ' + config.action + '</p>');
                }
                break;
        }
    }

    /**
     * Show context menu for a node
     */
    function showNodeContextMenu(nodeId, x, y) {
        // Remove any existing context menu
        $('.node-context-menu').remove();

        // Create context menu HTML
        const menuHtml = '<div class="node-context-menu" style="left: ' + x + 'px; top: ' + y + 'px;">' +
            '<ul>' +
            '<li class="edit-node" data-node-id="' + nodeId + '"><i class="dashicons dashicons-edit"></i> Edit</li>' +
            '<li class="duplicate-node" data-node-id="' + nodeId + '"><i class="dashicons dashicons-admin-page"></i> Duplicate</li>' +
            '<li class="delete-node" data-node-id="' + nodeId + '"><i class="dashicons dashicons-trash"></i> Delete</li>' +
            '</ul>' +
            '</div>';

        // Add context menu to the page
        $('body').append(menuHtml);

        // Add event handlers
        $('.edit-node').on('click', function() {
            const nodeId = $(this).data('node-id');
            const nodeType = $('#' + nodeId).data('type');
            showNodeConfigModal(nodeId, nodeType);
        });

        $('.duplicate-node').on('click', function() {
            const nodeId = $(this).data('node-id');
            duplicateNode(nodeId);
        });

        $('.delete-node').on('click', function() {
            const nodeId = $(this).data('node-id');
            deleteNode(nodeId);
        });

        // Prevent context menu from closing when clicking inside it
        $('.node-context-menu').on('click', function(e) {
            e.stopPropagation();
        });
    }

    /**
     * Duplicate a node
     */
    function duplicateNode(nodeId) {
        const $originalNode = $('#' + nodeId);
        const nodeType = $originalNode.data('type');
        const nodeConfig = $originalNode.data('config') || {};

        // Create a new node
        const newNodeId = 'node-' + Date.now();
        const newNodeHtml = createNodeHtml(newNodeId, nodeType);

        // Add the new node to the canvas
        $('#flow-editor-canvas').append(newNodeHtml);

        // Position the new node near the original
        const originalPosition = $originalNode.position();
        $('#' + newNodeId).css({
            left: originalPosition.left + 20,
            top: originalPosition.top + 20
        });

        // Initialize the new node
        initNode(newNodeId, nodeType);

        // Copy configuration
        $('#' + newNodeId).data('config', JSON.parse(JSON.stringify(nodeConfig)));

        // Update display
        updateNodeDisplay(newNodeId, nodeType, nodeConfig);
    }

    /**
     * Delete a node
     */
    function deleteNode(nodeId) {
        // Remove all connections
        window.flowJsPlumb.remove(nodeId);

        // Remove the node
        $('#' + nodeId).remove();
    }

    /**
     * Truncate text to a specified length
     */
    function truncateText(text, maxLength) {
        if (!text) return '';

        if (text.length <= maxLength) {
            return text;
        }

        return text.substring(0, maxLength) + '...';
    }

    /**
     * Show command modal
     */
    function showCommandModal() {
        // Remove any existing modal
        $('.command-modal').remove();

        // Create modal HTML
        const modalHtml = '<div class="node-config-modal command-modal">' +
            '<div class="node-config-content">' +
            '<div class="node-config-header">' +
            '<h3>Add New Command</h3>' +
            '<button class="close-modal">&times;</button>' +
            '</div>' +
            '<div class="node-config-body">' +
            '<div class="form-group">' +
            '<label for="command-name">Command</label>' +
            '<input type="text" id="command-name" placeholder="/command" required>' +
            '<p class="description">The command that users will type to trigger this response.</p>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="command-description">Description</label>' +
            '<input type="text" id="command-description" placeholder="Command description">' +
            '<p class="description">A short description of what this command does.</p>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="command-response-type">Response Type</label>' +
            '<select id="command-response-type">' +
            '<option value="text">Text</option>' +
            '<option value="flow">Flow</option>' +
            '</select>' +
            '<p class="description">How the bot should respond to this command.</p>' +
            '</div>' +
            '<div class="form-group response-text-group">' +
            '<label for="command-response-text">Response Text</label>' +
            '<textarea id="command-response-text" rows="4" placeholder="Enter the response text"></textarea>' +
            '<p class="description">The text that the bot will send in response to this command.</p>' +
            '</div>' +
            '<div class="form-group response-flow-group" style="display:none;">' +
            '<label for="command-response-flow">Response Flow</label>' +
            '<select id="command-response-flow">' +
            '<option value="">Select a flow</option>' +
            '</select>' +
            '<p class="description">The flow that will be triggered by this command.</p>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="command-active">Status</label>' +
            '<label class="switch">' +
            '<input type="checkbox" id="command-active" value="1" checked>' +
            '<span class="slider round"></span>' +
            '</label>' +
            '<span class="status-label">Active</span>' +
            '<p class="description">Enable or disable this command.</p>' +
            '</div>' +
            '</div>' +
            '<div class="node-config-footer">' +
            '<button class="save-command">Save Command</button>' +
            '</div>' +
            '</div>' +
            '</div>';

        // Add modal to the page
        $('body').append(modalHtml);

        // Show modal with animation
        setTimeout(() => {
            $('.command-modal').addClass('show');
        }, 10);

        // Add event handlers
        $('.close-modal').on('click', function() {
            $('.command-modal').removeClass('show');
            setTimeout(() => {
                $('.command-modal').remove();
            }, 300);
        });

        // Handle response type change
        $('#command-response-type').on('change', function() {
            const responseType = $(this).val();

            $('.response-text-group, .response-flow-group').hide();

            if (responseType === 'text') {
                $('.response-text-group').show();
            } else if (responseType === 'flow') {
                $('.response-flow-group').show();
            }
        });

        // Handle save button click
        $('.save-command').on('click', function() {
            // Get form values
            const command = $('#command-name').val();
            const description = $('#command-description').val();
            const responseType = $('#command-response-type').val();
            const responseText = $('#command-response-text').val();
            const responseFlow = $('#command-response-flow').val();
            const isActive = $('#command-active').is(':checked');

            // Validate form
            if (!command) {
                showNotification('Please enter a command.', 'error');
                return;
            }

            if (responseType === 'text' && !responseText) {
                showNotification('Please enter a response text.', 'error');
                return;
            }

            if (responseType === 'flow' && !responseFlow) {
                showNotification('Please select a flow.', 'error');
                return;
            }

            // Add command to table
            const commandRow = '<tr>' +
                '<td>' + command + '</td>' +
                '<td>' + (description || '') + '</td>' +
                '<td>' + (responseType === 'text' ? 'Text Response' : 'Flow') + '</td>' +
                '<td>' + (isActive ? '<span class="status-active">Active</span>' : '<span class="status-inactive">Inactive</span>') + '</td>' +
                '<td>' +
                '<a href="#" class="button button-small edit-command" data-command="' + command + '">Edit</a> ' +
                '<a href="#" class="button button-small button-link-delete delete-command" data-command="' + command + '">Delete</a>' +
                '</td>' +
                '</tr>';

            $('.command-table tbody').append(commandRow);

            // Close modal
            $('.command-modal').removeClass('show');
            setTimeout(() => {
                $('.command-modal').remove();
            }, 300);

            // Show success message
            showNotification('Command added successfully.', 'success');

            // Update preview
            if (responseType === 'text') {
                // Add command to preview
                addMessageToPreview(command, 'user');

                // Add response to preview
                setTimeout(() => {
                    addMessageToPreview(responseText, 'bot');
                }, 1000);
            }
        });
    }

    /**
     * Add message to preview
     */
    function addMessageToPreview(text, type) {
        // Get current time
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const timeString = hours + ':' + minutes;

        // Create message HTML
        const messageHtml = '<div class="telegram-message ' + type + '-message">' +
            '<div class="message-content">' + formatMessageText(text) + '</div>' +
            '<div class="message-time">' + timeString + '</div>' +
            '</div>';

        // Add message to chat
        $('.telegram-chat').append(messageHtml);

        // Scroll to bottom
        $('.telegram-chat').scrollTop($('.telegram-chat')[0].scrollHeight);
    }

    /**
     * Add keyboard to preview
     */
    function addKeyboardToPreview(buttons) {
        // Remove existing keyboard
        $('.telegram-keyboard').remove();

        // Create keyboard HTML
        let keyboardHtml = '<div class="telegram-keyboard">';

        // Add buttons
        buttons.forEach(button => {
            keyboardHtml += '<div class="telegram-button">' + button + '</div>';
        });

        keyboardHtml += '</div>';

        // Add keyboard to chat
        $('.telegram-chat').append(keyboardHtml);

        // Scroll to bottom
        $('.telegram-chat').scrollTop($('.telegram-chat')[0].scrollHeight);

        // Add click handler for buttons
        $('.telegram-button').on('click', function() {
            const buttonText = $(this).text();

            // Add button text as user message
            addMessageToPreview('/' + buttonText.toLowerCase(), 'user');

            // Remove keyboard
            $('.telegram-keyboard').remove();

            // Simulate bot response
            setTimeout(() => {
                if (buttonText.toLowerCase() === 'help') {
                    addMessageToPreview('Here are the available commands:\n/start - Start the bot\n/help - Show this help message\n/about - About this bot', 'bot');
                } else if (buttonText.toLowerCase() === 'about') {
                    addMessageToPreview('This is a Telegram bot created with Farm Faucet\'s Telegram Bot Builder.', 'bot');
                }
            }, 1000);
        });
    }

    /**
     * Format message text with newlines
     */
    function formatMessageText(text) {
        return text.replace(/\n/g, '<br>');
    }

    /**
     * Show template preview modal
     */
    function showTemplatePreviewModal(templateId) {
        // Remove any existing modal
        $('.template-preview-modal').remove();

        // Get template data
        const templateData = getTemplateData(templateId);

        if (!templateData) {
            showNotification('Template not found.', 'error');
            return;
        }

        // Create modal HTML
        const modalHtml = '<div class="node-config-modal template-preview-modal">' +
            '<div class="node-config-content">' +
            '<div class="node-config-header">' +
            '<h3>Template Preview: ' + templateData.name + '</h3>' +
            '<button class="close-modal">&times;</button>' +
            '</div>' +
            '<div class="node-config-body">' +
            '<div class="template-preview-description">' +
            '<p>' + templateData.description + '</p>' +
            '</div>' +
            '<div class="template-preview-image">' +
            '<img src="' + templateData.previewImage + '" alt="' + templateData.name + ' Preview">' +
            '</div>' +
            '<div class="template-preview-features">' +
            '<h4>Features</h4>' +
            '<ul>' + templateData.features.map(feature => '<li>' + feature + '</li>').join('') + '</ul>' +
            '</div>' +
            '</div>' +
            '<div class="node-config-footer">' +
            '<button class="use-template-from-preview" data-template="' + templateId + '">Use This Template</button>' +
            '</div>' +
            '</div>' +
            '</div>';

        // Add modal to the page
        $('body').append(modalHtml);

        // Show modal with animation
        setTimeout(() => {
            $('.template-preview-modal').addClass('show');
        }, 10);

        // Add event handlers
        $('.close-modal').on('click', function() {
            $('.template-preview-modal').removeClass('show');
            setTimeout(() => {
                $('.template-preview-modal').remove();
            }, 300);
        });

        // Use template button
        $('.use-template-from-preview').on('click', function() {
            const template = $(this).data('template');

            // Close modal
            $('.template-preview-modal').removeClass('show');
            setTimeout(() => {
                $('.template-preview-modal').remove();

                // Show flow editor tab with the selected template
                $('.farmfaucet-tg-bot-tabs-nav a[href="#flow-editor"]').click();

                // Load template data
                loadTemplateData(template);

                // Show success notification
                showNotification('Template loaded successfully. You can now customize it.', 'success');
            }, 300);
        });
    }

    /**
     * Get template data
     */
    function getTemplateData(templateId) {
        // Template data (in production, this would be loaded from the server)
        const templates = {
            welcome_basic: {
                name: 'Basic Welcome',
                description: 'A simple welcome message with basic instructions for new users.',
                previewImage: 'https://via.placeholder.com/600x400?text=Welcome+Template+Preview',
                features: [
                    'Welcome message for new users',
                    'Basic command instructions',
                    'Help menu',
                    'About section'
                ],
                nodes: [
                    {
                        id: 'node-start',
                        type: 'message',
                        position: { x: 100, y: 100 },
                        config: {
                            text: 'Welcome to our bot! 👋\n\nI\'m here to help you get started.',
                            format: 'text'
                        }
                    },
                    {
                        id: 'node-buttons',
                        type: 'button',
                        position: { x: 100, y: 250 },
                        config: {
                            text: 'Help',
                            type: 'callback',
                            callback_data: 'help'
                        }
                    }
                ],
                connections: [
                    {
                        source: 'node-start',
                        target: 'node-buttons'
                    }
                ]
            },
            welcome_registration: {
                name: 'User Registration',
                description: 'Welcome flow with user registration and profile setup.',
                previewImage: 'https://via.placeholder.com/600x400?text=Registration+Template+Preview',
                features: [
                    'User registration form',
                    'Profile setup wizard',
                    'Data validation',
                    'Welcome message'
                ],
                nodes: [],
                connections: []
            },
            faucet_basic: {
                name: 'Basic Faucet',
                description: 'Simple faucet flow with CAPTCHA verification.',
                previewImage: 'https://via.placeholder.com/600x400?text=Faucet+Template+Preview',
                features: [
                    'CAPTCHA verification',
                    'Reward distribution',
                    'Cooldown timer',
                    'Balance checking'
                ],
                nodes: [],
                connections: []
            },
            faucet_advanced: {
                name: 'Advanced Faucet',
                description: 'Faucet with user balance, referrals, and withdrawal options.',
                previewImage: 'https://via.placeholder.com/600x400?text=Advanced+Faucet+Preview',
                features: [
                    'Multiple reward tiers',
                    'Referral system',
                    'Withdrawal options',
                    'User statistics'
                ],
                nodes: [],
                connections: []
            },
            support_faq: {
                name: 'FAQ Bot',
                description: 'Frequently asked questions with automated responses.',
                previewImage: 'https://via.placeholder.com/600x400?text=FAQ+Template+Preview',
                features: [
                    'Common question detection',
                    'Automated responses',
                    'Keyword matching',
                    'Fallback to human support'
                ],
                nodes: [],
                connections: []
            },
            support_ticket: {
                name: 'Support Ticket',
                description: 'Support ticket system with admin notification.',
                previewImage: 'https://via.placeholder.com/600x400?text=Support+Ticket+Preview',
                features: [
                    'Ticket creation',
                    'Admin notifications',
                    'Status tracking',
                    'Priority levels'
                ],
                nodes: [],
                connections: []
            },
            moderation_basic: {
                name: 'Basic Moderation',
                description: 'Simple moderation with spam detection and user warnings.',
                previewImage: 'https://via.placeholder.com/600x400?text=Moderation+Template+Preview',
                features: [
                    'Spam detection',
                    'User warnings',
                    'Mute functionality',
                    'Ban management'
                ],
                nodes: [],
                connections: []
            },
            moderation_advanced: {
                name: 'Advanced Moderation',
                description: 'Advanced moderation with user levels, auto-moderation, and admin controls.',
                previewImage: 'https://via.placeholder.com/600x400?text=Advanced+Moderation+Preview',
                features: [
                    'User permission levels',
                    'Auto-moderation rules',
                    'Content filtering',
                    'Detailed logs'
                ],
                nodes: [],
                connections: []
            }
        };

        return templates[templateId] || null;
    }

    /**
     * Load template data into flow editor
     */
    function loadTemplateData(templateId) {
        // Get template data
        const templateData = getTemplateData(templateId);

        if (!templateData) {
            showNotification('Template not found.', 'error');
            return;
        }

        // Clear canvas
        $('#flow-editor-canvas').empty();

        // Set flow name based on template
        $('#flow-name').val(templateData.name);

        // Create nodes
        templateData.nodes.forEach(nodeData => {
            // Create node HTML
            const nodeHtml = createNodeHtml(nodeData.id, nodeData.type);

            // Add node to canvas
            $('#flow-editor-canvas').append(nodeHtml);

            // Position node
            $('#' + nodeData.id).css({
                left: nodeData.position.x + 'px',
                top: nodeData.position.y + 'px'
            });

            // Initialize node with jsPlumb
            initNode(nodeData.id, nodeData.type);

            // Store configuration
            $('#' + nodeData.id).data('config', nodeData.config);

            // Update node display
            updateNodeDisplay(nodeData.id, nodeData.type, nodeData.config);
        });

        // Create connections
        setTimeout(() => {
            templateData.connections.forEach(connection => {
                // Get source and target endpoints
                const sourceEndpoints = window.flowJsPlumb.getEndpoints(connection.source);
                const targetEndpoints = window.flowJsPlumb.getEndpoints(connection.target);

                if (sourceEndpoints && sourceEndpoints.length > 0 && targetEndpoints && targetEndpoints.length > 0) {
                    // Connect source output to target input
                    window.flowJsPlumb.connect({
                        source: sourceEndpoints[0],
                        target: targetEndpoints[0]
                    });
                }
            });
        }, 100);
    }

})(jQuery);
