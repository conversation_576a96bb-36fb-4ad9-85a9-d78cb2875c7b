/**
 * Currency Maker Admin CSS
 */

/* Admin Cards */
.currency-maker-section .farmfaucet-admin-card {
    margin-bottom: 30px;
}

.currency-maker-section .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.currency-maker-section .farmfaucet-add-currency-button {
    margin-left: auto;
}

.currency-maker-section .farmfaucet-add-currency-button .dashicons {
    margin-right: 5px;
}

/* Currencies Table */
.farmfaucet-currencies-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.farmfaucet-currencies-table th,
.farmfaucet-currencies-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.farmfaucet-currencies-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.farmfaucet-currencies-table tr:hover {
    background-color: #f9f9f9;
}

.farmfaucet-currencies-table .currency-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}

.farmfaucet-currencies-table .color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-block;
    border: 1px solid #ddd;
}

.farmfaucet-currencies-table .status-active {
    color: #4CAF50;
    font-weight: bold;
}

.farmfaucet-currencies-table .status-inactive {
    color: #f44336;
    font-weight: bold;
}

.farmfaucet-currencies-table .farmfaucet-edit-currency-button,
.farmfaucet-currencies-table .farmfaucet-delete-currency-button {
    margin-right: 5px;
}

.farmfaucet-currencies-table .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    vertical-align: text-bottom;
}

.farmfaucet-no-currencies {
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
    color: #666;
}

/* Modal */
.farmfaucet-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.farmfaucet-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.farmfaucet-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.farmfaucet-modal-header h3 {
    margin: 0;
    font-size: 20px;
}

.farmfaucet-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.farmfaucet-modal-close:hover {
    color: #000;
}

.farmfaucet-modal-body {
    padding: 20px;
}

.farmfaucet-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.farmfaucet-modal-footer button {
    margin-left: 10px;
}

/* Form Styles */
.farmfaucet-form-group {
    margin-bottom: 20px;
}

.farmfaucet-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.farmfaucet-form-group input[type="text"],
.farmfaucet-form-group input[type="number"],
.farmfaucet-form-group input[type="url"],
.farmfaucet-form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.farmfaucet-form-group .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Color Picker */
.wp-picker-container {
    display: inline-block;
}

.wp-color-result {
    margin: 0 6px 0 0 !important;
}
