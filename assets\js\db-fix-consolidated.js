/**
 * Farm Faucet Database Fix Consolidated
 *
 * This script ensures the database structure is correct for the buttons table
 * and fixes any issues with button updates
 */
(function($) {
    $(document).ready(function() {
        // Add a database fix button to the settings tab
        if ($('body').hasClass('toplevel_page_farmfaucet') &&
            (window.location.href.indexOf('tab=settings') > -1 || window.location.href.indexOf('tab=') === -1)) {
            $('.shortcode-notice').after(
                '<div class="database-fix-section">' +
                '<h3>Database Maintenance</h3>' +
                '<p class="description">Use this button to fix any database structure issues that might occur after updates.</p>' +
                '<button id="fix-database-button" class="button button-primary" style="margin-top: 10px;">Fix Database Structure</button>' +
                '</div>'
            );
            
            // Add click handler for the fix button
            $('#fix-database-button').on('click', function() {
                // Show loading state
                var $button = $(this);
                $button.text('Fixing Database...').prop('disabled', true);
                
                // Send AJAX request to update database
                $.ajax({
                    url: farmfaucet_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_fix_database',
                        nonce: farmfaucet_admin.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Database structure updated successfully!');
                        } else {
                            alert('Error updating database: ' + (response.data?.message || 'Unknown error'));
                        }

                        // Reset button
                        $button.text('Fix Database Structure').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        alert('Error communicating with server: ' + error);

                        // Reset button
                        $button.text('Fix Database Structure').prop('disabled', false);
                    }
                });
            });
        }
    });
})(jQuery);
