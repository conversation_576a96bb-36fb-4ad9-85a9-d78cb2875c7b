/**
 * Simple captcha handling for Farm Faucet
 * This is a completely rewritten, simplified approach to captcha handling
 */
(function($) {
    'use strict';

    // Ensure jQuery is available globally for captcha callbacks
    window.farmfaucetJQuery = window.farmfaucetJQuery || $;

    // Global variables
    window.farmfaucetCaptchaWidgets = window.farmfaucetCaptchaWidgets || {};
    window.farmfaucetCaptchasInitialized = false;

    // Global callback functions
    window.hCaptchaCallback = function(token) {
        console.log('hCaptcha callback called with token');
        // Enable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', false).attr('aria-disabled', 'false');
        // Update status messages if they exist
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
        // Remove loading message
        $('.farmfaucet-captcha-loading').remove();
    };

    window.hCaptchaExpiredCallback = function() {
        console.log('hCaptcha expired callback called');
        // Disable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', true).attr('aria-disabled', 'true');
        // Update status messages if they exist
        $('.pre-captcha-status').show();
        $('.post-captcha-status').hide();
    };

    window.hCaptchaErrorCallback = function(err) {
        console.error('hCaptcha error callback called:', err);
        // Show error message
        $('.farmfaucet-captcha-loading').html('Error loading captcha. Please refresh the page.').addClass('farmfaucet-error');
    };

    window.recaptchaCallback = function(token) {
        console.log('reCAPTCHA callback called with token');
        // Enable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', false).attr('aria-disabled', 'false');
        // Update status messages if they exist
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
        // Remove loading message
        $('.farmfaucet-captcha-loading').remove();
    };

    window.recaptchaExpiredCallback = function() {
        console.log('reCAPTCHA expired callback called');
        // Disable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', true).attr('aria-disabled', 'true');
        // Update status messages if they exist
        $('.pre-captcha-status').show();
        $('.post-captcha-status').hide();
    };

    window.recaptchaErrorCallback = function(err) {
        console.error('reCAPTCHA error callback called:', err);
        // Show error message
        $('.farmfaucet-captcha-loading').html('Error loading captcha. Please refresh the page.').addClass('farmfaucet-error');
    };

    window.turnstileCallback = function(token) {
        console.log('Turnstile callback called with token');
        // Enable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', false).attr('aria-disabled', 'false');
        // Update status messages if they exist
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
        // Remove loading message
        $('.farmfaucet-captcha-loading').remove();
    };

    window.turnstileExpiredCallback = function() {
        console.log('Turnstile expired callback called');
        // Disable the claim button
        $('.farmfaucet-claim-btn').prop('disabled', true).attr('aria-disabled', 'true');
        // Update status messages if they exist
        $('.pre-captcha-status').show();
        $('.post-captcha-status').hide();
    };

    window.turnstileErrorCallback = function(err) {
        console.error('Turnstile error callback called:', err);
        // Show error message
        $('.farmfaucet-captcha-loading').html('Error loading captcha. Please refresh the page.').addClass('farmfaucet-error');
    };

    // Function to render hCaptcha
    function renderHcaptcha() {
        console.log('Attempting to render hCaptcha');
        if (typeof hcaptcha === 'undefined') {
            console.error('hCaptcha API not loaded');
            // Try to load it again
            loadHcaptchaScript();
            return false;
        }

        console.log('hCaptcha API is loaded, found h-captcha elements:', $('.h-captcha').length);

        $('.h-captcha').each(function() {
            try {
                const captchaId = $(this).attr('id');
                // Try to get sitekey from data attribute first, then from global vars
                let sitekey = $(this).data('sitekey');
                if (!sitekey && window.farmfaucet_vars && window.farmfaucet_vars.hcaptcha_sitekey) {
                    sitekey = window.farmfaucet_vars.hcaptcha_sitekey;
                    // Set it on the element for future reference
                    $(this).attr('data-sitekey', sitekey);
                }

                if (!captchaId) {
                    console.error('Missing captcha ID for hCaptcha');
                    return;
                }

                if (!sitekey) {
                    console.error('Missing sitekey for hCaptcha');
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                        .html('hCaptcha site key not configured. Please contact the site administrator.')
                        .addClass('farmfaucet-error');
                    return;
                }

                // Check if this captcha has already been rendered
                if (window.farmfaucetCaptchaWidgets[captchaId]) {
                    console.log('hCaptcha already rendered for', captchaId);
                    return;
                }

                // Get the form container for this captcha
                const formContainer = $(this).closest('.farmfaucet-container');

                // Check if this form is using a different captcha type
                const formCaptchaType = formContainer.find('input[name="captcha_type"]').val();
                if (formCaptchaType && formCaptchaType !== 'hcaptcha') {
                    console.log('Skipping hCaptcha rendering for', captchaId, 'because form uses', formCaptchaType);
                    return;
                }

                console.log('Rendering hCaptcha for', captchaId, 'with sitekey:', sitekey);

                // Make sure the element is visible
                $(this).css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure the parent container is visible
                $(this).closest('.farmfaucet-captcha-container').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Render the captcha
                try {
                    window.farmfaucetCaptchaWidgets[captchaId] = hcaptcha.render(captchaId, {
                        sitekey: sitekey,
                        theme: 'light',
                        size: 'normal',
                        callback: window.hCaptchaCallback,
                        'expired-callback': window.hCaptchaExpiredCallback,
                        'error-callback': window.hCaptchaErrorCallback
                    });

                    console.log('hCaptcha rendered successfully for', captchaId);

                    // Remove loading message
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading').remove();
                } catch (renderError) {
                    console.error('Error in hcaptcha.render:', renderError);
                    // Try a different approach if rendering fails
                    $(this).html('<div class="h-captcha" data-sitekey="' + sitekey + '"></div>');
                    hcaptcha.render($(this).find('.h-captcha')[0]);
                }
            } catch (e) {
                console.error('Error rendering hCaptcha:', e);
                $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                    .html('Error loading captcha. Please refresh the page.')
                    .addClass('farmfaucet-error');
            }
        });

        return true;
    }

    // Function to load hCaptcha script
    function loadHcaptchaScript() {
        if (document.querySelector('script[src*="hcaptcha.com/1/api.js"]')) {
            console.log('hCaptcha script already loaded');
            return;
        }

        console.log('Loading hCaptcha script');
        const script = document.createElement('script');
        script.src = 'https://js.hcaptcha.com/1/api.js?onload=farmfaucetHcaptchaLoad&render=explicit';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    }

    // Function to load reCAPTCHA script
    function loadRecaptchaScript() {
        if (document.querySelector('script[src*="google.com/recaptcha/api.js"]')) {
            console.log('reCAPTCHA script already loaded');
            return;
        }

        console.log('Loading reCAPTCHA script');
        const script = document.createElement('script');
        script.src = 'https://www.google.com/recaptcha/api.js?onload=farmfaucetRecaptchaLoad&render=explicit';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    }

    // Function to load Cloudflare Turnstile script
    function loadTurnstileScript() {
        if (document.querySelector('script[src*="challenges.cloudflare.com/turnstile/v0/api.js"]')) {
            console.log('Turnstile script already loaded');
            return;
        }

        console.log('Loading Turnstile script');
        const script = document.createElement('script');
        script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=farmfaucetTurnstileLoad&render=explicit';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    }

    // Function to render reCAPTCHA
    function renderRecaptcha() {
        console.log('Attempting to render reCAPTCHA');
        if (typeof grecaptcha === 'undefined' || typeof grecaptcha.render !== 'function') {
            console.error('reCAPTCHA API not loaded');
            // Try to load it again
            loadRecaptchaScript();
            return false;
        }

        console.log('reCAPTCHA API is loaded, found g-recaptcha elements:', $('.g-recaptcha').length);

        $('.g-recaptcha').each(function() {
            try {
                const captchaId = $(this).attr('id');
                // Try to get sitekey from data attribute first, then from global vars
                let sitekey = $(this).data('sitekey');
                if (!sitekey && window.farmfaucet_vars && window.farmfaucet_vars.recaptcha_sitekey) {
                    sitekey = window.farmfaucet_vars.recaptcha_sitekey;
                    // Set it on the element for future reference
                    $(this).attr('data-sitekey', sitekey);
                }

                if (!captchaId) {
                    console.error('Missing captcha ID for reCAPTCHA');
                    return;
                }

                if (!sitekey) {
                    console.error('Missing sitekey for reCAPTCHA');
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                        .html('reCAPTCHA site key not configured. Please contact the site administrator.')
                        .addClass('farmfaucet-error');
                    return;
                }

                // Check if this captcha has already been rendered
                if (window.farmfaucetCaptchaWidgets[captchaId]) {
                    console.log('reCAPTCHA already rendered for', captchaId);
                    return;
                }

                // Get the form container for this captcha
                const formContainer = $(this).closest('.farmfaucet-container');

                // Check if this form is using a different captcha type
                const formCaptchaType = formContainer.find('input[name="captcha_type"]').val();
                if (formCaptchaType && formCaptchaType !== 'recaptcha') {
                    console.log('Skipping reCAPTCHA rendering for', captchaId, 'because form uses', formCaptchaType);
                    return;
                }

                console.log('Rendering reCAPTCHA for', captchaId, 'with sitekey:', sitekey);

                // Make sure the element is visible
                $(this).css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure the parent container is visible
                $(this).closest('.farmfaucet-captcha-container').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Render the captcha
                try {
                    window.farmfaucetCaptchaWidgets[captchaId] = grecaptcha.render(captchaId, {
                        'sitekey': sitekey,
                        'theme': 'light',
                        'size': 'normal',
                        'callback': window.recaptchaCallback,
                        'expired-callback': window.recaptchaExpiredCallback,
                        'error-callback': window.recaptchaErrorCallback
                    });

                    console.log('reCAPTCHA rendered successfully for', captchaId);

                    // Remove loading message
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading').remove();
                } catch (renderError) {
                    console.error('Error in grecaptcha.render:', renderError);
                    // Try a different approach if rendering fails
                    $(this).html('<div class="g-recaptcha" data-sitekey="' + sitekey + '"></div>');
                    grecaptcha.render($(this).find('.g-recaptcha')[0]);
                }
            } catch (e) {
                console.error('Error rendering reCAPTCHA:', e);
                $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                    .html('Error loading captcha. Please refresh the page.')
                    .addClass('farmfaucet-error');
            }
        });

        return true;
    }

    // Callback for hCaptcha API load
    window.farmfaucetHcaptchaLoad = function() {
        console.log('hCaptcha API loaded');
        renderHcaptcha();
    };

    // Callback for reCAPTCHA API load
    window.farmfaucetRecaptchaLoad = function() {
        console.log('reCAPTCHA API loaded');
        renderRecaptcha();
    };

    // Function to render Cloudflare Turnstile
    function renderTurnstile() {
        console.log('Attempting to render Turnstile');
        if (typeof turnstile === 'undefined') {
            console.error('Turnstile API not loaded');
            // Try to load it again
            loadTurnstileScript();
            return false;
        }

        console.log('Turnstile API is loaded, found cf-turnstile elements:', $('.cf-turnstile').length);

        $('.cf-turnstile').each(function() {
            try {
                const captchaId = $(this).attr('id');
                // Try to get sitekey from data attribute first, then from global vars
                let sitekey = $(this).data('sitekey');
                if (!sitekey && window.farmfaucet_vars && window.farmfaucet_vars.turnstile_sitekey) {
                    sitekey = window.farmfaucet_vars.turnstile_sitekey;
                    // Set it on the element for future reference
                    $(this).attr('data-sitekey', sitekey);
                }

                if (!captchaId) {
                    console.error('Missing captcha ID for Turnstile');
                    return;
                }

                if (!sitekey) {
                    console.error('Missing sitekey for Turnstile');
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                        .html('Cloudflare Turnstile site key not configured. Please contact the site administrator.')
                        .addClass('farmfaucet-error');
                    return;
                }

                // Check if this captcha has already been rendered
                if (window.farmfaucetCaptchaWidgets[captchaId]) {
                    console.log('Turnstile already rendered for', captchaId);
                    return;
                }

                // Get the form container for this captcha
                const formContainer = $(this).closest('.farmfaucet-container');

                // Check if this form is using a different captcha type
                const formCaptchaType = formContainer.find('input[name="captcha_type"]').val();
                if (formCaptchaType && formCaptchaType !== 'turnstile') {
                    console.log('Skipping Turnstile rendering for', captchaId, 'because form uses', formCaptchaType);
                    return;
                }

                console.log('Rendering Turnstile for', captchaId, 'with sitekey:', sitekey);

                // Make sure the element is visible
                $(this).css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure the parent container is visible
                $(this).closest('.farmfaucet-captcha-container').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Render the captcha
                try {
                    window.farmfaucetCaptchaWidgets[captchaId] = turnstile.render('#' + captchaId, {
                        sitekey: sitekey,
                        theme: 'light',
                        size: 'normal',
                        callback: window.turnstileCallback,
                        'expired-callback': window.turnstileExpiredCallback,
                        'error-callback': window.turnstileErrorCallback
                    });

                    console.log('Turnstile rendered successfully for', captchaId);

                    // Remove loading message
                    $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading').remove();
                } catch (renderError) {
                    console.error('Error in turnstile.render:', renderError);
                    // Try a different approach if rendering fails
                    $(this).html('<div class="cf-turnstile" data-sitekey="' + sitekey + '"></div>');
                    turnstile.render($(this).find('.cf-turnstile')[0]);
                }
            } catch (e) {
                console.error('Error rendering Turnstile:', e);
                $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading')
                    .html('Error loading captcha. Please refresh the page.')
                    .addClass('farmfaucet-error');
            }
        });

        return true;
    }

    // Callback for Turnstile API load
    window.farmfaucetTurnstileLoad = function() {
        console.log('Turnstile API loaded');
        renderTurnstile();
    };

    // Function to make captcha containers visible
    function makeCaptchaVisible() {
        // Make captcha containers visible
        $('.farmfaucet-captcha-container').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '100px',
            'z-index': '1'
        });

        // Make captcha wrappers visible
        $('.h-captcha-wrapper, .g-recaptcha-wrapper, .cf-turnstile-wrapper').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1',
            'justify-content': 'center',
            'min-height': '78px',
            'z-index': '1'
        });

        // Make captcha elements visible
        $('.h-captcha, .g-recaptcha, .cf-turnstile').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'min-height': '78px',
            'z-index': '1'
        });

        // Make sure iframes are visible
        $('iframe[src*="hcaptcha.com"], iframe[src*="google.com/recaptcha"], iframe[src*="challenges.cloudflare.com"]').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'z-index': '1'
        });
    }

    // Function to initialize captcha based on type
    function initializeCaptcha() {
        console.log('Initializing captcha');

        // Make sure elements are visible first
        makeCaptchaVisible();

        // Get captcha type from the form first, then global vars, or default to hCaptcha
        let captchaType = 'hcaptcha';

        // Check if there's a form-specific captcha type
        const captchaTypeInput = $('input[name="captcha_type"]');
        console.log('Found captcha type input:', captchaTypeInput.length, 'elements');

        if (captchaTypeInput.length > 0) {
            const formCaptchaType = captchaTypeInput.val();
            console.log('Form captcha type value:', formCaptchaType);

            if (formCaptchaType && (formCaptchaType === 'hcaptcha' || formCaptchaType === 'recaptcha' || formCaptchaType === 'turnstile')) {
                captchaType = formCaptchaType;
                console.log('Using form-specific captcha type:', captchaType);
            }
        } else {
            console.log('No captcha type input found in form, checking global vars');
            if (window.farmfaucet_vars && window.farmfaucet_vars.captcha_type) {
                captchaType = window.farmfaucet_vars.captcha_type;
                console.log('Using global captcha type:', captchaType);
            }
        }

        // Log the captcha containers we found
        console.log('hCaptcha containers:', $('.h-captcha').length);
        console.log('reCAPTCHA containers:', $('.g-recaptcha').length);
        console.log('Turnstile containers:', $('.cf-turnstile').length);

        console.log('Final captcha type:', captchaType);

        // Initialize the appropriate captcha type
        if (captchaType === 'hcaptcha') {
            // Check if hCaptcha API is loaded
            if (typeof hcaptcha !== 'undefined') {
                console.log('hCaptcha API already loaded, rendering');
                renderHcaptcha();
            } else {
                console.log('hCaptcha API not loaded, loading script');
                loadHcaptchaScript();
            }
        } else if (captchaType === 'recaptcha') {
            // Check if reCAPTCHA API is loaded
            if (typeof grecaptcha !== 'undefined' && typeof grecaptcha.render === 'function') {
                console.log('reCAPTCHA API already loaded, rendering');
                renderRecaptcha();
            } else {
                console.log('reCAPTCHA API not loaded, loading script');
                loadRecaptchaScript();
            }
        } else if (captchaType === 'turnstile') {
            // Check if Turnstile API is loaded
            if (typeof turnstile !== 'undefined') {
                console.log('Turnstile API already loaded, rendering');
                renderTurnstile();
            } else {
                console.log('Turnstile API not loaded, loading script');
                loadTurnstileScript();
            }
        } else {
            console.error('Unknown captcha type:', captchaType);
        }
    }

    // Function to try direct rendering as a last resort
    function tryDirectRendering() {
        console.log('Trying direct rendering');

        // Get captcha type from the form first, then global vars, or default to hCaptcha
        let captchaType = 'hcaptcha';

        // Check if there's a form-specific captcha type
        const formCaptchaType = $('input[name="captcha_type"]').val();
        if (formCaptchaType && (formCaptchaType === 'hcaptcha' || formCaptchaType === 'recaptcha' || formCaptchaType === 'turnstile')) {
            captchaType = formCaptchaType;
            console.log('Using form-specific captcha type for direct rendering:', captchaType);
        } else if (window.farmfaucet_vars && window.farmfaucet_vars.captcha_type) {
            captchaType = window.farmfaucet_vars.captcha_type;
            console.log('Using global captcha type for direct rendering:', captchaType);
        }

        console.log('Final captcha type for direct rendering:', captchaType);

        if (captchaType === 'hcaptcha') {
            if (typeof hcaptcha !== 'undefined') {
                renderHcaptcha();
            } else {
                loadHcaptchaScript();
            }
        } else if (captchaType === 'recaptcha') {
            if (typeof grecaptcha !== 'undefined' && typeof grecaptcha.render === 'function') {
                renderRecaptcha();
            } else {
                loadRecaptchaScript();
            }
        } else if (captchaType === 'turnstile') {
            if (typeof turnstile !== 'undefined') {
                renderTurnstile();
            } else {
                loadTurnstileScript();
            }
        }
    }

    // Function to ensure captcha is initialized
    function ensureCaptchaInitialized() {
        console.log('Ensuring captcha is initialized');
        makeCaptchaVisible();

        // Get the captcha type
        let captchaType = 'hcaptcha';
        if (window.farmfaucet_vars && window.farmfaucet_vars.captcha_type) {
            captchaType = window.farmfaucet_vars.captcha_type;
        }

        // Check if we have captcha containers
        const hcaptchaContainers = $('.h-captcha').length;
        const recaptchaContainers = $('.g-recaptcha').length;
        const turnstileContainers = $('.cf-turnstile').length;

        console.log('Found captcha containers:', {
            hcaptcha: hcaptchaContainers,
            recaptcha: recaptchaContainers,
            turnstile: turnstileContainers
        });

        // If we have containers, initialize the appropriate captcha
        if (hcaptchaContainers > 0 && (captchaType === 'hcaptcha' || $('.h-captcha').closest('.farmfaucet-container').find('input[name="captcha_type"][value="hcaptcha"]').length > 0)) {
            console.log('Initializing hCaptcha');
            if (typeof hcaptcha !== 'undefined') {
                renderHcaptcha();
            } else {
                loadHcaptchaScript();
            }
        }

        if (recaptchaContainers > 0 && (captchaType === 'recaptcha' || $('.g-recaptcha').closest('.farmfaucet-container').find('input[name="captcha_type"][value="recaptcha"]').length > 0)) {
            console.log('Initializing reCAPTCHA');
            if (typeof grecaptcha !== 'undefined' && typeof grecaptcha.render === 'function') {
                renderRecaptcha();
            } else {
                loadRecaptchaScript();
            }
        }

        if (turnstileContainers > 0 && (captchaType === 'turnstile' || $('.cf-turnstile').closest('.farmfaucet-container').find('input[name="captcha_type"][value="turnstile"]').length > 0)) {
            console.log('Initializing Turnstile');
            if (typeof turnstile !== 'undefined') {
                renderTurnstile();
            } else {
                loadTurnstileScript();
            }
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Document ready, initializing captcha');
        initializeCaptcha();

        // Try direct rendering after a delay if captcha still not loaded
        setTimeout(function() {
            if ($('.farmfaucet-captcha-loading').length > 0) {
                console.log('Captcha still not loaded after delay, trying direct rendering');
                tryDirectRendering();
                ensureCaptchaInitialized();
            }
        }, 1000);
    });

    // Initialize when window is fully loaded
    $(window).on('load', function() {
        console.log('Window loaded, making captcha visible');
        makeCaptchaVisible();
        ensureCaptchaInitialized();

        // Try direct rendering after a delay if captcha still not loaded
        setTimeout(function() {
            if ($('.farmfaucet-captcha-loading').length > 0) {
                console.log('Captcha still not loaded after window load, trying direct rendering');
                tryDirectRendering();
                ensureCaptchaInitialized();

                // Final attempt after a longer delay
                setTimeout(function() {
                    if ($('.farmfaucet-captcha-loading').length > 0) {
                        console.log('Final attempt to load captcha');
                        // Force reload of the appropriate script
                        const captchaType = window.farmfaucet_vars ? window.farmfaucet_vars.captcha_type : 'hcaptcha';
                        if (captchaType === 'hcaptcha') {
                            loadHcaptchaScript();
                        } else if (captchaType === 'recaptcha') {
                            loadRecaptchaScript();
                        } else if (captchaType === 'turnstile') {
                            loadTurnstileScript();
                        }

                        // One last attempt to make everything visible
                        makeCaptchaVisible();
                    }
                }, 2000);
            }
        }, 1000);
    });

    // Add a mutation observer to detect when new captcha elements are added to the DOM
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes are captcha containers
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1 && (node.classList.contains('farmfaucet-captcha-container') ||
                            $(node).find('.farmfaucet-captcha-container').length > 0)) {
                            console.log('Detected new captcha container, initializing');
                            setTimeout(ensureCaptchaInitialized, 100);
                            break;
                        }
                    }
                }
            });
        });

        // Start observing the document body for changes
        observer.observe(document.body, { childList: true, subtree: true });
    }

})(jQuery);
