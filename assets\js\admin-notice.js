/**
 * Farm Faucet - Admin Notice
 * This script adds an admin notice to inform users about the database update
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Admin Notice loaded');
        addDatabaseUpdateNotice();
    });

    /**
     * Add database update notice
     */
    function addDatabaseUpdateNotice() {
        // Create notice HTML
        var noticeHtml = '<div class="notice notice-warning is-dismissible">' +
            '<p><strong>Farm Faucet Database Update Required</strong></p>' +
            '<p>The Farm Faucet plugin requires a database update to fix issues with currency creation, bot creation, and form transparency.</p>' +
            '<p><a href="' + farmfaucet_admin.plugin_url + 'update-database-clean.php" class="button button-primary">Update Database</a></p>' +
            '</div>';
        
        // Add notice to the top of the page
        $('.wrap').prepend(noticeHtml);
        
        // Make notice dismissible
        $('.notice-dismiss').on('click', function() {
            $(this).closest('.notice').remove();
        });
    }
})(jQuery);
