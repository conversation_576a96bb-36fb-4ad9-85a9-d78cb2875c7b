/**
 * Telegram Bot Builder Redesign JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';

    // Bot List Section
    const botListSection = $('.bot-list-section');
    const botCommandsSection = $('.bot-commands-section');

    // Login Form Settings
    initLoginFormSettings();

    // Add New Bot
    $('.add-new-bot').on('click', function() {
        openBotModal();
    });

    // Edit Bot
    $(document).on('click', '.edit-bot', function() {
        const botId = $(this).data('bot-id');
        openBotModal(botId);
    });

    // Delete Bot
    $(document).on('click', '.delete-bot', function() {
        const botId = $(this).data('bot-id');
        const botName = $(this).closest('.bot-card').find('.bot-card-header h4').text();

        if (confirm(`Are you sure you want to delete the bot "${botName}"? This action cannot be undone.`)) {
            deleteBot(botId);
        }
    });

    // Manage Commands
    $(document).on('click', '.manage-commands', function() {
        const botId = $(this).data('bot-id');
        const botName = $(this).closest('.bot-card').find('.bot-card-header h4').text();

        // Hide bot list and show commands
        botListSection.hide();
        botCommandsSection.show();

        // Set current bot name
        $('.current-bot-name').text(botName);
        $('.current-bot-name').data('bot-id', botId);

        // Load commands for this bot
        loadBotCommands(botId);
    });

    // Back to Bots
    $('.back-to-bots').on('click', function() {
        botCommandsSection.hide();
        botListSection.show();
    });

    // Add New Command
    $('.add-new-command').on('click', function() {
        const botId = $('.current-bot-name').data('bot-id');
        openCommandModal(botId);
    });

    // Edit Command
    $(document).on('click', '.edit-command', function() {
        const commandId = $(this).data('command-id');
        openCommandModal(null, commandId);
    });

    // Delete Command
    $(document).on('click', '.delete-command', function() {
        const commandId = $(this).data('command-id');
        const commandName = $(this).closest('.command-card').find('.command-card-header h4').text();

        if (confirm(`Are you sure you want to delete the command "${commandName}"? This action cannot be undone.`)) {
            deleteCommand(commandId);
        }
    });

    /**
     * Open Bot Modal
     *
     * @param {number} botId Bot ID for editing, null for new bot
     */
    function openBotModal(botId = null) {
        // Create modal if it doesn't exist
        if ($('#bot-modal').length === 0) {
            const modalHtml = `
                <div id="bot-modal" class="farmfaucet-modal">
                    <div class="farmfaucet-modal-content">
                        <div class="farmfaucet-modal-header">
                            <h3>${botId ? 'Edit Bot' : 'Add New Bot'}</h3>
                            <button type="button" class="farmfaucet-modal-close">&times;</button>
                        </div>
                        <div class="farmfaucet-modal-body">
                            <form id="bot-form" class="farmfaucet-form">
                                <input type="hidden" name="bot_id" id="bot-id" value="${botId || ''}">

                                <div class="form-group">
                                    <label for="bot-name">Bot Name</label>
                                    <input type="text" name="bot_name" id="bot-name" class="regular-text" required>
                                    <p class="description">Enter a name for your bot. This is for your reference only.</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-token">Bot Token</label>
                                    <input type="text" name="bot_token" id="bot-token" class="regular-text" required>
                                    <p class="description">Enter the token provided by BotFather. <a href="https://core.telegram.org/bots#botfather" target="_blank">How to get a token?</a></p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-username">Bot Username</label>
                                    <input type="text" name="bot_username" id="bot-username" class="regular-text" required>
                                    <p class="description">Enter the username of your bot (without @).</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-type">Bot Type</label>
                                    <select name="bot_type" id="bot-type" required>
                                        <option value="text">Text Bot</option>
                                        <option value="chat">Chat Bot</option>
                                    </select>
                                    <p class="description">Select the type of bot you want to create.</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-active">Status</label>
                                    <label class="switch">
                                        <input type="checkbox" name="is_active" id="bot-active" value="1" checked>
                                        <span class="slider round"></span>
                                    </label>
                                    <span class="status-label">Active</span>
                                    <p class="description">Enable or disable this bot.</p>
                                </div>

                                <div class="form-group">
                                    <label for="bot-start-message">Start Message</label>
                                    <textarea name="start_message" id="bot-start-message" rows="5" class="regular-text"></textarea>
                                    <p class="description">Enter the message that will be sent when a user starts the bot.</p>
                                </div>
                            </form>
                        </div>
                        <div class="farmfaucet-modal-footer">
                            <button type="button" class="button" id="cancel-bot">Cancel</button>
                            <button type="button" class="button button-primary" id="save-bot">Save Bot</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // Close modal
            $(document).on('click', '.farmfaucet-modal-close, #cancel-bot', function() {
                closeModal('#bot-modal');
            });

            // Save bot
            $(document).on('click', '#save-bot', function() {
                saveBot();
            });

            // Toggle status label
            $(document).on('change', '#bot-active', function() {
                const statusLabel = $(this).closest('.form-group').find('.status-label');
                if ($(this).is(':checked')) {
                    statusLabel.text('Active').css('color', '#4CAF50');
                } else {
                    statusLabel.text('Inactive').css('color', '#999');
                }
            });
        }

        // Update modal title
        $('#bot-modal .farmfaucet-modal-header h3').text(botId ? 'Edit Bot' : 'Add New Bot');

        // Clear form
        $('#bot-form')[0].reset();
        $('#bot-id').val(botId || '');

        // If editing, load bot data
        if (botId) {
            loadBotData(botId);
        }

        // Show modal
        openModal('#bot-modal');
    }

    /**
     * Open Command Modal
     *
     * @param {number} botId Bot ID for new command
     * @param {number} commandId Command ID for editing, null for new command
     */
    function openCommandModal(botId = null, commandId = null) {
        // Create modal if it doesn't exist
        if ($('#command-modal').length === 0) {
            const modalHtml = `
                <div id="command-modal" class="farmfaucet-modal">
                    <div class="farmfaucet-modal-content">
                        <div class="farmfaucet-modal-header">
                            <h3>${commandId ? 'Edit Command' : 'Add New Command'}</h3>
                            <button type="button" class="farmfaucet-modal-close">&times;</button>
                        </div>
                        <div class="farmfaucet-modal-body">
                            <form id="command-form" class="farmfaucet-form">
                                <input type="hidden" name="command_id" id="command-id" value="${commandId || ''}">
                                <input type="hidden" name="bot_id" id="command-bot-id" value="${botId || ''}">

                                <div class="form-group">
                                    <label for="command-trigger">Command Trigger</label>
                                    <input type="text" name="command_trigger" id="command-trigger" class="regular-text" required>
                                    <p class="description">Enter the command that will trigger this response (e.g., /start, /help).</p>
                                </div>

                                <div class="form-group">
                                    <label for="command-description">Description</label>
                                    <input type="text" name="command_description" id="command-description" class="regular-text" required>
                                    <p class="description">Enter a description for this command.</p>
                                </div>

                                <div class="form-group">
                                    <label for="response-type">Response Type</label>
                                    <select name="response_type" id="response-type" required>
                                        <option value="text">Text Message</option>
                                        <option value="buttons">Buttons</option>
                                        <option value="action">Action</option>
                                    </select>
                                    <p class="description">Select the type of response for this command.</p>
                                </div>

                                <!-- Text Response -->
                                <div class="response-section text-response">
                                    <div class="form-group">
                                        <label for="text-response">Text Response</label>
                                        <textarea name="text_response" id="text-response" rows="5" class="regular-text"></textarea>
                                        <p class="description">Enter the text that will be sent in response to this command.</p>
                                    </div>
                                </div>

                                <!-- Button Response -->
                                <div class="response-section button-response" style="display: none;">
                                    <div class="form-group">
                                        <label for="button-message">Button Message</label>
                                        <textarea name="button_message" id="button-message" rows="5" class="regular-text"></textarea>
                                        <p class="description">Enter the message that will be displayed with the buttons.</p>
                                    </div>

                                    <div class="button-list">
                                        <h4>Buttons</h4>
                                        <div class="button-items"></div>
                                        <button type="button" class="button add-button">Add Button</button>
                                    </div>
                                </div>

                                <!-- Action Response -->
                                <div class="response-section action-response" style="display: none;">
                                    <div class="form-group">
                                        <label for="action-type">Action Type</label>
                                        <select name="action_type" id="action-type">
                                            <option value="faucet">Faucet Claim</option>
                                            <option value="balance">Check Balance</option>
                                            <option value="profile">User Profile</option>
                                            <option value="custom">Custom Action</option>
                                        </select>
                                        <p class="description">Select the type of action to perform.</p>
                                    </div>

                                    <div class="form-group action-params">
                                        <label for="action-params">Action Parameters</label>
                                        <textarea name="action_params" id="action-params" rows="5" class="regular-text"></textarea>
                                        <p class="description">Enter the parameters for this action in JSON format.</p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="command-active">Status</label>
                                    <label class="switch">
                                        <input type="checkbox" name="is_active" id="command-active" value="1" checked>
                                        <span class="slider round"></span>
                                    </label>
                                    <span class="status-label">Active</span>
                                    <p class="description">Enable or disable this command.</p>
                                </div>
                            </form>
                        </div>
                        <div class="farmfaucet-modal-footer">
                            <button type="button" class="button" id="cancel-command">Cancel</button>
                            <button type="button" class="button button-primary" id="save-command">Save Command</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // Close modal
            $(document).on('click', '.farmfaucet-modal-close, #cancel-command', function() {
                closeModal('#command-modal');
            });

            // Save command
            $(document).on('click', '#save-command', function() {
                saveCommand();
            });

            // Toggle response sections based on response type
            $(document).on('change', '#response-type', function() {
                const responseType = $(this).val();
                $('.response-section').hide();
                $(`.${responseType}-response`).show();
            });

            // Toggle status label
            $(document).on('change', '#command-active', function() {
                const statusLabel = $(this).closest('.form-group').find('.status-label');
                if ($(this).is(':checked')) {
                    statusLabel.text('Active').css('color', '#4CAF50');
                } else {
                    statusLabel.text('Inactive').css('color', '#999');
                }
            });

            // Add button
            $(document).on('click', '.add-button', function(e) {
                e.preventDefault();
                addButtonItem();
            });

            // Remove button
            $(document).on('click', '.remove-button', function(e) {
                e.preventDefault();
                $(this).closest('.button-item').remove();
            });
        }

        // Update modal title
        $('#command-modal .farmfaucet-modal-header h3').text(commandId ? 'Edit Command' : 'Add New Command');

        // Clear form
        $('#command-form')[0].reset();
        $('#command-id').val(commandId || '');
        $('#command-bot-id').val(botId || '');
        $('.button-items').empty();

        // Reset response sections
        $('.response-section').hide();
        $('.text-response').show();
        $('#response-type').val('text');

        // If editing, load command data
        if (commandId) {
            loadCommandData(commandId);
        }

        // Show modal
        openModal('#command-modal');
    }

    /**
     * Add Button Item
     *
     * @param {Object} data Button data
     */
    function addButtonItem(data = {}) {
        const buttonItem = `
            <div class="button-item">
                <div class="form-group">
                    <label>Button Text</label>
                    <input type="text" name="button_text[]" value="${data.text || ''}" class="regular-text" required>
                </div>
                <div class="form-group">
                    <label>Button Type</label>
                    <select name="button_type[]" class="button-type">
                        <option value="url" ${data.type === 'url' ? 'selected' : ''}>URL</option>
                        <option value="callback" ${data.type === 'callback' ? 'selected' : ''}>Callback</option>
                        <option value="command" ${data.type === 'command' ? 'selected' : ''}>Command</option>
                    </select>
                </div>
                <div class="form-group button-value-container">
                    <label>Button Value</label>
                    <input type="text" name="button_value[]" value="${data.value || ''}" class="regular-text" required>
                    <p class="description">For URL: enter the URL. For Callback: enter the callback data. For Command: enter the command.</p>
                </div>
                <button type="button" class="button button-link-delete remove-button">Remove</button>
            </div>
        `;

        $('.button-items').append(buttonItem);
    }

    /**
     * Load Bot Data
     *
     * @param {number} botId Bot ID
     */
    function loadBotData(botId) {
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_bot',
                nonce: farmfaucetTgBot.nonce,
                bot_id: botId
            },
            success: function(response) {
                if (response.success && response.data) {
                    const bot = response.data;

                    // Fill form fields
                    $('#bot-id').val(bot.id);
                    $('#bot-name').val(bot.bot_name);
                    $('#bot-token').val(bot.bot_token);
                    $('#bot-username').val(bot.bot_username);
                    $('#bot-type').val(bot.bot_type);
                    $('#bot-active').prop('checked', bot.is_active == 1);

                    // Update status label
                    const statusLabel = $('#bot-active').closest('.form-group').find('.status-label');
                    if (bot.is_active == 1) {
                        statusLabel.text('Active').css('color', '#4CAF50');
                    } else {
                        statusLabel.text('Inactive').css('color', '#999');
                    }

                    // Parse settings
                    if (bot.settings) {
                        try {
                            const settings = JSON.parse(bot.settings);
                            if (settings.start_message) {
                                $('#bot-start-message').val(settings.start_message);
                            }
                        } catch (e) {
                            console.error('Error parsing bot settings:', e);
                        }
                    }
                } else {
                    alert('Error loading bot data. Please try again.');
                    closeModal('#bot-modal');
                }
            },
            error: function() {
                alert('Error loading bot data. Please try again.');
                closeModal('#bot-modal');
            }
        });
    }

    /**
     * Load Command Data
     *
     * @param {number} commandId Command ID
     */
    function loadCommandData(commandId) {
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_command',
                nonce: farmfaucetTgBot.nonce,
                command_id: commandId
            },
            success: function(response) {
                if (response.success && response.data) {
                    const command = response.data;

                    // Fill form fields
                    $('#command-id').val(command.id);
                    $('#command-bot-id').val(command.bot_id);
                    $('#command-trigger').val(command.command_trigger);
                    $('#command-description').val(command.command_description);
                    $('#response-type').val(command.response_type);
                    $('#command-active').prop('checked', command.is_active == 1);

                    // Update status label
                    const statusLabel = $('#command-active').closest('.form-group').find('.status-label');
                    if (command.is_active == 1) {
                        statusLabel.text('Active').css('color', '#4CAF50');
                    } else {
                        statusLabel.text('Inactive').css('color', '#999');
                    }

                    // Show appropriate response section
                    $('.response-section').hide();
                    $(`.${command.response_type}-response`).show();

                    // Parse response data
                    if (command.response_data) {
                        try {
                            const responseData = JSON.parse(command.response_data);

                            if (command.response_type === 'text') {
                                $('#text-response').val(responseData.text || '');
                            } else if (command.response_type === 'buttons') {
                                $('#button-message').val(responseData.message || '');

                                // Add buttons
                                if (responseData.buttons && responseData.buttons.length > 0) {
                                    $('.button-items').empty();
                                    responseData.buttons.forEach(function(button) {
                                        addButtonItem(button);
                                    });
                                }
                            } else if (command.response_type === 'action') {
                                $('#action-type').val(responseData.action_type || 'faucet');
                                $('#action-params').val(JSON.stringify(responseData.params || {}, null, 2));
                            }
                        } catch (e) {
                            console.error('Error parsing command response data:', e);
                        }
                    }
                } else {
                    alert('Error loading command data. Please try again.');
                    closeModal('#command-modal');
                }
            },
            error: function() {
                alert('Error loading command data. Please try again.');
                closeModal('#command-modal');
            }
        });
    }

    /**
     * Save Bot
     */
    function saveBot() {
        const form = $('#bot-form');

        // Validate form
        if (!form[0].checkValidity()) {
            form[0].reportValidity();
            return;
        }

        // Get form data
        const formData = {
            bot_id: $('#bot-id').val(),
            bot_name: $('#bot-name').val(),
            bot_token: $('#bot-token').val(),
            bot_username: $('#bot-username').val(),
            bot_type: $('#bot-type').val(),
            is_active: $('#bot-active').is(':checked') ? 1 : 0,
            settings: JSON.stringify({
                start_message: $('#bot-start-message').val()
            })
        };

        // Save bot
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_save_bot',
                nonce: farmfaucetTgBot.nonce,
                ...formData
            },
            beforeSend: function() {
                $('#save-bot').prop('disabled', true).text('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    alert('Bot saved successfully!');
                    closeModal('#bot-modal');

                    // Reload page to show updated bot list
                    window.location.reload();
                } else {
                    alert('Error saving bot: ' + (response.data.message || 'Please try again.'));
                    $('#save-bot').prop('disabled', false).text('Save Bot');
                }
            },
            error: function() {
                alert('Error saving bot. Please try again.');
                $('#save-bot').prop('disabled', false).text('Save Bot');
            }
        });
    }

    /**
     * Save Command
     */
    function saveCommand() {
        const form = $('#command-form');

        // Validate form
        if (!form[0].checkValidity()) {
            form[0].reportValidity();
            return;
        }

        // Get form data
        const formData = {
            command_id: $('#command-id').val(),
            bot_id: $('#command-bot-id').val(),
            command_trigger: $('#command-trigger').val(),
            command_description: $('#command-description').val(),
            response_type: $('#response-type').val(),
            is_active: $('#command-active').is(':checked') ? 1 : 0
        };

        // Get response data based on type
        let responseData = {};

        if (formData.response_type === 'text') {
            responseData = {
                text: $('#text-response').val()
            };
        } else if (formData.response_type === 'buttons') {
            const buttons = [];

            // Get button data
            $('.button-item').each(function() {
                const buttonText = $(this).find('input[name="button_text[]"]').val();
                const buttonType = $(this).find('select[name="button_type[]"]').val();
                const buttonValue = $(this).find('input[name="button_value[]"]').val();

                if (buttonText && buttonValue) {
                    buttons.push({
                        text: buttonText,
                        type: buttonType,
                        value: buttonValue
                    });
                }
            });

            responseData = {
                message: $('#button-message').val(),
                buttons: buttons
            };
        } else if (formData.response_type === 'action') {
            responseData = {
                action_type: $('#action-type').val(),
                params: {}
            };

            // Parse action params
            try {
                responseData.params = JSON.parse($('#action-params').val());
            } catch (e) {
                alert('Invalid JSON in action parameters. Please check the format.');
                return;
            }
        }

        formData.response_data = JSON.stringify(responseData);

        // Save command
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_save_command',
                nonce: farmfaucetTgBot.nonce,
                ...formData
            },
            beforeSend: function() {
                $('#save-command').prop('disabled', true).text('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    alert('Command saved successfully!');
                    closeModal('#command-modal');

                    // Reload commands
                    loadBotCommands($('#command-bot-id').val());
                } else {
                    alert('Error saving command: ' + (response.data.message || 'Please try again.'));
                    $('#save-command').prop('disabled', false).text('Save Command');
                }
            },
            error: function() {
                alert('Error saving command. Please try again.');
                $('#save-command').prop('disabled', false).text('Save Command');
            }
        });
    }

    /**
     * Delete Bot
     *
     * @param {number} botId Bot ID
     */
    function deleteBot(botId) {
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_bot',
                nonce: farmfaucetTgBot.nonce,
                bot_id: botId
            },
            success: function(response) {
                if (response.success) {
                    alert('Bot deleted successfully!');

                    // Reload page to show updated bot list
                    window.location.reload();
                } else {
                    alert('Error deleting bot: ' + (response.data.message || 'Please try again.'));
                }
            },
            error: function() {
                alert('Error deleting bot. Please try again.');
            }
        });
    }

    /**
     * Delete Command
     *
     * @param {number} commandId Command ID
     */
    function deleteCommand(commandId) {
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_command',
                nonce: farmfaucetTgBot.nonce,
                command_id: commandId
            },
            success: function(response) {
                if (response.success) {
                    alert('Command deleted successfully!');

                    // Reload commands
                    const botId = $('.current-bot-name').data('bot-id');
                    loadBotCommands(botId);
                } else {
                    alert('Error deleting command: ' + (response.data.message || 'Please try again.'));
                }
            },
            error: function() {
                alert('Error deleting command. Please try again.');
            }
        });
    }

    /**
     * Load Bot Commands
     *
     * @param {number} botId Bot ID
     */
    function loadBotCommands(botId) {
        $.ajax({
            url: farmfaucetTgBot.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_commands',
                nonce: farmfaucetTgBot.nonce,
                bot_id: botId
            },
            beforeSend: function() {
                $('.command-grid').hide();
                $('.no-commands-message').hide();
                $('.command-list-container').append('<div class="loading-container"><div class="spinner"></div><p>Loading commands...</p></div>');
            },
            success: function(response) {
                $('.loading-container').remove();

                if (response.success) {
                    const commands = response.data;

                    if (commands.length === 0) {
                        $('.no-commands-message').show();
                        return;
                    }

                    // Clear command grid
                    $('.command-grid').empty();

                    // Add commands
                    commands.forEach(function(command) {
                        const commandCard = `
                            <div class="command-card" data-command-id="${command.id}">
                                <div class="command-card-header">
                                    <h4>${command.command_trigger}</h4>
                                </div>
                                <div class="command-card-body">
                                    <p class="command-description">${command.command_description}</p>
                                    <p class="command-type">
                                        ${getResponseTypeLabel(command.response_type)}
                                    </p>
                                </div>
                                <div class="command-card-actions">
                                    <button type="button" class="button edit-command" data-command-id="${command.id}">
                                        <span class="dashicons dashicons-edit"></span> Edit
                                    </button>
                                    <button type="button" class="button button-link-delete delete-command" data-command-id="${command.id}">
                                        <span class="dashicons dashicons-trash"></span> Delete
                                    </button>
                                </div>
                            </div>
                        `;

                        $('.command-grid').append(commandCard);
                    });

                    // Show command grid
                    $('.command-grid').show();
                } else {
                    $('.no-commands-message').show();
                    console.error('Error loading commands:', response.data.message);
                }
            },
            error: function() {
                $('.loading-container').remove();
                $('.no-commands-message').show();
                console.error('Error loading commands');
            }
        });
    }

    /**
     * Get Response Type Label
     *
     * @param {string} responseType Response type
     * @return {string} Response type label
     */
    function getResponseTypeLabel(responseType) {
        switch (responseType) {
            case 'text':
                return 'Text Message';
            case 'buttons':
                return 'Buttons';
            case 'action':
                return 'Action';
            default:
                return responseType;
        }
    }

    /**
     * Open Modal
     *
     * @param {string} selector Modal selector
     */
    function openModal(selector) {
        $(selector).fadeIn(300);
        $('body').addClass('modal-open');
    }

    /**
     * Close Modal
     *
     * @param {string} selector Modal selector
     */
    function closeModal(selector) {
        $(selector).fadeOut(300);
        $('body').removeClass('modal-open');
    }

    /**
     * Initialize Login Form Settings
     */
    function initLoginFormSettings() {
        // Toggle Telegram login fields
        $('#enable-tg-login').on('change', function() {
            if ($(this).is(':checked')) {
                $('.tg-login-field').fadeIn(300);
            } else {
                $('.tg-login-field').fadeOut(300);
            }
        });

        // Save login form settings
        $('.save-login-settings').on('click', function() {
            const formData = {
                action: 'farmfaucet_save_login_settings',
                nonce: farmfaucetTgBotBuilder.nonce,
                login_page_url: $('#login-page-url').val(),
                register_page_url: $('#register-page-url').val(),
                forgot_password_url: $('#forgot-password-url').val(),
                profile_page_url: $('#profile-page-url').val(),
                enable_tg_login: $('#enable-tg-login').is(':checked') ? 1 : 0,
                tg_login_bot: $('#tg-login-bot').val()
            };

            // Show loading state
            const $button = $(this);
            const originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update-alt spin"></span> Saving...');
            $button.prop('disabled', true);

            $.ajax({
                url: farmfaucetTgBotBuilder.ajaxUrl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        showNotice('Login form settings saved successfully.', 'success');
                    } else {
                        // Show error message
                        showNotice(response.data || 'Error saving login form settings.', 'error');
                    }
                },
                error: function() {
                    // Show error message
                    showNotice('Error saving login form settings. Please try again.', 'error');
                },
                complete: function() {
                    // Restore button
                    $button.html(originalText);
                    $button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Show Notice
     *
     * @param {string} message Notice message
     * @param {string} type Notice type (success, error, warning, info)
     */
    function showNotice(message, type = 'info') {
        // Remove existing notices
        $('.farmfaucet-notice').remove();

        // Create notice
        const notice = $(`<div class="farmfaucet-notice notice notice-${type} is-dismissible"><p>${message}</p></div>`);

        // Add notice to the top of the page
        $('.wrap.farmfaucet-admin-wrap').prepend(notice);

        // Add dismiss button
        notice.append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');

        // Handle dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(300, function() {
                notice.remove();
            });
        });

        // Auto dismiss after 5 seconds
        setTimeout(function() {
            notice.fadeOut(300, function() {
                notice.remove();
            });
        }, 5000);
    }