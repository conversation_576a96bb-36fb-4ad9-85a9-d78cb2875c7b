<?php

/**
 * Farmfaucet Database Updater for Countdown Standby Feature
 *
 * Adds the countdown_standby column to the buttons table
 */
class Farmfaucet_DB_Updater_Countdown_Standby
{
    /**
     * Run the update to add the countdown_standby column
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add countdown_standby column if it doesn't exist
        if (!in_array('countdown_standby', $column_names)) {
            $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN countdown_standby tinyint(1) NOT NULL DEFAULT 0 AFTER countdown_pre_click_message");
        }
    }
}
