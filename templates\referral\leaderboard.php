<?php
/**
 * Template for displaying referral leaderboard
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-referral-leaderboard-container">
    <h2 class="leaderboard-title"><?php echo esc_html($atts['title']); ?></h2>
    
    <div class="leaderboard-period-tabs">
        <a href="<?php echo esc_url(add_query_arg('period', 'all')); ?>" class="period-tab <?php echo $atts['period'] === 'all' ? 'active' : ''; ?>">
            <?php esc_html_e('All Time', 'farmfaucet'); ?>
        </a>
        <a href="<?php echo esc_url(add_query_arg('period', 'month')); ?>" class="period-tab <?php echo $atts['period'] === 'month' ? 'active' : ''; ?>">
            <?php esc_html_e('This Month', 'farmfaucet'); ?>
        </a>
        <a href="<?php echo esc_url(add_query_arg('period', 'week')); ?>" class="period-tab <?php echo $atts['period'] === 'week' ? 'active' : ''; ?>">
            <?php esc_html_e('This Week', 'farmfaucet'); ?>
        </a>
        <a href="<?php echo esc_url(add_query_arg('period', 'today')); ?>" class="period-tab <?php echo $atts['period'] === 'today' ? 'active' : ''; ?>">
            <?php esc_html_e('Today', 'farmfaucet'); ?>
        </a>
    </div>
    
    <div class="referral-leaderboard">
        <table class="leaderboard-table">
            <thead>
                <tr>
                    <th class="rank-column"><?php esc_html_e('Rank', 'farmfaucet'); ?></th>
                    <th class="user-column"><?php esc_html_e('User', 'farmfaucet'); ?></th>
                    <?php if ($atts['show_referrals']) : ?>
                        <th class="referrals-column"><?php esc_html_e('Referrals', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                    <?php if ($atts['show_earnings']) : ?>
                        <th class="earnings-column"><?php esc_html_e('Earnings', 'farmfaucet'); ?></th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($leaderboard as $index => $user) : ?>
                    <tr class="<?php echo $index < 3 ? 'top-' . ($index + 1) : ''; ?>">
                        <td class="rank-column">
                            <div class="rank-badge"><?php echo esc_html($index + 1); ?></div>
                        </td>
                        <td class="user-column">
                            <?php if ($atts['show_avatar'] && !empty($user['avatar'])) : ?>
                                <img src="<?php echo esc_url($user['avatar']); ?>" alt="<?php echo esc_attr($user['display_name']); ?>" class="user-avatar">
                            <?php endif; ?>
                            <span class="user-name"><?php echo esc_html($user['display_name']); ?></span>
                        </td>
                        <?php if ($atts['show_referrals']) : ?>
                            <td class="referrals-column">
                                <span class="referrals-count"><?php echo esc_html($user['total_referrals']); ?></span>
                            </td>
                        <?php endif; ?>
                        <?php if ($atts['show_earnings']) : ?>
                            <td class="earnings-column">
                                <span class="earnings-amount">
                                    <?php 
                                    if (!empty($currency_info)) {
                                        echo esc_html($user['total_earnings']) . ' ' . esc_html($currency_info['symbol']);
                                    } else {
                                        echo esc_html($user['total_earnings']);
                                    }
                                    ?>
                                </span>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
