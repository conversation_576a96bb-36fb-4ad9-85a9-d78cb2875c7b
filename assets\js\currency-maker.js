/**
 * Currency Maker Frontend JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initCurrencyMaker();
    });

    /**
     * Initialize Currency Maker functionality
     */
    function initCurrencyMaker() {
        // Exchange button click handler
        $('.farmfaucet-exchange-button').on('click', function() {
            const currencyId = $(this).data('currency-id');
            showExchangeForm(currencyId);
        });

        // Exchange form cancel button click handler
        $('.farmfaucet-exchange-cancel').on('click', function() {
            hideExchangeForm();
        });

        // From currency change handler
        $('#farmfaucet-exchange-from').on('change', function() {
            updateExchangeForm();
        });

        // To currency change handler
        $('#farmfaucet-exchange-to').on('change', function() {
            updateExchangeForm();
        });

        // Amount input change handler
        $('#farmfaucet-exchange-amount').on('input', function() {
            updateExchangeForm();
        });

        // Exchange form submit handler
        $('.farmfaucet-exchange-submit').on('click', function() {
            handleExchange();
        });

        // Initialize exchange form
        updateExchangeForm();
    }

    /**
     * Show exchange form
     * 
     * @param {number} currencyId Currency ID to exchange from
     */
    function showExchangeForm(currencyId) {
        // Set the selected currency
        $('#farmfaucet-exchange-from').val(currencyId).trigger('change');
        
        // Show the form
        $('.farmfaucet-exchange-form').fadeIn(300);
        
        // Scroll to the form
        $('html, body').animate({
            scrollTop: $('.farmfaucet-exchange-form').offset().top - 50
        }, 500);
    }

    /**
     * Hide exchange form
     */
    function hideExchangeForm() {
        $('.farmfaucet-exchange-form').fadeOut(300);
        $('.farmfaucet-exchange-message').hide();
    }

    /**
     * Update exchange form
     */
    function updateExchangeForm() {
        const $fromCurrency = $('#farmfaucet-exchange-from option:selected');
        const $toCurrency = $('#farmfaucet-exchange-to option:selected');
        const amount = parseFloat($('#farmfaucet-exchange-amount').val()) || 0;
        
        // Update symbols
        $('.amount-symbol').text($fromCurrency.data('symbol'));
        $('.receive-symbol').text($toCurrency.data('symbol'));
        
        // Update balance info
        const balance = parseFloat($fromCurrency.data('balance')) || 0;
        $('.balance-info').text(`Available: ${balance.toFixed(8)} ${$fromCurrency.data('symbol')}`);
        
        // Calculate receive amount
        if (amount > 0) {
            // In a real implementation, you would calculate the exchange rate
            // For now, we'll use a simplified approach
            const fee = amount * (parseFloat($('.fee-info').text()) / 100);
            const exchangeRate = 1.0; // This would be calculated based on the currencies
            const receiveAmount = (amount - fee) * exchangeRate;
            
            $('.receive-amount').text(`${receiveAmount.toFixed(8)} ${$toCurrency.data('symbol')}`);
        } else {
            $('.receive-amount').text(`0.00000000 ${$toCurrency.data('symbol')}`);
        }
    }

    /**
     * Handle exchange form submission
     */
    function handleExchange() {
        const $message = $('.farmfaucet-exchange-message');
        
        // Get form data
        const fromCurrencyId = $('#farmfaucet-exchange-from').val();
        const toCurrencyId = $('#farmfaucet-exchange-to').val();
        const amount = parseFloat($('#farmfaucet-exchange-amount').val()) || 0;
        
        // Validate form
        if (fromCurrencyId === toCurrencyId) {
            showMessage($message, 'Please select different currencies for exchange.', 'error');
            return;
        }
        
        if (amount <= 0) {
            showMessage($message, 'Please enter a valid amount.', 'error');
            return;
        }
        
        const balance = parseFloat($('#farmfaucet-exchange-from option:selected').data('balance')) || 0;
        if (amount > balance) {
            showMessage($message, 'Insufficient balance for exchange.', 'error');
            return;
        }
        
        // Show loading message
        showMessage($message, 'Processing exchange...', 'info');
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetCurrencyMaker.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_exchange_currency',
                nonce: farmfaucetCurrencyMaker.nonce,
                from_currency_id: fromCurrencyId,
                to_currency_id: toCurrencyId,
                amount: amount
            },
            success: function(response) {
                if (response.success) {
                    showMessage($message, response.data.message || farmfaucetCurrencyMaker.i18n.exchangeSuccess, 'success');
                    
                    // Refresh the page after a delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    showMessage($message, response.data.message || farmfaucetCurrencyMaker.i18n.exchangeFailed, 'error');
                }
            },
            error: function() {
                showMessage($message, farmfaucetCurrencyMaker.i18n.error, 'error');
            }
        });
    }

    /**
     * Show message in the message container
     * 
     * @param {jQuery} $container Message container
     * @param {string} message Message text
     * @param {string} type Message type (success, error, info)
     */
    function showMessage($container, message, type) {
        // Remove existing classes
        $container.removeClass('farmfaucet-success farmfaucet-error farmfaucet-info');
        
        // Add appropriate class based on message type
        $container.addClass('farmfaucet-' + type);
        
        // Set message text
        $container.text(message);
        
        // Show message
        $container.fadeIn(300);
    }

})(jQuery);
