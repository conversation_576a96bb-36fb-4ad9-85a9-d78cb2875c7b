<?php
/**
 * Template for the Telegram Bot Signup form
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-tg-signup-form">
    <h2 class="farmfaucet-tg-form-title"><?php esc_html_e('Sign Up', 'farmfaucet'); ?></h2>
    
    <form method="post">
        <div class="farmfaucet-tg-form-group">
            <label for="name"><?php esc_html_e('Full Name', 'farmfaucet'); ?></label>
            <input type="text" name="name" id="name" required>
        </div>
        
        <div class="farmfaucet-tg-form-group">
            <label for="username"><?php esc_html_e('Username', 'farmfaucet'); ?></label>
            <input type="text" name="username" id="username" required>
        </div>
        
        <div class="farmfaucet-tg-form-group">
            <label for="email"><?php esc_html_e('Email', 'farmfaucet'); ?></label>
            <input type="email" name="email" id="email" required>
        </div>
        
        <div class="farmfaucet-tg-form-group">
            <label for="telegram_number"><?php esc_html_e('Telegram Number', 'farmfaucet'); ?></label>
            <input type="tel" name="telegram_number" id="telegram_number" placeholder="+1234567890" required>
        </div>
        
        <div class="farmfaucet-tg-form-group">
            <label for="password"><?php esc_html_e('Password', 'farmfaucet'); ?></label>
            <input type="password" name="password" id="password" required>
        </div>
        
        <div class="farmfaucet-tg-form-group">
            <label for="confirm_password"><?php esc_html_e('Confirm Password', 'farmfaucet'); ?></label>
            <input type="password" name="confirm_password" id="confirm_password" required>
        </div>
        
        <button type="submit" class="farmfaucet-tg-form-button"><?php echo esc_html($atts['button_text']); ?></button>
    </form>
    
    <div class="farmfaucet-tg-form-links">
        <a href="<?php echo esc_url(wp_login_url()); ?>"><?php esc_html_e('Already have an account? Login', 'farmfaucet'); ?></a>
    </div>
</div>
