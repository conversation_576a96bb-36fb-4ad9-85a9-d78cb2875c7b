<?php
/**
 * Farm Faucet - Update Database for New Appearance Fields
 * 
 * This script adds the new appearance fields to the faucets table:
 * - button_border_radius
 * - input_label_color  
 * - input_placeholder_color
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Update Appearance Fields</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 10px; border-left: 4px solid #4CAF50; margin: 10px 0; }
        .error { color: #f44336; background: #fdf0f0; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }
        .info { color: #2196F3; background: #f0f7ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .warning { color: #ff9800; background: #fff8f0; padding: 10px; border-left: 4px solid #ff9800; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Farm Faucet - Update Appearance Fields</h1>
    <p>This script will add the new appearance fields to your faucets table.</p>

<?php

global $wpdb;

// Function to check if a column exists
function column_exists($table, $column) {
    global $wpdb;
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
    return !empty($columns);
}

// Function to execute a query and handle errors
function execute_query($query) {
    global $wpdb;
    $result = $wpdb->query($query);
    if ($result === false) {
        echo '<div class="error"><p>Error executing query: ' . $wpdb->last_error . '</p></div>';
        echo '<pre>' . $query . '</pre>';
        return false;
    }
    return true;
}

// Get the faucets table name
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

// Check if the faucets table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if (!$table_exists) {
    echo '<div class="error"><p>The faucets table does not exist. Please make sure the Farm Faucet plugin is installed correctly.</p></div>';
} else {
    echo '<div class="info"><p>Found faucets table: ' . $faucets_table . '</p></div>';
    
    // Add button_border_radius column if it doesn't exist
    if (!column_exists($faucets_table, 'button_border_radius')) {
        $query = "ALTER TABLE {$faucets_table} ADD COLUMN button_border_radius varchar(20) NOT NULL DEFAULT '25px' AFTER form_transparent";
        if (execute_query($query)) {
            echo '<div class="success"><p>✅ Added button_border_radius column to the faucets table.</p></div>';
        }
    } else {
        echo '<div class="info"><p>The button_border_radius column already exists.</p></div>';
    }
    
    // Add input_label_color column if it doesn't exist
    if (!column_exists($faucets_table, 'input_label_color')) {
        $query = "ALTER TABLE {$faucets_table} ADD COLUMN input_label_color varchar(20) NOT NULL DEFAULT '#333333' AFTER button_border_radius";
        if (execute_query($query)) {
            echo '<div class="success"><p>✅ Added input_label_color column to the faucets table.</p></div>';
        }
    } else {
        echo '<div class="info"><p>The input_label_color column already exists.</p></div>';
    }
    
    // Add input_placeholder_color column if it doesn't exist
    if (!column_exists($faucets_table, 'input_placeholder_color')) {
        $query = "ALTER TABLE {$faucets_table} ADD COLUMN input_placeholder_color varchar(20) NOT NULL DEFAULT '#999999' AFTER input_label_color";
        if (execute_query($query)) {
            echo '<div class="success"><p>✅ Added input_placeholder_color column to the faucets table.</p></div>';
        }
    } else {
        echo '<div class="info"><p>The input_placeholder_color column already exists.</p></div>';
    }
    
    // Update existing faucets to set default values for new fields
    $query = "UPDATE {$faucets_table} SET 
        button_border_radius = '25px', 
        input_label_color = '#333333', 
        input_placeholder_color = '#999999' 
        WHERE button_border_radius IS NULL OR button_border_radius = '' 
        OR input_label_color IS NULL OR input_label_color = ''
        OR input_placeholder_color IS NULL OR input_placeholder_color = ''";
    
    if (execute_query($query)) {
        echo '<div class="success"><p>✅ Updated existing faucets with default appearance values.</p></div>';
    }
    
    // Show current table structure
    echo '<h2>Current Faucets Table Structure</h2>';
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    if ($columns) {
        echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; margin: 10px 0;">';
        echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td>' . esc_html($column->Field) . '</td>';
            echo '<td>' . esc_html($column->Type) . '</td>';
            echo '<td>' . esc_html($column->Null) . '</td>';
            echo '<td>' . esc_html($column->Key) . '</td>';
            echo '<td>' . esc_html($column->Default) . '</td>';
            echo '<td>' . esc_html($column->Extra) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    echo '<div class="success"><h2>✅ Database Update Complete!</h2></div>';
    echo '<div class="info"><p>The new appearance fields have been successfully added to your faucets table. You can now:</p>';
    echo '<ul>';
    echo '<li>Set custom button border radius for each faucet</li>';
    echo '<li>Customize input label colors for better visibility on transparent backgrounds</li>';
    echo '<li>Set custom placeholder text colors</li>';
    echo '</ul></div>';
    
    echo '<div class="warning"><p><strong>Next Steps:</strong></p>';
    echo '<ol>';
    echo '<li>Go to your WordPress admin panel</li>';
    echo '<li>Navigate to Farm Faucet → Faucets</li>';
    echo '<li>Edit any faucet to see the new appearance options</li>';
    echo '<li>Test the new features on your frontend</li>';
    echo '</ol></div>';
}

?>

<p><a href="<?php echo admin_url('admin.php?page=farmfaucet'); ?>" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">← Back to Farm Faucet Admin</a></p>

</body>
</html>
