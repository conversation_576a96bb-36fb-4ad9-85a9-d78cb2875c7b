/**
 * Farm Faucet Button Form Handler
 * 
 * Handles the button form UI interactions and AJAX operations
 */
jQuery(document).ready(function($) {
    // Initialize Select2 for multiple select fields
    if ($.fn.select2) {
        $('.farmfaucet-select2').select2({
            placeholder: 'Select pages',
            allowClear: true,
            width: '100%'
        });
    }

    // Toggle milestone settings visibility
    $('#milestone-enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.milestone-settings').slideDown();
        } else {
            $('.milestone-settings').slideUp();
        }
    });

    // Toggle milestone type specific fields
    $('#milestone-type').on('change', function() {
        if ($(this).val() === 'page_specific') {
            $('.milestone-pages-field').slideDown();
        } else {
            $('.milestone-pages-field').slideUp();
        }
    });

    // Toggle milestone card background style options
    $('#milestone-transparent-bg').on('change', function() {
        if ($(this).is(':checked')) {
            $('.milestone-card-bg-options').slideUp();
        } else {
            $('.milestone-card-bg-options').slideDown();
        }
    });

    // Toggle milestone card background gradient options
    $('#milestone-card-bg-style').on('change', function() {
        if ($(this).val() === 'gradient') {
            $('.milestone-card-solid-option').slideUp();
            $('.milestone-card-gradient-options').slideDown();
        } else {
            $('.milestone-card-solid-option').slideDown();
            $('.milestone-card-gradient-options').slideUp();
        }
    });

    // Toggle milestone bar gradient options
    $('#milestone-bar-style').on('change', function() {
        if ($(this).val() === 'gradient') {
            $('.milestone-bar-solid-option').slideUp();
            $('.milestone-bar-gradient-options').slideDown();
        } else {
            $('.milestone-bar-solid-option').slideDown();
            $('.milestone-bar-gradient-options').slideUp();
        }
    });

    // Toggle countdown settings visibility
    $('#countdown-enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.countdown-settings').slideDown();
        } else {
            $('.countdown-settings').slideUp();
        }
    });

    // Toggle click activation settings
    $('#countdown-click-activation').on('change', function() {
        if ($(this).is(':checked')) {
            $('.click-activation-settings').slideDown();
        } else {
            $('.click-activation-settings').slideUp();
        }
    });

    // Toggle lock settings visibility
    $('#is-locked').on('change', function() {
        if ($(this).is(':checked')) {
            $('.lock-settings').slideDown();
        } else {
            $('.lock-settings').slideUp();
        }
    });

    // Handle button form submission
    $('#farmfaucet-button-form').on('submit', function(e) {
        e.preventDefault();
        
        // Disable submit button to prevent double submission
        $('#save-button').prop('disabled', true).text('Saving...');
        
        // Get form data
        var formData = $(this).serialize();
        
        // Add action
        formData += '&action=farmfaucet_save_button';
        
        // Send AJAX request
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.data.message);
                    
                    // Redirect to buttons list
                    window.location.href = response.data.redirect;
                } else {
                    // Show error message
                    alert(response.data);
                    
                    // Re-enable submit button
                    $('#save-button').prop('disabled', false).text('Save Button');
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                alert('Error: ' + error);
                
                // Re-enable submit button
                $('#save-button').prop('disabled', false).text('Save Button');
            }
        });
    });

    // Initialize form based on current values
    function initializeForm() {
        // Initialize milestone settings
        if ($('#milestone-enabled').is(':checked')) {
            $('.milestone-settings').show();
            
            // Initialize milestone type
            if ($('#milestone-type').val() === 'page_specific') {
                $('.milestone-pages-field').show();
            }
            
            // Initialize milestone card background
            if ($('#milestone-transparent-bg').is(':checked')) {
                $('.milestone-card-bg-options').hide();
            } else if ($('#milestone-card-bg-style').val() === 'gradient') {
                $('.milestone-card-solid-option').hide();
                $('.milestone-card-gradient-options').show();
            }
            
            // Initialize milestone bar style
            if ($('#milestone-bar-style').val() === 'gradient') {
                $('.milestone-bar-solid-option').hide();
                $('.milestone-bar-gradient-options').show();
            }
        }
        
        // Initialize countdown settings
        if ($('#countdown-enabled').is(':checked')) {
            $('.countdown-settings').show();
            
            if ($('#countdown-click-activation').is(':checked')) {
                $('.click-activation-settings').show();
            }
        }
        
        // Initialize lock settings
        if ($('#is-locked').is(':checked')) {
            $('.lock-settings').show();
        }
    }
    
    // Run initialization
    initializeForm();
});
