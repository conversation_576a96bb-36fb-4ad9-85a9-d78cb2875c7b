<?php

/**
 * Scheduler class for the Farmfaucet plugin
 *
 * This class handles scheduled tasks like daily resets
 */
class Farmfaucet_Scheduler
{
    /**
     * Initialize the scheduler
     */
    public static function init()
    {
        // Register the daily reset hook
        add_action('farmfaucet_daily_reset', [self::class, 'perform_daily_reset']);

        // Register the leaderboard reset hook
        add_action('farmfaucet_leaderboard_reset', [self::class, 'perform_leaderboard_reset']);

        // Check if we need to schedule the daily reset
        self::maybe_schedule_daily_reset();

        // Check if we need to schedule the leaderboard reset
        self::maybe_schedule_leaderboard_reset();

        // Add hook for plugin activation
        register_activation_hook(FARMFAUCET_FILE, [self::class, 'activate']);

        // Add hook for plugin deactivation
        register_deactivation_hook(FARMFAUCET_FILE, [self::class, 'deactivate']);
    }

    /**
     * Schedule the daily reset if it's enabled
     */
    public static function maybe_schedule_daily_reset()
    {
        $reset_time = get_option('farmfaucet_daily_reset', '');

        // If reset time is empty, clear any scheduled events
        if (empty($reset_time)) {
            if (wp_next_scheduled('farmfaucet_daily_reset')) {
                wp_clear_scheduled_hook('farmfaucet_daily_reset');
            }
            return;
        }

        // Parse the reset time (format: HH:MM)
        list($hours, $minutes) = explode(':', $reset_time);

        // Calculate the next reset time
        $current_time = current_time('timestamp');
        $current_date = date('Y-m-d', $current_time);
        $next_reset = strtotime($current_date . ' ' . $reset_time);

        // If the next reset time is in the past, schedule for tomorrow
        if ($next_reset < $current_time) {
            $next_reset = strtotime('+1 day', $next_reset);
        }

        // Check if we already have a scheduled event
        $scheduled_time = wp_next_scheduled('farmfaucet_daily_reset');

        // If the scheduled time is different from what we want, clear it and reschedule
        if ($scheduled_time && abs($scheduled_time - $next_reset) > 60) {
            wp_clear_scheduled_hook('farmfaucet_daily_reset');
            $scheduled_time = false;
        }

        // Schedule the event if it's not already scheduled
        if (!$scheduled_time) {
            wp_schedule_event($next_reset, 'daily', 'farmfaucet_daily_reset');
        }
    }

    /**
     * Perform the daily reset
     */
    public static function perform_daily_reset()
    {
        global $wpdb;

        try {
            // Get all user completion records from transients
            $transient_prefix = '_transient_farmfaucet_completed_';
            $transient_timeout_prefix = '_transient_timeout_farmfaucet_completed_';

            // Get all transients with our prefix
            $transients = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT option_name FROM $wpdb->options WHERE option_name LIKE %s",
                    $wpdb->esc_like($transient_prefix) . '%'
                )
            );

            // Delete each transient
            if ($transients) {
                foreach ($transients as $transient) {
                    $transient_name = str_replace('_transient_', '', $transient->option_name);
                    delete_transient($transient_name);
                }
            }

            // Log the reset
            error_log('Farmfaucet daily reset completed at ' . current_time('mysql'));
        } catch (Exception $e) {
            error_log('Farmfaucet daily reset error: ' . $e->getMessage());
        }
    }

    /**
     * Actions to perform on plugin activation
     */
    public static function activate()
    {
        // Schedule the daily reset
        self::maybe_schedule_daily_reset();
    }

    /**
     * Actions to perform on plugin deactivation
     */
    public static function deactivate()
    {
        // Clear the scheduled event
        wp_clear_scheduled_hook('farmfaucet_daily_reset');
    }

    /**
     * Update the scheduler when settings are changed
     */
    public static function update_scheduler()
    {
        self::maybe_schedule_daily_reset();
        self::maybe_schedule_leaderboard_reset();
    }

    /**
     * Schedule the leaderboard reset if a date is set
     */
    public static function maybe_schedule_leaderboard_reset()
    {
        $reset_date = get_option('farmfaucet_leaderboard_reset_date', '');

        // If reset date is empty, clear any scheduled events
        if (empty($reset_date)) {
            if (wp_next_scheduled('farmfaucet_leaderboard_reset')) {
                wp_clear_scheduled_hook('farmfaucet_leaderboard_reset');
            }
            return;
        }

        // Calculate the reset timestamp (midnight of the selected date)
        $reset_timestamp = strtotime($reset_date . ' 00:00:00');

        // If the reset date is in the past, clear any scheduled events
        $current_time = current_time('timestamp');
        if ($reset_timestamp < $current_time) {
            if (wp_next_scheduled('farmfaucet_leaderboard_reset')) {
                wp_clear_scheduled_hook('farmfaucet_leaderboard_reset');
            }
            return;
        }

        // Check if we already have a scheduled event
        $scheduled_time = wp_next_scheduled('farmfaucet_leaderboard_reset');

        // If the scheduled time is different from what we want, clear it and reschedule
        if ($scheduled_time && abs($scheduled_time - $reset_timestamp) > 60) {
            wp_clear_scheduled_hook('farmfaucet_leaderboard_reset');
            $scheduled_time = false;
        }

        // Schedule the event if it's not already scheduled
        if (!$scheduled_time) {
            wp_schedule_single_event($reset_timestamp, 'farmfaucet_leaderboard_reset');
        }
    }

    /**
     * Perform the leaderboard reset
     */
    public static function perform_leaderboard_reset()
    {
        global $wpdb;

        try {
            // Get the leaderboard table name
            $table_name = $wpdb->prefix . 'farmfaucet_leaderboard';

            // Truncate the leaderboard table
            $wpdb->query("TRUNCATE TABLE $table_name");

            // Clear the reset date option
            update_option('farmfaucet_leaderboard_reset_date', '');

            // Log the reset
            error_log('Farmfaucet leaderboard reset completed at ' . current_time('mysql'));
        } catch (Exception $e) {
            error_log('Farmfaucet leaderboard reset error: ' . $e->getMessage());
        }
    }
}
