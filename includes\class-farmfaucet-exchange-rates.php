<?php
/**
 * Farm Faucet Exchange Rates Handler
 * 
 * Handles real-time cryptocurrency exchange rates from CoinMarketCap API
 */

if (!defined('ABSPATH')) {
    exit;
}

class Farmfaucet_Exchange_Rates
{
    private static $instance = null;
    private $api_key;
    private $cache_duration = 300; // 5 minutes cache

    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        $this->api_key = get_option('farmfaucet_coinmarketcap_api_key', '');
    }

    /**
     * Get exchange rate between two currencies
     * 
     * @param string $from_currency Source currency (e.g., 'LTC')
     * @param string $to_currency Target currency (e.g., 'BTC')
     * @return float|false Exchange rate or false on error
     */
    public function get_exchange_rate($from_currency, $to_currency)
    {
        // If same currency, return 1
        if (strtoupper($from_currency) === strtoupper($to_currency)) {
            return 1.0;
        }

        // Check cache first
        $cache_key = 'farmfaucet_rate_' . strtolower($from_currency) . '_' . strtolower($to_currency);
        $cached_rate = get_transient($cache_key);
        
        if ($cached_rate !== false) {
            return floatval($cached_rate);
        }

        // Get rates from API
        $rate = $this->fetch_exchange_rate($from_currency, $to_currency);
        
        if ($rate !== false) {
            // Cache the rate
            set_transient($cache_key, $rate, $this->cache_duration);
        }

        return $rate;
    }

    /**
     * Fetch exchange rate from CoinMarketCap API
     * 
     * @param string $from_currency
     * @param string $to_currency
     * @return float|false
     */
    private function fetch_exchange_rate($from_currency, $to_currency)
    {
        // If no API key, use fallback rates
        if (empty($this->api_key)) {
            return $this->get_fallback_rate($from_currency, $to_currency);
        }

        try {
            // CoinMarketCap API endpoint
            $url = 'https://pro-api.coinmarketcap.com/v1/tools/price-conversion';
            
            $args = array(
                'headers' => array(
                    'X-CMC_PRO_API_KEY' => $this->api_key,
                    'Accept' => 'application/json',
                ),
                'timeout' => 10,
            );

            // Build query parameters
            $query_params = array(
                'amount' => 1,
                'symbol' => strtoupper($from_currency),
                'convert' => strtoupper($to_currency)
            );

            $request_url = $url . '?' . http_build_query($query_params);
            
            $response = wp_remote_get($request_url, $args);

            if (is_wp_error($response)) {
                error_log('Farmfaucet Exchange Rate API Error: ' . $response->get_error_message());
                return $this->get_fallback_rate($from_currency, $to_currency);
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (isset($data['data']['quote'][strtoupper($to_currency)]['price'])) {
                return floatval($data['data']['quote'][strtoupper($to_currency)]['price']);
            }

            // If API call failed, use fallback
            return $this->get_fallback_rate($from_currency, $to_currency);

        } catch (Exception $e) {
            error_log('Farmfaucet Exchange Rate Exception: ' . $e->getMessage());
            return $this->get_fallback_rate($from_currency, $to_currency);
        }
    }

    /**
     * Get fallback exchange rates (approximate values)
     * These are backup rates in case the API is unavailable
     * 
     * @param string $from_currency
     * @param string $to_currency
     * @return float|false
     */
    private function get_fallback_rate($from_currency, $to_currency)
    {
        // Approximate exchange rates (as of 2024) - these should be updated periodically
        $rates = array(
            'LTC' => array(
                'BTC' => 0.0015,    // 1 LTC ≈ 0.0015 BTC
                'ETH' => 0.025,     // 1 LTC ≈ 0.025 ETH
                'DOGE' => 800,      // 1 LTC ≈ 800 DOGE
                'TRX' => 8000,      // 1 LTC ≈ 8000 TRX
                'BCH' => 0.15,      // 1 LTC ≈ 0.15 BCH
                'USD' => 70,        // 1 LTC ≈ $70
            ),
            'BTC' => array(
                'LTC' => 667,       // 1 BTC ≈ 667 LTC
                'ETH' => 16.7,      // 1 BTC ≈ 16.7 ETH
                'DOGE' => 533333,   // 1 BTC ≈ 533,333 DOGE
                'TRX' => 5333333,   // 1 BTC ≈ 5,333,333 TRX
                'BCH' => 100,       // 1 BTC ≈ 100 BCH
                'USD' => 46667,     // 1 BTC ≈ $46,667
            ),
            'ETH' => array(
                'BTC' => 0.06,      // 1 ETH ≈ 0.06 BTC
                'LTC' => 40,        // 1 ETH ≈ 40 LTC
                'DOGE' => 32000,    // 1 ETH ≈ 32,000 DOGE
                'TRX' => 320000,    // 1 ETH ≈ 320,000 TRX
                'BCH' => 6,         // 1 ETH ≈ 6 BCH
                'USD' => 2800,      // 1 ETH ≈ $2,800
            ),
            'DOGE' => array(
                'BTC' => 0.000001875, // 1 DOGE ≈ 0.000001875 BTC
                'LTC' => 0.00125,   // 1 DOGE ≈ 0.00125 LTC
                'ETH' => 0.00003125, // 1 DOGE ≈ 0.00003125 ETH
                'TRX' => 10,        // 1 DOGE ≈ 10 TRX
                'BCH' => 0.0001875, // 1 DOGE ≈ 0.0001875 BCH
                'USD' => 0.0875,    // 1 DOGE ≈ $0.0875
            ),
            'TRX' => array(
                'BTC' => 0.0000001875, // 1 TRX ≈ 0.0000001875 BTC
                'LTC' => 0.000125,  // 1 TRX ≈ 0.000125 LTC
                'ETH' => 0.000003125, // 1 TRX ≈ 0.000003125 ETH
                'DOGE' => 0.1,      // 1 TRX ≈ 0.1 DOGE
                'BCH' => 0.00001875, // 1 TRX ≈ 0.00001875 BCH
                'USD' => 0.00875,   // 1 TRX ≈ $0.00875
            ),
            'BCH' => array(
                'BTC' => 0.01,      // 1 BCH ≈ 0.01 BTC
                'LTC' => 6.67,      // 1 BCH ≈ 6.67 LTC
                'ETH' => 0.167,     // 1 BCH ≈ 0.167 ETH
                'DOGE' => 5333,     // 1 BCH ≈ 5,333 DOGE
                'TRX' => 53333,     // 1 BCH ≈ 53,333 TRX
                'USD' => 467,       // 1 BCH ≈ $467
            )
        );

        $from = strtoupper($from_currency);
        $to = strtoupper($to_currency);

        if (isset($rates[$from][$to])) {
            return floatval($rates[$from][$to]);
        }

        // Try reverse rate
        if (isset($rates[$to][$from])) {
            return 1.0 / floatval($rates[$to][$from]);
        }

        // If no rate found, return false
        return false;
    }

    /**
     * Convert amount from one currency to another
     * 
     * @param float $amount Amount to convert
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Converted amount or false on error
     */
    public function convert_amount($amount, $from_currency, $to_currency)
    {
        $rate = $this->get_exchange_rate($from_currency, $to_currency);
        
        if ($rate === false) {
            return false;
        }

        return floatval($amount) * $rate;
    }

    /**
     * Get supported currencies
     * 
     * @return array List of supported currencies
     */
    public function get_supported_currencies()
    {
        return array('BTC', 'LTC', 'ETH', 'DOGE', 'TRX', 'BCH');
    }

    /**
     * Format amount with appropriate decimal places
     * 
     * @param float $amount
     * @param string $currency
     * @return string Formatted amount
     */
    public function format_amount($amount, $currency)
    {
        $currency = strtoupper($currency);
        
        // Different currencies need different decimal precision
        $decimals = array(
            'BTC' => 8,
            'LTC' => 8,
            'ETH' => 8,
            'DOGE' => 4,
            'TRX' => 2,
            'BCH' => 8
        );

        $decimal_places = isset($decimals[$currency]) ? $decimals[$currency] : 8;
        
        // Format and remove trailing zeros
        $formatted = rtrim(rtrim(number_format($amount, $decimal_places, '.', ''), '0'), '.');
        
        // Ensure we don't return empty string
        return $formatted === '' ? '0' : $formatted;
    }
}
