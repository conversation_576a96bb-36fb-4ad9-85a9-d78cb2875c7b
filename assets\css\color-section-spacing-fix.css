/**
 * Farm Faucet - Color Section Spacing Fix
 * Fixes overlapping issues in appearance settings color sections
 */

/* Admin color picker container spacing */
.farmfaucet-color-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.farmfaucet-color-section h4 {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
    color: #333;
}

/* Color picker field spacing */
.farmfaucet-color-field {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.farmfaucet-color-field label {
    min-width: 150px;
    font-weight: 500;
    color: #333;
}

.farmfaucet-color-field input[type="color"] {
    width: 50px;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.farmfaucet-color-field input[type="text"] {
    width: 100px;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Prevent overlap between sections */
.farmfaucet-appearance-section {
    margin: 25px 0;
    clear: both;
}

.farmfaucet-appearance-section:not(:last-child) {
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 20px;
    margin-bottom: 25px;
}

/* Container background section */
.container-background-section {
    background: #f0f8f0;
    border-left: 4px solid #4CAF50;
}

/* Form background section */
.form-background-section {
    background: #f0f7ff;
    border-left: 4px solid #2196F3;
}

/* Border and button section */
.border-button-section {
    background: #fff8f0;
    border-left: 4px solid #ff9800;
}

/* Text and input section */
.text-input-section {
    background: #f3e5f5;
    border-left: 4px solid #9c27b0;
}

/* Fix for WordPress admin form tables */
.farmfaucet-faucet-appearance .form-table th {
    width: 200px;
    padding: 20px 10px;
    vertical-align: top;
    background: #f9f9f9;
    border-bottom: 1px solid #e0e0e0;
}

.farmfaucet-faucet-appearance .form-table td {
    padding: 20px 10px;
    vertical-align: top;
    border-bottom: 1px solid #f0f0f0;
}

.farmfaucet-faucet-appearance .form-table tr:nth-child(even) {
    background: #fafafa;
}

.farmfaucet-faucet-appearance .form-table tr:last-child th,
.farmfaucet-faucet-appearance .form-table tr:last-child td {
    border-bottom: none;
}

/* Color picker specific fixes */
.wp-picker-container {
    margin: 10px 0;
    display: inline-block;
}

.wp-picker-container .wp-color-result {
    height: 35px;
    border-radius: 4px;
    margin-right: 10px;
}

.wp-picker-container .wp-picker-input-wrap {
    display: inline-block;
    vertical-align: top;
}

/* Fix for overlapping color sections in faucet appearance */
.farmfaucet-appearance-row {
    margin: 15px 0;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.farmfaucet-appearance-row:last-child {
    border-bottom: none;
}

/* Specific spacing for different color setting groups */
.background-colors-group {
    background: #f0f8f0;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.form-colors-group {
    background: #f0f7ff;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
}

.button-colors-group {
    background: #fff8f0;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid #ff9800;
}

.text-colors-group {
    background: #f3e5f5;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid #9c27b0;
}

/* Group headers */
.color-group-header {
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .farmfaucet-color-field {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .farmfaucet-color-field label {
        min-width: auto;
        width: 100%;
    }
    
    .farmfaucet-color-section {
        margin: 15px 0;
        padding: 10px;
    }
    
    .farmfaucet-faucet-appearance .form-table th,
    .farmfaucet-faucet-appearance .form-table td {
        display: block;
        width: 100%;
        padding: 10px;
    }
    
    .farmfaucet-faucet-appearance .form-table th {
        background: #f0f0f0;
        font-weight: 600;
    }
}

/* Fix for color picker positioning */
.wp-picker-container .wp-picker-holder {
    position: absolute;
    z-index: 100;
    background: white;
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Ensure proper spacing between form elements */
.farmfaucet-form-row {
    margin-bottom: 20px;
    clear: both;
}

.farmfaucet-form-row:last-child {
    margin-bottom: 0;
}

/* Fix for toggle switches in appearance settings */
.farmfaucet-toggle-container {
    margin: 15px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

/* Ensure no overlap with transparency settings */
.transparency-settings {
    background: #f0f0f0;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.transparency-settings h4 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

/* Fix for border radius and other numeric inputs */
.farmfaucet-numeric-input {
    width: 80px;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

/* Ensure proper spacing for all appearance controls */
.farmfaucet-appearance-control {
    margin: 10px 0;
    padding: 8px 0;
}

.farmfaucet-appearance-control label {
    display: inline-block;
    min-width: 150px;
    font-weight: 500;
    vertical-align: top;
}

.farmfaucet-appearance-control .control-input {
    display: inline-block;
    vertical-align: top;
}

/* Fix for any remaining overlaps */
.farmfaucet-admin-page .form-table {
    border-collapse: separate;
    border-spacing: 0;
}

.farmfaucet-admin-page .form-table th,
.farmfaucet-admin-page .form-table td {
    border: none;
    box-shadow: none;
}

/* Ensure consistent spacing across all tabs */
.farmfaucet-tab-content {
    padding: 20px 0;
}

.farmfaucet-tab-content .form-table {
    margin-top: 20px;
}

/* Final fix for any remaining color section overlaps */
.color-settings-wrapper {
    overflow: hidden;
    clear: both;
}

.color-settings-wrapper > div {
    margin-bottom: 25px;
}

.color-settings-wrapper > div:last-child {
    margin-bottom: 0;
}
