<?php
/**
 * Plugin Name: Farm Faucet (Minimal)
 * Description: Minimal version for testing settings save and transaction display
 * Version: 1.0.0
 * Author: ZpromoterZ
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FARMFAUCET_VERSION', '1.0.0');
define('FARMFAUCET_PATH', plugin_dir_path(__FILE__));
define('FARMFAUCET_URL', plugin_dir_url(__FILE__));

// Load essential classes only
require_once FARMFAUCET_PATH . 'includes/class-farmfaucet-admin.php';
require_once FARMFAUCET_PATH . 'includes/class-farmfaucet-logger.php';

// Initialize admin interface
if (is_admin()) {
    add_action('init', function() {
        if (class_exists('Farmfaucet_Admin')) {
            Farmfaucet_Admin::init();
        }
    });
}

// Add activation hook
register_activation_hook(__FILE__, function() {
    // Create basic database tables if needed
    global $wpdb;
    
    // Create logs table
    $table_name = $wpdb->prefix . 'farmfaucet_logs';
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) DEFAULT NULL,
        faucet_id varchar(50) DEFAULT NULL,
        type varchar(20) NOT NULL DEFAULT 'info',
        message text NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY faucet_id (faucet_id),
        KEY type (type),
        KEY timestamp (timestamp)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Create faucets table
    $table_name = $wpdb->prefix . 'farmfaucet_faucets';
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id varchar(50) NOT NULL,
        name varchar(255) NOT NULL,
        type varchar(50) NOT NULL DEFAULT 'stage',
        settings longtext,
        appearance longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    dbDelta($sql);
});

// Add some sample transaction data for testing
add_action('wp_loaded', function() {
    if (isset($_GET['add_sample_transactions']) && current_user_can('manage_options')) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_logs';
        
        // Add sample successful transaction
        $wpdb->insert($table_name, [
            'user_id' => get_current_user_id(),
            'faucet_id' => 'test-faucet-1',
            'type' => 'success',
            'message' => 'Successfully claimed 0.00001 BTC from Test Faucet',
            'timestamp' => current_time('mysql')
        ]);
        
        // Add sample error transaction
        $wpdb->insert($table_name, [
            'user_id' => get_current_user_id(),
            'faucet_id' => 'test-faucet-2',
            'type' => 'error',
            'message' => 'Failed to process claim: Insufficient balance (error_code: 1001)',
            'timestamp' => current_time('mysql')
        ]);
        
        wp_redirect(admin_url('admin.php?page=farmfaucet&tab=faucets'));
        exit;
    }
});
?>
