<?php

/**
 * Safe loader class for the Farmfaucet plugin
 *
 * This class provides a safe way to load the plugin functionality
 * and prevents critical errors from breaking the entire site
 */
class Farmfaucet_Safe_Loader
{
    /**
     * Initialize the plugin safely
     */
    public static function init()
    {
        try {
            // Only load frontend scripts on pages with our shortcode
            add_action('wp', function () {
                try {
                    self::maybe_load_frontend_assets();
                } catch (Exception $e) {
                    // Log the error but don't break the page
                    error_log('Farmfaucet frontend assets error: ' . $e->getMessage());
                }
            });

            // Register shortcodes
            add_shortcode('farmfaucet', [self::class, 'safe_render_claim_form']);
            add_shortcode('farmfaucet_button', [self::class, 'safe_render_button']);

            // Register AJAX handlers
            add_action('wp_ajax_process_claim', [self::class, 'safe_process_claim']);
            add_action('wp_ajax_nopriv_process_claim', [self::class, 'safe_process_claim']);

            // Register dynamic shortcodes
            self::register_dynamic_shortcodes();
        } catch (Exception $e) {
            // Log the error but don't break the site
            error_log('Farmfaucet initialization error: ' . $e->getMessage());
        }
    }

    /**
     * Register dynamic shortcodes for each faucet
     */
    private static function register_dynamic_shortcodes()
    {
        try {
            // Get all faucets
            $faucets = Farmfaucet_Logger::get_faucets();

            if (is_array($faucets)) {
                foreach ($faucets as $faucet) {
                    // Skip the default shortcode as it's already registered
                    if (isset($faucet['shortcode']) && $faucet['shortcode'] !== 'farmfaucet') {
                        add_shortcode($faucet['shortcode'], function () use ($faucet) {
                            return self::safe_render_claim_form(['id' => $faucet['id']]);
                        });
                    }
                }
            }
        } catch (Exception $e) {
            // Log the error but don't break the site
            error_log('Farmfaucet dynamic shortcodes error: ' . $e->getMessage());
        }
    }

    /**
     * Safely render the claim form
     */
    public static function safe_render_claim_form($atts = [])
    {
        try {
            $frontend = Farmfaucet_Frontend::init();
            return $frontend->render_claim_form($atts);
        } catch (Exception $e) {
            // Log the error and return a friendly message
            error_log('Farmfaucet claim form error: ' . $e->getMessage());
            return '<div class="farmfaucet-error">Faucet temporarily unavailable. Please try again later.</div>';
        }
    }

    /**
     * Safely render a button
     */
    public static function safe_render_button($atts = [])
    {
        try {
            $frontend = Farmfaucet_Frontend::init();
            return $frontend->render_faucet_button($atts);
        } catch (Exception $e) {
            // Log the error and return a friendly message
            error_log('Farmfaucet button error: ' . $e->getMessage());
            return '<div class="farmfaucet-error">Button temporarily unavailable. Please try again later.</div>';
        }
    }

    /**
     * Safely process a claim
     */
    public static function safe_process_claim()
    {
        try {
            $frontend = Farmfaucet_Frontend::init();
            $frontend->process_claim();
        } catch (Exception $e) {
            // Log the error and return a friendly message
            error_log('Farmfaucet claim processing error: ' . $e->getMessage());
            wp_send_json_error('An error occurred while processing your claim. Please try again later.');
        }
    }

    /**
     * Check if the current page contains a farmfaucet shortcode and load assets if needed
     */
    private static function maybe_load_frontend_assets()
    {
        global $post;

        // Only check on singular pages
        if (!is_singular() || !is_object($post)) {
            return;
        }

        // Check for farmfaucet shortcodes
        $has_shortcode = false;

        // Check for main shortcodes
        if (
            has_shortcode($post->post_content, 'farmfaucet') ||
            has_shortcode($post->post_content, 'farmfaucet_button')
        ) {
            $has_shortcode = true;
        }

        // Check for dynamic shortcodes
        if (!$has_shortcode) {
            try {
                $faucets = Farmfaucet_Logger::get_faucets();
                if (is_array($faucets)) {
                    foreach ($faucets as $faucet) {
                        if (isset($faucet['shortcode']) && has_shortcode($post->post_content, $faucet['shortcode'])) {
                            $has_shortcode = true;
                            break;
                        }
                    }
                }

                // Check for button shortcodes
                if (!$has_shortcode) {
                    $buttons = Farmfaucet_Logger::get_buttons();
                    if (is_array($buttons)) {
                        foreach ($buttons as $button) {
                            if (isset($button['shortcode']) && has_shortcode($post->post_content, $button['shortcode'])) {
                                $has_shortcode = true;
                                break;
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                // Log the error but don't break the page
                error_log('Farmfaucet shortcode check error: ' . $e->getMessage());
            }
        }

        // Load assets if a shortcode is found
        if ($has_shortcode) {
            self::enqueue_frontend_assets();
        }
    }

    /**
     * Enqueue frontend assets
     */
    private static function enqueue_frontend_assets()
    {
        try {
            // Initialize the frontend class which will handle all script loading
            $frontend = Farmfaucet_Frontend::init();

            // Let the frontend class handle the script loading
            if (method_exists($frontend, 'enqueue_public_assets')) {
                $frontend->enqueue_public_assets();
                return; // Exit early as the frontend class has handled script loading
            }

            // Fallback if the frontend class doesn't have the method
            // Get captcha type
            $captcha_type = get_option('farmfaucet_captcha_type', 'hcaptcha');

            // Validate captcha type
            if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
                error_log("Farmfaucet: Invalid captcha type '{$captcha_type}', defaulting to hCaptcha");
                $captcha_type = 'hcaptcha';
                // Try to update the option if possible
                if (function_exists('update_option')) {
                    update_option('farmfaucet_captcha_type', 'hcaptcha');
                }
            }

            // Only load CSS files here since the frontend class will handle JS
            wp_enqueue_style(
                'farmfaucet-style',
                FARMFAUCET_URL . 'assets/css/style.css',
                [],
                FARMFAUCET_VERSION
            );

            // Add captcha-specific CSS fixes
            wp_enqueue_style(
                'farmfaucet-captcha-fix',
                FARMFAUCET_URL . 'assets/css/captcha-fix.css',
                ['farmfaucet-style'],
                FARMFAUCET_VERSION
            );

            // Add milestone styles
            wp_enqueue_style(
                'farmfaucet-milestone',
                FARMFAUCET_URL . 'assets/css/milestone.css',
                ['farmfaucet-style'],
                FARMFAUCET_VERSION
            );
        } catch (Exception $e) {
            error_log('Farmfaucet asset loading error: ' . $e->getMessage());
        }
    }
}
