<?php
if (!defined('ABSPATH')) exit;

// Get faucet data from the template variable
if (!isset($faucet)) {
  echo '<div class="farmfaucet-error">' . esc_html__('No faucet available', 'farmfaucet') . '</div>';
  return;
}

// Check cooldown for this specific faucet
$user_ip = class_exists('Farmfaucet_Security') ? Farmfaucet_Security::get_user_ip() : $_SERVER['REMOTE_ADDR'];
$user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
$transient_key = 'farmfaucet_cooldown_' . $faucet['id'] . '_' . md5($user_ip);
$cooldown_active = function_exists('get_transient') ? get_transient($transient_key) : false;

// Generate background style based on faucet settings
$bg_style = '';
$text_style = '';

// Apply background style
if (isset($faucet['transparent_bg']) && $faucet['transparent_bg'] == 1) {
  $bg_style = 'background: transparent;';
} else {
  if (isset($faucet['bg_style']) && $faucet['bg_style'] == 'gradient' && isset($faucet['bg_gradient_start']) && isset($faucet['bg_gradient_end'])) {
    $bg_style = "background: linear-gradient(135deg, {$faucet['bg_gradient_start']}, {$faucet['bg_gradient_end']});";
  } else if (isset($faucet['bg_color'])) {
    $bg_style = "background-color: {$faucet['bg_color']};";
  }
}

// Apply text color and shadow
$text_color = isset($faucet['text_color']) ? $faucet['text_color'] : '#4CAF50';
$text_shadow = isset($faucet['text_shadow']) && $faucet['text_shadow'] != 'none' ? "text-shadow: {$faucet['text_shadow']};" : '';
$text_style = "color: {$text_color}; {$text_shadow}";

// Get button color
$button_color = isset($faucet['button_color']) ? $faucet['button_color'] : '#4CAF50';

// Get border color
$border_color = isset($faucet['border_color']) ? $faucet['border_color'] : '#4CAF50';

// Get border radius
$border_radius = isset($faucet['border_radius']) ? $faucet['border_radius'] : '8px';

// Get form background color
$form_bg_color = isset($faucet['form_bg_color']) ? $faucet['form_bg_color'] : '#ffffff';

// Check if form background should be transparent
$form_transparent = isset($faucet['form_transparent']) && $faucet['form_transparent'] ? 1 : 0;

// Add transparent class if needed
$transparent_class = isset($faucet['transparent_bg']) && $faucet['transparent_bg'] ? 'transparent-bg' : '';

// Add custom border class if needed
$border_class = isset($faucet['border_color']) && $faucet['border_color'] ? 'custom-border' : '';

// Get view style
$view_style = isset($faucet['view_style']) ? $faucet['view_style'] : 'default';
$container_class = 'farmfaucet-container dummy-faucet';
if ($view_style === 'card') {
  $container_class .= ' card-view';
} else if ($view_style === 'compact') {
  $container_class .= ' compact-view';
}

// Add appearance classes
$container_class .= ' ' . $transparent_class . ' ' . $border_class;

// Get currency data - for dummy faucets, we use created currencies
$currency_id = isset($faucet['currency_id']) ? $faucet['currency_id'] : 0;
$currency_name = '';
$currency_symbol = '';

if (class_exists('Farmfaucet_Currency_Maker')) {
  $currency_maker = Farmfaucet_Currency_Maker::init();

  // For dummy faucets, we need to get the created currency
  if ($currency_id) {
    $currency = $currency_maker->get_currency($currency_id);
    if ($currency) {
      $currency_name = $currency['name'];
      $currency_symbol = $currency['symbol'];
    }
  } else {
    // If no currency is set, get the first available created currency
    $currencies = $currency_maker->get_currencies(true);
    if (!empty($currencies)) {
      $currency = reset($currencies);
      $currency_id = $currency['id'];
      $currency_name = $currency['name'];
      $currency_symbol = $currency['symbol'];
    }
  }
}
?>
<div class="<?php echo esc_attr($container_class); ?>"
  data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
  data-faucet-name="<?php echo esc_attr($faucet['name']); ?>"
  data-faucet-shortcode="<?php echo esc_attr($faucet['shortcode']); ?>"
  data-border-radius="<?php echo esc_attr($border_radius); ?>"
  data-form-bg-color="<?php echo esc_attr($form_bg_color); ?>"
  data-form-transparent="<?php echo esc_attr($form_transparent); ?>"
  role="region"
  aria-label="<?php esc_attr_e('Cryptocurrency Faucet Claim Form', 'farmfaucet'); ?>"
  style="<?php echo esc_attr($bg_style); ?> --button-color: <?php echo esc_attr($button_color); ?>; --border-color: <?php echo esc_attr($border_color); ?>; --form-bg-color: <?php echo esc_attr($form_bg_color); ?>; --button-border-radius: <?php echo esc_attr(isset($faucet['button_border_radius']) ? $faucet['button_border_radius'] : '25px'); ?>; --input-label-color: <?php echo esc_attr(isset($faucet['input_label_color']) ? $faucet['input_label_color'] : '#333333'); ?>; --input-placeholder-color: <?php echo esc_attr(isset($faucet['input_placeholder_color']) ? $faucet['input_placeholder_color'] : '#999999'); ?>;">

  <?php if ($cooldown_active) : ?>
    <!-- Cooldown active state -->
    <div class="farmfaucet-header">
      <h2 class="status-header" style="<?php echo esc_attr($text_style); ?>">
        ⏳ <?php esc_html_e('COOLDOWN ACTIVE', 'farmfaucet'); ?>
      </h2>
      <div class="cooldown-timer" data-cooldown="<?php echo esc_attr($faucet['cooldown']); ?>" style="<?php echo esc_attr($text_style); ?>">
        <?php esc_html_e('Next claim available in:', 'farmfaucet'); ?> <span class="time-remaining"></span>
      </div>
    </div>
  <?php else : ?>
    <!-- Active faucet state -->
    <div class="farmfaucet-header">
      <h2 class="status-header" id="form-heading" style="<?php echo esc_attr($text_style); ?>">
        <span class="pre-captcha-status">📋 <?php echo esc_html($faucet['name']); ?></span>
        <span class="post-captcha-status" style="display:none;">✅ <?php esc_html_e('READY TO CLAIM', 'farmfaucet'); ?></span>
      </h2>
      <div class="reward-notice" aria-live="polite" style="<?php echo esc_attr($text_style); ?>">
        <?php
        // For dummy faucets, show the correct amount with proper formatting
        $amount = isset($faucet['amount']) ? $faucet['amount'] : '0';

        // Format amount properly - don't add unnecessary decimals for whole numbers
        $formatted_amount = $amount;
        if (is_numeric($amount)) {
          // If it's a whole number, don't add decimals
          if (floor($amount) == $amount) {
            $formatted_amount = number_format($amount, 0);
          } else {
            // For decimal numbers, show up to 8 decimal places but remove trailing zeros
            $formatted_amount = rtrim(rtrim(number_format($amount, 8, '.', ''), '0'), '.');
          }
        }

        // Get the currency display
        $currency_display = '';
        if (!empty($currency_name)) {
          $currency_display = $currency_name;
          if (!empty($currency_symbol)) {
            $currency_display .= ' (' . $currency_symbol . ')';
          }
        } else {
          $currency_display = $currency_symbol;
        }

        echo wp_kses_post(sprintf(
          __('You will receive ➔ %s %s', 'farmfaucet'),
          '<span class="amount">' . esc_html($formatted_amount) . '</span>',
          '<span class="currency">' . esc_html($currency_display) . '</span>'
        )); ?>
      </div>
    </div>

    <form id="farmfaucet-dummy-form" method="post" class="farmfaucet-form" aria-labelledby="form-heading">
      <?php if ($user_id === 0) : ?>
        <div class="form-group">
          <p class="login-required"><?php esc_html_e('Please log in to claim rewards', 'farmfaucet'); ?></p>
          <a href="<?php echo function_exists('wp_login_url') ? esc_url(wp_login_url(function_exists('get_permalink') ? get_permalink() : '')) : (function_exists('site_url') ? esc_url(site_url('/wp-login.php')) : '/wp-login.php'); ?>" class="farmfaucet-login-btn">
            <?php esc_html_e('Log In', 'farmfaucet'); ?>
          </a>
        </div>
      <?php else : ?>

        <!-- Hidden field for faucet ID -->
        <input type="hidden" name="faucet_id" value="<?php echo esc_attr($faucet['id']); ?>">
        <input type="hidden" name="currency_id" value="<?php echo esc_attr($currency_id); ?>">

        <div class="farmfaucet-captcha-container" data-faucet-id="<?php echo esc_attr($faucet['id']); ?>" style="display: flex; visibility: visible; opacity: 1;">
          <?php
          if (class_exists('Farmfaucet_Captcha_Handler')) {
            echo Farmfaucet_Captcha_Handler::render_captcha($faucet);
          } else {
            try {
              // Get captcha type from faucet-specific setting or fallback to global settings
              $captcha_type = !empty($faucet['captcha_type']) ? $faucet['captcha_type'] : get_option('farmfaucet_captcha_type', 'hcaptcha');

              // Validate captcha type
              if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
                $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
              }

              // Get captcha site keys - check both possible option names
              $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_site_key', '');
              if (empty($hcaptcha_sitekey)) {
                $hcaptcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
              }

              $recaptcha_sitekey = get_option('farmfaucet_recaptcha_site_key', '');
              if (empty($recaptcha_sitekey)) {
                $recaptcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
              }

              $turnstile_sitekey = get_option('farmfaucet_turnstile_site_key', '');
              if (empty($turnstile_sitekey)) {
                $turnstile_sitekey = get_option('farmfaucet_turnstile_sitekey', '');
              }

              // Generate a unique ID for this captcha
              $captcha_id = 'farmfaucet-captcha-' . $faucet['id'] . '-' . uniqid();

              // Add hidden field for captcha type
              echo '<input type="hidden" name="captcha_type" value="' . esc_attr($captcha_type) . '">';

              // Render the appropriate captcha
              if ($captcha_type === 'hcaptcha') {
                if (empty($hcaptcha_sitekey)) {
                  echo '<div class="farmfaucet-error">' . esc_html__('hCaptcha site key not configured', 'farmfaucet') . '</div>';
                } else {
          ?>
                  <div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                    <!-- Simple container for hCaptcha -->
                    <div id="<?php echo $captcha_id; ?>"
                      class="h-captcha farmfaucet-captcha"
                      data-sitekey="<?php echo esc_attr($hcaptcha_sitekey); ?>"
                      data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                      style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
                  </div>
                <?php
                }
              } elseif ($captcha_type === 'recaptcha') {
                if (empty($recaptcha_sitekey)) {
                  echo '<div class="farmfaucet-error">' . esc_html__('reCAPTCHA site key not configured', 'farmfaucet') . '</div>';
                } else {
                ?>
                  <div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                    <!-- Simple container for reCAPTCHA -->
                    <div id="<?php echo $captcha_id; ?>"
                      class="g-recaptcha farmfaucet-captcha"
                      data-sitekey="<?php echo esc_attr($recaptcha_sitekey); ?>"
                      data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                      style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
                  </div>
                <?php
                }
              } elseif ($captcha_type === 'turnstile') {
                if (empty($turnstile_sitekey)) {
                  echo '<div class="farmfaucet-error">' . esc_html__('Cloudflare Turnstile site key not configured', 'farmfaucet') . '</div>';
                } else {
                ?>
                  <div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">
                    <!-- Simple container for Cloudflare Turnstile -->
                    <div id="<?php echo $captcha_id; ?>"
                      class="cf-turnstile farmfaucet-captcha"
                      data-sitekey="<?php echo esc_attr($turnstile_sitekey); ?>"
                      data-faucet-id="<?php echo esc_attr($faucet['id']); ?>"
                      style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>
                  </div>
          <?php
                }
              }
            } catch (Exception $e) {
              echo '<div class="farmfaucet-error">' . esc_html__('Error loading captcha', 'farmfaucet') . '</div>';
            }
          }
          ?>
        </div>

        <button type="submit"
          class="farmfaucet-claim-btn farmfaucet-button custom-color"
          disabled
          aria-disabled="true"
          id="claim-button">
          <?php esc_html_e('CLAIM NOW', 'farmfaucet'); ?>
        </button>
      <?php endif; ?>
    </form>
  <?php endif; ?>
  <div class="farmfaucet-notification" role="status" aria-live="assertive"></div>
</div>

<!-- No code generator needed for dummy faucet -->