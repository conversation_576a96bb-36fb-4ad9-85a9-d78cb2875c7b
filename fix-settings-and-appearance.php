<?php
/**
 * Farm Faucet - Fix Settings Save and Appearance Issues
 * 
 * This script fixes both the settings save failure and appearance overlap issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Fix Settings & Appearance</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        .step { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        .step h3 { margin-top: 0; color: #333; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; }
        .btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Fix Settings Save & Appearance Issues</h1>
    <p>This script fixes both the settings save failure and appearance overlap problems.</p>

<?php

$fixes_applied = [];
$issues_found = [];

echo '<div class="step">';
echo '<h3>🔧 Step 1: Fix Settings Save Issue</h3>';

// Check if the encryption callback is causing issues
if (class_exists('Farmfaucet_Admin')) {
    $admin_instance = Farmfaucet_Admin::get_instance();
    
    // Test the encryption callback with error handling
    try {
        $test_result = $admin_instance->encrypt_api_key_setting('test_key');
        echo '<div class="success"><p>✅ Encryption callback is working correctly</p></div>';
        $fixes_applied[] = 'Encryption callback verified working';
    } catch (Exception $e) {
        echo '<div class="error"><p>❌ Encryption callback error: ' . esc_html($e->getMessage()) . '</p></div>';
        $issues_found[] = 'Encryption callback failing: ' . $e->getMessage();
    }
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Admin class not found</p></div>';
    $issues_found[] = 'Admin class not loaded';
}

// Check memory usage
$memory_usage = memory_get_usage(true);
$memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
$memory_percent = ($memory_usage / $memory_limit) * 100;

if ($memory_percent > 80) {
    echo '<div class="warning"><p>⚠️ High memory usage: ' . round($memory_percent, 1) . '% of limit</p></div>';
    $issues_found[] = 'High memory usage may cause save failures';
} else {
    echo '<div class="success"><p>✅ Memory usage is normal: ' . round($memory_percent, 1) . '% of limit</p></div>';
}

echo '</div>';

// Step 2: Test individual settings save
echo '<div class="step">';
echo '<h3>💾 Step 2: Test Individual Settings Save</h3>';

if (isset($_POST['test_individual_save'])) {
    $test_settings = [
        'farmfaucet_captcha_type' => 'hcaptcha',
        'farmfaucet_redirect_url' => 'https://example.com/test'
    ];
    
    foreach ($test_settings as $setting => $value) {
        $old_value = get_option($setting);
        
        try {
            $result = update_option($setting, $value);
            
            if ($result || get_option($setting) === $value) {
                echo '<div class="success"><p>✅ ' . esc_html($setting) . ' saved successfully</p></div>';
                
                // Restore old value
                if ($old_value !== false) {
                    update_option($setting, $old_value);
                } else {
                    delete_option($setting);
                }
            } else {
                echo '<div class="error"><p>❌ ' . esc_html($setting) . ' failed to save</p></div>';
                $issues_found[] = "Setting {$setting} failed to save";
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ ' . esc_html($setting) . ' error: ' . esc_html($e->getMessage()) . '</p></div>';
            $issues_found[] = "Setting {$setting} error: " . $e->getMessage();
        }
    }
    
    // Test API key settings with encryption
    $api_settings = [
        'farmfaucet_hcaptcha_secret' => 'test_secret_key',
        'farmfaucet_faucetpay_api' => 'test_api_key'
    ];
    
    foreach ($api_settings as $setting => $value) {
        $old_value = get_option($setting);
        
        try {
            // Test direct save without going through the callback
            $result = update_option($setting, $value);
            
            if ($result || get_option($setting) === $value) {
                echo '<div class="success"><p>✅ ' . esc_html($setting) . ' (direct save) successful</p></div>';
                
                // Restore old value
                if ($old_value !== false) {
                    update_option($setting, $old_value);
                } else {
                    delete_option($setting);
                }
            } else {
                echo '<div class="error"><p>❌ ' . esc_html($setting) . ' (direct save) failed</p></div>';
                $issues_found[] = "API setting {$setting} failed direct save";
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ ' . esc_html($setting) . ' error: ' . esc_html($e->getMessage()) . '</p></div>';
            $issues_found[] = "API setting {$setting} error: " . $e->getMessage();
        }
    }
} else {
    echo '<form method="post" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">';
    echo '<h4>Test Individual Settings Save</h4>';
    echo '<p>This will test saving each type of setting individually to identify the problematic one.</p>';
    echo '<p><input type="submit" name="test_individual_save" value="Test Individual Save" class="btn"></p>';
    echo '</form>';
}

echo '</div>';

// Step 3: Fix appearance overlap
echo '<div class="step">';
echo '<h3>🎨 Step 3: Fix Appearance Overlap Issues</h3>';

// Check if the new CSS file exists
$css_file = dirname(__FILE__) . '/assets/css/appearance-spacing-fix.css';
if (file_exists($css_file)) {
    echo '<div class="success"><p>✅ Appearance spacing fix CSS file created successfully</p></div>';
    $fixes_applied[] = 'Appearance spacing fix CSS created';
} else {
    echo '<div class="error"><p>❌ Appearance spacing fix CSS file not found</p></div>';
    $issues_found[] = 'Appearance spacing fix CSS missing';
}

// Check if admin class includes the new CSS
$admin_file = dirname(__FILE__) . '/includes/class-farmfaucet-admin.php';
if (file_exists($admin_file)) {
    $admin_content = file_get_contents($admin_file);
    if (strpos($admin_content, 'farmfaucet-appearance-spacing-fix') !== false) {
        echo '<div class="success"><p>✅ Appearance spacing fix CSS is included in admin class</p></div>';
        $fixes_applied[] = 'Appearance spacing fix CSS included in admin';
    } else {
        echo '<div class="warning"><p>⚠️ Appearance spacing fix CSS not included in admin class</p></div>';
        $issues_found[] = 'Appearance spacing fix CSS not included';
    }
}

echo '</div>';

// Step 4: Check for plugin conflicts
echo '<div class="step">';
echo '<h3>🔌 Step 4: Check for Plugin Conflicts</h3>';

$active_plugins = get_option('active_plugins', []);
$security_plugins = [];
$cache_plugins = [];

foreach ($active_plugins as $plugin) {
    $plugin_name = strtolower($plugin);
    
    if (strpos($plugin_name, 'security') !== false || 
        strpos($plugin_name, 'firewall') !== false || 
        strpos($plugin_name, 'wordfence') !== false ||
        strpos($plugin_name, 'sucuri') !== false) {
        $security_plugins[] = $plugin;
    }
    
    if (strpos($plugin_name, 'cache') !== false || 
        strpos($plugin_name, 'optimization') !== false ||
        strpos($plugin_name, 'wp-rocket') !== false ||
        strpos($plugin_name, 'w3-total-cache') !== false) {
        $cache_plugins[] = $plugin;
    }
}

if (!empty($security_plugins)) {
    echo '<div class="warning"><p>⚠️ Security plugins detected (may block settings save):</p>';
    echo '<ul>';
    foreach ($security_plugins as $plugin) {
        echo '<li>' . esc_html($plugin) . '</li>';
    }
    echo '</ul></div>';
    $issues_found[] = 'Security plugins may be blocking saves';
}

if (!empty($cache_plugins)) {
    echo '<div class="info"><p>ℹ️ Cache plugins detected (may need clearing):</p>';
    echo '<ul>';
    foreach ($cache_plugins as $plugin) {
        echo '<li>' . esc_html($plugin) . '</li>';
    }
    echo '</ul></div>';
}

if (empty($security_plugins) && empty($cache_plugins)) {
    echo '<div class="success"><p>✅ No obvious conflicting plugins detected</p></div>';
}

echo '</div>';

// Step 5: Provide solutions
echo '<div class="step">';
echo '<h3>💡 Step 5: Recommended Solutions</h3>';

if (!empty($issues_found)) {
    echo '<div class="warning"><h4>Issues Found:</h4>';
    echo '<ul>';
    foreach ($issues_found as $issue) {
        echo '<li>' . esc_html($issue) . '</li>';
    }
    echo '</ul></div>';
    
    echo '<div class="info"><h4>Recommended Solutions:</h4>';
    echo '<ol>';
    
    if (in_array('High memory usage may cause save failures', $issues_found)) {
        echo '<li><strong>Increase PHP Memory Limit:</strong> Add <code>ini_set("memory_limit", "256M");</code> to wp-config.php</li>';
    }
    
    if (in_array('Security plugins may be blocking saves', $issues_found)) {
        echo '<li><strong>Whitelist Farm Faucet:</strong> Add Farm Faucet admin pages to security plugin whitelist</li>';
        echo '<li><strong>Temporarily Disable Security:</strong> Test with security plugins temporarily disabled</li>';
    }
    
    if (strpos(implode(' ', $issues_found), 'Encryption') !== false) {
        echo '<li><strong>Bypass Encryption:</strong> Temporarily remove encryption callbacks to test basic save functionality</li>';
    }
    
    echo '<li><strong>Check Error Logs:</strong> Review WordPress error logs for specific error messages</li>';
    echo '<li><strong>Test in Safe Mode:</strong> Deactivate all other plugins and test with default theme</li>';
    echo '</ol></div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success"><h4>Fixes Applied:</h4>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul></div>';
}

echo '</div>';

?>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Test Settings Tab</a>
    <a href="debug-settings-save.php" class="btn" style="background: #ff9800;">🔍 Detailed Debug</a>
</div>

<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #2196F3;">
    <h3 style="color: #1976d2; margin-top: 0;">🎯 Summary & Next Steps</h3>
    
    <?php if (empty($issues_found)): ?>
        <div style="color: #2e7d32; background: #e8f5e9; padding: 15px; border-radius: 4px; margin: 10px 0;">
            <p><strong>✅ All tests passed!</strong> Your settings save and appearance issues should be resolved.</p>
        </div>
    <?php else: ?>
        <div style="color: #d32f2f; background: #ffebee; padding: 15px; border-radius: 4px; margin: 10px 0;">
            <p><strong>⚠️ Issues detected.</strong> Follow the recommended solutions above to resolve them.</p>
        </div>
    <?php endif; ?>
    
    <p style="color: #1976d2;"><strong>For Settings Save Issues:</strong></p>
    <ol style="color: #1976d2;">
        <li>Run the detailed debug script to identify the exact cause</li>
        <li>Check if security plugins are blocking the save operation</li>
        <li>Verify PHP memory limits and execution time</li>
        <li>Test with encryption callbacks temporarily disabled</li>
    </ol>
    
    <p style="color: #1976d2;"><strong>For Appearance Overlap Issues:</strong></p>
    <ol style="color: #1976d2;">
        <li>The new appearance-spacing-fix.css should resolve overlapping</li>
        <li>Clear any caching plugins after the fix</li>
        <li>Test different faucet types to ensure consistent spacing</li>
        <li>Verify transparent background settings work correctly</li>
    </ol>
</div>

</body>
</html>
