/**
 * jQuery fix for potential conflicts with other plugins
 * Includes specific fixes for captcha issues
 */
(function($) {
    'use strict';

    // Store a reference to jQuery for Farmfaucet
    window.farmfaucetJQuery = $;

    // Fix potential conflicts with other libraries that use $
    $(document).ready(function() {
        try {
            // Reinitialize farmfaucet timers if they were broken by other scripts
            if (typeof window.updateButtonTimers === 'function' && $('.farmfaucet-cooldown-timer').length) {
                setInterval(window.updateButtonTimers, 1000);
                window.updateButtonTimers();
            }

            // Reinitialize cooldown timer if it exists
            if (typeof window.updateCooldownTimer === 'function' && $('.cooldown-timer').length) {
                setInterval(window.updateCooldownTimer, 1000);
                window.updateCooldownTimer();
            }

            // Fix for captcha visibility issues
            setTimeout(function() {
                // Make sure captcha containers are visible
                $('.farmfaucet-captcha-container').css({
                    'display': 'flex',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure captcha elements are visible
                $('.h-captcha, .g-recaptcha').css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Make sure captcha iframes are visible
                $('iframe[src*="hcaptcha.com"], iframe[src*="google.com/recaptcha"]').css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                console.log('Applied captcha visibility fixes');
            }, 2000);

            // Trigger an event to confirm jQuery is working
            $(document).trigger('farmfaucet-jquery-loaded');
        } catch (e) {
            console.error('Farmfaucet jQuery error:', e);
        }
    });
})(jQuery);
