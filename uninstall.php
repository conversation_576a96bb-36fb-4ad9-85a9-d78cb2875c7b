<?php
// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Remove plugin options
$options = [
    'farmfaucet_hcaptcha_sitekey',
    'farmfaucet_hcaptcha_secret',
    'farmfaucet_faucetpay_api',
    'farmfaucet_currency',
    'farmfaucet_amount',
    'farmfaucet_cooldown'
];

foreach ($options as $option) {
    delete_option($option);
}

// Remove database tables
global $wpdb;

// Drop logs table
$logs_table = $wpdb->prefix . 'farmfaucet_logs';
$wpdb->query("DROP TABLE IF EXISTS $logs_table");

// Drop faucets table
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
$wpdb->query("DROP TABLE IF EXISTS $faucets_table");

// Clear any remaining transients
$wpdb->query(
    $wpdb->prepare(
        "DELETE FROM $wpdb->options WHERE option_name LIKE %s",
        '_transient_farmfaucet_cooldown_%'
    )
);
