<?php
/**
 * Test Minimal Plugin Loading
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing minimal plugin loading...\n";

try {
    echo "Step 1: Loading security class...\n";
    require_once 'includes/class-farmfaucet-security.php';
    echo "✅ Security class loaded\n";
    
    echo "Step 2: Loading admin class...\n";
    require_once 'includes/class-farmfaucet-admin.php';
    echo "✅ Admin class loaded\n";
    
    echo "Step 3: Loading logger class...\n";
    require_once 'includes/class-farmfaucet-logger.php';
    echo "✅ Logger class loaded\n";
    
    echo "Step 4: Loading installer class...\n";
    require_once 'includes/class-farmfaucet-installer.php';
    echo "✅ Installer class loaded\n";
    
    echo "Step 5: Testing main plugin file...\n";
    include_once 'farmfaucet.php';
    echo "✅ Main plugin file loaded successfully!\n";
    
    echo "\n🎉 SUCCESS: Minimal plugin loads without hanging!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
}
?>
