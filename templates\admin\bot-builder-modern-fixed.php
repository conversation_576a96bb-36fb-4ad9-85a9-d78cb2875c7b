<?php
/**
 * Telegram Bot Builder Modern Template - Fixed Version
 * 
 * This is a fixed version of the Telegram Bot Builder template
 * that focuses only on the essential functionality.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>
<div class="wrap farmfaucet-admin">
    <h1><?php _e('Telegram Bot Builder', 'farmfaucet'); ?></h1>
    
    <div class="farmfaucet-admin-section tg-bot-builder-section">
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php _e('Your Telegram Bots', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <p class="description">
                    <?php _e('Create and manage your Telegram bots. Connect your website with Telegram to engage with your users.', 'farmfaucet'); ?>
                </p>
                
                <div class="bot-actions">
                    <button type="button" id="create-new-bot" class="button button-primary">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Create New Bot', 'farmfaucet'); ?>
                    </button>
                </div>
                
                <div class="bot-list-container">
                    <?php
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'farmfaucet_tg_bots';
                    
                    // Check if table exists
                    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                    
                    if ($table_exists) {
                        $bots = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC", ARRAY_A);
                        
                        if (!empty($bots)) {
                            echo '<div class="bot-grid">';
                            foreach ($bots as $bot) {
                                ?>
                                <div class="bot-card" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                    <div class="bot-card-header">
                                        <h4><?php echo esc_html($bot['name']); ?></h4>
                                        <span class="bot-status <?php echo $bot['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $bot['is_active'] ? __('Active', 'farmfaucet') : __('Inactive', 'farmfaucet'); ?>
                                        </span>
                                    </div>
                                    <div class="bot-card-body">
                                        <p><strong><?php _e('Username:', 'farmfaucet'); ?></strong> @<?php echo esc_html($bot['username']); ?></p>
                                        <p><strong><?php _e('Type:', 'farmfaucet'); ?></strong> <?php echo esc_html(ucfirst($bot['bot_type'])); ?></p>
                                        <p><strong><?php _e('Created:', 'farmfaucet'); ?></strong> <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($bot['created_at']))); ?></p>
                                    </div>
                                    <div class="bot-card-footer">
                                        <button type="button" class="bot-action-button edit" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-edit"></span> <?php _e('Edit', 'farmfaucet'); ?>
                                        </button>
                                        <button type="button" class="bot-action-button delete" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-trash"></span> <?php _e('Delete', 'farmfaucet'); ?>
                                        </button>
                                        <button type="button" class="bot-action-button manage" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                            <span class="dashicons dashicons-admin-generic"></span> <?php _e('Manage', 'farmfaucet'); ?>
                                        </button>
                                    </div>
                                </div>
                                <?php
                            }
                            echo '</div>';
                        } else {
                            ?>
                            <div class="no-bots-message">
                                <p><?php _e('You haven\'t created any bots yet. Click the "Create New Bot" button to get started.', 'farmfaucet'); ?></p>
                            </div>
                            <?php
                        }
                    } else {
                        ?>
                        <div class="no-table-message">
                            <p><?php _e('The bot database table does not exist. Please run the database update script.', 'farmfaucet'); ?></p>
                            <button type="button" id="run-db-update" class="button"><?php _e('Run Database Update', 'farmfaucet'); ?></button>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php _e('Getting Started with Telegram Bots', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="getting-started-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4><?php _e('Create a Bot with BotFather', 'farmfaucet'); ?></h4>
                            <p><?php _e('Open Telegram and search for @BotFather. Start a chat and use the /newbot command to create a new bot.', 'farmfaucet'); ?></p>
                            <a href="https://t.me/botfather" target="_blank" class="button"><?php _e('Open BotFather', 'farmfaucet'); ?></a>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4><?php _e('Get Your Bot Token', 'farmfaucet'); ?></h4>
                            <p><?php _e('BotFather will give you a token for your new bot. Copy this token as you\'ll need it in the next step.', 'farmfaucet'); ?></p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4><?php _e('Create Your Bot in Farm Faucet', 'farmfaucet'); ?></h4>
                            <p><?php _e('Click the "Create New Bot" button above and paste your bot token. Farm Faucet will automatically connect to your Telegram bot.', 'farmfaucet'); ?></p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4><?php _e('Configure Your Bot', 'farmfaucet'); ?></h4>
                            <p><?php _e('After creating your bot, you can configure it to respond to commands, send messages, and interact with your users.', 'farmfaucet'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bot Dialog -->
    <div id="bot-dialog" title="<?php _e('Create New Bot', 'farmfaucet'); ?>" style="display: none;">
        <form id="bot-form" class="farmfaucet-form">
            <div class="form-group">
                <label for="bot-token"><?php _e('Bot Token', 'farmfaucet'); ?></label>
                <input type="text" id="bot-token" name="bot_token" class="regular-text" required>
                <p class="description"><?php _e('Enter your Telegram Bot API token. You can create a bot and get a token from @BotFather on Telegram.', 'farmfaucet'); ?></p>
                <button type="button" id="test-bot-token" class="button"><?php _e('Test Token', 'farmfaucet'); ?></button>
                <span id="test-token-result"></span>
            </div>
        </form>
    </div>
</div>
