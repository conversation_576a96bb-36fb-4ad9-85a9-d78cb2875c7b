<?php

/**
 * Farm Faucet - Dashboard Admin Template
 *
 * This template displays the dashboard tab in the admin panel with improved styling
 * and organization of shortcodes and settings.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Get current settings
$avatar_size = get_option('farmfaucet_avatar_size', 50);
$earnings_period = get_option('farmfaucet_earnings_graph_default_period', 'day');
$earnings_range = get_option('farmfaucet_earnings_graph_default_range', 7);
$earnings_type = get_option('farmfaucet_earnings_graph_default_type', 'line');
$earnings_height = get_option('farmfaucet_earnings_graph_default_height', 300);
$earnings_width = get_option('farmfaucet_earnings_graph_default_width', '100%');
$earnings_color = get_option('farmfaucet_earnings_graph_color', '#4CAF50');

// Save avatar settings if form is submitted
if (isset($_POST['submit']) && isset($_POST['farmfaucet_avatar_size'])) {
    // Verify nonce
    if (isset($_POST['farmfaucet_dashboard_nonce']) && wp_verify_nonce($_POST['farmfaucet_dashboard_nonce'], 'farmfaucet_dashboard_settings')) {
        // Update avatar size
        $new_size = absint($_POST['farmfaucet_avatar_size']);
        update_option('farmfaucet_avatar_size', $new_size);

        // Show success message
        echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Avatar settings updated successfully!', 'farmfaucet') . '</p></div>';

        // Update the variable for display
        $avatar_size = $new_size;
    }
}

// Save earnings graph settings if form is submitted
if (isset($_POST['submit_earnings_graph'])) {
    // Verify nonce
    if (isset($_POST['farmfaucet_earnings_graph_nonce']) && wp_verify_nonce($_POST['farmfaucet_earnings_graph_nonce'], 'farmfaucet_earnings_graph_settings')) {
        // Update period
        if (isset($_POST['farmfaucet_earnings_graph_default_period'])) {
            $new_period = sanitize_text_field($_POST['farmfaucet_earnings_graph_default_period']);
            if (in_array($new_period, ['day', 'week', 'month'])) {
                update_option('farmfaucet_earnings_graph_default_period', $new_period);
                $earnings_period = $new_period;
            }
        }

        // Update range
        if (isset($_POST['farmfaucet_earnings_graph_default_range'])) {
            $new_range = absint($_POST['farmfaucet_earnings_graph_default_range']);
            update_option('farmfaucet_earnings_graph_default_range', $new_range);
            $earnings_range = $new_range;
        }

        // Update type
        if (isset($_POST['farmfaucet_earnings_graph_default_type'])) {
            $new_type = sanitize_text_field($_POST['farmfaucet_earnings_graph_default_type']);
            if (in_array($new_type, ['line', 'bar'])) {
                update_option('farmfaucet_earnings_graph_default_type', $new_type);
                $earnings_type = $new_type;
            }
        }

        // Update height
        if (isset($_POST['farmfaucet_earnings_graph_default_height'])) {
            $new_height = absint($_POST['farmfaucet_earnings_graph_default_height']);
            update_option('farmfaucet_earnings_graph_default_height', $new_height);
            $earnings_height = $new_height;
        }

        // Update width
        if (isset($_POST['farmfaucet_earnings_graph_default_width'])) {
            $new_width = sanitize_text_field($_POST['farmfaucet_earnings_graph_default_width']);
            update_option('farmfaucet_earnings_graph_default_width', $new_width);
            $earnings_width = $new_width;
        }

        // Update color
        if (isset($_POST['farmfaucet_earnings_graph_color'])) {
            $new_color = sanitize_hex_color($_POST['farmfaucet_earnings_graph_color']);
            update_option('farmfaucet_earnings_graph_color', $new_color);
            $earnings_color = $new_color;
        }

        // Show success message
        echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Earnings graph settings updated successfully!', 'farmfaucet') . '</p></div>';
    }
}

?>
<div class="farmfaucet-admin-container">
    <!-- Dashboard Tutorial Section -->
    <div class="farmfaucet-admin-section">
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Dashboard System Tutorial', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="dashboard-tutorial">
                    <p><?php esc_html_e('The Farm Faucet Dashboard system allows you to create a personalized user dashboard for your website visitors. Here\'s how to use it:', 'farmfaucet'); ?></p>

                    <ol class="tutorial-steps">
                        <li><?php esc_html_e('Configure the settings for each shortcode in the sections below', 'farmfaucet'); ?></li>
                        <li><?php esc_html_e('Copy the shortcodes you want to use and paste them into your dashboard page', 'farmfaucet'); ?></li>
                        <li><?php esc_html_e('Customize the shortcodes with parameters to override the default settings', 'farmfaucet'); ?></li>
                        <li><?php esc_html_e('Create a layout that works for your users by combining multiple shortcodes', 'farmfaucet'); ?></li>
                    </ol>

                    <div class="tutorial-note">
                        <p><strong><?php esc_html_e('Note:', 'farmfaucet'); ?></strong> <?php esc_html_e('Each shortcode has default settings that can be overridden with parameters. For example, you can change the size of the user avatar by adding the size parameter: [farmfaucet_user_display_picture size="100"]', 'farmfaucet'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Shortcodes Section -->
    <div class="farmfaucet-admin-section">
        <h2><?php esc_html_e('User Profile Shortcodes', 'farmfaucet'); ?></h2>

        <!-- User Avatar Settings -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('User Avatar Settings', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <?php wp_nonce_field('farmfaucet_dashboard_settings', 'farmfaucet_dashboard_nonce'); ?>

                    <div class="form-group">
                        <label for="farmfaucet_avatar_size"><?php esc_html_e('Default Avatar Size (pixels)', 'farmfaucet'); ?></label>
                        <div class="range-slider-container">
                            <input type="range" id="farmfaucet_avatar_size" name="farmfaucet_avatar_size" min="20" max="200" value="<?php echo esc_attr($avatar_size); ?>" class="range-slider">
                            <span id="avatar-size-value"><?php echo esc_html($avatar_size); ?>px</span>
                        </div>
                        <p class="description"><?php esc_html_e('This sets the default size for user avatars. You can override this in individual shortcodes.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="avatar-preview">
                        <div class="avatar-preview-label"><?php esc_html_e('Preview:', 'farmfaucet'); ?></div>
                        <img src="<?php echo esc_url(get_avatar_url(get_current_user_id(), ['size' => $avatar_size])); ?>" alt="<?php esc_attr_e('Avatar Preview', 'farmfaucet'); ?>" width="<?php echo esc_attr($avatar_size); ?>" height="<?php echo esc_attr($avatar_size); ?>" style="border-radius: 50%;">
                    </div>

                    <div class="shortcode-container">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('User Avatar Shortcode', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_user_display_picture]"><?php esc_html_e('Copy Shortcode', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_user_display_picture]</code>
                            <p class="description"><?php esc_html_e('Displays the user\'s profile picture', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>size</code> - <?php esc_html_e('Size in pixels (default: global setting)', 'farmfaucet'); ?></li>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Profile Picture")', 'farmfaucet'); ?></li>
                                <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                        <div class="shortcode-example">
                            <h5><?php esc_html_e('Example:', 'farmfaucet'); ?></h5>
                            <code>[farmfaucet_user_display_picture size="100" title="My Avatar" show_title="yes"]</code>
                        </div>
                    </div>

                    <div class="shortcode-container">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Editable User Avatar Shortcode', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_avatar_editable]"><?php esc_html_e('Copy Shortcode', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_avatar_editable]</code>
                            <p class="description"><?php esc_html_e('Displays an editable version of the user\'s avatar', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>size</code> - <?php esc_html_e('Size in pixels (default: global setting)', 'farmfaucet'); ?></li>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Profile Picture")', 'farmfaucet'); ?></li>
                                <li><code>button_text</code> - <?php esc_html_e('Text for the upload button (default: "Upload")', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                        <div class="shortcode-example">
                            <h5><?php esc_html_e('Example:', 'farmfaucet'); ?></h5>
                            <code>[farmfaucet_avatar_editable size="150" button_text="Change Picture"]</code>
                        </div>
                    </div>

                    <div class="submit">
                        <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php esc_attr_e('Save Avatar Settings', 'farmfaucet'); ?>">
                    </div>
                </form>
            </div>
        </div>

        <!-- User Information Shortcodes -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('User Information Shortcodes', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="shortcodes-grid">
                    <!-- Username Shortcode -->
                    <div class="shortcode-item">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Username', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_user_username]"><?php esc_html_e('Copy', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_user_username]</code>
                            <p class="description"><?php esc_html_e('Displays the user\'s login username', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Username")', 'farmfaucet'); ?></li>
                                <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Display Name Shortcode -->
                    <div class="shortcode-item">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Display Name', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_user_display_name]"><?php esc_html_e('Copy', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_user_display_name]</code>
                            <p class="description"><?php esc_html_e('Displays the user\'s display name', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Display Name")', 'farmfaucet'); ?></li>
                                <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Editable Username Form Shortcode -->
                    <div class="shortcode-item">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Editable Username Form', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_username_form]"><?php esc_html_e('Copy', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_username_form]</code>
                            <p class="description"><?php esc_html_e('Displays an editable form for the user\'s display name', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>button_text</code> - <?php esc_html_e('Text for the save button (default: "Save")', 'farmfaucet'); ?></li>
                                <li><code>placeholder</code> - <?php esc_html_e('Placeholder text for the input field', 'farmfaucet'); ?></li>
                                <li><code>label</code> - <?php esc_html_e('Label text for the form', 'farmfaucet'); ?></li>
                                <li><code>success_message</code> - <?php esc_html_e('Message shown after successful update', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                        <div class="shortcode-example">
                            <h5><?php esc_html_e('Example:', 'farmfaucet'); ?></h5>
                            <code>[farmfaucet_username_form button_text="Update Name" label="Your Display Name" success_message="Name updated successfully!"]</code>
                        </div>
                    </div>

                    <!-- Email Shortcode -->
                    <div class="shortcode-item">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Email Address', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_user_email]"><?php esc_html_e('Copy', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_user_email]</code>
                            <p class="description"><?php esc_html_e('Displays the user\'s email address', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Email")', 'farmfaucet'); ?></li>
                                <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Joined Date Shortcode -->
                    <div class="shortcode-item">
                        <div class="shortcode-header">
                            <h4><?php esc_html_e('Joined Date', 'farmfaucet'); ?></h4>
                            <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_joined_date]"><?php esc_html_e('Copy', 'farmfaucet'); ?></button>
                        </div>
                        <div class="shortcode-info">
                            <code>[farmfaucet_joined_date]</code>
                            <p class="description"><?php esc_html_e('Displays when the user joined the site', 'farmfaucet'); ?></p>
                        </div>
                        <div class="shortcode-params">
                            <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title text (default: "Joined")', 'farmfaucet'); ?></li>
                                <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                                <li><code>format</code> - <?php esc_html_e('Date format (default: WordPress date format)', 'farmfaucet'); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Earnings Shortcodes Section -->
    <div class="farmfaucet-admin-section">
        <h2><?php esc_html_e('Earnings Shortcodes', 'farmfaucet'); ?></h2>

        <!-- Earnings Graph Shortcode -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Earnings Graph', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <!-- Earnings Graph Settings -->
                <form method="post" action="">
                    <?php wp_nonce_field('farmfaucet_earnings_graph_settings', 'farmfaucet_earnings_graph_nonce'); ?>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_default_period"><?php esc_html_e('Default Time Period', 'farmfaucet'); ?></label>
                        <select id="farmfaucet_earnings_graph_default_period" name="farmfaucet_earnings_graph_default_period" class="regular-text">
                            <option value="day" <?php selected(get_option('farmfaucet_earnings_graph_default_period', 'day'), 'day'); ?>><?php esc_html_e('Daily', 'farmfaucet'); ?></option>
                            <option value="week" <?php selected(get_option('farmfaucet_earnings_graph_default_period', 'day'), 'week'); ?>><?php esc_html_e('Weekly', 'farmfaucet'); ?></option>
                            <option value="month" <?php selected(get_option('farmfaucet_earnings_graph_default_period', 'day'), 'month'); ?>><?php esc_html_e('Monthly', 'farmfaucet'); ?></option>
                        </select>
                        <p class="description"><?php esc_html_e('Select the default time period for grouping earnings data.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_default_range"><?php esc_html_e('Default Time Range', 'farmfaucet'); ?></label>
                        <select id="farmfaucet_earnings_graph_default_range" name="farmfaucet_earnings_graph_default_range" class="regular-text">
                            <option value="7" <?php selected(get_option('farmfaucet_earnings_graph_default_range', '7'), '7'); ?>><?php esc_html_e('Last 7 days/weeks/months', 'farmfaucet'); ?></option>
                            <option value="14" <?php selected(get_option('farmfaucet_earnings_graph_default_range', '7'), '14'); ?>><?php esc_html_e('Last 14 days/weeks/months', 'farmfaucet'); ?></option>
                            <option value="30" <?php selected(get_option('farmfaucet_earnings_graph_default_range', '7'), '30'); ?>><?php esc_html_e('Last 30 days/weeks/months', 'farmfaucet'); ?></option>
                            <option value="90" <?php selected(get_option('farmfaucet_earnings_graph_default_range', '7'), '90'); ?>><?php esc_html_e('Last 90 days/weeks/months', 'farmfaucet'); ?></option>
                        </select>
                        <p class="description"><?php esc_html_e('Select the default number of time units to display.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_default_type"><?php esc_html_e('Default Graph Type', 'farmfaucet'); ?></label>
                        <select id="farmfaucet_earnings_graph_default_type" name="farmfaucet_earnings_graph_default_type" class="regular-text">
                            <option value="line" <?php selected(get_option('farmfaucet_earnings_graph_default_type', 'line'), 'line'); ?>><?php esc_html_e('Line Graph', 'farmfaucet'); ?></option>
                            <option value="bar" <?php selected(get_option('farmfaucet_earnings_graph_default_type', 'line'), 'bar'); ?>><?php esc_html_e('Bar Graph', 'farmfaucet'); ?></option>
                        </select>
                        <p class="description"><?php esc_html_e('Select the default graph type.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_default_height"><?php esc_html_e('Default Graph Height (pixels)', 'farmfaucet'); ?></label>
                        <div class="range-slider-container">
                            <input type="range" id="farmfaucet_earnings_graph_default_height" name="farmfaucet_earnings_graph_default_height" min="200" max="600" value="<?php echo esc_attr(get_option('farmfaucet_earnings_graph_default_height', 300)); ?>" class="range-slider">
                            <span id="graph-height-value"><?php echo esc_html(get_option('farmfaucet_earnings_graph_default_height', 300)); ?>px</span>
                        </div>
                        <p class="description"><?php esc_html_e('Set the default height for the earnings graph.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_default_width"><?php esc_html_e('Default Graph Width', 'farmfaucet'); ?></label>
                        <select id="farmfaucet_earnings_graph_default_width" name="farmfaucet_earnings_graph_default_width" class="regular-text">
                            <option value="100%" <?php selected(get_option('farmfaucet_earnings_graph_default_width', '100%'), '100%'); ?>><?php esc_html_e('Full Width (100%)', 'farmfaucet'); ?></option>
                            <option value="75%" <?php selected(get_option('farmfaucet_earnings_graph_default_width', '100%'), '75%'); ?>><?php esc_html_e('Three Quarters (75%)', 'farmfaucet'); ?></option>
                            <option value="50%" <?php selected(get_option('farmfaucet_earnings_graph_default_width', '100%'), '50%'); ?>><?php esc_html_e('Half Width (50%)', 'farmfaucet'); ?></option>
                        </select>
                        <p class="description"><?php esc_html_e('Select the default width for the earnings graph.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="form-group">
                        <label for="farmfaucet_earnings_graph_color"><?php esc_html_e('Graph Color', 'farmfaucet'); ?></label>
                        <input type="color" id="farmfaucet_earnings_graph_color" name="farmfaucet_earnings_graph_color" value="<?php echo esc_attr(get_option('farmfaucet_earnings_graph_color', '#4CAF50')); ?>" class="color-picker">
                        <p class="description"><?php esc_html_e('Select the default color for the earnings graph.', 'farmfaucet'); ?></p>
                    </div>

                    <div class="submit">
                        <input type="submit" name="submit_earnings_graph" id="submit_earnings_graph" class="button button-primary" value="<?php esc_attr_e('Save Graph Settings', 'farmfaucet'); ?>">
                    </div>
                </form>

                <div class="graph-preview">
                    <h4><?php esc_html_e('Graph Preview', 'farmfaucet'); ?></h4>
                    <div class="graph-preview-container" style="height: <?php echo esc_attr(get_option('farmfaucet_earnings_graph_default_height', 300)); ?>px; width: <?php echo esc_attr(get_option('farmfaucet_earnings_graph_default_width', '100%')); ?>; margin: 0 auto;">
                        <canvas id="earnings-graph-preview"></canvas>
                    </div>
                </div>

                <div class="shortcode-container" style="margin-top: 30px;">
                    <div class="shortcode-header">
                        <h4><?php esc_html_e('Earnings Graph Shortcode', 'farmfaucet'); ?></h4>
                        <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_earnings_graph]"><?php esc_html_e('Copy Shortcode', 'farmfaucet'); ?></button>
                    </div>
                    <div class="shortcode-info">
                        <code>[farmfaucet_earnings_graph]</code>
                        <p class="description"><?php esc_html_e('Displays a graph of user earnings over time', 'farmfaucet'); ?></p>
                    </div>
                    <div class="shortcode-params">
                        <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                        <ul>
                            <li><code>period</code> - <?php esc_html_e('Time period grouping: "day", "week", or "month" (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>range</code> - <?php esc_html_e('Number of time units to display (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>type</code> - <?php esc_html_e('Graph type: "line" or "bar" (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>height</code> - <?php esc_html_e('Height of the graph in pixels (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>width</code> - <?php esc_html_e('Width of the graph (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>color</code> - <?php esc_html_e('Color of the graph in hex format (default: global setting)', 'farmfaucet'); ?></li>
                            <li><code>title</code> - <?php esc_html_e('Title text (default: "Earnings by [Period]")', 'farmfaucet'); ?></li>
                        </ul>
                    </div>
                    <div class="shortcode-example">
                        <h5><?php esc_html_e('Examples:', 'farmfaucet'); ?></h5>
                        <code>[farmfaucet_earnings_graph]</code><br>
                        <code>[farmfaucet_earnings_graph period="week" range="12" type="bar" height="400" width="75%" color="#2196F3" title="Weekly Earnings"]</code>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Earned Shortcode -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Total Earned', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="shortcode-container">
                    <div class="shortcode-header">
                        <h4><?php esc_html_e('Total Earned Shortcode', 'farmfaucet'); ?></h4>
                        <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_total_earned]"><?php esc_html_e('Copy Shortcode', 'farmfaucet'); ?></button>
                    </div>
                    <div class="shortcode-info">
                        <code>[farmfaucet_total_earned]</code>
                        <p class="description"><?php esc_html_e('Displays the total amount earned by the user', 'farmfaucet'); ?></p>
                    </div>
                    <div class="shortcode-params">
                        <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                        <ul>
                            <li><code>title</code> - <?php esc_html_e('Title text (default: "Total Earned")', 'farmfaucet'); ?></li>
                            <li><code>show_title</code> - <?php esc_html_e('Show title? "yes" or "no" (default: "no")', 'farmfaucet'); ?></li>
                        </ul>
                    </div>
                    <div class="shortcode-example">
                        <h5><?php esc_html_e('Example:', 'farmfaucet'); ?></h5>
                        <code>[farmfaucet_total_earned title="My Total Earnings" show_title="yes"]</code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Shortcodes Section -->
    <div class="farmfaucet-admin-section">
        <h2><?php esc_html_e('Leaderboard Shortcodes', 'farmfaucet'); ?></h2>

        <!-- Leaderboard Shortcode -->
        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('User Leaderboard', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="shortcode-container">
                    <div class="shortcode-header">
                        <h4><?php esc_html_e('Leaderboard Shortcode', 'farmfaucet'); ?></h4>
                        <button type="button" class="copy-shortcode button button-secondary" data-shortcode="[farmfaucet_leaderboard]"><?php esc_html_e('Copy Shortcode', 'farmfaucet'); ?></button>
                    </div>
                    <div class="shortcode-info">
                        <code>[farmfaucet_leaderboard]</code>
                        <p class="description"><?php esc_html_e('Displays a leaderboard of top users', 'farmfaucet'); ?></p>
                    </div>
                    <div class="shortcode-params">
                        <h5><?php esc_html_e('Parameters:', 'farmfaucet'); ?></h5>
                        <ul>
                            <li><code>limit</code> - <?php esc_html_e('Number of users to display (default: 15)', 'farmfaucet'); ?></li>
                            <li><code>title</code> - <?php esc_html_e('Title text (default: "Top Users")', 'farmfaucet'); ?></li>
                            <li><code>show_rank</code> - <?php esc_html_e('Show rank numbers? "yes" or "no" (default: "yes")', 'farmfaucet'); ?></li>
                            <li><code>show_avatar</code> - <?php esc_html_e('Show user avatars? "yes" or "no" (default: "yes")', 'farmfaucet'); ?></li>
                            <li><code>show_completions</code> - <?php esc_html_e('Show completion counts? "yes" or "no" (default: "yes")', 'farmfaucet'); ?></li>
                        </ul>
                    </div>
                    <div class="shortcode-example">
                        <h5><?php esc_html_e('Example:', 'farmfaucet'); ?></h5>
                        <code>[farmfaucet_leaderboard limit="10" title="Top 10 Users"]</code>
                    </div>
                </div>

                <div class="leaderboard-preview">
                    <h4><?php esc_html_e('Leaderboard Preview', 'farmfaucet'); ?></h4>
                    <p class="description"><?php esc_html_e('The leaderboard displays with a transparent background and modern styling:', 'farmfaucet'); ?></p>
                    <div class="preview-image">
                        <img src="<?php echo esc_url(FARMFAUCET_URL . 'assets/images/leaderboard-preview.png'); ?>" alt="<?php esc_attr_e('Leaderboard Preview', 'farmfaucet'); ?>" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Layout Examples Section -->
    <div class="farmfaucet-admin-section">
        <h2><?php esc_html_e('Dashboard Layout Examples', 'farmfaucet'); ?></h2>

        <div class="farmfaucet-admin-card">
            <div class="card-header">
                <h3><?php esc_html_e('Example Layouts', 'farmfaucet'); ?></h3>
            </div>
            <div class="card-body">
                <div class="layout-examples">
                    <div class="layout-example">
                        <h4><?php esc_html_e('Basic User Profile', 'farmfaucet'); ?></h4>
                        <div class="layout-code">
                            <pre>
&lt;div class="user-profile"&gt;
    &lt;div class="profile-header"&gt;
        [farmfaucet_user_display_picture size="100"]
        &lt;div class="user-info"&gt;
            &lt;h2&gt;[farmfaucet_user_display_name]&lt;/h2&gt;
            &lt;p&gt;@[farmfaucet_user_username]&lt;/p&gt;
            &lt;p&gt;[farmfaucet_user_email]&lt;/p&gt;
            &lt;p&gt;<?php esc_html_e('Member since', 'farmfaucet'); ?>: [farmfaucet_joined_date]&lt;/p&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
</pre>
                        </div>
                        <button type="button" class="copy-layout button button-secondary"><?php esc_html_e('Copy Layout', 'farmfaucet'); ?></button>
                    </div>

                    <div class="layout-example">
                        <h4><?php esc_html_e('Earnings Dashboard', 'farmfaucet'); ?></h4>
                        <div class="layout-code">
                            <pre>
&lt;div class="earnings-dashboard"&gt;
    &lt;div class="earnings-header"&gt;
        &lt;h2&gt;<?php esc_html_e('Your Earnings', 'farmfaucet'); ?>&lt;/h2&gt;
        &lt;div class="total-earned"&gt;
            &lt;h3&gt;<?php esc_html_e('Total Earned', 'farmfaucet'); ?>:&lt;/h3&gt;
            &lt;div class="amount"&gt;[farmfaucet_total_earned]&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="earnings-graph"&gt;
        [farmfaucet_earnings_graph days="30" height="400" title="<?php esc_html_e('Earnings History (30 Days)', 'farmfaucet'); ?>"]
    &lt;/div&gt;
&lt;/div&gt;
</pre>
                        </div>
                        <button type="button" class="copy-layout button button-secondary"><?php esc_html_e('Copy Layout', 'farmfaucet'); ?></button>
                    </div>

                    <div class="layout-example">
                        <h4><?php esc_html_e('Complete Dashboard', 'farmfaucet'); ?></h4>
                        <div class="layout-code">
                            <pre>
&lt;div class="dashboard-container"&gt;
    &lt;div class="dashboard-sidebar"&gt;
        &lt;div class="user-profile"&gt;
            [farmfaucet_user_display_picture size="150"]
            &lt;h2&gt;[farmfaucet_user_display_name]&lt;/h2&gt;
            &lt;p&gt;@[farmfaucet_user_username]&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class="user-stats"&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-label"&gt;<?php esc_html_e('Total Earned', 'farmfaucet'); ?>:&lt;/span&gt;
                &lt;span class="stat-value"&gt;[farmfaucet_total_earned]&lt;/span&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-label"&gt;<?php esc_html_e('Member Since', 'farmfaucet'); ?>:&lt;/span&gt;
                &lt;span class="stat-value"&gt;[farmfaucet_joined_date]&lt;/span&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="dashboard-main"&gt;
        &lt;div class="dashboard-card"&gt;
            [farmfaucet_earnings_graph days="14" height="350" title="<?php esc_html_e('Recent Earnings', 'farmfaucet'); ?>"]
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
</pre>
                        </div>
                        <button type="button" class="copy-layout button button-secondary"><?php esc_html_e('Copy Layout', 'farmfaucet'); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Avatar size slider
        $('#farmfaucet_avatar_size').on('input', function() {
            var size = $(this).val();
            $('#avatar-size-value').text(size + 'px');
            $('.avatar-preview img').css({
                'width': size + 'px',
                'height': size + 'px'
            });
        });

        // Copy shortcode buttons
        $('.copy-shortcode').on('click', function() {
            var shortcode = $(this).data('shortcode');
            copyToClipboard(shortcode);

            // Show copied message
            var originalText = $(this).text();
            $(this).text('<?php esc_html_e('Copied!', 'farmfaucet'); ?>');

            // Reset button text after 2 seconds
            setTimeout(function() {
                $('.copy-shortcode').each(function() {
                    if ($(this).text() === '<?php esc_html_e('Copied!', 'farmfaucet'); ?>') {
                        $(this).text(originalText);
                    }
                });
            }, 2000);
        });

        // Copy layout buttons
        $('.copy-layout').on('click', function() {
            var layout = $(this).prev('.layout-code').find('pre').text();
            copyToClipboard(layout);

            // Show copied message
            var originalText = $(this).text();
            $(this).text('<?php esc_html_e('Copied!', 'farmfaucet'); ?>');

            // Reset button text after 2 seconds
            setTimeout(function() {
                $('.copy-layout').each(function() {
                    if ($(this).text() === '<?php esc_html_e('Copied!', 'farmfaucet'); ?>') {
                        $(this).text(originalText);
                    }
                });
            }, 2000);
        });

        // Helper function to copy text to clipboard
        function copyToClipboard(text) {
            var $temp = $("<textarea>");
            $("body").append($temp);
            $temp.val(text).select();
            document.execCommand("copy");
            $temp.remove();
        }
    });
</script>

<style>
    /* Dashboard Tab Specific Styles */
    .dashboard-tutorial {
        background-color: #f9f9f9;
        border-left: 4px solid #4CAF50;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .tutorial-steps {
        margin-left: 20px;
        margin-bottom: 15px;
    }

    .tutorial-steps li {
        margin-bottom: 10px;
    }

    .tutorial-note {
        background-color: #fffbea;
        border-left: 4px solid #f0c674;
        padding: 10px 15px;
        margin-top: 15px;
        border-radius: 4px;
    }

    .shortcodes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 15px;
    }

    .shortcode-item {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding: 15px;
        border: 1px solid #e0e0e0;
    }

    .shortcode-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .shortcode-header h4 {
        margin: 0;
        color: #4CAF50;
    }

    .shortcode-info {
        margin-bottom: 15px;
    }

    .shortcode-info code {
        display: block;
        background: #f0f0f0;
        padding: 8px;
        margin-bottom: 8px;
        border-radius: 4px;
        border-left: 3px solid #4CAF50;
        font-family: monospace;
        word-break: break-all;
    }

    .shortcode-params h5,
    .shortcode-example h5 {
        margin: 0 0 8px 0;
        color: #4CAF50;
    }

    .shortcode-params ul {
        margin: 0;
        padding-left: 20px;
    }

    .shortcode-params li {
        margin-bottom: 5px;
    }

    .shortcode-example code {
        display: block;
        background: #f0f0f0;
        padding: 8px;
        border-radius: 4px;
        border-left: 3px solid #4CAF50;
        font-family: monospace;
        word-break: break-all;
    }

    .shortcode-container {
        margin-bottom: 20px;
    }

    .layout-examples {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .layout-example {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding: 15px;
        border: 1px solid #e0e0e0;
    }

    .layout-example h4 {
        margin-top: 0;
        color: #4CAF50;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 10px;
    }

    .layout-code {
        background: #f0f0f0;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
        max-height: 300px;
        overflow-y: auto;
    }

    .layout-code pre {
        margin: 0;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 13px;
    }

    .copy-shortcode,
    .copy-layout {
        background: #4CAF50 !important;
        border-color: #43A047 !important;
        color: white !important;
    }

    .copy-shortcode:hover,
    .copy-layout:hover {
        background: #43A047 !important;
        border-color: #388E3C !important;
    }

    .avatar-preview {
        display: flex;
        align-items: center;
        margin: 15px 0;
    }

    .avatar-preview-label {
        margin-right: 15px;
        font-weight: 600;
    }

    .range-slider-container {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .range-slider {
        flex: 1;
        margin-right: 10px;
        accent-color: #4CAF50;
    }

    #avatar-size-value {
        min-width: 50px;
        font-weight: bold;
    }

    @media (max-width: 782px) {
        .shortcodes-grid {
            grid-template-columns: 1fr;
        }
    }
</style>