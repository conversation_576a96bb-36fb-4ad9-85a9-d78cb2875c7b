<?php

/**
 * User profile and leaderboard functionality
 */
class Farmfaucet_Users
{
    /**
     * Get or create a user profile
     *
     * @param string $user_hash The user hash
     * @return array The user profile data
     */
    public static function get_or_create_user($user_hash)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_users';

        // Check if user exists
        $user = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE user_hash = %s", $user_hash),
            ARRAY_A
        );

        if (!$user) {
            // Generate a random display name
            $random_name = self::generate_random_name();

            // Create new user
            $wpdb->insert(
                $table_name,
                [
                    'user_hash' => $user_hash,
                    'display_name' => $random_name,
                    'profile_picture' => '',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['%s', '%s', '%s', '%s', '%s']
            );

            // Get the newly created user
            $user = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $table_name WHERE user_hash = %s", $user_hash),
                ARRAY_A
            );
        }

        return $user;
    }

    /**
     * Update user display name
     *
     * @param string $user_hash The user hash
     * @param string $display_name The new display name
     * @return bool True if successful, false otherwise
     */
    public static function update_display_name($user_hash, $display_name)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_users';

        // Sanitize display name
        $display_name = sanitize_text_field($display_name);

        // Update user
        $result = $wpdb->update(
            $table_name,
            [
                'display_name' => $display_name,
                'updated_at' => current_time('mysql')
            ],
            ['user_hash' => $user_hash],
            ['%s', '%s'],
            ['%s']
        );

        return $result !== false;
    }

    /**
     * Update user profile picture
     *
     * @param string $user_hash The user hash
     * @param string $profile_picture The URL of the profile picture
     * @return bool True if successful, false otherwise
     */
    public static function update_profile_picture($user_hash, $profile_picture)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_users';

        // Sanitize profile picture URL
        $profile_picture = esc_url_raw($profile_picture);

        // Update user
        $result = $wpdb->update(
            $table_name,
            [
                'profile_picture' => $profile_picture,
                'updated_at' => current_time('mysql')
            ],
            ['user_hash' => $user_hash],
            ['%s', '%s'],
            ['%s']
        );

        return $result !== false;
    }

    /**
     * Check if a user is logged into WordPress and update their profile
     *
     * @param string $user_hash The user hash from Farm Faucet
     * @param string $display_name The display name to update
     * @param string $profile_picture The profile picture URL to update
     * @return bool True if WordPress user was updated, false otherwise
     */
    public static function update_wordpress_user($user_hash, $display_name = '', $profile_picture = '')
    {
        // Check if WordPress functions are available
        if (!function_exists('is_user_logged_in') || !function_exists('get_current_user_id')) {
            return false;
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return false;
        }

        $user_id = get_current_user_id();
        $updated = false;

        // Update display name if provided
        if (!empty($display_name)) {
            // Get current user info
            $current_user = get_userdata($user_id);

            // Update both display_name and user_login for consistency
            $user_data = array(
                'ID' => $user_id,
                'display_name' => $display_name,
                'user_nicename' => sanitize_title($display_name),
                'nickname' => $display_name
            );

            if (function_exists('wp_update_user')) {
                $result = wp_update_user($user_data);
                if (!is_wp_error($result)) {
                    $updated = true;

                    // Also update user_login if possible (requires direct DB access)
                    global $wpdb;
                    $safe_display_name = sanitize_user($display_name, true);

                    // Only update if the sanitized username is valid
                    if (!empty($safe_display_name) && strlen($safe_display_name) >= 3) {
                        // Check if username already exists
                        $username_exists = $wpdb->get_var($wpdb->prepare(
                            "SELECT COUNT(*) FROM {$wpdb->users} WHERE user_login = %s AND ID != %d",
                            $safe_display_name,
                            $user_id
                        ));

                        // Only update if username doesn't exist
                        if ($username_exists == 0) {
                            $wpdb->update(
                                $wpdb->users,
                                ['user_login' => $safe_display_name],
                                ['ID' => $user_id]
                            );
                        }
                    }
                }
            }
        }

        // Update profile picture if provided
        if (!empty($profile_picture)) {
            if (function_exists('update_user_meta')) {
                // Update user meta for profile picture
                update_user_meta($user_id, 'wp_user_avatar', $profile_picture);

                // If Simple Local Avatars plugin is active
                if (function_exists('simple_local_avatars')) {
                    update_user_meta($user_id, 'simple_local_avatar', $profile_picture);
                }

                // If WP User Avatar plugin is active
                if (class_exists('WP_User_Avatar')) {
                    update_user_meta($user_id, 'wp_user_avatar', $profile_picture);
                }

                $updated = true;
            }
        }

        return $updated;
    }

    /**
     * Record a faucet completion for leaderboard tracking
     *
     * @param string $user_hash The user hash
     * @return bool True if successful, false otherwise
     */
    public static function record_completion($user_hash)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_leaderboard';

        // Check if user exists in leaderboard
        $user = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE user_hash = %s", $user_hash),
            ARRAY_A
        );

        if (!$user) {
            // Create new leaderboard entry
            $result = $wpdb->insert(
                $table_name,
                [
                    'user_hash' => $user_hash,
                    'total_completions' => 1,
                    'last_completion' => current_time('mysql'),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['%s', '%d', '%s', '%s', '%s']
            );
        } else {
            // Update existing leaderboard entry
            $result = $wpdb->update(
                $table_name,
                [
                    'total_completions' => $user['total_completions'] + 1,
                    'last_completion' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['user_hash' => $user_hash],
                ['%d', '%s', '%s'],
                ['%s']
            );
        }

        return $result !== false;
    }

    /**
     * Get the top users for the leaderboard
     *
     * @param int $limit The number of users to return
     * @return array The top users
     */
    public static function get_top_users($limit = 15)
    {
        global $wpdb;
        $leaderboard_table = $wpdb->prefix . 'farmfaucet_leaderboard';
        $users_table = $wpdb->prefix . 'farmfaucet_users';

        $query = "
            SELECT l.*, u.display_name, u.profile_picture
            FROM $leaderboard_table l
            LEFT JOIN $users_table u ON l.user_hash = u.user_hash
            ORDER BY l.total_completions DESC, l.last_completion DESC
            LIMIT %d
        ";

        $results = $wpdb->get_results(
            $wpdb->prepare($query, $limit),
            ARRAY_A
        );

        return $results;
    }

    /**
     * Get user's rank on the leaderboard
     *
     * @param string $user_hash The user hash
     * @return int|null The user's rank or null if not found
     */
    public static function get_user_rank($user_hash)
    {
        global $wpdb;
        $leaderboard_table = $wpdb->prefix . 'farmfaucet_leaderboard';

        // Get user's total completions
        $user = $wpdb->get_row(
            $wpdb->prepare("SELECT total_completions FROM $leaderboard_table WHERE user_hash = %s", $user_hash),
            ARRAY_A
        );

        if (!$user) {
            return null;
        }

        // Count users with more completions
        $rank_query = "
            SELECT COUNT(*) + 1 as rank
            FROM $leaderboard_table
            WHERE total_completions > %d
            OR (total_completions = %d AND last_completion < (
                SELECT last_completion
                FROM $leaderboard_table
                WHERE user_hash = %s
            ))
        ";

        $rank = $wpdb->get_var(
            $wpdb->prepare($rank_query, $user['total_completions'], $user['total_completions'], $user_hash)
        );

        return (int) $rank;
    }

    /**
     * Generate a random avatar URL for users without a profile picture
     *
     * @param string $user_hash The user hash
     * @return string The avatar URL
     */
    public static function get_default_avatar($user_hash)
    {
        // Use the user hash to generate a consistent avatar
        $hash = md5($user_hash);

        // Use a placeholder avatar service
        return 'https://www.gravatar.com/avatar/' . $hash . '?d=identicon&s=200';
    }

    /**
     * Check if a display name is already in use by another user
     *
     * @param string $display_name The display name to check
     * @param string $current_user_hash The current user's hash (to exclude from check)
     * @return bool True if the name is already in use, false otherwise
     */
    public static function is_display_name_taken($display_name, $current_user_hash)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_users';

        // Check if any other user has this display name
        $count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE display_name = %s AND user_hash != %s",
                $display_name,
                $current_user_hash
            )
        );

        return $count > 0;
    }

    /**
     * Generate a random display name
     *
     * @return string A random display name
     */
    public static function generate_random_name()
    {
        // List of adjectives
        $adjectives = [
            'Happy',
            'Brave',
            'Clever',
            'Mighty',
            'Swift',
            'Wise',
            'Noble',
            'Gentle',
            'Bright',
            'Bold',
            'Calm',
            'Eager',
            'Fair',
            'Kind',
            'Proud',
            'Quick',
            'Shiny',
            'Smart',
            'Strong',
            'Witty',
            'Agile',
            'Daring',
            'Fierce',
            'Jolly'
        ];

        // List of nouns
        $nouns = [
            'Miner',
            'Farmer',
            'Hunter',
            'Knight',
            'Wizard',
            'Dragon',
            'Tiger',
            'Eagle',
            'Falcon',
            'Lion',
            'Panda',
            'Wolf',
            'Bear',
            'Fox',
            'Hawk',
            'Dolphin',
            'Phoenix',
            'Unicorn',
            'Warrior',
            'Explorer',
            'Pioneer',
            'Voyager',
            'Hero',
            'Champion'
        ];

        // Generate a random name
        $adjective = $adjectives[array_rand($adjectives)];
        $noun = $nouns[array_rand($nouns)];
        $number = mt_rand(10, 999);

        return $adjective . $noun . $number;
    }
}
