<div class="currency-maker-welcome-card">
    <h3><?php _e('Welcome to Currency Manager', 'farmfaucet'); ?></h3>
    <p><?php _e('Currency Manager allows you to create and manage custom cryptocurrencies for your faucet. Users can earn, exchange, and spend these currencies on your site.', 'farmfaucet'); ?></p>

    <div class="currency-maker-tutorial">
        <h4><?php _e('Getting Started', 'farmfaucet'); ?></h4>
        <p><?php _e('Follow these steps to create your first custom currency:', 'farmfaucet'); ?></p>
        <ol>
            <li><?php _e('Click the <strong>Add Currency</strong> button above', 'farmfaucet'); ?></li>
            <li><?php _e('Fill in the required information (name, code, symbol, etc.)', 'farmfaucet'); ?></li>
            <li><?php _e('Choose a base currency and set an exchange rate', 'farmfaucet'); ?></li>
            <li><?php _e('Select a color and icon for your currency', 'farmfaucet'); ?></li>
            <li><?php _e('Click <strong>Save</strong> to create your currency', 'farmfaucet'); ?></li>
        </ol>

        <h4><?php _e('Using Your Currencies', 'farmfaucet'); ?></h4>
        <p><?php _e('Once created, you can:', 'farmfaucet'); ?></p>
        <ul>
            <li><?php _e('Display user balances with the <code>[farmfaucet_currency_balance]</code> shortcode', 'farmfaucet'); ?></li>
            <li><?php _e('Show a list of all currencies with the <code>[farmfaucet_currency_list]</code> shortcode', 'farmfaucet'); ?></li>
            <li><?php _e('Use currencies for rewards in faucets, referrals, and advertising', 'farmfaucet'); ?></li>
            <li><?php _e('Allow users to exchange between different currencies', 'farmfaucet'); ?></li>
        </ul>
    </div>

    <div class="currency-maker-field-explanation">
        <h4><?php _e('Understanding Currency Fields', 'farmfaucet'); ?></h4>
        <table>
            <thead>
                <tr>
                    <th><?php _e('Field', 'farmfaucet'); ?></th>
                    <th><?php _e('Description', 'farmfaucet'); ?></th>
                    <th><?php _e('Example', 'farmfaucet'); ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong><?php _e('Currency Name', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('The full name of your currency', 'farmfaucet'); ?></td>
                    <td>Bitcoin, Farm Coin</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Currency Code', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('A short, unique identifier (3-5 characters)', 'farmfaucet'); ?></td>
                    <td>BTC, FARM</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Currency Symbol', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('The symbol used when displaying amounts', 'farmfaucet'); ?></td>
                    <td>₿, F$</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Base Currency', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('The real cryptocurrency your custom currency is based on', 'farmfaucet'); ?></td>
                    <td>BTC, ETH, USDT</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Exchange Rate', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('How many units of your currency equal 1 unit of the base currency', 'farmfaucet'); ?></td>
                    <td>1000 (1000 FARM = 1 BTC)</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Color', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('The color associated with your currency (for visual identification)', 'farmfaucet'); ?></td>
                    <td>#f7931a (Bitcoin orange)</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Icon URL', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('URL to an image representing your currency (ideally square, 64x64px or larger)', 'farmfaucet'); ?></td>
                    <td>https://example.com/farm-coin.png</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Active', 'farmfaucet'); ?></strong></td>
                    <td><?php _e('Whether the currency is available for use on your site', 'farmfaucet'); ?></td>
                    <td>Yes/No</td>
                </tr>
            </tbody>
        </table>

        <h4><?php _e('Tips for Success', 'farmfaucet'); ?></h4>
        <ul>
            <li><?php _e('<strong>Exchange Rates:</strong> Set realistic exchange rates based on the value you want to assign to your currency.', 'farmfaucet'); ?></li>
            <li><?php _e('<strong>Currency Codes:</strong> Use uppercase letters for currency codes to follow standard conventions.', 'farmfaucet'); ?></li>
            <li><?php _e('<strong>Icons:</strong> Use transparent PNG images with a square aspect ratio for best results.', 'farmfaucet'); ?></li>
            <li><?php _e('<strong>Testing:</strong> After creating a currency, test it with a small amount before full deployment.', 'farmfaucet'); ?></li>
        </ul>
    </div>
</div>