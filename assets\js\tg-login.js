/**
 * Telegram Bot Login JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initTelegramLogin();
    });

    /**
     * Initialize Telegram Login functionality
     */
    function initTelegramLogin() {
        // Login button click handler
        $('.farmfaucet-tg-login-button').on('click', function() {
            $(this).fadeOut(300, function() {
                $('.farmfaucet-tg-login-form').fadeIn(300);
            });
        });

        // Signup button click handler
        $('.farmfaucet-tg-signup-button').on('click', function() {
            $(this).fadeOut(300, function() {
                $('.farmfaucet-tg-signup-form').fadeIn(300);
            });
        });

        // Login form submit handler
        $('.farmfaucet-tg-login-submit').on('click', function() {
            handleLogin();
        });

        // Signup form submit handler
        $('.farmfaucet-tg-signup-submit').on('click', function() {
            handleSignup();
        });

        // Get verification code link click handler
        $('.farmfaucet-tg-get-code').on('click', function(e) {
            e.preventDefault();
            getVerificationCode();
        });

        // Verify code button click handler
        $('.farmfaucet-tg-verify-submit').on('click', function() {
            verifyCode();
        });
    }

    /**
     * Handle login form submission
     */
    function handleLogin() {
        const $form = $('.farmfaucet-tg-login-form');
        const $message = $('.farmfaucet-tg-login-message');
        
        // Get form data
        const username = $('#farmfaucet-tg-login-username').val();
        const password = $('#farmfaucet-tg-login-password').val();
        const redirect = $form.find('input[name="redirect"]').val();
        
        // Validate form
        if (!username || !password) {
            showMessage($message, 'Please fill in all required fields.', 'error');
            return;
        }
        
        // Show loading message
        showMessage($message, 'Logging in...', 'info');
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetTgLogin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_tg_login',
                nonce: farmfaucetTgLogin.nonce,
                username: username,
                password: password,
                redirect: redirect
            },
            success: function(response) {
                if (response.success) {
                    showMessage($message, response.data.message, 'success');
                    
                    // Redirect after successful login
                    setTimeout(function() {
                        window.location.href = response.data.redirect;
                    }, 1500);
                } else {
                    showMessage($message, response.data.message, 'error');
                }
            },
            error: function() {
                showMessage($message, farmfaucetTgLogin.i18n.error, 'error');
            }
        });
    }

    /**
     * Handle signup form submission
     */
    function handleSignup() {
        const $form = $('.farmfaucet-tg-signup-form');
        const $message = $('.farmfaucet-tg-signup-message');
        
        // Get form data
        const email = $('#farmfaucet-tg-signup-email').val();
        const username = $('#farmfaucet-tg-signup-username').val();
        const password = $('#farmfaucet-tg-signup-password').val();
        const displayName = $('#farmfaucet-tg-signup-display-name').val();
        const phone = $('#farmfaucet-tg-signup-phone').val();
        
        // Validate form
        if (!email || !username || !password || !displayName || !phone) {
            showMessage($message, 'Please fill in all required fields.', 'error');
            return;
        }
        
        // Show loading message
        showMessage($message, 'Processing signup...', 'info');
        
        // Show verification section
        $('.farmfaucet-tg-verification').fadeIn(300);
        
        // Start verification timer
        startVerificationTimer();
    }

    /**
     * Get verification code from Telegram bot
     */
    function getVerificationCode() {
        const $message = $('.farmfaucet-tg-signup-message');
        const phone = $('#farmfaucet-tg-signup-phone').val();
        
        // Validate phone
        if (!phone) {
            showMessage($message, 'Please enter your Telegram phone number.', 'error');
            return;
        }
        
        // Show loading message
        showMessage($message, 'Sending verification code...', 'info');
        
        // Open Telegram bot in new tab
        const botUsername = farmfaucetTgLogin.botUsername;
        if (botUsername) {
            window.open(`https://t.me/${botUsername}`, '_blank');
        }
        
        // Show success message
        showMessage($message, farmfaucetTgLogin.i18n.verificationSent, 'info');
    }

    /**
     * Verify the code entered by the user
     */
    function verifyCode() {
        const $form = $('.farmfaucet-tg-signup-form');
        const $message = $('.farmfaucet-tg-signup-message');
        
        // Get form data
        const email = $('#farmfaucet-tg-signup-email').val();
        const username = $('#farmfaucet-tg-signup-username').val();
        const password = $('#farmfaucet-tg-signup-password').val();
        const displayName = $('#farmfaucet-tg-signup-display-name').val();
        const phone = $('#farmfaucet-tg-signup-phone').val();
        const code = $('#farmfaucet-tg-verification-code').val();
        const redirect = $form.find('input[name="redirect"]').val();
        
        // Validate code
        if (!code) {
            showMessage($message, 'Please enter the verification code.', 'error');
            return;
        }
        
        // Show loading message
        showMessage($message, 'Verifying code...', 'info');
        
        // Send AJAX request to verify code
        $.ajax({
            url: farmfaucetTgLogin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_verify_tg_code',
                nonce: farmfaucetTgLogin.nonce,
                phone: phone,
                code: code
            },
            success: function(response) {
                if (response.success) {
                    // Code verified, create account
                    createAccount(email, username, password, displayName, phone, code, redirect);
                } else {
                    showMessage($message, response.data.message, 'error');
                }
            },
            error: function() {
                showMessage($message, farmfaucetTgLogin.i18n.error, 'error');
            }
        });
    }

    /**
     * Create user account after verification
     */
    function createAccount(email, username, password, displayName, phone, code, redirect) {
        const $message = $('.farmfaucet-tg-signup-message');
        
        // Show loading message
        showMessage($message, 'Creating your account...', 'info');
        
        // Send AJAX request to create account
        $.ajax({
            url: farmfaucetTgLogin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_tg_signup',
                nonce: farmfaucetTgLogin.nonce,
                email: email,
                username: username,
                password: password,
                display_name: displayName,
                phone: phone,
                verification_code: code,
                redirect: redirect
            },
            success: function(response) {
                if (response.success) {
                    showMessage($message, response.data.message, 'success');
                    
                    // Redirect after successful signup
                    setTimeout(function() {
                        window.location.href = response.data.redirect;
                    }, 1500);
                } else {
                    showMessage($message, response.data.message, 'error');
                }
            },
            error: function() {
                showMessage($message, farmfaucetTgLogin.i18n.error, 'error');
            }
        });
    }

    /**
     * Start verification timer
     */
    function startVerificationTimer() {
        const $timer = $('.farmfaucet-tg-verification-timer .timer-value');
        const timeout = farmfaucetTgLogin.verificationTimeout || 300; // Default 5 minutes
        let timeLeft = timeout;
        
        // Update timer every second
        const timerInterval = setInterval(function() {
            timeLeft--;
            
            // Format time as MM:SS
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            const formattedTime = 
                (minutes < 10 ? '0' + minutes : minutes) + ':' + 
                (seconds < 10 ? '0' + seconds : seconds);
            
            // Update timer display
            $timer.text(formattedTime);
            
            // Check if timer has expired
            if (timeLeft <= 0) {
                clearInterval(timerInterval);
                $timer.text('00:00');
                $('.farmfaucet-tg-verification').addClass('expired');
                showMessage($('.farmfaucet-tg-signup-message'), 'Verification code expired. Please try again.', 'error');
            }
        }, 1000);
    }

    /**
     * Show message in the message container
     */
    function showMessage($container, message, type) {
        // Remove existing classes
        $container.removeClass('farmfaucet-success farmfaucet-error farmfaucet-info');
        
        // Add appropriate class based on message type
        $container.addClass('farmfaucet-' + type);
        
        // Set message text
        $container.text(message);
        
        // Show message
        $container.fadeIn(300);
    }

})(jQuery);
