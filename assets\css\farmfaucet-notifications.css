/**
 * Farm Faucet Interactive Notification System Styles
 */

/* Notification container */
.farmfaucet-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

/* Individual notification */
.farmfaucet-notification {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    overflow: hidden;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    opacity: 0;
    pointer-events: auto;
    position: relative;
    border-left: 4px solid #ccc;
}

/* Show state */
.farmfaucet-notification.farmfaucet-notification-show {
    transform: translateX(0);
    opacity: 1;
}

/* Notification content */
.farmfaucet-notification-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

/* Notification icon */
.farmfaucet-notification-icon {
    font-size: 20px;
    flex-shrink: 0;
    line-height: 1;
}

/* Notification message */
.farmfaucet-notification-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    font-weight: 500;
}

/* Close button */
.farmfaucet-notification-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.farmfaucet-notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #666;
}

/* Progress bar */
.farmfaucet-notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    width: 0%;
    transition: width 0.1s ease;
}

/* Notification types */
.farmfaucet-notification-success {
    border-left-color: #4CAF50;
    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
}

.farmfaucet-notification-success .farmfaucet-notification-progress {
    background: #4CAF50;
}

.farmfaucet-notification-error {
    border-left-color: #f44336;
    background: linear-gradient(135deg, #fff8f8 0%, #ffe8e8 100%);
}

.farmfaucet-notification-error .farmfaucet-notification-progress {
    background: #f44336;
}

.farmfaucet-notification-warning {
    border-left-color: #ff9800;
    background: linear-gradient(135deg, #fffbf8 0%, #fff3e8 100%);
}

.farmfaucet-notification-warning .farmfaucet-notification-progress {
    background: #ff9800;
}

.farmfaucet-notification-info {
    border-left-color: #2196F3;
    background: linear-gradient(135deg, #f8fbff 0%, #e8f3ff 100%);
}

.farmfaucet-notification-info .farmfaucet-notification-progress {
    background: #2196F3;
}

.farmfaucet-notification-loading {
    border-left-color: #9C27B0;
    background: linear-gradient(135deg, #faf8ff 0%, #f3e8ff 100%);
}

.farmfaucet-notification-loading .farmfaucet-notification-icon {
    animation: farmfaucet-loading-spin 1s linear infinite;
}

/* Progress bar animation */
@keyframes farmfaucet-progress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* Loading spinner animation */
@keyframes farmfaucet-loading-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Hover effects */
.farmfaucet-notification:hover {
    transform: translateX(-5px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.farmfaucet-notification:hover .farmfaucet-notification-progress {
    animation-play-state: paused;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .farmfaucet-notifications-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .farmfaucet-notification-content {
        padding: 14px 16px;
        gap: 10px;
    }
    
    .farmfaucet-notification-message {
        font-size: 13px;
    }
    
    .farmfaucet-notification-icon {
        font-size: 18px;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .farmfaucet-notification {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .farmfaucet-notification-message {
        color: #ffffff;
    }
    
    .farmfaucet-notification-close {
        color: #ccc;
    }
    
    .farmfaucet-notification-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .farmfaucet-notification-success {
        background: linear-gradient(135deg, #1a2e1a 0%, #0d1f0d 100%);
    }
    
    .farmfaucet-notification-error {
        background: linear-gradient(135deg, #2e1a1a 0%, #1f0d0d 100%);
    }
    
    .farmfaucet-notification-warning {
        background: linear-gradient(135deg, #2e251a 0%, #1f190d 100%);
    }
    
    .farmfaucet-notification-info {
        background: linear-gradient(135deg, #1a232e 0%, #0d161f 100%);
    }
    
    .farmfaucet-notification-loading {
        background: linear-gradient(135deg, #251a2e 0%, #190d1f 100%);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .farmfaucet-notification {
        border: 2px solid;
        box-shadow: none;
    }
    
    .farmfaucet-notification-success {
        border-color: #4CAF50;
        background: #ffffff;
    }
    
    .farmfaucet-notification-error {
        border-color: #f44336;
        background: #ffffff;
    }
    
    .farmfaucet-notification-warning {
        border-color: #ff9800;
        background: #ffffff;
    }
    
    .farmfaucet-notification-info {
        border-color: #2196F3;
        background: #ffffff;
    }
    
    .farmfaucet-notification-loading {
        border-color: #9C27B0;
        background: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .farmfaucet-notification {
        transition: opacity 0.3s ease;
        transform: none;
    }
    
    .farmfaucet-notification.farmfaucet-notification-show {
        transform: none;
    }
    
    .farmfaucet-notification:hover {
        transform: none;
    }
    
    .farmfaucet-notification-loading .farmfaucet-notification-icon {
        animation: none;
    }
    
    @keyframes farmfaucet-progress {
        from { width: 100%; }
        to { width: 0%; }
    }
}
