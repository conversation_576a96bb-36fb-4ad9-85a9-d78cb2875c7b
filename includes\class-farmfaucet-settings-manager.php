<?php

/**
 * Centralized Settings Manager for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.3
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Settings_Manager
 *
 * Handles ALL settings registration and saving in one place
 */
class Farmfaucet_Settings_Manager
{
    /**
     * Singleton instance
     *
     * @var Farmfaucet_Settings_Manager
     */
    private static $instance;

    /**
     * All registered settings
     *
     * @var array
     */
    private static $all_settings = [];

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Settings_Manager
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Register all settings on admin_init
        add_action('admin_init', [$this, 'register_all_settings'], 5); // Early priority
    }

    /**
     * Register all settings in one place
     */
    public function register_all_settings()
    {
        // Define ALL settings in one place to avoid conflicts
        $settings_groups = [
            'farmfaucet_settings' => [
                // Main plugin settings
                'farmfaucet_captcha_type' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => 'hcaptcha'
                ],
                'farmfaucet_hcaptcha_sitekey' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_hcaptcha_secret' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_recaptcha_sitekey' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_recaptcha_secret' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_turnstile_sitekey' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_turnstile_secret' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_faucetpay_api' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_redirect_url' => [
                    'sanitize_callback' => 'esc_url_raw',
                    'default' => ''
                ],
                'farmfaucet_daily_reset' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => '00:00'
                ],
                'farmfaucet_leaderboard_reset_date' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_login_form_password_change_url' => [
                    'sanitize_callback' => 'esc_url_raw',
                    'default' => ''
                ],
                'farmfaucet_login_form_account_login_url' => [
                    'sanitize_callback' => 'esc_url_raw',
                    'default' => ''
                ]
            ],
            'farmfaucet_advertising_settings' => [
                'farmfaucet_advertising_enabled' => [
                    'sanitize_callback' => 'intval',
                    'default' => 1
                ],
                'farmfaucet_ad_approval_required' => [
                    'sanitize_callback' => 'intval',
                    'default' => 1
                ],
                'farmfaucet_ad_cost' => [
                    'sanitize_callback' => 'floatval',
                    'default' => 1.0
                ],
                'farmfaucet_ad_duration_days' => [
                    'sanitize_callback' => 'absint',
                    'default' => 7
                ],
                'farmfaucet_ad_max_votes' => [
                    'sanitize_callback' => 'absint',
                    'default' => 3
                ],
                'farmfaucet_ad_vote_reward' => [
                    'sanitize_callback' => 'floatval',
                    'default' => 0.1
                ]
            ],
            'farmfaucet_currency_settings' => [
                'farmfaucet_currency_maker_enabled' => [
                    'sanitize_callback' => 'intval',
                    'default' => 1
                ],
                'farmfaucet_currency_exchange_fee' => [
                    'sanitize_callback' => 'floatval',
                    'default' => 2.0
                ]
            ],
            'farmfaucet_referral_settings' => [
                'farmfaucet_referral_enabled' => [
                    'sanitize_callback' => 'intval',
                    'default' => 1
                ],
                'farmfaucet_referral_reward_type' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => 'percentage'
                ],
                'farmfaucet_referral_reward_percentage' => [
                    'sanitize_callback' => 'floatval',
                    'default' => 10.0
                ],
                'farmfaucet_referral_reward_fixed' => [
                    'sanitize_callback' => 'floatval',
                    'default' => 0.0
                ]
            ],
            'farmfaucet_tg_bot_settings' => [
                'farmfaucet_tg_bot_builder_enabled' => [
                    'sanitize_callback' => 'intval',
                    'default' => 0
                ],
                'farmfaucet_tg_bot_webhook_url' => [
                    'sanitize_callback' => 'esc_url_raw',
                    'default' => ''
                ],
                'farmfaucet_tg_bot_default_type' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => 'text'
                ],
                'farmfaucet_tg_bot_captcha_type' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => 'hcaptcha'
                ],
                'farmfaucet_tg_bot_captcha_sitekey' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_tg_bot_captcha_secret' => [
                    'sanitize_callback' => 'sanitize_text_field',
                    'default' => ''
                ],
                'farmfaucet_tg_login_enabled' => [
                    'sanitize_callback' => 'intval',
                    'default' => 0
                ],
                'farmfaucet_tg_login_bot' => [
                    'sanitize_callback' => 'absint',
                    'default' => 0
                ],
                'farmfaucet_tg_login_redirect' => [
                    'sanitize_callback' => 'absint',
                    'default' => 0
                ],
                'farmfaucet_tg_signup_redirect' => [
                    'sanitize_callback' => 'absint',
                    'default' => 0
                ],
                'farmfaucet_tg_otp_expiration' => [
                    'sanitize_callback' => 'absint',
                    'default' => 5
                ]
            ]
        ];

        // Register all settings
        foreach ($settings_groups as $group => $settings) {
            foreach ($settings as $setting_name => $args) {
                register_setting($group, $setting_name, $args);
                self::$all_settings[$setting_name] = array_merge($args, ['group' => $group]);
            }
        }
    }

    /**
     * Get all registered settings
     *
     * @return array
     */
    public static function get_all_settings()
    {
        return self::$all_settings;
    }

    /**
     * Get settings by group
     *
     * @param string $group Settings group
     * @return array
     */
    public static function get_settings_by_group($group)
    {
        $group_settings = [];
        foreach (self::$all_settings as $setting => $args) {
            if (isset($args['group']) && $args['group'] === $group) {
                $group_settings[$setting] = $args;
            }
        }
        return $group_settings;
    }

    /**
     * Safe option update with validation
     *
     * @param string $option Option name
     * @param mixed $value Option value
     * @return bool
     */
    public static function safe_update_option($option, $value)
    {
        try {
            // Check if setting is registered
            if (!isset(self::$all_settings[$option])) {
                error_log("Farmfaucet: Attempting to update unregistered setting: {$option}");
                return false;
            }

            $setting_config = self::$all_settings[$option];
            
            // Apply sanitization
            if (isset($setting_config['sanitize_callback']) && is_callable($setting_config['sanitize_callback'])) {
                $value = call_user_func($setting_config['sanitize_callback'], $value);
            }

            // Update the option
            return update_option($option, $value);
            
        } catch (Exception $e) {
            error_log("Farmfaucet settings update error for {$option}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Safe option get with default
     *
     * @param string $option Option name
     * @param mixed $default Default value
     * @return mixed
     */
    public static function safe_get_option($option, $default = null)
    {
        try {
            // Use registered default if available
            if (isset(self::$all_settings[$option]['default']) && $default === null) {
                $default = self::$all_settings[$option]['default'];
            }

            return get_option($option, $default);
            
        } catch (Exception $e) {
            error_log("Farmfaucet settings get error for {$option}: " . $e->getMessage());
            return $default;
        }
    }

    /**
     * Validate settings form submission
     *
     * @param array $posted_data Posted form data
     * @param string $group Settings group to validate
     * @return array Validated data
     */
    public static function validate_form_submission($posted_data, $group)
    {
        $validated_data = [];
        $group_settings = self::get_settings_by_group($group);

        foreach ($group_settings as $setting => $config) {
            if (isset($posted_data[$setting])) {
                $value = $posted_data[$setting];
                
                // Apply sanitization
                if (isset($config['sanitize_callback']) && is_callable($config['sanitize_callback'])) {
                    $value = call_user_func($config['sanitize_callback'], $value);
                }
                
                $validated_data[$setting] = $value;
            }
        }

        return $validated_data;
    }

    /**
     * Bulk update settings for a group
     *
     * @param array $settings Settings to update
     * @param string $group Settings group
     * @return bool
     */
    public static function bulk_update_settings($settings, $group)
    {
        try {
            $validated_settings = self::validate_form_submission($settings, $group);
            $success_count = 0;
            $total_count = count($validated_settings);

            foreach ($validated_settings as $setting => $value) {
                if (self::safe_update_option($setting, $value)) {
                    $success_count++;
                }
            }

            return $success_count === $total_count;
            
        } catch (Exception $e) {
            error_log("Farmfaucet bulk settings update error: " . $e->getMessage());
            return false;
        }
    }
}
