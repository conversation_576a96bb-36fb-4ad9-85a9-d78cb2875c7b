<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #4CAF50;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .test-section p {
            margin-bottom: 20px;
        }
        
        .test-section pre {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .test-section code {
            font-family: monospace;
        }
        
        .test-form {
            display: flex;
            flex-direction: column;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .test-form input,
        .test-form button {
            margin-bottom: 10px;
            padding: 10px;
        }
        
        .test-form button {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-form button:hover {
            background-color: #45a049;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 4px;
            display: none;
        }
        
        .error {
            background-color: #ffebee;
            color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Telegram Bot Login Test</h1>
        
        <div class="test-section">
            <h2>Login Form</h2>
            <p>This is a test of the Telegram Bot Login form.</p>
            <pre><code>[farmfaucet_tg_login]</code></pre>
            
            <div class="farmfaucet-tg-login-form">
                <h2 class="farmfaucet-tg-form-title">Login</h2>
                
                <form method="post" class="test-form">
                    <div class="farmfaucet-tg-form-group">
                        <label for="username_or_email">Username, Email or Telegram Number</label>
                        <input type="text" name="username_or_email" id="username_or_email" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="password">Password</label>
                        <input type="password" name="password" id="password" required>
                    </div>
                    
                    <button type="submit" class="farmfaucet-tg-form-button">Login with Telegram</button>
                </form>
                
                <div class="farmfaucet-tg-form-links">
                    <a href="#">Forgot Password?</a>
                    <a href="#">Sign Up</a>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Signup Form</h2>
            <p>This is a test of the Telegram Bot Signup form.</p>
            <pre><code>[farmfaucet_tg_signup]</code></pre>
            
            <div class="farmfaucet-tg-signup-form">
                <h2 class="farmfaucet-tg-form-title">Sign Up</h2>
                
                <form method="post" class="test-form">
                    <div class="farmfaucet-tg-form-group">
                        <label for="name">Full Name</label>
                        <input type="text" name="name" id="name" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="username">Username</label>
                        <input type="text" name="username" id="username" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="email">Email</label>
                        <input type="email" name="email" id="email" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="telegram_number">Telegram Number</label>
                        <input type="tel" name="telegram_number" id="telegram_number" placeholder="+1234567890" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="password">Password</label>
                        <input type="password" name="password" id="password" required>
                    </div>
                    
                    <div class="farmfaucet-tg-form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password" name="confirm_password" id="confirm_password" required>
                    </div>
                    
                    <button type="submit" class="farmfaucet-tg-form-button">Sign Up with Telegram</button>
                </form>
                
                <div class="farmfaucet-tg-form-links">
                    <a href="#">Already have an account? Login</a>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>OTP Verification Form</h2>
            <p>This is a test of the Telegram Bot OTP verification form.</p>
            
            <div class="farmfaucet-tg-otp-form">
                <h2 class="farmfaucet-tg-form-title">Verify Your Telegram</h2>
                
                <div class="otp-message">
                    We've sent a verification code to your Telegram. Please enter it below.
                    Code expires in <span class="otp-timer" data-time="300">5:00</span>
                </div>
                
                <form method="post" class="test-form">
                    <div class="farmfaucet-tg-form-group">
                        <label for="otp_code">Verification Code</label>
                        <input type="text" name="otp_code" id="otp_code" class="farmfaucet-tg-otp-input" maxlength="6" required>
                    </div>
                    
                    <input type="hidden" name="user_id" value="1">
                    
                    <button type="submit" class="farmfaucet-tg-form-button">Verify</button>
                </form>
                
                <div class="farmfaucet-tg-form-links">
                    <a href="#" class="resend-otp" data-user-id="1">Resend Code</a>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Logout Button</h2>
            <p>This is a test of the Telegram Bot Logout button.</p>
            <pre><code>[farmfaucet_tg_logout]</code></pre>
            
            <a href="#" class="farmfaucet-tg-logout-button">Logout</a>
        </div>
    </div>
    
    <script>
        // Simulate OTP timer
        const timerElement = document.querySelector('.otp-timer');
        if (timerElement) {
            let timeLeft = parseInt(timerElement.dataset.time) || 300;
            
            const timerInterval = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                
                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    timerElement.closest('.otp-message').innerHTML = '<span class="otp-expired">OTP code has expired. Please request a new one.</span>';
                }
                
                timeLeft--;
            }, 1000);
        }
        
        // Simulate form submissions
        document.querySelectorAll('.test-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formType = this.closest('div').className;
                let message = '';
                
                if (formType === 'farmfaucet-tg-login-form') {
                    message = 'Login successful!';
                } else if (formType === 'farmfaucet-tg-signup-form') {
                    message = 'Signup successful! Please verify your Telegram number.';
                    
                    // Show OTP form
                    document.querySelector('.farmfaucet-tg-otp-form').scrollIntoView({
                        behavior: 'smooth'
                    });
                } else if (formType === 'farmfaucet-tg-otp-form') {
                    message = 'Verification successful!';
                }
                
                alert(message);
            });
        });
        
        // Simulate resend OTP
        document.querySelector('.resend-otp').addEventListener('click', function(e) {
            e.preventDefault();
            
            const link = this;
            const originalText = link.textContent;
            
            link.textContent = 'Sending...';
            link.classList.add('disabled');
            
            setTimeout(() => {
                alert('Verification code sent!');
                
                // Reset timer
                const timerElement = document.querySelector('.otp-timer');
                if (timerElement) {
                    const timeLeft = 300;
                    timerElement.dataset.time = timeLeft;
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                }
                
                // Re-enable link after 60 seconds
                setTimeout(() => {
                    link.textContent = originalText;
                    link.classList.remove('disabled');
                }, 60000);
            }, 1000);
        });
    </script>
</body>
</html>
