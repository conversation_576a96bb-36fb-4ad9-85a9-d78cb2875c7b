<?php

/**
 * Database updater for Telegram Bot Builder
 *
 * @package Farmfaucet
 * @since 2.3
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_DB_Updater_Tg_Bot
 * 
 * Handles database updates for Telegram Bot Builder
 */
class Farmfaucet_DB_Updater_Tg_Bot {
    
    /**
     * Initialize the updater
     */
    public static function init() {
        // Check if we need to create or update tables
        add_action('plugins_loaded', [__CLASS__, 'check_version']);
        
        // Register activation hook
        register_activation_hook(FARMFAUCET_FILE, [__CLASS__, 'activate']);
    }
    
    /**
     * Plugin activation hook
     */
    public static function activate() {
        // Create database tables
        self::create_tables();
        
        // Set version
        update_option('farmfaucet_tg_bot_db_version', FARMFAUCET_VERSION);
    }
    
    /**
     * Check if we need to update the database
     */
    public static function check_version() {
        $db_version = get_option('farmfaucet_tg_bot_db_version', '0');
        
        // If the database version is not the same as the plugin version, update
        if (version_compare($db_version, FARMFAUCET_VERSION, '<')) {
            self::update();
        }
    }
    
    /**
     * Update the database
     */
    public static function update() {
        $db_version = get_option('farmfaucet_tg_bot_db_version', '0');
        
        // Create tables if they don't exist
        self::create_tables();
        
        // Run version-specific updates
        if (version_compare($db_version, '2.3', '<')) {
            self::update_to_2_3();
        }
        
        // Update version
        update_option('farmfaucet_tg_bot_db_version', FARMFAUCET_VERSION);
    }
    
    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Bots table
        $table_bots = $wpdb->prefix . 'farmfaucet_tg_bots';
        
        // Flows table
        $table_flows = $wpdb->prefix . 'farmfaucet_tg_flows';
        
        // Commands table
        $table_commands = $wpdb->prefix . 'farmfaucet_tg_commands';
        
        // Analytics table
        $table_analytics = $wpdb->prefix . 'farmfaucet_tg_analytics';
        
        // Moderation logs table
        $table_moderation = $wpdb->prefix . 'farmfaucet_tg_moderation';
        
        // Create bots table
        $sql_bots = "CREATE TABLE $table_bots (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_name varchar(100) NOT NULL,
            bot_token varchar(255) NOT NULL,
            bot_username varchar(100) NOT NULL,
            bot_type varchar(20) NOT NULL DEFAULT 'text',
            webhook_url varchar(255) DEFAULT '',
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            settings longtext DEFAULT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            UNIQUE KEY bot_token (bot_token)
        ) $charset_collate;";
        
        // Create flows table
        $sql_flows = "CREATE TABLE $table_flows (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            flow_name varchar(100) NOT NULL,
            flow_data longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id)
        ) $charset_collate;";
        
        // Create commands table
        $sql_commands = "CREATE TABLE $table_commands (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            command_name varchar(100) NOT NULL,
            command_description varchar(255) DEFAULT '',
            command_response longtext NOT NULL,
            buttons longtext DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id)
        ) $charset_collate;";
        
        // Create analytics table
        $sql_analytics = "CREATE TABLE $table_analytics (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data longtext DEFAULT NULL,
            user_id varchar(50) DEFAULT NULL,
            chat_id varchar(50) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY chat_id (chat_id)
        ) $charset_collate;";
        
        // Create moderation logs table
        $sql_moderation = "CREATE TABLE $table_moderation (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bot_id mediumint(9) NOT NULL,
            action_type varchar(50) NOT NULL,
            user_id varchar(50) NOT NULL,
            chat_id varchar(50) NOT NULL,
            reason text DEFAULT NULL,
            duration int(11) DEFAULT 0,
            admin_id varchar(50) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY bot_id (bot_id),
            KEY action_type (action_type),
            KEY user_id (user_id),
            KEY chat_id (chat_id)
        ) $charset_collate;";
        
        // Include WordPress database upgrade functions
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        // Create the tables
        dbDelta($sql_bots);
        dbDelta($sql_flows);
        dbDelta($sql_commands);
        dbDelta($sql_analytics);
        dbDelta($sql_moderation);
    }
    
    /**
     * Update to version 2.3
     */
    public static function update_to_2_3() {
        // Add default settings
        if (get_option('farmfaucet_tg_bot_builder_enabled') === false) {
            update_option('farmfaucet_tg_bot_builder_enabled', 0);
        }
        
        if (get_option('farmfaucet_tg_bot_default_type') === false) {
            update_option('farmfaucet_tg_bot_default_type', 'text');
        }
        
        if (get_option('farmfaucet_tg_bot_captcha_type') === false) {
            update_option('farmfaucet_tg_bot_captcha_type', 'hcaptcha');
        }
    }
}
