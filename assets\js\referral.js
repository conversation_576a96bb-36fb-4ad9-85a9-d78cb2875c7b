/**
 * Referral System Frontend JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initReferralSystem();
    });

    /**
     * Initialize Referral System functionality
     */
    function initReferralSystem() {
        // Check if referral code is in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const refCode = urlParams.get('ref');

        if (refCode) {
            // Set cookie with referral code
            setCookie('farmfaucet_referral', refCode, 30);
        }

        // Initialize copy to clipboard functionality
        initCopyToClipboard();

        // Initialize referral stats refresh
        initReferralStats();

        // Initialize leaderboard tabs
        initLeaderboardTabs();
    }

    /**
     * Set cookie
     *
     * @param {string} name Cookie name
     * @param {string} value Cookie value
     * @param {number} days Cookie expiration in days
     */
    function setCookie(name, value, days) {
        let expires = '';

        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = '; expires=' + date.toUTCString();
        }

        document.cookie = name + '=' + value + expires + '; path=/';
    }

    /**
     * Initialize copy to clipboard functionality
     */
    function initCopyToClipboard() {
        $('.farmfaucet-copy-button').on('click', function(e) {
            e.preventDefault();

            const $this = $(this);
            const $input = $this.closest('.referral-link-input').find('input');
            const originalText = $this.text();

            // Copy to clipboard
            $input.select();
            document.execCommand('copy');

            // Update button text
            $this.text(farmfaucetReferral.i18n.copied);
            $this.addClass('copied');

            // Reset button text after 2 seconds
            setTimeout(function() {
                $this.text(originalText);
                $this.removeClass('copied');
            }, 2000);
        });
    }

    /**
     * Initialize referral stats refresh
     */
    function initReferralStats() {
        const $statsContainer = $('.farmfaucet-referral-stats-container');

        if (!$statsContainer.length) {
            return;
        }

        // Refresh stats every 60 seconds
        setInterval(function() {
            refreshReferralStats();
        }, 60000);
    }

    /**
     * Refresh referral stats
     */
    function refreshReferralStats() {
        const $statsContainer = $('.farmfaucet-referral-stats-container');

        if (!$statsContainer.length) {
            return;
        }

        $.ajax({
            url: farmfaucetReferral.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_referral_stats',
                nonce: farmfaucetReferral.nonce
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateStatsDisplay(response.data.stats);
                }
            }
        });
    }

    /**
     * Update stats display
     *
     * @param {Object} stats Referral stats
     */
    function updateStatsDisplay(stats) {
        // Update total referrals
        $('.stat-total-referrals .stat-value').text(stats.total_referrals);

        // Update active referrals
        $('.stat-active-referrals .stat-value').text(stats.active_referrals);

        // Update total earnings
        $('.stat-total-earnings .stat-value').text(stats.total_earnings);

        // Update conversion rate
        $('.stat-conversion-rate .stat-value').text(stats.conversion_rate + '%');

        // Update conversion bar
        $('.conversion-progress').css('width', stats.conversion_rate + '%');
    }

    /**
     * Initialize leaderboard tabs
     */
    function initLeaderboardTabs() {
        $('.period-tab').on('click', function(e) {
            e.preventDefault();

            const $this = $(this);
            const period = $this.data('period');
            const $leaderboard = $this.closest('.farmfaucet-referral-leaderboard-container');

            // Update active tab
            $leaderboard.find('.period-tab').removeClass('active');
            $this.addClass('active');

            // Show loading state
            $leaderboard.find('.referral-leaderboard').html('<div class="loading-spinner">Loading...</div>');

            // Load leaderboard data
            $.ajax({
                url: farmfaucetReferral.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'farmfaucet_get_referral_leaderboard',
                    nonce: farmfaucetReferral.nonce,
                    period: period
                },
                success: function(response) {
                    if (response.success) {
                        $leaderboard.find('.referral-leaderboard').html(response.data.html);
                    } else {
                        $leaderboard.find('.referral-leaderboard').html('<div class="error-message">' + response.data.message + '</div>');
                    }
                },
                error: function() {
                    $leaderboard.find('.referral-leaderboard').html('<div class="error-message">' + farmfaucetReferral.i18n.error + '</div>');
                }
            });
        });
    }

})(jQuery);
