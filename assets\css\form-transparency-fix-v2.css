/**
 * Form Transparency Fix V2
 * 
 * This CSS fixes issues with form transparency and background blur.
 */

/* Fix for transparent form backgrounds */
.farmfaucet-container.transparent-bg {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
}

.farmfaucet-container.transparent-bg .farmfaucet-form {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
}

.farmfaucet-container.transparent-bg .farmfaucet-form-inner {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
}

/* Fix for form inputs with transparent background */
.farmfaucet-container.transparent-bg input[type="text"],
.farmfaucet-container.transparent-bg input[type="number"],
.farmfaucet-container.transparent-bg input[type="email"],
.farmfaucet-container.transparent-bg input[type="password"],
.farmfaucet-container.transparent-bg input[type="tel"],
.farmfaucet-container.transparent-bg input[type="url"],
.farmfaucet-container.transparent-bg select,
.farmfaucet-container.transparent-bg textarea {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: inherit !important;
}

/* Fix for form transparency when form_transparent is enabled */
.farmfaucet-container .farmfaucet-form.form-transparent {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
}

.farmfaucet-container .farmfaucet-form.form-transparent .farmfaucet-form-inner {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
}

/* Fix for currency maker modal */
#farmfaucet-currency-modal {
    z-index: 99999 !important;
}

#farmfaucet-currency-modal .farmfaucet-modal-content {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Fix for color picker in currency maker */
.wp-picker-container {
    z-index: 999999 !important;
}

.wp-picker-container .iris-picker {
    position: absolute !important;
    z-index: 999999 !important;
    display: block !important;
}

.wp-picker-container .wp-color-result {
    cursor: pointer !important;
}

/* Fix for dummy faucet */
.farmfaucet-dummy-faucet .farmfaucet-form {
    background-color: transparent !important;
}

.farmfaucet-dummy-faucet .farmfaucet-form-inner {
    background-color: transparent !important;
}

.farmfaucet-dummy-faucet.transparent-bg {
    background-color: transparent !important;
}

/* Fix for captcha container */
.farmfaucet-captcha-container {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1 !important;
}

.farmfaucet-captcha-container iframe {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1 !important;
}

/* Fix for Telegram Bot Builder button */
#create-new-bot {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#create-new-bot:hover {
    background-color: #45a049 !important;
}

.ui-dialog-buttonset button:first-child {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.ui-dialog-buttonset button:first-child:hover {
    background-color: #45a049 !important;
}
