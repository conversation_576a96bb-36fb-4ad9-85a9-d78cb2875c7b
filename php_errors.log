[22-Apr-2025 12:31:44 Europe/Berlin] Step 1: Basic PHP is working
[22-Apr-2025 12:31:44 Europe/Berlin] Step 2: Checking file system access
[22-Apr-2025 12:31:44 Europe/Berlin] Found 17 files in current directory
[22-Apr-2025 12:31:44 Europe/Berlin] Step 3: Checking if we can include files
[22-Apr-2025 12:31:44 Europe/Berlin] Security class file exists
[22-Apr-2025 12:31:44 Europe/Berlin] Step 4: Trying to include security class
[22-Apr-2025 12:31:44 Europe/Berlin] Security class included successfully
[22-Apr-2025 12:31:44 Europe/Berlin] Step 5: Checking if security class exists
[22-Apr-2025 12:31:44 Europe/Berlin] Security class exists
[22-Apr-2025 12:31:44 Europe/Berlin] Step 6: Trying to include installer class
[22-Apr-2025 12:31:44 Europe/Berlin] Installer class included successfully
[22-Apr-2025 12:31:44 Europe/Berlin] Step 7: Checking if installer class exists
[22-Apr-2025 12:31:44 Europe/Berlin] Installer class exists
[22-Apr-2025 12:31:44 Europe/Berlin] Step 8: Trying to include main plugin file
[22-Apr-2025 12:39:51 Europe/Berlin] Step 1: Basic PHP is working
[22-Apr-2025 12:39:51 Europe/Berlin] Step 2: Checking file system access
[22-Apr-2025 12:39:51 Europe/Berlin] Found 17 files in current directory
[22-Apr-2025 12:39:51 Europe/Berlin] Step 3: Checking if we can include files
[22-Apr-2025 12:39:51 Europe/Berlin] Security class file exists
[22-Apr-2025 12:39:51 Europe/Berlin] Step 4: Trying to include security class
[22-Apr-2025 12:39:51 Europe/Berlin] Security class included successfully
[22-Apr-2025 12:39:51 Europe/Berlin] Step 5: Checking if security class exists
[22-Apr-2025 12:39:51 Europe/Berlin] Security class exists
[22-Apr-2025 12:39:51 Europe/Berlin] Step 6: Trying to include installer class
[22-Apr-2025 12:39:51 Europe/Berlin] Installer class included successfully
[22-Apr-2025 12:39:51 Europe/Berlin] Step 7: Checking if installer class exists
[22-Apr-2025 12:39:51 Europe/Berlin] Installer class exists
[22-Apr-2025 12:39:51 Europe/Berlin] Step 8: Trying to include main plugin file
[25-Jun-2025 11:51:03 Europe/Berlin] Step 1: Basic PHP is working
[25-Jun-2025 11:51:03 Europe/Berlin] Step 2: Checking file system access
[25-Jun-2025 11:51:03 Europe/Berlin] Found 37 files in current directory
[25-Jun-2025 11:51:03 Europe/Berlin] Step 3: Checking if we can include files
[25-Jun-2025 11:51:03 Europe/Berlin] Security class file exists
[25-Jun-2025 11:51:03 Europe/Berlin] Step 4: Trying to include security class
[25-Jun-2025 11:51:03 Europe/Berlin] Security class included successfully
[25-Jun-2025 11:51:03 Europe/Berlin] Step 5: Checking if security class exists
[25-Jun-2025 11:51:03 Europe/Berlin] Security class exists
[25-Jun-2025 11:51:03 Europe/Berlin] Step 6: Trying to include installer class
[25-Jun-2025 11:51:03 Europe/Berlin] Installer class included successfully
[25-Jun-2025 11:51:03 Europe/Berlin] Step 7: Checking if installer class exists
[25-Jun-2025 11:51:03 Europe/Berlin] Installer class exists
[25-Jun-2025 11:51:03 Europe/Berlin] Step 8: Trying to include main plugin file
[11-Jul-2025 01:52:13 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 01:52:13 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 01:52:13 Europe/Berlin] Found 42 files in current directory
[11-Jul-2025 01:52:13 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 01:52:13 Europe/Berlin] Security class file exists
[11-Jul-2025 01:52:13 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 01:52:13 Europe/Berlin] Security class included successfully
[11-Jul-2025 01:52:13 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 01:52:13 Europe/Berlin] Security class exists
[11-Jul-2025 01:52:13 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 01:52:13 Europe/Berlin] Installer class included successfully
[11-Jul-2025 01:52:13 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 01:52:13 Europe/Berlin] Installer class exists
[11-Jul-2025 01:52:13 Europe/Berlin] Step 8: Trying to include main plugin file
[11-Jul-2025 03:32:33 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:32:33 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:32:33 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:32:33 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:32:33 Europe/Berlin] Security class file exists
[11-Jul-2025 03:32:33 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:32:33 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:32:33 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:32:33 Europe/Berlin] Security class exists
[11-Jul-2025 03:32:33 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:32:33 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:32:33 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:32:33 Europe/Berlin] Installer class exists
[11-Jul-2025 03:32:33 Europe/Berlin] Step 8: Trying to include main plugin file
[11-Jul-2025 03:34:00 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:34:00 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:34:00 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:34:00 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:34:00 Europe/Berlin] Security class file exists
[11-Jul-2025 03:34:00 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:34:00 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:34:00 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:34:00 Europe/Berlin] Security class exists
[11-Jul-2025 03:34:00 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:34:00 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:34:00 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:34:00 Europe/Berlin] Installer class exists
[11-Jul-2025 03:34:00 Europe/Berlin] Step 8: Trying to include main plugin file
[11-Jul-2025 03:34:48 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:34:48 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:34:48 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:34:48 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:34:48 Europe/Berlin] Security class file exists
[11-Jul-2025 03:34:48 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:34:48 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:34:48 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:34:48 Europe/Berlin] Security class exists
[11-Jul-2025 03:34:48 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:34:48 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:34:48 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:34:48 Europe/Berlin] Installer class exists
[11-Jul-2025 03:34:48 Europe/Berlin] Step 8: Trying to include main plugin file
[11-Jul-2025 03:36:19 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:36:19 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:36:19 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:36:19 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:36:19 Europe/Berlin] Security class file exists
[11-Jul-2025 03:36:19 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:36:19 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:36:19 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:36:19 Europe/Berlin] Security class exists
[11-Jul-2025 03:36:19 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:36:19 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:36:19 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:36:19 Europe/Berlin] Installer class exists
[11-Jul-2025 03:36:19 Europe/Berlin] Step 8: Testing individual file includes
[11-Jul-2025 03:36:19 Europe/Berlin] Testing include: includes/class-farmfaucet-settings-manager.php
[11-Jul-2025 03:38:58 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:38:58 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:38:58 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:38:58 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:38:58 Europe/Berlin] Security class file exists
[11-Jul-2025 03:38:58 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:38:58 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:38:58 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:38:58 Europe/Berlin] Security class exists
[11-Jul-2025 03:38:58 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:38:58 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:38:58 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:38:58 Europe/Berlin] Installer class exists
[11-Jul-2025 03:38:58 Europe/Berlin] Step 8: Testing individual file includes
[11-Jul-2025 03:38:58 Europe/Berlin] Testing include: includes/class-farmfaucet-settings-manager.php
[11-Jul-2025 03:39:52 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:39:52 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:39:52 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:39:52 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:39:52 Europe/Berlin] Security class file exists
[11-Jul-2025 03:39:52 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:39:52 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:39:52 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:39:52 Europe/Berlin] Security class exists
[11-Jul-2025 03:39:52 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:39:52 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:39:52 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:39:52 Europe/Berlin] Installer class exists
[11-Jul-2025 03:39:52 Europe/Berlin] Step 8: Testing individual file includes
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-api.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-api.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-users.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-users.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:39:52 Europe/Berlin] Successfully included: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:39:52 Europe/Berlin] Testing include: includes/class-farmfaucet-tg-bot-login.php
[11-Jul-2025 03:41:07 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:41:07 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:41:07 Europe/Berlin] Found 45 files in current directory
[11-Jul-2025 03:41:07 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:41:07 Europe/Berlin] Security class file exists
[11-Jul-2025 03:41:07 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:41:07 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:41:07 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:41:07 Europe/Berlin] Security class exists
[11-Jul-2025 03:41:07 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:41:07 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:41:07 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:41:07 Europe/Berlin] Installer class exists
[11-Jul-2025 03:41:07 Europe/Berlin] Step 8: Testing individual file includes
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-api.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-api.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-users.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-users.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:41:07 Europe/Berlin] Successfully included: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:41:07 Europe/Berlin] Testing include: includes/class-farmfaucet-tg-bot-login.php
[11-Jul-2025 03:49:29 Europe/Berlin] Step 1: Basic PHP is working
[11-Jul-2025 03:49:29 Europe/Berlin] Step 2: Checking file system access
[11-Jul-2025 03:49:29 Europe/Berlin] Found 46 files in current directory
[11-Jul-2025 03:49:29 Europe/Berlin] Step 3: Checking if we can include files
[11-Jul-2025 03:49:29 Europe/Berlin] Security class file exists
[11-Jul-2025 03:49:29 Europe/Berlin] Step 4: Trying to include security class
[11-Jul-2025 03:49:29 Europe/Berlin] Security class included successfully
[11-Jul-2025 03:49:29 Europe/Berlin] Step 5: Checking if security class exists
[11-Jul-2025 03:49:29 Europe/Berlin] Security class exists
[11-Jul-2025 03:49:29 Europe/Berlin] Step 6: Trying to include installer class
[11-Jul-2025 03:49:29 Europe/Berlin] Installer class included successfully
[11-Jul-2025 03:49:29 Europe/Berlin] Step 7: Checking if installer class exists
[11-Jul-2025 03:49:29 Europe/Berlin] Installer class exists
[11-Jul-2025 03:49:29 Europe/Berlin] Step 8: Testing individual file includes
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-admin.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-frontend.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-api.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-api.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-logger.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-compatibility.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-safe-loader.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-scheduler.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-users.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-users.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-db-updater.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-task-completion.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:49:29 Europe/Berlin] Successfully included: includes/class-farmfaucet-captcha-handler.php
[11-Jul-2025 03:49:29 Europe/Berlin] Testing include: includes/class-farmfaucet-tg-bot-login.php
