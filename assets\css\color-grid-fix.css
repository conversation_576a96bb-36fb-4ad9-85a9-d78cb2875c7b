/**
 * Color Grid Fix CSS
 */

/* Make sure the color grid is properly styled */
.color-grid {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    width: 320px;
}

.color-grid.active {
    display: grid !important;
}

.color-swatch-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    margin: 0 auto;
}

.color-swatch-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.color-swatch-option.selected {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #4CAF50;
}

.color-grid-container {
    position: relative;
}

.color-select-wrapper {
    display: flex;
    align-items: center;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
}

.color-name-display {
    flex: 1;
    font-weight: 500;
}

.color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-left: 10px;
}

/* Copy button styling */
.copy-shortcode {
    margin-left: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    background: #4CAF50 !important;
    color: white !important;
    border-color: #43A047 !important;
}

.copy-shortcode:hover {
    background: #43A047 !important;
    color: white !important;
}

/* Range input styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: #e0e0e0;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
