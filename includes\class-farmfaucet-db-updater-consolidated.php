<?php

/**
 * Farmfaucet Consolidated Database Updater
 *
 * Handles all database updates for the Farmfaucet plugin in one place
 */
class Farmfaucet_DB_Updater_Consolidated
{
    /**
     * Initialize the updater
     */
    public static function init()
    {
        // Register activation hook
        register_activation_hook(FARMFAUCET_PLUGIN_FILE, [__CLASS__, 'activate']);

        // Check if we need to update the database
        add_action('plugins_loaded', [__CLASS__, 'check_version']);

        // Add admin notice if database update is needed
        add_action('admin_notices', [__CLASS__, 'show_update_notice']);
    }

    /**
     * Plugin activation hook
     */
    public static function activate()
    {
        // Create or update all database tables
        self::update_all_tables();

        // Set version
        update_option('farmfaucet_db_version', FARMFAUCET_VERSION);
    }

    /**
     * Check if we need to update the database
     */
    public static function check_version()
    {
        $db_version = get_option('farmfaucet_db_version', '0');

        // If the database version is not the same as the plugin version, update
        if (version_compare($db_version, FARMFAUCET_VERSION, '<')) {
            // Store that an update is needed
            update_option('farmfaucet_db_update_needed', true);
        }
    }

    /**
     * Show admin notice if database update is needed
     */
    public static function show_update_notice()
    {
        if (get_option('farmfaucet_db_update_needed', false)) {
?>
            <div class="notice notice-warning is-dismissible">
                <p><strong><?php _e('Farm Faucet Database Update Required', 'farmfaucet'); ?></strong></p>
                <p><?php _e('The Farm Faucet plugin requires a database update to fix issues with currency creation, bot creation, and form transparency.', 'farmfaucet'); ?></p>
                <p><a href="<?php echo admin_url('admin.php?page=farmfaucet&action=update_db'); ?>" class="button button-primary"><?php _e('Update Database', 'farmfaucet'); ?></a></p>
            </div>
<?php
        }
    }

    /**
     * Run all database updates
     */
    public static function run_updates()
    {
        // Update all tables
        self::update_all_tables();

        // Update version
        update_option('farmfaucet_db_version', FARMFAUCET_VERSION);

        // Clear update needed flag
        delete_option('farmfaucet_db_update_needed');

        return true;
    }

    /**
     * Update all database tables
     */
    private static function update_all_tables()
    {
        global $wpdb;

        // Update faucets table
        self::update_faucets_table();

        // Update currencies table
        self::update_currencies_table();

        // Update Telegram bots table
        self::update_tg_bots_table();

        // Update buttons table
        self::update_buttons_table();
    }

    /**
     * Update faucets table
     */
    private static function update_faucets_table()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add form_bg_color column if it doesn't exist
        if (!in_array('form_bg_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius");
        }

        // Add form_transparent column if it doesn't exist
        if (!in_array('form_transparent', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color");
        }

        // Add button_border_radius column if it doesn't exist
        if (!in_array('button_border_radius', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN button_border_radius varchar(20) NOT NULL DEFAULT '25px' AFTER form_transparent");
        }

        // Add input_label_color column if it doesn't exist
        if (!in_array('input_label_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN input_label_color varchar(20) NOT NULL DEFAULT '#333333' AFTER button_border_radius");
        }

        // Add input_placeholder_color column if it doesn't exist
        if (!in_array('input_placeholder_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN input_placeholder_color varchar(20) NOT NULL DEFAULT '#999999' AFTER input_label_color");
        }

        // Update existing faucets to set default values
        $wpdb->query("UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''");

        // Add transparent_bg column if it doesn't exist
        if (!in_array('transparent_bg', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER faucet_color");
        }

        // Add bg_style column if it doesn't exist
        if (!in_array('bg_style', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_style varchar(20) NOT NULL DEFAULT 'solid' AFTER transparent_bg");
        }

        // Add bg_color column if it doesn't exist
        if (!in_array('bg_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_color varchar(50) NOT NULL DEFAULT '#f8fff8' AFTER bg_style");
        }

        // Add bg_gradient_start column if it doesn't exist
        if (!in_array('bg_gradient_start', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_gradient_start varchar(50) NOT NULL DEFAULT '#f8fff8' AFTER bg_color");
        }

        // Add bg_gradient_end column if it doesn't exist
        if (!in_array('bg_gradient_end', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN bg_gradient_end varchar(50) NOT NULL DEFAULT '#e8f5e9' AFTER bg_gradient_start");
        }

        // Add text_color column if it doesn't exist
        if (!in_array('text_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN text_color varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER bg_gradient_end");
        }

        // Add text_shadow column if it doesn't exist
        if (!in_array('text_shadow', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN text_shadow varchar(100) NOT NULL DEFAULT 'none' AFTER text_color");
        }
    }

    /**
     * Update currencies table
     */
    private static function update_currencies_table()
    {
        global $wpdb;
        $currencies_table = $wpdb->prefix . 'farmfaucet_currencies';

        // Check if the currencies table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$currencies_table}'") === $currencies_table;

        if (!$table_exists) {
            // Create the currencies table
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$currencies_table} (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                code varchar(10) NOT NULL,
                symbol varchar(10) NOT NULL,
                base_currency varchar(10) NOT NULL,
                exchange_rate decimal(18,8) NOT NULL,
                color varchar(20) DEFAULT '#4CAF50',
                icon varchar(255) DEFAULT NULL,
                currency_type varchar(20) DEFAULT 'earnings',
                is_active tinyint(1) NOT NULL DEFAULT 1,
                created_by bigint(20) NOT NULL,
                created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY code (code),
                KEY base_currency (base_currency),
                KEY currency_type (currency_type),
                KEY is_active (is_active)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        } else {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$currencies_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Make sure the color column exists and has the right default
            if (!in_array('color', $column_names)) {
                $wpdb->query("ALTER TABLE {$currencies_table} ADD COLUMN color varchar(20) DEFAULT '#4CAF50' AFTER exchange_rate");
            } else {
                // Update the color column to ensure it has the right default
                $wpdb->query("ALTER TABLE {$currencies_table} MODIFY COLUMN color varchar(20) DEFAULT '#4CAF50'");
            }

            // Make sure the currency_type column exists
            if (!in_array('currency_type', $column_names)) {
                $wpdb->query("ALTER TABLE {$currencies_table} ADD COLUMN currency_type varchar(20) DEFAULT 'earnings' AFTER icon");
            }
        }
    }

    /**
     * Update Telegram bots table
     */
    private static function update_tg_bots_table()
    {
        global $wpdb;
        $bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';

        // Check if the bots table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bots_table}'") === $bots_table;

        if (!$table_exists) {
            // Create the bots table
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$bots_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                bot_name varchar(100) NOT NULL,
                bot_token varchar(255) NOT NULL,
                bot_username varchar(100) NOT NULL,
                bot_type varchar(20) NOT NULL DEFAULT 'text',
                webhook_url varchar(255) DEFAULT '',
                created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
                settings longtext DEFAULT NULL,
                is_active tinyint(1) DEFAULT 1,
                PRIMARY KEY  (id),
                UNIQUE KEY bot_token (bot_token)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }

    /**
     * Update buttons table
     */
    private static function update_buttons_table()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;

        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Update column sizes for color fields
        $color_columns = [
            'button_color' => 'blue',
            'milestone_card_bg_color' => '#FFFFFF',
            'milestone_card_gradient_start' => '#FFFFFF',
            'milestone_card_gradient_end' => '#F5F5F5',
            'milestone_bar_color' => '#4CAF50',
            'milestone_gradient_start' => '#4CAF50',
            'milestone_gradient_end' => '#2196F3'
        ];

        foreach ($color_columns as $column => $default) {
            if (in_array($column, $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN {$column} varchar(50) NOT NULL DEFAULT '{$default}'");
            }
        }
    }
}
