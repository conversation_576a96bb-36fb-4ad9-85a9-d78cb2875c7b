<?php

/**
 * Farm Faucet Database Update Script
 *
 * This script updates the database structure to ensure all required columns exist
 * and have the correct data types.
 */

// Load WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Check if user is logged in and is an admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to run this script.');
}

// Include the security class
require_once(dirname(__FILE__) . '/includes/class-farmfaucet-security.php');

// Run the database update
Farmfaucet_Security::update_database_schema();

// Force update of button_color column size
global $wpdb;
$buttons_table = $wpdb->prefix . 'farmfaucet_buttons';
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN button_color varchar(50) NOT NULL DEFAULT 'blue'");
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_bg_color varchar(50) NOT NULL DEFAULT '#FFFFFF'");
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_gradient_start varchar(50) NOT NULL DEFAULT '#FFFFFF'");
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_card_gradient_end varchar(50) NOT NULL DEFAULT '#F5F5F5'");
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_bar_color varchar(50) NOT NULL DEFAULT '#4CAF50'");

// Add form_bg_color column if it doesn't exist
$column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$faucets_table} LIKE 'form_bg_color'");
if (!$column_exists) {
    $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff' AFTER border_radius");
}

// Add form_transparent column if it doesn't exist
$column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$faucets_table} LIKE 'form_transparent'");
if (!$column_exists) {
    $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN form_transparent tinyint(1) NOT NULL DEFAULT 0 AFTER form_bg_color");
}
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_gradient_start varchar(50) NOT NULL DEFAULT '#4CAF50'");
$wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN milestone_gradient_end varchar(50) NOT NULL DEFAULT '#2196F3'");

// Check if milestone_lock_faucet column exists
$column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$buttons_table} LIKE 'milestone_lock_faucet'");
if (!$column_exists) {
    $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_lock_faucet tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_specific_count");
}

// Check if milestone_transparent_bg column exists
$column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$buttons_table} LIKE 'milestone_transparent_bg'");
if (!$column_exists) {
    $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN milestone_transparent_bg tinyint(1) NOT NULL DEFAULT 0 AFTER milestone_display_style");
}

echo '<div style="background-color: #dff0d8; color: #3c763d; padding: 15px; border: 1px solid #d6e9c6; border-radius: 4px; margin: 20px 0;">';
echo '<h2>Database Update Complete</h2>';
echo '<p>The Farm Faucet database has been updated successfully.</p>';
echo '<p>All required columns now exist and have the correct data types.</p>';
echo '<p><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button button-primary">Return to Farm Faucet</a></p>';
echo '</div>';
