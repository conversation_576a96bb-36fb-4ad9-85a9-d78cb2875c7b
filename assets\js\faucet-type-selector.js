/**
 * Faucet Type Selector
 *
 * Handles the display of different faucet type fields in the admin interface
 */
jQuery(document).ready(function($) {
    // Function to show/hide faucet type fields
    function toggleFaucetTypeFields() {
        var selectedType = $('#faucet-type').val();

        // Hide all faucet type fields
        $('.faucet-type-fields').hide();

        // Show the selected faucet type fields
        $('.' + selectedType + '-faucet-fields').show();

        // Update form fields based on faucet type
        if (selectedType === 'stage') {
            // Standard faucet fields
            $('#faucet-currency').closest('.form-field').show();
            $('#faucet-amount').closest('.form-field').show();
        } else if (selectedType === 'dummy') {
            // Dummy faucet fields - uses created currencies
            $('#faucet-currency').closest('.form-field').hide();
            $('#faucet-amount').closest('.form-field').show();
        } else if (selectedType === 'withdrawal') {
            // Withdrawal faucet fields
            $('#faucet-currency').closest('.form-field').hide();
            $('#faucet-amount').closest('.form-field').hide();
        }
    }

    // Initialize on page load
    toggleFaucetTypeFields();

    // Listen for changes to the faucet type dropdown
    $('#faucet-type').on('change', toggleFaucetTypeFields);

    // Add faucet type to the AJAX data when creating or updating a faucet
    var originalCreateFaucet = window.createFaucet;
    if (typeof originalCreateFaucet === 'function') {
        window.createFaucet = function() {
            // Add faucet_type to the form data
            var faucetType = $('#faucet-type').val();
            $('#faucet-form').append('<input type="hidden" name="faucet_type" value="' + faucetType + '">');

            // Call the original function
            originalCreateFaucet();
        };
    }

    // When editing a faucet, set the correct faucet type
    $(document).on('faucetLoaded', function(e, faucet) {
        if (faucet && faucet.faucet_type) {
            $('#faucet-type').val(faucet.faucet_type).trigger('change');

            // Disable changing faucet type when editing
            $('#faucet-type').prop('disabled', true);
            $('#faucet-type').after('<p class="description">' +
                'Faucet type cannot be changed after creation.' +
                '</p>');
        }

        // Fix for faucet name field getting populated with "faucet buttons"
        if (faucet && faucet.name) {
            $('#faucet-name').val(faucet.name);
        }
    });

    // For dummy and withdrawal faucets, handle currency_id
    $(document).on('faucetLoaded', function(e, faucet) {
        if (faucet && faucet.currency_id) {
            $('#faucet-currency-id, #withdrawal-currency-id').val(faucet.currency_id);
        }

        if (faucet && faucet.min_withdrawal) {
            $('#min-withdrawal').val(faucet.min_withdrawal);
        }

        if (faucet && faucet.view_style) {
            $('#faucet-view-style').val(faucet.view_style);
        }

        // Handle available currencies for withdrawal faucets
        if (faucet && faucet.available_currencies) {
            try {
                var availableCurrencies = JSON.parse(faucet.available_currencies);
                if (Array.isArray(availableCurrencies)) {
                    // Uncheck all first
                    $('input[name="available_currencies[]"]').prop('checked', false);

                    // Check the ones that are available
                    availableCurrencies.forEach(function(currency) {
                        $('input[name="available_currencies[]"][value="' + currency + '"]').prop('checked', true);
                    });
                }
            } catch (e) {
                console.error('Error parsing available currencies:', e);
            }
        }
    });
});
