<?php

/**
 * Referral System for Farm Faucet
 *
 * @package Farmfaucet
 * @since 2.2
 */

// Security check
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class Farmfaucet_Referral
 *
 * Handles referral system functionality
 */
class Farmfaucet_Referral
{

    /**
     * Singleton instance
     *
     * @var Farmfaucet_Referral
     */
    private static $instance;

    /**
     * Initialize the class and set up hooks
     *
     * @return Farmfaucet_Referral
     */
    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);

        // Register shortcodes
        add_shortcode('farmfaucet_referral_link', [$this, 'render_referral_link_shortcode']);
        add_shortcode('farmfaucet_referral_stats', [$this, 'render_referral_stats_shortcode']);
        add_shortcode('farmfaucet_referral_leaderboard', [$this, 'render_referral_leaderboard_shortcode']);

        // Add referral tracking to claim process
        add_action('farmfaucet_before_claim', [$this, 'track_referral_visit']);
        add_action('farmfaucet_after_successful_claim', [$this, 'process_referral_reward'], 10, 2);

        // AJAX handlers for frontend
        add_action('wp_ajax_farmfaucet_get_referral_stats', [$this, 'ajax_get_referral_stats']);
        add_action('wp_ajax_nopriv_farmfaucet_get_referral_stats', [$this, 'ajax_get_referral_stats']);

        // AJAX handlers for admin
        add_action('wp_ajax_farmfaucet_update_referral_status', [$this, 'ajax_update_referral_status']);
        add_action('wp_ajax_farmfaucet_delete_referral', [$this, 'ajax_delete_referral']);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Create database tables on plugin activation
        add_action('farmfaucet_activate', [$this, 'create_tables']);
    }

    /**
     * Register settings for Referral System
     */
    public function register_settings()
    {
        // Referral system settings
        register_setting('farmfaucet_settings', 'farmfaucet_referral_enabled', [
            'sanitize_callback' => 'intval',
            'default' => 1
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_reward_type', [
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'percentage'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_reward_percentage', [
            'sanitize_callback' => 'floatval',
            'default' => 10.0
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_reward_fixed', [
            'sanitize_callback' => 'floatval',
            'default' => 0.0
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_currency_id', [
            'sanitize_callback' => 'intval',
            'default' => 0
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_levels', [
            'sanitize_callback' => 'intval',
            'default' => 1
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_level_percentages', [
            'sanitize_callback' => [$this, 'sanitize_level_percentages'],
            'default' => '10,5,2'
        ]);

        register_setting('farmfaucet_settings', 'farmfaucet_referral_min_withdrawal', [
            'sanitize_callback' => 'floatval',
            'default' => 1.0
        ]);
    }

    /**
     * Sanitize level percentages
     *
     * @param string $input Comma-separated percentages
     * @return string Sanitized comma-separated percentages
     */
    public function sanitize_level_percentages($input)
    {
        $percentages = explode(',', $input);
        $sanitized = [];

        foreach ($percentages as $percentage) {
            $sanitized[] = floatval(trim($percentage));
        }

        return implode(',', $sanitized);
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets()
    {
        wp_enqueue_style(
            'farmfaucet-referral',
            FARMFAUCET_URL . 'assets/css/referral.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-referral',
            FARMFAUCET_URL . 'assets/js/referral.js',
            ['jquery'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-referral', 'farmfaucetReferral', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-referral-nonce'),
            'i18n' => [
                'copied' => __('Copied to clipboard!', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook)
    {
        // Only load on Farm Faucet admin pages
        if (strpos($hook, 'farmfaucet') === false) {
            return;
        }

        wp_enqueue_style(
            'farmfaucet-referral-admin',
            FARMFAUCET_URL . 'assets/css/referral-admin.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-referral-admin',
            FARMFAUCET_URL . 'assets/js/referral-admin.js',
            ['jquery', 'jquery-ui-datepicker'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-referral-admin', 'farmfaucetReferralAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet-referral-admin-nonce'),
            'i18n' => [
                'confirmDelete' => __('Are you sure you want to delete this referral? This action cannot be undone.', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Create database tables
     */
    public function create_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Referrals table
        $referrals_table = $wpdb->prefix . 'farmfaucet_referrals';

        $sql = "CREATE TABLE IF NOT EXISTS $referrals_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            referrer_id bigint(20) NOT NULL,
            referred_id bigint(20) NOT NULL,
            level int(11) NOT NULL DEFAULT 1,
            status varchar(20) NOT NULL DEFAULT 'active',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY referred_id (referred_id),
            UNIQUE KEY referrer_referred (referrer_id, referred_id)
        ) $charset_collate;";

        // Referral visits table
        $visits_table = $wpdb->prefix . 'farmfaucet_referral_visits';

        $sql .= "CREATE TABLE IF NOT EXISTS $visits_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            referrer_id bigint(20) NOT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text NOT NULL,
            visited_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            converted tinyint(1) NOT NULL DEFAULT 0,
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY ip_address (ip_address),
            KEY converted (converted)
        ) $charset_collate;";

        // Referral earnings table
        $earnings_table = $wpdb->prefix . 'farmfaucet_referral_earnings';

        $sql .= "CREATE TABLE IF NOT EXISTS $earnings_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            referrer_id bigint(20) NOT NULL,
            referred_id bigint(20) NOT NULL,
            amount decimal(18,8) NOT NULL,
            currency_id bigint(20) DEFAULT NULL,
            claim_id bigint(20) DEFAULT NULL,
            level int(11) NOT NULL DEFAULT 1,
            status varchar(20) NOT NULL DEFAULT 'pending',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY referred_id (referred_id),
            KEY claim_id (claim_id),
            KEY status (status)
        ) $charset_collate;";

        // Execute SQL
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get referral by referrer and referred IDs
     *
     * @param int $referrer_id Referrer user ID
     * @param int $referred_id Referred user ID
     * @return array|null Referral data or null if not found
     */
    public function get_referral($referrer_id, $referred_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE referrer_id = %d AND referred_id = %d",
                $referrer_id,
                $referred_id
            ),
            ARRAY_A
        );

        return $result;
    }

    /**
     * Get referrals by referrer ID
     *
     * @param int $referrer_id Referrer user ID
     * @param string $status Filter by status (all, active, inactive)
     * @param int $limit Number of referrals to return
     * @param int $offset Offset for pagination
     * @return array Array of referrals
     */
    public function get_referrals_by_referrer($referrer_id, $status = 'all', $limit = 0, $offset = 0)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $sql = $wpdb->prepare("SELECT * FROM $table_name WHERE referrer_id = %d", $referrer_id);

        if ($status !== 'all') {
            $sql .= $wpdb->prepare(" AND status = %s", $status);
        }

        $sql .= " ORDER BY created_at DESC";

        if ($limit > 0) {
            $sql .= $wpdb->prepare(" LIMIT %d OFFSET %d", $limit, $offset);
        }

        $results = $wpdb->get_results($sql, ARRAY_A);

        return $results ?: [];
    }

    /**
     * Get referrer ID for a user
     *
     * @param int $user_id User ID
     * @return int|null Referrer ID or null if not found
     */
    public function get_referrer_id($user_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $result = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT referrer_id FROM $table_name WHERE referred_id = %d AND status = 'active' ORDER BY level ASC LIMIT 1",
                $user_id
            )
        );

        return $result ? intval($result) : null;
    }

    /**
     * Get referral chain for a user
     *
     * @param int $user_id User ID
     * @param int $max_levels Maximum number of levels to retrieve
     * @return array Array of referrer IDs by level
     */
    public function get_referral_chain($user_id, $max_levels = 3)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';
        $chain = [];
        $current_user_id = $user_id;

        for ($level = 1; $level <= $max_levels; $level++) {
            $referrer_id = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT referrer_id FROM $table_name WHERE referred_id = %d AND status = 'active' ORDER BY level ASC LIMIT 1",
                    $current_user_id
                )
            );

            if (!$referrer_id) {
                break;
            }

            $chain[$level] = intval($referrer_id);
            $current_user_id = $referrer_id;
        }

        return $chain;
    }

    /**
     * Create a new referral
     *
     * @param int $referrer_id Referrer user ID
     * @param int $referred_id Referred user ID
     * @param int $level Referral level
     * @return int|false Referral ID on success, false on failure
     */
    public function create_referral($referrer_id, $referred_id, $level = 1)
    {
        global $wpdb;

        // Check if users exist
        if (!get_userdata($referrer_id) || !get_userdata($referred_id)) {
            return false;
        }

        // Check if referral already exists
        if ($this->get_referral($referrer_id, $referred_id)) {
            return false;
        }

        // Insert referral
        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $result = $wpdb->insert(
            $table_name,
            [
                'referrer_id' => $referrer_id,
                'referred_id' => $referred_id,
                'level' => $level,
                'status' => 'active',
                'created_at' => current_time('mysql')
            ],
            [
                '%d', // referrer_id
                '%d', // referred_id
                '%d', // level
                '%s', // status
                '%s'  // created_at
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update referral status
     *
     * @param int $referrer_id Referrer user ID
     * @param int $referred_id Referred user ID
     * @param string $status New status (active, inactive)
     * @return bool True on success, false on failure
     */
    public function update_referral_status($referrer_id, $referred_id, $status)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $result = $wpdb->update(
            $table_name,
            ['status' => $status],
            [
                'referrer_id' => $referrer_id,
                'referred_id' => $referred_id
            ],
            ['%s'],
            ['%d', '%d']
        );

        return $result !== false;
    }

    /**
     * Delete a referral
     *
     * @param int $referrer_id Referrer user ID
     * @param int $referred_id Referred user ID
     * @return bool True on success, false on failure
     */
    public function delete_referral($referrer_id, $referred_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referrals';

        $result = $wpdb->delete(
            $table_name,
            [
                'referrer_id' => $referrer_id,
                'referred_id' => $referred_id
            ],
            ['%d', '%d']
        );

        return $result !== false;
    }

    /**
     * Track referral visit
     *
     * @param array $claim_data Claim data
     * @return void
     */
    public function track_referral_visit($claim_data)
    {
        // Check if referral system is enabled
        if (!get_option('farmfaucet_referral_enabled', 1)) {
            return;
        }

        // Check if referral code is in the cookie
        $referral_code = isset($_COOKIE['farmfaucet_referral']) ? sanitize_text_field($_COOKIE['farmfaucet_referral']) : '';

        if (empty($referral_code)) {
            return;
        }

        // Get referrer ID from code
        $referrer_id = $this->decode_referral_code($referral_code);

        if (!$referrer_id || !get_userdata($referrer_id)) {
            return;
        }

        // Get current user
        $current_user_id = get_current_user_id();

        // Don't track if user is the referrer
        if ($current_user_id && $current_user_id == $referrer_id) {
            return;
        }

        // Record visit
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referral_visits';

        $wpdb->insert(
            $table_name,
            [
                'referrer_id' => $referrer_id,
                'ip_address' => $this->get_user_ip(),
                'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
                'visited_at' => current_time('mysql'),
                'converted' => 0
            ],
            [
                '%d', // referrer_id
                '%s', // ip_address
                '%s', // user_agent
                '%s', // visited_at
                '%d'  // converted
            ]
        );
    }

    /**
     * Process referral reward after successful claim
     *
     * @param array $claim_data Claim data
     * @param int $user_id User ID
     * @return void
     */
    public function process_referral_reward($claim_data, $user_id)
    {
        // Check if referral system is enabled
        if (!get_option('farmfaucet_referral_enabled', 1)) {
            return;
        }

        // Get referral chain
        $max_levels = get_option('farmfaucet_referral_levels', 1);
        $referral_chain = $this->get_referral_chain($user_id, $max_levels);

        if (empty($referral_chain)) {
            // Check if referral code is in the cookie
            $referral_code = isset($_COOKIE['farmfaucet_referral']) ? sanitize_text_field($_COOKIE['farmfaucet_referral']) : '';

            if (empty($referral_code)) {
                return;
            }

            // Get referrer ID from code
            $referrer_id = $this->decode_referral_code($referral_code);

            if (!$referrer_id || !get_userdata($referrer_id)) {
                return;
            }

            // Don't process if user is the referrer
            if ($user_id == $referrer_id) {
                return;
            }

            // Create referral relationship
            $this->create_referral($referrer_id, $user_id);

            // Update referral chain
            $referral_chain = [$referrer_id];
        }

        // Get level percentages
        $level_percentages_str = get_option('farmfaucet_referral_level_percentages', '10,5,2');
        $level_percentages = explode(',', $level_percentages_str);

        // Get reward type
        $reward_type = get_option('farmfaucet_referral_reward_type', 'percentage');

        // Get claim amount
        $claim_amount = isset($claim_data['amount']) ? floatval($claim_data['amount']) : 0;

        // Get claim ID
        $claim_id = isset($claim_data['id']) ? intval($claim_data['id']) : 0;

        // Get currency ID
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);

        // Process rewards for each level
        foreach ($referral_chain as $level => $referrer_id) {
            // Skip if level exceeds available percentages
            if ($level > count($level_percentages)) {
                continue;
            }

            // Calculate reward amount
            $reward_amount = 0;

            if ($reward_type === 'percentage') {
                $percentage = isset($level_percentages[$level - 1]) ? floatval($level_percentages[$level - 1]) : 0;
                $reward_amount = $claim_amount * ($percentage / 100);
            } else {
                $reward_amount = get_option('farmfaucet_referral_reward_fixed', 0);
            }

            // Skip if reward amount is zero
            if ($reward_amount <= 0) {
                continue;
            }

            // Record earning
            $this->record_referral_earning($referrer_id, $user_id, $reward_amount, $currency_id, $claim_id, $level);

            // Add reward to user balance if Currency Maker is available
            if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
                $currency_maker = Farmfaucet_Currency_Maker::init();
                $currency_maker->update_user_currency_balance($referrer_id, $currency_id, $reward_amount);
            }
        }

        // Mark referral visit as converted
        $this->mark_referral_visit_converted($user_id);
    }

    /**
     * Record referral earning
     *
     * @param int $referrer_id Referrer user ID
     * @param int $referred_id Referred user ID
     * @param float $amount Earning amount
     * @param int $currency_id Currency ID
     * @param int $claim_id Claim ID
     * @param int $level Referral level
     * @return int|false Earning ID on success, false on failure
     */
    public function record_referral_earning($referrer_id, $referred_id, $amount, $currency_id, $claim_id, $level = 1)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referral_earnings';

        $result = $wpdb->insert(
            $table_name,
            [
                'referrer_id' => $referrer_id,
                'referred_id' => $referred_id,
                'amount' => $amount,
                'currency_id' => $currency_id,
                'claim_id' => $claim_id,
                'level' => $level,
                'status' => 'completed',
                'created_at' => current_time('mysql')
            ],
            [
                '%d', // referrer_id
                '%d', // referred_id
                '%f', // amount
                '%d', // currency_id
                '%d', // claim_id
                '%d', // level
                '%s', // status
                '%s'  // created_at
            ]
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Mark referral visit as converted
     *
     * @param int $user_id User ID
     * @return bool True on success, false on failure
     */
    public function mark_referral_visit_converted($user_id)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'farmfaucet_referral_visits';

        $ip_address = $this->get_user_ip();
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

        $result = $wpdb->update(
            $table_name,
            ['converted' => 1],
            [
                'ip_address' => $ip_address,
                'user_agent' => $user_agent,
                'converted' => 0
            ],
            ['%d'],
            ['%s', '%s', '%d']
        );

        return $result !== false;
    }

    /**
     * Get user IP address
     *
     * @return string User IP address
     */
    private function get_user_ip()
    {
        $ip = '';

        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }

    /**
     * Generate referral code for a user
     *
     * @param int $user_id User ID
     * @return string Referral code
     */
    public function generate_referral_code($user_id)
    {
        // Simple encoding: base64 of user ID with some salt
        $code = base64_encode($user_id . '|' . wp_salt('auth'));

        // Make it URL-safe
        $code = str_replace(['+', '/', '='], ['-', '_', ''], $code);

        return $code;
    }

    /**
     * Decode referral code to get user ID
     *
     * @param string $code Referral code
     * @return int|false User ID on success, false on failure
     */
    public function decode_referral_code($code)
    {
        // Restore base64 characters
        $code = str_replace(['-', '_'], ['+', '/'], $code);

        // Add padding if needed
        $code = str_pad($code, strlen($code) + (4 - strlen($code) % 4) % 4, '=');

        // Decode
        $decoded = base64_decode($code);

        if ($decoded === false) {
            return false;
        }

        // Extract user ID
        $parts = explode('|', $decoded);

        if (count($parts) !== 2) {
            return false;
        }

        return intval($parts[0]);
    }

    /**
     * Get referral statistics for a user
     *
     * @param int $user_id User ID
     * @return array Referral statistics
     */
    public function get_referral_stats($user_id)
    {
        global $wpdb;

        $referrals_table = $wpdb->prefix . 'farmfaucet_referrals';
        $earnings_table = $wpdb->prefix . 'farmfaucet_referral_earnings';
        $visits_table = $wpdb->prefix . 'farmfaucet_referral_visits';

        // Get total referrals
        $total_referrals = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $referrals_table WHERE referrer_id = %d",
                $user_id
            )
        );

        // Get active referrals
        $active_referrals = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $referrals_table WHERE referrer_id = %d AND status = 'active'",
                $user_id
            )
        );

        // Get total earnings
        $total_earnings = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT SUM(amount) FROM $earnings_table WHERE referrer_id = %d AND status = 'completed'",
                $user_id
            )
        );

        // Get total visits
        $total_visits = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $visits_table WHERE referrer_id = %d",
                $user_id
            )
        );

        // Get converted visits
        $converted_visits = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $visits_table WHERE referrer_id = %d AND converted = 1",
                $user_id
            )
        );

        // Calculate conversion rate
        $conversion_rate = $total_visits > 0 ? ($converted_visits / $total_visits) * 100 : 0;

        return [
            'total_referrals' => intval($total_referrals),
            'active_referrals' => intval($active_referrals),
            'total_earnings' => floatval($total_earnings ?: 0),
            'total_visits' => intval($total_visits),
            'converted_visits' => intval($converted_visits),
            'conversion_rate' => round($conversion_rate, 2)
        ];
    }

    /**
     * Get referral leaderboard
     *
     * @param int $limit Number of users to return
     * @param string $period Period to filter by (all, today, week, month)
     * @return array Leaderboard data
     */
    public function get_referral_leaderboard($limit = 10, $period = 'all')
    {
        global $wpdb;

        $earnings_table = $wpdb->prefix . 'farmfaucet_referral_earnings';

        $where = "WHERE status = 'completed'";

        // Add period filter
        if ($period !== 'all') {
            $date_condition = '';

            switch ($period) {
                case 'today':
                    $date_condition = "DATE(created_at) = CURDATE()";
                    break;
                case 'week':
                    $date_condition = "YEARWEEK(created_at) = YEARWEEK(CURDATE())";
                    break;
                case 'month':
                    $date_condition = "YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())";
                    break;
            }

            if ($date_condition) {
                $where .= " AND $date_condition";
            }
        }

        $sql = "SELECT referrer_id, SUM(amount) as total_earnings, COUNT(DISTINCT referred_id) as total_referrals
                FROM $earnings_table
                $where
                GROUP BY referrer_id
                ORDER BY total_earnings DESC
                LIMIT %d";

        $results = $wpdb->get_results($wpdb->prepare($sql, $limit), ARRAY_A);

        // Add user data
        foreach ($results as &$result) {
            $user = get_userdata($result['referrer_id']);

            if ($user) {
                $result['username'] = $user->user_login;
                $result['display_name'] = $user->display_name;
                $result['avatar'] = get_avatar_url($result['referrer_id'], ['size' => 50]);
            } else {
                $result['username'] = __('Unknown', 'farmfaucet');
                $result['display_name'] = __('Unknown', 'farmfaucet');
                $result['avatar'] = '';
            }
        }

        return $results ?: [];
    }

    /**
     * AJAX handler for getting referral statistics
     */
    public function ajax_get_referral_stats()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-referral-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Get user ID
        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error(['message' => __('You must be logged in to view referral statistics.', 'farmfaucet')]);
        }

        // Get referral stats
        $stats = $this->get_referral_stats($user_id);

        // Get referral code
        $referral_code = $this->generate_referral_code($user_id);

        // Get referral link
        $referral_link = add_query_arg('ref', $referral_code, home_url());

        // Get currency info if Currency Maker is available
        $currency_info = [];
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);

        if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currency = $currency_maker->get_currency($currency_id);

            if ($currency) {
                $currency_info = [
                    'id' => $currency['id'],
                    'name' => $currency['name'],
                    'symbol' => $currency['symbol'],
                    'icon' => $currency['icon_url']
                ];
            }
        }

        wp_send_json_success([
            'stats' => $stats,
            'referral_code' => $referral_code,
            'referral_link' => $referral_link,
            'currency' => $currency_info
        ]);
    }

    /**
     * AJAX handler for updating referral status
     */
    public function ajax_update_referral_status()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-referral-admin-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'farmfaucet')]);
        }

        // Get parameters
        $referrer_id = isset($_POST['referrer_id']) ? intval($_POST['referrer_id']) : 0;
        $referred_id = isset($_POST['referred_id']) ? intval($_POST['referred_id']) : 0;
        $status = isset($_POST['status']) ? sanitize_text_field($_POST['status']) : '';

        // Validate parameters
        if (!$referrer_id || !$referred_id || !in_array($status, ['active', 'inactive'])) {
            wp_send_json_error(['message' => __('Invalid parameters.', 'farmfaucet')]);
        }

        // Update referral status
        $result = $this->update_referral_status($referrer_id, $referred_id, $status);

        if ($result) {
            wp_send_json_success(['message' => __('Referral status updated successfully.', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to update referral status.', 'farmfaucet')]);
        }
    }

    /**
     * AJAX handler for deleting a referral
     */
    public function ajax_delete_referral()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet-referral-admin-nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'farmfaucet')]);
        }

        // Get parameters
        $referrer_id = isset($_POST['referrer_id']) ? intval($_POST['referrer_id']) : 0;
        $referred_id = isset($_POST['referred_id']) ? intval($_POST['referred_id']) : 0;

        // Validate parameters
        if (!$referrer_id || !$referred_id) {
            wp_send_json_error(['message' => __('Invalid parameters.', 'farmfaucet')]);
        }

        // Delete referral
        $result = $this->delete_referral($referrer_id, $referred_id);

        if ($result) {
            wp_send_json_success(['message' => __('Referral deleted successfully.', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete referral.', 'farmfaucet')]);
        }
    }

    /**
     * Render referral link shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_referral_link_shortcode($atts)
    {
        // Check if referral system is enabled
        if (!get_option('farmfaucet_referral_enabled', 1)) {
            return '<div class="farmfaucet-referral-notice">' . __('Referral system is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-referral-notice">' . __('You must be logged in to view your referral link.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'title' => __('Your Referral Link', 'farmfaucet'),
            'show_code' => 1,
            'show_stats' => 1,
            'button_text' => __('Copy Link', 'farmfaucet'),
            'button_copied_text' => __('Copied!', 'farmfaucet')
        ], $atts);

        // Get user ID
        $user_id = get_current_user_id();

        // Get referral code
        $referral_code = $this->generate_referral_code($user_id);

        // Get referral link
        $referral_link = add_query_arg('ref', $referral_code, home_url());

        // Get referral stats if needed
        $stats = $atts['show_stats'] ? $this->get_referral_stats($user_id) : null;

        // Get currency info if Currency Maker is available
        $currency_info = [];
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);

        if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currency = $currency_maker->get_currency($currency_id);

            if ($currency) {
                $currency_info = [
                    'id' => $currency['id'],
                    'name' => $currency['name'],
                    'symbol' => $currency['symbol'],
                    'icon' => $currency['icon_url']
                ];
            }
        }

        // Start output buffer
        ob_start();

        // Include template
        include(FARMFAUCET_DIR . 'templates/referral/referral-link.php');

        // Return buffered content
        return ob_get_clean();
    }

    /**
     * Render referral stats shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_referral_stats_shortcode($atts)
    {
        // Check if referral system is enabled
        if (!get_option('farmfaucet_referral_enabled', 1)) {
            return '<div class="farmfaucet-referral-notice">' . __('Referral system is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-referral-notice">' . __('You must be logged in to view your referral statistics.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'title' => __('Your Referral Statistics', 'farmfaucet'),
            'show_earnings' => 1,
            'show_referrals' => 1,
            'show_visits' => 1,
            'show_conversion' => 1,
            'layout' => 'grid', // grid, list
            'columns' => 2
        ], $atts);

        // Get user ID
        $user_id = get_current_user_id();

        // Get referral stats
        $stats = $this->get_referral_stats($user_id);

        // Get currency info if Currency Maker is available
        $currency_info = [];
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);

        if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currency = $currency_maker->get_currency($currency_id);

            if ($currency) {
                $currency_info = [
                    'id' => $currency['id'],
                    'name' => $currency['name'],
                    'symbol' => $currency['symbol'],
                    'icon' => $currency['icon_url']
                ];
            }
        }

        // Start output buffer
        ob_start();

        // Include template based on layout
        $layout = sanitize_key($atts['layout']);
        $template_file = FARMFAUCET_DIR . 'templates/referral/stats-' . $layout . '.php';

        // Fallback to grid layout if template doesn't exist
        if (!file_exists($template_file)) {
            $template_file = FARMFAUCET_DIR . 'templates/referral/stats-grid.php';
        }

        // Include template
        include($template_file);

        // Return buffered content
        return ob_get_clean();
    }

    /**
     * Render referral leaderboard shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_referral_leaderboard_shortcode($atts)
    {
        // Check if referral system is enabled
        if (!get_option('farmfaucet_referral_enabled', 1)) {
            return '<div class="farmfaucet-referral-notice">' . __('Referral system is currently disabled.', 'farmfaucet') . '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'title' => __('Referral Leaderboard', 'farmfaucet'),
            'limit' => 10,
            'period' => 'all', // all, today, week, month
            'show_avatar' => 1,
            'show_earnings' => 1,
            'show_referrals' => 1
        ], $atts);

        // Get leaderboard data
        $leaderboard = $this->get_referral_leaderboard($atts['limit'], $atts['period']);

        if (empty($leaderboard)) {
            return '<div class="farmfaucet-referral-notice">' . __('No referral data available.', 'farmfaucet') . '</div>';
        }

        // Get currency info if Currency Maker is available
        $currency_info = [];
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);

        if (class_exists('Farmfaucet_Currency_Maker') && $currency_id) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currency = $currency_maker->get_currency($currency_id);

            if ($currency) {
                $currency_info = [
                    'id' => $currency['id'],
                    'name' => $currency['name'],
                    'symbol' => $currency['symbol'],
                    'icon' => $currency['icon_url']
                ];
            }
        }

        // Start output buffer
        ob_start();

        // Include template
        include(FARMFAUCET_DIR . 'templates/referral/leaderboard.php');

        // Return buffered content
        return ob_get_clean();
    }

    /**
     * Render admin page
     */
    public static function render_admin_page()
    {
        // Get instance
        $instance = self::init();

        // Get current tab
        $current_tab = isset($_GET['tab']) ? sanitize_key($_GET['tab']) : 'settings';

        // Define tabs
        $tabs = [
            'settings' => __('Settings', 'farmfaucet'),
            'referrals' => __('Referrals', 'farmfaucet'),
            'earnings' => __('Earnings', 'farmfaucet'),
            'shortcodes' => __('Shortcodes', 'farmfaucet')
        ];

        // Start output buffer
        ob_start();

        // Render tabs
        echo '<div class="wrap farmfaucet-admin-wrap">';
        echo '<h1>' . esc_html__('Referral System', 'farmfaucet') . '</h1>';

        echo '<nav class="nav-tab-wrapper">';
        foreach ($tabs as $tab_id => $tab_name) {
            $active = $current_tab === $tab_id ? 'nav-tab-active' : '';
            echo '<a href="?page=farmfaucet&tab=referral&subtab=' . esc_attr($tab_id) . '" class="nav-tab ' . esc_attr($active) . '">' . esc_html($tab_name) . '</a>';
        }
        echo '</nav>';

        echo '<div class="tab-content">';

        // Render tab content
        switch ($current_tab) {
            case 'referrals':
                $instance->render_referrals_tab();
                break;
            case 'earnings':
                $instance->render_earnings_tab();
                break;
            case 'shortcodes':
                $instance->render_shortcodes_tab();
                break;
            case 'settings':
            default:
                $instance->render_settings_tab();
                break;
        }

        echo '</div>'; // .tab-content
        echo '</div>'; // .wrap

        // Return buffered content
        echo ob_get_clean();
    }

    /**
     * Render settings tab
     */
    private function render_settings_tab()
    {
        // Check if settings were updated
        $updated = isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true';

        // Get settings
        $enabled = get_option('farmfaucet_referral_enabled', 1);
        $reward_type = get_option('farmfaucet_referral_reward_type', 'percentage');
        $reward_percentage = get_option('farmfaucet_referral_reward_percentage', 10);
        $reward_fixed = get_option('farmfaucet_referral_reward_fixed', 0);
        $currency_id = get_option('farmfaucet_referral_currency_id', 0);
        $levels = get_option('farmfaucet_referral_levels', 1);
        $level_percentages = get_option('farmfaucet_referral_level_percentages', '10,5,2');
        $min_withdrawal = get_option('farmfaucet_referral_min_withdrawal', 1);

        // Get available currencies if Currency Maker is available
        $currencies = [];
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currencies = $currency_maker->get_currencies();
        }

        // Start output buffer
        ob_start();

        // Show update message
        if ($updated) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Settings updated successfully.', 'farmfaucet') . '</p></div>';
        }

        // Render settings form
?>
        <div class="referral-section">
            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php esc_html_e('Referral System Settings', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <form method="post" action="options.php">
                        <?php settings_fields('farmfaucet_settings'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Enable Referral System', 'farmfaucet'); ?></th>
                                <td>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="farmfaucet_referral_enabled" value="1" <?php checked($enabled, 1); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <p class="description"><?php esc_html_e('Enable or disable the referral system.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php esc_html_e('Reward Type', 'farmfaucet'); ?></th>
                                <td>
                                    <select name="farmfaucet_referral_reward_type" id="farmfaucet_referral_reward_type">
                                        <option value="percentage" <?php selected($reward_type, 'percentage'); ?>><?php esc_html_e('Percentage of Claim', 'farmfaucet'); ?></option>
                                        <option value="fixed" <?php selected($reward_type, 'fixed'); ?>><?php esc_html_e('Fixed Amount', 'farmfaucet'); ?></option>
                                    </select>
                                    <p class="description"><?php esc_html_e('Choose how referral rewards are calculated.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr class="reward-percentage-row">
                                <th scope="row"><?php esc_html_e('Reward Percentage', 'farmfaucet'); ?></th>
                                <td>
                                    <input type="number" name="farmfaucet_referral_reward_percentage" value="<?php echo esc_attr($reward_percentage); ?>" step="0.1" min="0" max="100">
                                    <p class="description"><?php esc_html_e('Percentage of claim amount to reward referrers.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr class="reward-fixed-row">
                                <th scope="row"><?php esc_html_e('Fixed Reward Amount', 'farmfaucet'); ?></th>
                                <td>
                                    <input type="number" name="farmfaucet_referral_reward_fixed" value="<?php echo esc_attr($reward_fixed); ?>" step="0.00000001" min="0">
                                    <p class="description"><?php esc_html_e('Fixed amount to reward referrers for each claim.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php esc_html_e('Reward Currency', 'farmfaucet'); ?></th>
                                <td>
                                    <select name="farmfaucet_referral_currency_id">
                                        <option value="0" <?php selected($currency_id, 0); ?>><?php esc_html_e('Same as Claim Currency', 'farmfaucet'); ?></option>
                                        <?php foreach ($currencies as $currency) : ?>
                                            <option value="<?php echo esc_attr($currency['id']); ?>" <?php selected($currency_id, $currency['id']); ?>>
                                                <?php echo esc_html($currency['name']); ?> (<?php echo esc_html($currency['symbol']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description"><?php esc_html_e('Choose which currency to use for referral rewards.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php esc_html_e('Referral Levels', 'farmfaucet'); ?></th>
                                <td>
                                    <input type="number" name="farmfaucet_referral_levels" id="farmfaucet_referral_levels" value="<?php echo esc_attr($levels); ?>" min="1" max="10">
                                    <p class="description"><?php esc_html_e('Number of referral levels to reward (multi-level marketing).', 'farmfaucet'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php esc_html_e('Level Percentages', 'farmfaucet'); ?></th>
                                <td>
                                    <input type="text" name="farmfaucet_referral_level_percentages" id="farmfaucet_referral_level_percentages" value="<?php echo esc_attr($level_percentages); ?>">
                                    <p class="description"><?php esc_html_e('Comma-separated list of percentages for each level (e.g., "10,5,2").', 'farmfaucet'); ?></p>
                                    <div id="level-percentages-preview"></div>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php esc_html_e('Minimum Withdrawal', 'farmfaucet'); ?></th>
                                <td>
                                    <input type="number" name="farmfaucet_referral_min_withdrawal" value="<?php echo esc_attr($min_withdrawal); ?>" step="0.00000001" min="0">
                                    <p class="description"><?php esc_html_e('Minimum amount required to withdraw referral earnings.', 'farmfaucet'); ?></p>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button(); ?>
                    </form>
                </div>
            </div>
        </div>
    <?php

        // Return buffered content
        echo ob_get_clean();
    }

    /**
     * Render earnings tab
     */
    private function render_earnings_tab()
    {
        // Get page and limit
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // Get filters
        $status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : 'all';
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        $date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
        $date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

        // Get earnings
        global $wpdb;
        $earnings_table = $wpdb->prefix . 'farmfaucet_referral_earnings';

        // Build query
        $sql = "SELECT e.*,
                u1.display_name as referrer_name,
                u2.display_name as referred_name
                FROM $earnings_table e
                LEFT JOIN {$wpdb->users} u1 ON e.referrer_id = u1.ID
                LEFT JOIN {$wpdb->users} u2 ON e.referred_id = u2.ID";

        $where = [];
        $query_args = [];

        if ($status !== 'all') {
            $where[] = "e.status = %s";
            $query_args[] = $status;
        }

        if (!empty($search)) {
            $where[] = "(u1.display_name LIKE %s OR u2.display_name LIKE %s)";
            $query_args[] = '%' . $wpdb->esc_like($search) . '%';
            $query_args[] = '%' . $wpdb->esc_like($search) . '%';
        }

        if (!empty($date_from)) {
            $where[] = "DATE(e.created_at) >= %s";
            $query_args[] = $date_from;
        }

        if (!empty($date_to)) {
            $where[] = "DATE(e.created_at) <= %s";
            $query_args[] = $date_to;
        }

        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }

        $sql .= " ORDER BY e.created_at DESC";
        $sql .= " LIMIT %d OFFSET %d";
        $query_args[] = $limit;
        $query_args[] = $offset;

        // Prepare and execute query
        $earnings = $wpdb->get_results($wpdb->prepare($sql, $query_args), ARRAY_A);

        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) FROM $earnings_table e";
        if (!empty($where)) {
            $count_sql .= " WHERE " . implode(' AND ', $where);
        }
        $total = $wpdb->get_var($wpdb->prepare($count_sql, array_slice($query_args, 0, -2)));

        // Calculate pagination
        $total_pages = ceil($total / $limit);

        // Get currency info if Currency Maker is available
        $currencies = [];
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $currencies = $currency_maker->get_currencies();
        }

        // Start output buffer
        ob_start();

        // Render earnings table
    ?>
        <div class="referral-section">
            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php esc_html_e('Referral Earnings', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <div class="referral-filters">
                        <form method="get" action="">
                            <input type="hidden" name="page" value="farmfaucet">
                            <input type="hidden" name="tab" value="referral">
                            <input type="hidden" name="subtab" value="earnings">

                            <div class="filter-group">
                                <label for="status" class="filter-label"><?php esc_html_e('Status:', 'farmfaucet'); ?></label>
                                <select name="status" id="status">
                                    <option value="all" <?php selected($status, 'all'); ?>><?php esc_html_e('All', 'farmfaucet'); ?></option>
                                    <option value="pending" <?php selected($status, 'pending'); ?>><?php esc_html_e('Pending', 'farmfaucet'); ?></option>
                                    <option value="completed" <?php selected($status, 'completed'); ?>><?php esc_html_e('Completed', 'farmfaucet'); ?></option>
                                    <option value="cancelled" <?php selected($status, 'cancelled'); ?>><?php esc_html_e('Cancelled', 'farmfaucet'); ?></option>
                                </select>

                                <label for="date_from" class="filter-label"><?php esc_html_e('From:', 'farmfaucet'); ?></label>
                                <input type="text" name="date_from" id="date_from" class="datepicker" value="<?php echo esc_attr($date_from); ?>" placeholder="<?php esc_attr_e('YYYY-MM-DD', 'farmfaucet'); ?>">

                                <label for="date_to" class="filter-label"><?php esc_html_e('To:', 'farmfaucet'); ?></label>
                                <input type="text" name="date_to" id="date_to" class="datepicker" value="<?php echo esc_attr($date_to); ?>" placeholder="<?php esc_attr_e('YYYY-MM-DD', 'farmfaucet'); ?>">

                                <label for="search" class="filter-label"><?php esc_html_e('Search:', 'farmfaucet'); ?></label>
                                <input type="text" name="search" id="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php esc_attr_e('Search users...', 'farmfaucet'); ?>">

                                <button type="submit" class="filter-button"><?php esc_html_e('Filter', 'farmfaucet'); ?></button>
                                <a href="?page=farmfaucet&tab=referral&subtab=earnings" class="reset-button"><?php esc_html_e('Reset', 'farmfaucet'); ?></a>
                            </div>
                        </form>
                    </div>

                    <?php if (empty($earnings)) : ?>
                        <div class="notice notice-info">
                            <p><?php esc_html_e('No earnings found.', 'farmfaucet'); ?></p>
                        </div>
                    <?php else : ?>
                        <table class="referrals-table">
                            <thead>
                                <tr>
                                    <th><?php esc_html_e('ID', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Referrer', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Referred', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Amount', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Level', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Status', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Date', 'farmfaucet'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($earnings as $earning) : ?>
                                    <tr>
                                        <td><?php echo esc_html($earning['id']); ?></td>
                                        <td>
                                            <div class="user-column">
                                                <?php echo get_avatar($earning['referrer_id'], 30, '', '', ['class' => 'user-avatar']); ?>
                                                <span><?php echo esc_html($earning['referrer_name']); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="user-column">
                                                <?php echo get_avatar($earning['referred_id'], 30, '', '', ['class' => 'user-avatar']); ?>
                                                <span><?php echo esc_html($earning['referred_name']); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $amount = floatval($earning['amount']);
                                            $currency_id = intval($earning['currency_id']);
                                            $currency_symbol = '';

                                            if ($currency_id && !empty($currencies)) {
                                                foreach ($currencies as $currency) {
                                                    if ($currency['id'] === $currency_id) {
                                                        $currency_symbol = $currency['symbol'];
                                                        break;
                                                    }
                                                }
                                            }

                                            echo esc_html($amount) . ' ' . esc_html($currency_symbol);
                                            ?>
                                        </td>
                                        <td><?php echo esc_html($earning['level']); ?></td>
                                        <td>
                                            <span class="status-<?php echo esc_attr($earning['status']); ?>">
                                                <?php echo esc_html(ucfirst($earning['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($earning['created_at']))); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                        <?php if ($total_pages > 1) : ?>
                            <div class="tablenav">
                                <div class="tablenav-pages">
                                    <span class="displaying-num">
                                        <?php printf(
                                            /* translators: %s: Number of items */
                                            _n('%s item', '%s items', $total, 'farmfaucet'),
                                            number_format_i18n($total)
                                        ); ?>
                                    </span>

                                    <span class="pagination-links">
                                        <?php
                                        // First page link
                                        if ($page > 1) {
                                            printf(
                                                '<a class="first-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', 1)),
                                                esc_html__('First page', 'farmfaucet'),
                                                '&laquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="first-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('First page', 'farmfaucet'),
                                                '&laquo;'
                                            );
                                        }

                                        // Previous page link
                                        if ($page > 1) {
                                            printf(
                                                '<a class="prev-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', max(1, $page - 1))),
                                                esc_html__('Previous page', 'farmfaucet'),
                                                '&lsaquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="prev-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Previous page', 'farmfaucet'),
                                                '&lsaquo;'
                                            );
                                        }

                                        // Current page
                                        printf(
                                            '<span class="paging-input"><span class="tablenav-paging-text">%s / <span class="total-pages">%s</span></span></span>',
                                            $page,
                                            $total_pages
                                        );

                                        // Next page link
                                        if ($page < $total_pages) {
                                            printf(
                                                '<a class="next-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', min($total_pages, $page + 1))),
                                                esc_html__('Next page', 'farmfaucet'),
                                                '&rsaquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="next-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Next page', 'farmfaucet'),
                                                '&rsaquo;'
                                            );
                                        }

                                        // Last page link
                                        if ($page < $total_pages) {
                                            printf(
                                                '<a class="last-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', $total_pages)),
                                                esc_html__('Last page', 'farmfaucet'),
                                                '&raquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="last-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Last page', 'farmfaucet'),
                                                '&raquo;'
                                            );
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php

        // Return buffered content
        echo ob_get_clean();
    }

    /**
     * Render shortcodes tab
     */
    private function render_shortcodes_tab()
    {
        // Start output buffer
        ob_start();

        // Render shortcodes documentation
    ?>
        <div class="referral-section">
            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php esc_html_e('Referral Shortcodes', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <p><?php esc_html_e('Use these shortcodes to display referral information on your website.', 'farmfaucet'); ?></p>

                    <div class="shortcode-group">
                        <h4><?php esc_html_e('Referral Link', 'farmfaucet'); ?></h4>
                        <div class="shortcode-item">
                            <code>[farmfaucet_referral_link]</code>
                            <span class="shortcode-description"><?php esc_html_e('Displays the user\'s referral link with a copy button.', 'farmfaucet'); ?></span>
                        </div>
                        <div class="shortcode-params">
                            <p><?php esc_html_e('Parameters:', 'farmfaucet'); ?></p>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title for the referral link section. Default: "Your Referral Link"', 'farmfaucet'); ?></li>
                                <li><code>show_code</code> - <?php esc_html_e('Whether to show the referral code. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_stats</code> - <?php esc_html_e('Whether to show referral statistics. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>button_text</code> - <?php esc_html_e('Text for the copy button. Default: "Copy Link"', 'farmfaucet'); ?></li>
                                <li><code>button_copied_text</code> - <?php esc_html_e('Text for the copy button after copying. Default: "Copied!"', 'farmfaucet'); ?></li>
                            </ul>
                            <p><?php esc_html_e('Example:', 'farmfaucet'); ?> <code>[farmfaucet_referral_link title="Share Your Link" show_stats="0"]</code></p>
                        </div>
                    </div>

                    <div class="shortcode-group">
                        <h4><?php esc_html_e('Referral Statistics', 'farmfaucet'); ?></h4>
                        <div class="shortcode-item">
                            <code>[farmfaucet_referral_stats]</code>
                            <span class="shortcode-description"><?php esc_html_e('Displays the user\'s referral statistics.', 'farmfaucet'); ?></span>
                        </div>
                        <div class="shortcode-params">
                            <p><?php esc_html_e('Parameters:', 'farmfaucet'); ?></p>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title for the statistics section. Default: "Your Referral Statistics"', 'farmfaucet'); ?></li>
                                <li><code>show_earnings</code> - <?php esc_html_e('Whether to show earnings. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_referrals</code> - <?php esc_html_e('Whether to show referral count. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_visits</code> - <?php esc_html_e('Whether to show visit count. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_conversion</code> - <?php esc_html_e('Whether to show conversion rate. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>layout</code> - <?php esc_html_e('Layout style. Options: "grid" or "list". Default: "grid"', 'farmfaucet'); ?></li>
                                <li><code>columns</code> - <?php esc_html_e('Number of columns for grid layout. Default: 2', 'farmfaucet'); ?></li>
                            </ul>
                            <p><?php esc_html_e('Example:', 'farmfaucet'); ?> <code>[farmfaucet_referral_stats layout="list" show_visits="0"]</code></p>
                        </div>
                    </div>

                    <div class="shortcode-group">
                        <h4><?php esc_html_e('Referral Leaderboard', 'farmfaucet'); ?></h4>
                        <div class="shortcode-item">
                            <code>[farmfaucet_referral_leaderboard]</code>
                            <span class="shortcode-description"><?php esc_html_e('Displays a leaderboard of top referrers.', 'farmfaucet'); ?></span>
                        </div>
                        <div class="shortcode-params">
                            <p><?php esc_html_e('Parameters:', 'farmfaucet'); ?></p>
                            <ul>
                                <li><code>title</code> - <?php esc_html_e('Title for the leaderboard. Default: "Referral Leaderboard"', 'farmfaucet'); ?></li>
                                <li><code>limit</code> - <?php esc_html_e('Number of users to display. Default: 10', 'farmfaucet'); ?></li>
                                <li><code>period</code> - <?php esc_html_e('Time period for the leaderboard. Options: "all", "today", "week", "month". Default: "all"', 'farmfaucet'); ?></li>
                                <li><code>show_avatar</code> - <?php esc_html_e('Whether to show user avatars. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_earnings</code> - <?php esc_html_e('Whether to show earnings. Default: 1 (yes)', 'farmfaucet'); ?></li>
                                <li><code>show_referrals</code> - <?php esc_html_e('Whether to show referral count. Default: 1 (yes)', 'farmfaucet'); ?></li>
                            </ul>
                            <p><?php esc_html_e('Example:', 'farmfaucet'); ?> <code>[farmfaucet_referral_leaderboard limit="5" period="month"]</code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php

        // Return buffered content
        echo ob_get_clean();
    }

    /**
     * Render referrals tab
     */
    private function render_referrals_tab()
    {
        // Get page and limit
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // Get filters
        $status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : 'all';
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

        // Get referrals
        global $wpdb;
        $referrals_table = $wpdb->prefix . 'farmfaucet_referrals';

        // Build query
        $sql = "SELECT r.*,
                u1.display_name as referrer_name,
                u2.display_name as referred_name
                FROM $referrals_table r
                LEFT JOIN {$wpdb->users} u1 ON r.referrer_id = u1.ID
                LEFT JOIN {$wpdb->users} u2 ON r.referred_id = u2.ID";

        $where = [];
        $query_args = [];

        if ($status !== 'all') {
            $where[] = "r.status = %s";
            $query_args[] = $status;
        }

        if (!empty($search)) {
            $where[] = "(u1.display_name LIKE %s OR u2.display_name LIKE %s)";
            $query_args[] = '%' . $wpdb->esc_like($search) . '%';
            $query_args[] = '%' . $wpdb->esc_like($search) . '%';
        }

        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }

        $sql .= " ORDER BY r.created_at DESC";
        $sql .= " LIMIT %d OFFSET %d";
        $query_args[] = $limit;
        $query_args[] = $offset;

        // Prepare and execute query
        $referrals = $wpdb->get_results($wpdb->prepare($sql, $query_args), ARRAY_A);

        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) FROM $referrals_table r";
        if (!empty($where)) {
            $count_sql .= " WHERE " . implode(' AND ', $where);
        }
        $total = $wpdb->get_var($wpdb->prepare($count_sql, array_slice($query_args, 0, -2)));

        // Calculate pagination
        $total_pages = ceil($total / $limit);

        // Start output buffer
        ob_start();

        // Render referrals table
    ?>
        <div class="referral-section">
            <div class="farmfaucet-admin-card">
                <div class="card-header">
                    <h3><?php esc_html_e('Manage Referrals', 'farmfaucet'); ?></h3>
                </div>
                <div class="card-body">
                    <div class="referral-filters">
                        <form method="get" action="">
                            <input type="hidden" name="page" value="farmfaucet">
                            <input type="hidden" name="tab" value="referral">
                            <input type="hidden" name="subtab" value="referrals">

                            <div class="filter-group">
                                <label for="status" class="filter-label"><?php esc_html_e('Status:', 'farmfaucet'); ?></label>
                                <select name="status" id="status">
                                    <option value="all" <?php selected($status, 'all'); ?>><?php esc_html_e('All', 'farmfaucet'); ?></option>
                                    <option value="active" <?php selected($status, 'active'); ?>><?php esc_html_e('Active', 'farmfaucet'); ?></option>
                                    <option value="inactive" <?php selected($status, 'inactive'); ?>><?php esc_html_e('Inactive', 'farmfaucet'); ?></option>
                                </select>

                                <label for="search" class="filter-label"><?php esc_html_e('Search:', 'farmfaucet'); ?></label>
                                <input type="text" name="search" id="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php esc_attr_e('Search users...', 'farmfaucet'); ?>">

                                <button type="submit" class="filter-button"><?php esc_html_e('Filter', 'farmfaucet'); ?></button>
                                <a href="?page=farmfaucet&tab=referral&subtab=referrals" class="reset-button"><?php esc_html_e('Reset', 'farmfaucet'); ?></a>
                            </div>
                        </form>
                    </div>

                    <?php if (empty($referrals)) : ?>
                        <div class="notice notice-info">
                            <p><?php esc_html_e('No referrals found.', 'farmfaucet'); ?></p>
                        </div>
                    <?php else : ?>
                        <table class="referrals-table">
                            <thead>
                                <tr>
                                    <th><?php esc_html_e('ID', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Referrer', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Referred', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Level', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Status', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Created', 'farmfaucet'); ?></th>
                                    <th><?php esc_html_e('Actions', 'farmfaucet'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($referrals as $referral) : ?>
                                    <tr>
                                        <td><?php echo esc_html($referral['id']); ?></td>
                                        <td>
                                            <div class="user-column">
                                                <?php echo get_avatar($referral['referrer_id'], 30, '', '', ['class' => 'user-avatar']); ?>
                                                <span><?php echo esc_html($referral['referrer_name']); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="user-column">
                                                <?php echo get_avatar($referral['referred_id'], 30, '', '', ['class' => 'user-avatar']); ?>
                                                <span><?php echo esc_html($referral['referred_name']); ?></span>
                                            </div>
                                        </td>
                                        <td><?php echo esc_html($referral['level']); ?></td>
                                        <td>
                                            <span class="status-<?php echo esc_attr($referral['status']); ?>">
                                                <?php echo esc_html(ucfirst($referral['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($referral['created_at']))); ?></td>
                                        <td>
                                            <div class="row-actions">
                                                <?php if ($referral['status'] === 'active') : ?>
                                                    <span class="deactivate">
                                                        <a href="#" class="deactivate-referral" data-referrer-id="<?php echo esc_attr($referral['referrer_id']); ?>" data-referred-id="<?php echo esc_attr($referral['referred_id']); ?>">
                                                            <?php esc_html_e('Deactivate', 'farmfaucet'); ?>
                                                        </a>
                                                    </span>
                                                <?php else : ?>
                                                    <span class="activate">
                                                        <a href="#" class="activate-referral" data-referrer-id="<?php echo esc_attr($referral['referrer_id']); ?>" data-referred-id="<?php echo esc_attr($referral['referred_id']); ?>">
                                                            <?php esc_html_e('Activate', 'farmfaucet'); ?>
                                                        </a>
                                                    </span>
                                                <?php endif; ?>
                                                <span class="delete">
                                                    <a href="#" class="delete-referral" data-referrer-id="<?php echo esc_attr($referral['referrer_id']); ?>" data-referred-id="<?php echo esc_attr($referral['referred_id']); ?>">
                                                        <?php esc_html_e('Delete', 'farmfaucet'); ?>
                                                    </a>
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                        <?php if ($total_pages > 1) : ?>
                            <div class="tablenav">
                                <div class="tablenav-pages">
                                    <span class="displaying-num">
                                        <?php printf(
                                            /* translators: %s: Number of items */
                                            _n('%s item', '%s items', $total, 'farmfaucet'),
                                            number_format_i18n($total)
                                        ); ?>
                                    </span>

                                    <span class="pagination-links">
                                        <?php
                                        // First page link
                                        if ($page > 1) {
                                            printf(
                                                '<a class="first-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', 1)),
                                                esc_html__('First page', 'farmfaucet'),
                                                '&laquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="first-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('First page', 'farmfaucet'),
                                                '&laquo;'
                                            );
                                        }

                                        // Previous page link
                                        if ($page > 1) {
                                            printf(
                                                '<a class="prev-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', max(1, $page - 1))),
                                                esc_html__('Previous page', 'farmfaucet'),
                                                '&lsaquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="prev-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Previous page', 'farmfaucet'),
                                                '&lsaquo;'
                                            );
                                        }

                                        // Current page
                                        printf(
                                            '<span class="paging-input"><span class="tablenav-paging-text">%s / <span class="total-pages">%s</span></span></span>',
                                            $page,
                                            $total_pages
                                        );

                                        // Next page link
                                        if ($page < $total_pages) {
                                            printf(
                                                '<a class="next-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', min($total_pages, $page + 1))),
                                                esc_html__('Next page', 'farmfaucet'),
                                                '&rsaquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="next-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Next page', 'farmfaucet'),
                                                '&rsaquo;'
                                            );
                                        }

                                        // Last page link
                                        if ($page < $total_pages) {
                                            printf(
                                                '<a class="last-page" href="%s"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></a>',
                                                esc_url(add_query_arg('paged', $total_pages)),
                                                esc_html__('Last page', 'farmfaucet'),
                                                '&raquo;'
                                            );
                                        } else {
                                            printf(
                                                '<span class="last-page disabled"><span class="screen-reader-text">%s</span><span aria-hidden="true">%s</span></span>',
                                                esc_html__('Last page', 'farmfaucet'),
                                                '&raquo;'
                                            );
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
<?php

        // Return buffered content
        echo ob_get_clean();
    }
}
