/**
 * Advertising System Admin CSS
 */

/* Admin Cards */
.farmfaucet-admin-card {
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.farmfaucet-admin-card .card-header {
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #eee;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
}

.farmfaucet-admin-card .card-body {
    padding: 20px;
}

/* Form Styles */
.farmfaucet-form .form-table th {
    width: 200px;
}

.farmfaucet-form .form-table td {
    vertical-align: top;
}

.farmfaucet-form .form-table input[type="text"],
.farmfaucet-form .form-table input[type="number"],
.farmfaucet-form .form-table input[type="url"] {
    width: 100%;
    max-width: 400px;
}

.farmfaucet-form .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Advertisements Table */
.farmfaucet-ads-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.farmfaucet-ads-table th,
.farmfaucet-ads-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.farmfaucet-ads-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.farmfaucet-ads-table tr:hover {
    background-color: #f9f9f9;
}

.farmfaucet-ads-table .row-actions {
    font-size: 12px;
    color: #666;
    visibility: hidden;
    margin-top: 5px;
}

.farmfaucet-ads-table tr:hover .row-actions {
    visibility: visible;
}

.farmfaucet-ads-table .row-actions a {
    text-decoration: none;
}

.farmfaucet-ads-table .row-actions .approve a {
    color: #4CAF50;
}

.farmfaucet-ads-table .row-actions .reject a,
.farmfaucet-ads-table .row-actions .delete a {
    color: #f44336;
}

.farmfaucet-ads-table .row-actions .edit a {
    color: #0088cc;
}

.farmfaucet-ads-table .status-active {
    color: #4CAF50;
    font-weight: bold;
}

.farmfaucet-ads-table .status-pending {
    color: #ff9800;
    font-weight: bold;
}

.farmfaucet-ads-table .status-rejected {
    color: #f44336;
    font-weight: bold;
}

.farmfaucet-no-ads {
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
    color: #666;
}

/* Shortcode Display */
.shortcode-group {
    margin-bottom: 30px;
}

.shortcode-group h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
}

.shortcode-item {
    margin-bottom: 10px;
}

.shortcode-item code {
    display: inline-block;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    margin-right: 10px;
}

.shortcode-description {
    color: #666;
}

.shortcode-params {
    margin-top: 10px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.shortcode-params p {
    margin-top: 0;
}

.shortcode-params ul {
    margin-bottom: 10px;
}

.shortcode-params li {
    margin-bottom: 5px;
}

/* Modal */
.farmfaucet-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.farmfaucet-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.farmfaucet-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.farmfaucet-modal-header h3 {
    margin: 0;
    font-size: 20px;
}

.farmfaucet-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.farmfaucet-modal-close:hover {
    color: #000;
}

.farmfaucet-modal-body {
    padding: 20px;
}

.farmfaucet-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.farmfaucet-modal-footer button {
    margin-left: 10px;
}

/* Ad Details */
#ad-details-container {
    padding: 15px;
}

#ad-details-container .ad-detail-item {
    margin-bottom: 15px;
}

#ad-details-container .ad-detail-label {
    font-weight: bold;
    margin-bottom: 5px;
}

#ad-details-container .ad-detail-value {
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

#ad-details-container .ad-detail-image {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin-top: 10px;
}

/* Responsive styles */
@media (max-width: 782px) {
    .farmfaucet-form .form-table th {
        width: 100%;
        display: block;
    }
    
    .farmfaucet-form .form-table td {
        display: block;
        padding-left: 0;
    }
}
