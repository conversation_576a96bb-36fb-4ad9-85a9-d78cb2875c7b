/**
 * Telegram <PERSON><PERSON>gin Frontend Styles
 */

/* Form container */
.farmfaucet-tg-login-form,
.farmfaucet-tg-signup-form,
.farmfaucet-tg-otp-form,
.farmfaucet-tg-login-link-message {
    max-width: 500px;
    margin: 0 auto 30px;
    padding: 25px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

/* Form title */
.farmfaucet-tg-form-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

/* Form fields */
.farmfaucet-tg-form-group {
    margin-bottom: 20px;
}

.farmfaucet-tg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.farmfaucet-tg-form-group input[type="text"],
.farmfaucet-tg-form-group input[type="email"],
.farmfaucet-tg-form-group input[type="password"],
.farmfaucet-tg-form-group input[type="tel"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.farmfaucet-tg-form-group input[type="text"]:focus,
.farmfaucet-tg-form-group input[type="email"]:focus,
.farmfaucet-tg-form-group input[type="password"]:focus,
.farmfaucet-tg-form-group input[type="tel"]:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Form buttons */
.farmfaucet-tg-form-button {
    display: inline-block;
    padding: 12px 24px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    width: 100%;
}

.farmfaucet-tg-form-button:hover {
    background-color: #45a049;
}

.farmfaucet-tg-form-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.4);
}

/* Form links */
.farmfaucet-tg-form-links {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
}

.farmfaucet-tg-form-links a {
    color: #4CAF50;
    text-decoration: none;
    margin: 0 10px;
}

.farmfaucet-tg-form-links a:hover {
    text-decoration: underline;
}

.farmfaucet-tg-form-links a.disabled {
    color: #999;
    cursor: not-allowed;
    pointer-events: none;
}

/* Form messages */
.form-error {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
    display: none;
}

.form-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
    display: none;
}

/* OTP form */
.farmfaucet-tg-otp-form {
    text-align: center;
}

.otp-message {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #e8f5e9;
    border-radius: 4px;
    color: #2e7d32;
}

.otp-timer {
    font-weight: bold;
    color: #2e7d32;
}

.otp-expired {
    color: #d32f2f;
    font-weight: bold;
}

.farmfaucet-tg-otp-input {
    font-size: 24px;
    letter-spacing: 5px;
    text-align: center;
    max-width: 200px;
    margin: 0 auto;
}

/* Logout button */
.farmfaucet-tg-logout-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    text-decoration: none;
}

.farmfaucet-tg-logout-button:hover {
    background-color: #d32f2f;
    text-decoration: none;
    color: white;
}

/* Login message */
.farmfaucet-tg-login-message {
    padding: 15px;
    background-color: #e8f5e9;
    border-radius: 4px;
    color: #2e7d32;
    margin-bottom: 20px;
    text-align: center;
}

/* Login link message */
.farmfaucet-tg-login-link-message {
    text-align: center;
}

.login-link-message {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #e8f5e9;
    border-radius: 4px;
    color: #2e7d32;
    line-height: 1.6;
}

.login-link-message p {
    margin-bottom: 10px;
}

.resend-link-container {
    margin-top: 20px;
}

.resend-link-container a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.resend-link-container a:hover {
    text-decoration: underline;
}

.resend-link-container a.disabled {
    color: #999;
    cursor: not-allowed;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .farmfaucet-tg-login-form,
    .farmfaucet-tg-signup-form,
    .farmfaucet-tg-otp-form,
    .farmfaucet-tg-login-link-message {
        padding: 20px 15px;
    }

    .farmfaucet-tg-form-title {
        font-size: 20px;
    }

    .farmfaucet-tg-form-group input[type="text"],
    .farmfaucet-tg-form-group input[type="email"],
    .farmfaucet-tg-form-group input[type="password"],
    .farmfaucet-tg-form-group input[type="tel"] {
        padding: 10px 12px;
        font-size: 14px;
    }

    .farmfaucet-tg-form-button {
        padding: 10px 20px;
        font-size: 14px;
    }
}
