/**
 * Form Style Fix for Farm Faucet
 *
 * This script fixes issues with form styling in the Farm Faucet plugin.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Form Style Fix loaded');
        initFormStyleFix();
    });

    /**
     * Initialize Form Style Fix
     */
    function initFormStyleFix() {
        console.log('Initializing Form Style Fix');

        // Fix for form background color
        fixFormBackgroundColor();

        // Fix for view style
        fixViewStyle();

        // Fix for dummy faucet amount display
        fixDummyFaucetAmount();

        // Add event listeners for changes
        addEventListeners();
    }

    /**
     * Fix form background color
     */
    function fixFormBackgroundColor() {
        // Apply form background color to all faucet forms
        $('.farmfaucet-container').each(function() {
            var $container = $(this);
            var formBgColor = $container.data('form-bg-color') || '#ffffff';
            var formTransparent = parseInt($container.data('form-transparent')) || 0;

            console.log('Container:', $container.attr('class'));
            console.log('Form BG Color:', formBgColor);
            console.log('Form Transparent:', formTransparent);

            // Apply form background color if not transparent
            if (formTransparent !== 1) {
                $container.find('.farmfaucet-form').css('background-color', formBgColor);
                $container.find('.farmfaucet-form').removeClass('form-transparent');

                // Also set the CSS variable for use in CSS
                $container.css('--form-bg-color', formBgColor);
            } else {
                $container.find('.farmfaucet-form').addClass('form-transparent');
                $container.find('.farmfaucet-form').css('background-color', 'transparent');

                // Also set the CSS variable for use in CSS
                $container.css('--form-bg-color', 'transparent');
            }

            // Apply styles to form elements
            if (formTransparent === 1) {
                $container.find('.farmfaucet-form input, .farmfaucet-form select, .farmfaucet-form textarea').addClass('transparent-input');
            } else {
                $container.find('.farmfaucet-form input, .farmfaucet-form select, .farmfaucet-form textarea').removeClass('transparent-input');
            }
        });
    }

    /**
     * Fix view style
     */
    function fixViewStyle() {
        // Apply view style to dummy faucets
        $('.farmfaucet-container.dummy-faucet').each(function() {
            var $container = $(this);
            var viewStyle = $container.data('view-style') || 'default';

            // Remove existing view style classes
            $container.removeClass('default-view card-view compact-view');

            // Add the appropriate view style class
            if (viewStyle === 'card') {
                $container.addClass('card-view');
            } else if (viewStyle === 'compact') {
                $container.addClass('compact-view');
            } else {
                $container.addClass('default-view');
            }
        });
    }

    /**
     * Fix dummy faucet amount display
     */
    function fixDummyFaucetAmount() {
        // Fix amount display in dummy faucets
        $('.farmfaucet-container.dummy-faucet').each(function() {
            var $container = $(this);
            var faucetId = $container.data('faucet-id');

            // Check if we have the necessary data already in the DOM
            var amount = $container.find('.amount').text();
            var currencySymbol = $container.find('.currency').text();

            // If we have the data, update the reward amount directly
            if (amount && currencySymbol) {
                $container.find('.reward-amount').text(amount + ' ' + currencySymbol);
            }

            // Add CSS classes for proper styling
            $container.find('.farmfaucet-form').addClass('dummy-faucet-form');
            $container.find('.reward-amount').addClass('dummy-faucet-amount');

            // Apply border color to the container
            var borderColor = $container.css('--border-color') || '#4CAF50';
            $container.css('border-color', borderColor);

            // Apply button color
            var buttonColor = $container.css('--button-color') || '#4CAF50';
            $container.find('.farmfaucet-claim-btn').css('background-color', buttonColor);
            $container.find('.farmfaucet-claim-btn').css('border-color', buttonColor);
        });
    }

    /**
     * Add event listeners for changes
     */
    function addEventListeners() {
        // Listen for form transparency changes
        $(document).on('change', '#faucet-form-transparent', function() {
            var isTransparent = $(this).is(':checked');

            if (isTransparent) {
                $('.farmfaucet-form').addClass('form-transparent');
            } else {
                $('.farmfaucet-form').removeClass('form-transparent');
            }
        });

        // Listen for view style changes
        $(document).on('change', '#faucet-view-style', function() {
            var viewStyle = $(this).val();

            // Update preview if available
            if ($('.farmfaucet-preview').length) {
                $('.farmfaucet-preview').removeClass('default-view card-view compact-view');

                if (viewStyle === 'card') {
                    $('.farmfaucet-preview').addClass('card-view');
                } else if (viewStyle === 'compact') {
                    $('.farmfaucet-preview').addClass('compact-view');
                } else {
                    $('.farmfaucet-preview').addClass('default-view');
                }
            }
        });
    }
})(jQuery);
