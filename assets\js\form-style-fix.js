/**
 * Farm Faucet Form Style Fix
 * 
 * This script ensures proper styling and functionality for all faucet forms
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Initialize form styling fixes
        initFormStyleFixes();
        
        // Initialize notification integration
        initNotificationIntegration();
        
        // Initialize cooldown timer fixes
        initCooldownTimerFixes();
        
    });

    /**
     * Initialize form styling fixes
     */
    function initFormStyleFixes() {
        
        // Apply CSS variables to all faucet containers
        $('.farmfaucet-container').each(function() {
            const $container = $(this);
            
            // Get data attributes for styling
            const buttonColor = $container.data('button-color') || '#4CAF50';
            const borderColor = $container.data('border-color') || '#4CAF50';
            const buttonBorderRadius = $container.data('button-border-radius') || '25px';
            const inputLabelColor = $container.data('input-label-color') || '#333333';
            const inputPlaceholderColor = $container.data('input-placeholder-color') || '#999999';
            const formBgColor = $container.data('form-bg-color') || '#ffffff';
            
            // Apply CSS variables
            $container.css({
                '--button-color': buttonColor,
                '--border-color': borderColor,
                '--button-border-radius': buttonBorderRadius,
                '--input-label-color': inputLabelColor,
                '--input-placeholder-color': inputPlaceholderColor,
                '--form-bg-color': formBgColor
            });
        });
        
        // Fix dropdown styling for transparent backgrounds
        $('.farmfaucet-container[data-form-transparent="1"] select').each(function() {
            const $select = $(this);
            
            // Ensure proper styling
            $select.css({
                'background-color': 'var(--form-bg-color, rgba(255, 255, 255, 0.1))',
                'border': '1px solid var(--border-color, rgba(255, 255, 255, 0.2))',
                'color': 'var(--input-label-color, #333333)'
            });
        });
        
        // Fix input placeholder colors
        $('.farmfaucet-container input').each(function() {
            const $input = $(this);
            const placeholderColor = $input.closest('.farmfaucet-container').css('--input-placeholder-color') || '#999999';
            
            // Apply placeholder color via CSS
            const style = `
                .farmfaucet-container input::placeholder {
                    color: ${placeholderColor} !important;
                }
            `;
            
            if (!$('#farmfaucet-placeholder-style').length) {
                $('<style id="farmfaucet-placeholder-style">' + style + '</style>').appendTo('head');
            }
        });
    }

    /**
     * Initialize notification integration
     */
    function initNotificationIntegration() {
        
        // Check if notification system is available
        if (!window.FarmfaucetNotifications) {
            console.log('FarmfaucetNotifications not available, using fallback notifications');
            return;
        }
        
        // Handle cooldown notifications
        $('.farmfaucet-cooldown-timer').each(function() {
            const $timer = $(this);
            const timeRemaining = $timer.text();
            
            if (timeRemaining && timeRemaining !== '00:00:00') {
                window.FarmfaucetNotifications.faucet.cooldownActive(timeRemaining);
            }
        });
        
        // Handle login required notifications
        $('.login-required').each(function() {
            window.FarmfaucetNotifications.faucet.loginRequired();
        });
        
        // Handle captcha required notifications
        $('.farmfaucet-captcha').each(function() {
            const $captcha = $(this);
            
            // Show captcha required notification when captcha becomes visible
            if ($captcha.is(':visible')) {
                window.FarmfaucetNotifications.faucet.captchaRequired();
            }
        });
    }

    /**
     * Initialize cooldown timer fixes
     */
    function initCooldownTimerFixes() {
        
        // Ensure cooldown timers work properly
        $('.farmfaucet-cooldown-timer').each(function() {
            const $timer = $(this);
            const $container = $timer.closest('.farmfaucet-container');
            const $claimBtn = $container.find('.farmfaucet-claim-btn');
            
            // If timer is active, disable the claim button
            if ($timer.is(':visible') && $timer.text() !== '00:00:00') {
                $claimBtn.prop('disabled', true).attr('aria-disabled', 'true');
            }
        });
        
        // Monitor for timer completion
        setInterval(function() {
            $('.farmfaucet-cooldown-timer').each(function() {
                const $timer = $(this);
                const $container = $timer.closest('.farmfaucet-container');
                const $claimBtn = $container.find('.farmfaucet-claim-btn');
                
                // If timer reaches 00:00:00, enable the claim button
                if ($timer.text() === '00:00:00') {
                    $claimBtn.prop('disabled', false).attr('aria-disabled', 'false');
                    $timer.hide();
                }
            });
        }, 1000);
    }

    /**
     * Handle form submission feedback
     */
    function handleFormSubmissionFeedback() {
        
        // Monitor form submissions
        $(document).on('submit', '.farmfaucet-form', function(e) {
            const $form = $(this);
            const $container = $form.closest('.farmfaucet-container');
            const faucetType = $container.data('faucet-type') || 'stage';
            
            // Show appropriate loading notification
            if (window.FarmfaucetNotifications) {
                switch (faucetType) {
                    case 'withdrawal':
                        window.FarmfaucetNotifications.faucet.withdrawalStarted();
                        break;
                    case 'conversion':
                        window.FarmfaucetNotifications.faucet.conversionStarted();
                        break;
                    case 'dummy':
                    case 'stage':
                    default:
                        window.FarmfaucetNotifications.faucet.claimStarted();
                        break;
                }
            }
        });
    }

    /**
     * Fix button border radius
     */
    function fixButtonBorderRadius() {
        $('.farmfaucet-claim-btn, .farmfaucet-button').each(function() {
            const $button = $(this);
            const $container = $button.closest('.farmfaucet-container');
            const buttonBorderRadius = $container.css('--button-border-radius') || '25px';
            
            $button.css('border-radius', buttonBorderRadius);
        });
    }

    // Initialize all fixes
    $(document).ready(function() {
        handleFormSubmissionFeedback();
        fixButtonBorderRadius();
        
        // Re-apply fixes when content changes
        $(document).on('DOMNodeInserted', function() {
            setTimeout(function() {
                initFormStyleFixes();
                fixButtonBorderRadius();
            }, 100);
        });
    });

})(jQuery);
