<?php

/**
 * Telegram <PERSON><PERSON> Login Shortcodes
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
if (!defined('MINUTE_IN_SECONDS')) {
    define('MINUTE_IN_SECONDS', 60);
}

/**
 * Class for handling Telegram Bot Login shortcodes
 */
class Farmfaucet_Tg_Bot_Login_Shortcodes
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        // Register shortcodes
        add_shortcode('farmfaucet_tg_login', [self::class, 'render_login_form']);
        add_shortcode('farmfaucet_tg_signup', [self::class, 'render_signup_form']);
        add_shortcode('farmfaucet_tg_logout', [self::class, 'render_logout_button']);

        // Register AJAX handlers
        add_action('wp_ajax_farmfaucet_tg_login', [self::class, 'process_login']);
        add_action('wp_ajax_nopriv_farmfaucet_tg_login', [self::class, 'process_login']);
        add_action('wp_ajax_farmfaucet_tg_signup', [self::class, 'process_signup']);
        add_action('wp_ajax_nopriv_farmfaucet_tg_signup', [self::class, 'process_signup']);
        add_action('wp_ajax_farmfaucet_tg_verify_otp', [self::class, 'verify_otp']);
        add_action('wp_ajax_nopriv_farmfaucet_tg_verify_otp', [self::class, 'verify_otp']);
        add_action('wp_ajax_farmfaucet_tg_resend_otp', [self::class, 'resend_otp']);
        add_action('wp_ajax_nopriv_farmfaucet_tg_resend_otp', [self::class, 'resend_otp']);

        // Handle auto-login from Telegram link
        add_action('init', [self::class, 'handle_auto_login']);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_scripts']);
    }

    /**
     * Enqueue scripts and styles for the frontend
     */
    public static function enqueue_scripts()
    {
        wp_enqueue_style(
            'farmfaucet-tg-login-css',
            FARMFAUCET_URL . 'assets/css/tg-bot-login-frontend.css',
            [],
            FARMFAUCET_VERSION
        );

        wp_enqueue_script(
            'farmfaucet-tg-login-js',
            FARMFAUCET_URL . 'assets/js/tg-bot-login-frontend.js',
            ['jquery'],
            FARMFAUCET_VERSION,
            true
        );

        wp_localize_script('farmfaucet-tg-login-js', 'farmfaucetTgLogin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_tg_login_nonce'),
            'loginRedirect' => get_permalink(get_option('farmfaucet_tg_login_redirect', '')),
            'signupRedirect' => get_permalink(get_option('farmfaucet_tg_signup_redirect', '')),
            'otpExpiration' => get_option('farmfaucet_tg_otp_expiration', 5) * 60, // Convert to seconds
            'i18n' => [
                'errorOccurred' => __('An error occurred. Please try again.', 'farmfaucet'),
                'invalidCredentials' => __('Invalid credentials. Please try again.', 'farmfaucet'),
                'invalidOtp' => __('Invalid OTP code. Please try again.', 'farmfaucet'),
                'otpExpired' => __('OTP code has expired. Please request a new one.', 'farmfaucet'),
                'userExists' => __('User already exists. Please login instead.', 'farmfaucet'),
                'telegramExists' => __('This Telegram number is already registered. Please use a different number.', 'farmfaucet'),
                'passwordMismatch' => __('Passwords do not match.', 'farmfaucet'),
                'requiredFields' => __('Please fill in all required fields.', 'farmfaucet'),
            ]
        ]);
    }

    /**
     * Render the login form
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public static function render_login_form($atts)
    {
        // If user is already logged in, show a message
        if (is_user_logged_in()) {
            return '<div class="farmfaucet-tg-login-message">' .
                __('You are already logged in.', 'farmfaucet') .
                '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'button_text' => __('Login with Telegram', 'farmfaucet'),
            'redirect' => '',
        ], $atts, 'farmfaucet_tg_login');

        // Start output buffering
        ob_start();

        // Include the login form template
        include(FARMFAUCET_DIR . 'templates/tg-bot-login-form.php');

        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Render the signup form
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public static function render_signup_form($atts)
    {
        // If user is already logged in, show a message
        if (is_user_logged_in()) {
            return '<div class="farmfaucet-tg-login-message">' .
                __('You are already logged in.', 'farmfaucet') .
                '</div>';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'button_text' => __('Sign Up with Telegram', 'farmfaucet'),
            'redirect' => '',
        ], $atts, 'farmfaucet_tg_signup');

        // Start output buffering
        ob_start();

        // Include the signup form template
        include(FARMFAUCET_DIR . 'templates/tg-bot-signup-form.php');

        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Render the logout button
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public static function render_logout_button($atts)
    {
        // If user is not logged in, show nothing
        if (!is_user_logged_in()) {
            return '';
        }

        // Parse attributes
        $atts = shortcode_atts([
            'button_text' => __('Logout', 'farmfaucet'),
            'redirect' => home_url(),
        ], $atts, 'farmfaucet_tg_logout');

        // Generate logout URL
        $logout_url = wp_logout_url($atts['redirect']);

        // Return the logout button
        return '<a href="' . esc_url($logout_url) . '" class="farmfaucet-tg-logout-button">' .
            esc_html($atts['button_text']) .
            '</a>';
    }

    /**
     * Process login form submission
     */
    public static function process_login()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_login_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Get form data
        $username_or_email = isset($_POST['username_or_email']) ? sanitize_text_field($_POST['username_or_email']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';

        // Validate required fields
        if (empty($username_or_email) || empty($password)) {
            wp_send_json_error(['message' => __('Please fill in all required fields', 'farmfaucet')]);
        }

        // Check if username_or_email is a Telegram number
        if (preg_match('/^\+?[0-9]{10,15}$/', $username_or_email)) {
            // Find user by Telegram number
            $user = self::get_user_by_telegram_number($username_or_email);

            if (!$user) {
                wp_send_json_error(['message' => __('Invalid credentials', 'farmfaucet')]);
            }

            $user_login = $user->user_login;
        } else {
            // Check if it's an email or username
            if (is_email($username_or_email)) {
                $user = get_user_by('email', $username_or_email);
            } else {
                $user = get_user_by('login', $username_or_email);
            }

            if (!$user) {
                wp_send_json_error(['message' => __('Invalid credentials', 'farmfaucet')]);
            }

            $user_login = $user->user_login;
        }

        // Attempt to log in
        $creds = [
            'user_login' => $user_login,
            'user_password' => $password,
            'remember' => true,
        ];

        $user = wp_signon($creds, false);

        if (is_wp_error($user)) {
            wp_send_json_error(['message' => __('Invalid credentials', 'farmfaucet')]);
        }

        // Login successful
        wp_send_json_success([
            'message' => __('Login successful', 'farmfaucet'),
            'redirect' => get_permalink(get_option('farmfaucet_tg_login_redirect', '')) ?: home_url(),
        ]);
    }

    /**
     * Process signup form submission
     */
    public static function process_signup()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_login_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Get form data
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $username = isset($_POST['username']) ? sanitize_user($_POST['username']) : '';
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        $telegram_number = isset($_POST['telegram_number']) ? sanitize_text_field($_POST['telegram_number']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

        // Validate required fields
        if (empty($name) || empty($username) || empty($email) || empty($telegram_number) || empty($password) || empty($confirm_password)) {
            wp_send_json_error(['message' => __('Please fill in all required fields', 'farmfaucet')]);
        }

        // Validate email
        if (!is_email($email)) {
            wp_send_json_error(['message' => __('Please enter a valid email address', 'farmfaucet')]);
        }

        // Validate telegram number
        if (!preg_match('/^\+?[0-9]{10,15}$/', $telegram_number)) {
            wp_send_json_error(['message' => __('Please enter a valid Telegram number', 'farmfaucet')]);
        }

        // Check if passwords match
        if ($password !== $confirm_password) {
            wp_send_json_error(['message' => __('Passwords do not match', 'farmfaucet')]);
        }

        // Check if username exists
        if (username_exists($username)) {
            wp_send_json_error(['message' => __('Username already exists. Please choose a different username.', 'farmfaucet')]);
        }

        // Check if email exists
        if (email_exists($email)) {
            wp_send_json_error(['message' => __('Email already exists. Please use a different email address.', 'farmfaucet')]);
        }

        // Check if Telegram number exists
        if (self::get_user_by_telegram_number($telegram_number)) {
            wp_send_json_error(['message' => __('This Telegram number is already registered. Please use a different number.', 'farmfaucet')]);
        }

        // Create user
        $user_id = wp_create_user($username, $password, $email);

        if (is_wp_error($user_id)) {
            wp_send_json_error(['message' => $user_id->get_error_message()]);
        }

        // Update user meta
        update_user_meta($user_id, 'first_name', $name);
        update_user_meta($user_id, 'telegram_number', $telegram_number);

        // Generate a unique token for one-time login
        $token = wp_generate_password(32, false);

        // Store token in transient
        $expiration = get_option('farmfaucet_tg_otp_expiration', 5) * MINUTE_IN_SECONDS;
        set_transient('farmfaucet_tg_login_token_' . $user_id, $token, $expiration);

        // Get selected bot for login
        $bot_id = get_option('farmfaucet_tg_login_bot', 0);

        if ($bot_id) {
            // Send login link to Telegram number
            self::send_login_link_to_telegram($telegram_number, $token, $user_id, $bot_id);
        }

        // Return success
        wp_send_json_success([
            'message' => __('Please check your Telegram for a login link', 'farmfaucet'),
            'user_id' => $user_id,
        ]);
    }

    /**
     * Verify OTP code
     */
    public static function verify_otp()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_login_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Get form data
        $otp_code = isset($_POST['otp_code']) ? sanitize_text_field($_POST['otp_code']) : '';
        $user_id = isset($_POST['user_id']) ? absint($_POST['user_id']) : 0;

        // Validate required fields
        if (empty($otp_code) || empty($user_id)) {
            wp_send_json_error(['message' => __('Please fill in all required fields', 'farmfaucet')]);
        }

        // Get stored OTP code
        $stored_otp = get_transient('farmfaucet_tg_otp_' . $user_id);

        if (!$stored_otp) {
            wp_send_json_error(['message' => __('OTP code has expired. Please request a new one.', 'farmfaucet')]);
        }

        // Verify OTP code
        if ($otp_code !== $stored_otp) {
            wp_send_json_error(['message' => __('Invalid OTP code. Please try again.', 'farmfaucet')]);
        }

        // Delete transient
        delete_transient('farmfaucet_tg_otp_' . $user_id);

        // Update user meta to mark as verified
        update_user_meta($user_id, 'telegram_verified', 1);

        // Log in the user
        $user = get_user_by('ID', $user_id);

        if ($user) {
            wp_set_auth_cookie($user_id, true);
            wp_set_current_user($user_id);
            do_action('wp_login', $user->user_login, $user);
        }

        // Return success
        wp_send_json_success([
            'message' => __('Verification successful', 'farmfaucet'),
            'redirect' => get_permalink(get_option('farmfaucet_tg_signup_redirect', '')) ?: home_url(),
        ]);
    }

    /**
     * Send login link to Telegram number
     *
     * @param string $telegram_number Telegram number
     * @param string $token One-time login token
     * @param int $bot_id Bot ID
     * @return bool True if sent, false otherwise
     */
    private static function send_login_link_to_telegram($telegram_number, $token, $user_id, $bot_id)
    {
        global $wpdb;

        // Get bot token
        $bot = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}farmfaucet_tg_bots WHERE id = %d",
            $bot_id
        ));

        if (!$bot) {
            return false;
        }

        // Decrypt bot token
        $bot_token = Farmfaucet_Security::decrypt_api_key($bot->bot_token);

        if (!$bot_token) {
            return false;
        }

        // Generate login URL
        $login_url = add_query_arg([
            'action' => 'farmfaucet_tg_auto_login',
            'token' => $token,
            'user_id' => $user_id
        ], home_url('/'));

        // Format message
        $message = sprintf(
            __('Click the link below to log in to your account. This link will expire in %d minutes and can only be used once: %s', 'farmfaucet'),
            get_option('farmfaucet_tg_otp_expiration', 5),
            $login_url
        );

        // Send message to Telegram
        $telegram_api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

        $response = wp_remote_post($telegram_api_url, [
            'body' => [
                'chat_id' => $telegram_number,
                'text' => $message,
                'parse_mode' => 'HTML',
            ],
            'timeout' => 15,
        ]);

        if (is_wp_error($response)) {
            error_log('Farmfaucet Telegram Login Link Error: ' . $response->get_error_message());
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['ok']) || !$data['ok']) {
            error_log('Farmfaucet Telegram Login Link Error: ' . (isset($data['description']) ? $data['description'] : 'Unknown error'));
            return false;
        }

        return true;
    }

    /**
     * Handle auto-login from Telegram link
     */
    public static function handle_auto_login()
    {
        // Check if this is an auto-login request
        if (
            isset($_GET['action']) && $_GET['action'] === 'farmfaucet_tg_auto_login' &&
            isset($_GET['token']) && isset($_GET['user_id'])
        ) {

            $token = sanitize_text_field($_GET['token']);
            $user_id = absint($_GET['user_id']);

            // Verify token
            $stored_token = get_transient('farmfaucet_tg_login_token_' . $user_id);

            if (!$stored_token || $stored_token !== $token) {
                // Invalid or expired token
                wp_die(__('Invalid or expired login link. Please request a new one.', 'farmfaucet'), __('Login Failed', 'farmfaucet'), [
                    'response' => 403,
                    'back_link' => true,
                ]);
            }

            // Delete the token to prevent reuse
            delete_transient('farmfaucet_tg_login_token_' . $user_id);

            // Mark user as verified
            update_user_meta($user_id, 'telegram_verified', 1);

            // Log the user in
            $user = get_user_by('ID', $user_id);

            if ($user) {
                wp_set_auth_cookie($user_id, true);
                wp_set_current_user($user_id);
                do_action('wp_login', $user->user_login, $user);

                // Redirect to dashboard or home page
                $redirect_url = get_permalink(get_option('farmfaucet_tg_signup_redirect', '')) ?: home_url();
                wp_redirect($redirect_url);
                exit;
            } else {
                // User not found
                wp_die(__('User not found. Please contact the administrator.', 'farmfaucet'), __('Login Failed', 'farmfaucet'), [
                    'response' => 404,
                    'back_link' => true,
                ]);
            }
        }
    }

    /**
     * Resend OTP code
     */
    public static function resend_otp()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_login_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Get user ID
        $user_id = isset($_POST['user_id']) ? absint($_POST['user_id']) : 0;

        if (empty($user_id)) {
            wp_send_json_error(['message' => __('Invalid user', 'farmfaucet')]);
        }

        // Get user
        $user = get_user_by('ID', $user_id);

        if (!$user) {
            wp_send_json_error(['message' => __('User not found', 'farmfaucet')]);
        }

        // Get telegram number
        $telegram_number = get_user_meta($user_id, 'telegram_number', true);

        if (empty($telegram_number)) {
            wp_send_json_error(['message' => __('Telegram number not found', 'farmfaucet')]);
        }

        // Check if OTP was recently sent (within the last minute)
        $last_otp_time = get_transient('farmfaucet_tg_last_otp_' . $user_id);

        if ($last_otp_time && (time() - $last_otp_time) < 60) {
            wp_send_json_error(['message' => __('Please wait before requesting a new code', 'farmfaucet')]);
        }

        // Generate a unique token for one-time login
        $token = wp_generate_password(32, false);

        // Store token in transient
        $expiration = get_option('farmfaucet_tg_otp_expiration', 5) * MINUTE_IN_SECONDS;
        set_transient('farmfaucet_tg_login_token_' . $user_id, $token, $expiration);

        // Store last login link time
        set_transient('farmfaucet_tg_last_otp_' . $user_id, time(), 3600); // 1 hour

        // Get selected bot for login
        $bot_id = get_option('farmfaucet_tg_login_bot', 0);

        if ($bot_id) {
            // Send login link to Telegram number
            self::send_login_link_to_telegram($telegram_number, $token, $user_id, $bot_id);
        }

        // Return success
        wp_send_json_success([
            'message' => __('Login link sent to your Telegram', 'farmfaucet'),
        ]);
    }

    /**
     * Get user by Telegram number
     *
     * @param string $telegram_number Telegram number
     * @return WP_User|false User object if found, false otherwise
     */
    private static function get_user_by_telegram_number($telegram_number)
    {
        // Sanitize the telegram number
        $telegram_number = sanitize_text_field($telegram_number);

        // Query users with this telegram number
        $users = get_users([
            'meta_key' => 'telegram_number',
            'meta_value' => $telegram_number,
            'number' => 1,
            'count_total' => false
        ]);

        if (empty($users)) {
            return false;
        }

        return $users[0];
    }
}
