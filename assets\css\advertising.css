/**
 * Advertising System Frontend CSS
 */

/* Common Styles */
.farmfaucet-ads-container {
    margin-bottom: 30px;
}

.farmfaucet-ads-info {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 20px;
    text-align: center;
}

.farmfaucet-ad-notice {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    font-style: italic;
    color: #666;
}

/* Grid Layout */
.farmfaucet-ads-grid {
    display: grid;
    grid-gap: 20px;
}

.farmfaucet-ads-grid.columns-1 {
    grid-template-columns: 1fr;
}

.farmfaucet-ads-grid.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.farmfaucet-ads-grid.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.farmfaucet-ads-grid.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.farmfaucet-ad-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.farmfaucet-ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.farmfaucet-ad-card .ad-image {
    height: 160px;
    overflow: hidden;
    background-color: #f5f5f5;
}

.farmfaucet-ad-card .ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.farmfaucet-ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.farmfaucet-ad-card .ad-content {
    padding: 15px;
}

.farmfaucet-ad-card .ad-title {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
}

.farmfaucet-ad-card .ad-title a {
    color: #333;
    text-decoration: none;
}

.farmfaucet-ad-card .ad-title a:hover {
    color: #0088cc;
}

.farmfaucet-ad-card .ad-description {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.farmfaucet-ad-card .ad-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.farmfaucet-ad-card .ad-votes {
    font-size: 14px;
    color: #666;
}

.farmfaucet-ad-card .votes-count {
    font-weight: bold;
    color: #333;
}

.farmfaucet-ad-card .ad-actions {
    display: flex;
    gap: 10px;
}

.farmfaucet-ad-card .ad-visit-button,
.farmfaucet-ad-card .ad-vote-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-ad-card .ad-visit-button {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.farmfaucet-ad-card .ad-visit-button:hover {
    background-color: #e5e5e5;
}

.farmfaucet-ad-card .ad-vote-button {
    background-color: #4CAF50;
    color: white;
    border: none;
}

.farmfaucet-ad-card .ad-vote-button:hover {
    background-color: #45a049;
}

.farmfaucet-ad-card .ad-voted {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

/* List Layout */
.farmfaucet-ads-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.farmfaucet-ad-item {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.farmfaucet-ad-item .ad-image {
    width: 120px;
    min-width: 120px;
    height: 120px;
    overflow: hidden;
    background-color: #f5f5f5;
}

.farmfaucet-ad-item .ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.farmfaucet-ad-item .ad-content {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.farmfaucet-ad-item .ad-title {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
}

.farmfaucet-ad-item .ad-description {
    flex: 1;
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.farmfaucet-ad-item .ad-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

/* Carousel Layout */
.farmfaucet-ads-carousel {
    position: relative;
    margin-bottom: 30px;
}

.carousel-container {
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.carousel-track {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-slide {
    min-width: 100%;
    box-sizing: border-box;
}

.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ccc;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.carousel-indicator.active {
    background-color: #4CAF50;
}

/* Create Advertisement Form */
.farmfaucet-ad-create-container {
    max-width: 800px;
    margin: 0 auto;
}

.farmfaucet-ad-create-container .form-title {
    margin-bottom: 20px;
    text-align: center;
}

.farmfaucet-ad-create-container .form-info {
    display: flex;
    justify-content: space-around;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.farmfaucet-form {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.farmfaucet-form .form-group {
    margin-bottom: 20px;
}

.farmfaucet-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.farmfaucet-form .required {
    color: #f44336;
}

.farmfaucet-form input[type="text"],
.farmfaucet-form input[type="url"],
.farmfaucet-form textarea,
.farmfaucet-form select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.farmfaucet-form .description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

.farmfaucet-form .form-actions {
    text-align: center;
    margin-top: 20px;
}

.farmfaucet-button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-button:hover {
    background-color: #45a049;
}

.farmfaucet-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.form-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

.form-message.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.form-message.error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.insufficient-balance {
    color: #f44336;
    font-weight: bold;
}

/* User Advertisements List */
.farmfaucet-user-ads-container {
    max-width: 1000px;
    margin: 0 auto;
}

.farmfaucet-user-ads-container .list-title {
    margin-bottom: 20px;
    text-align: center;
}

.farmfaucet-ads-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.farmfaucet-ads-table th,
.farmfaucet-ads-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.farmfaucet-ads-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.farmfaucet-ads-table tr:last-child td {
    border-bottom: none;
}

.farmfaucet-ads-table tr:hover {
    background-color: #f9f9f9;
}

.farmfaucet-ads-table .ad-status {
    font-weight: bold;
}

.farmfaucet-ads-table .status-pending {
    color: #ff9800;
}

.farmfaucet-ads-table .status-approved {
    color: #4CAF50;
}

.farmfaucet-ads-table .status-rejected {
    color: #f44336;
}

.farmfaucet-ads-table .ad-actions {
    display: flex;
    gap: 10px;
}

.farmfaucet-ads-table .ad-edit-button,
.farmfaucet-ads-table .ad-delete-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-ads-table .ad-edit-button {
    background-color: #0088cc;
    color: white;
    border: none;
}

.farmfaucet-ads-table .ad-edit-button:hover {
    background-color: #006699;
}

.farmfaucet-ads-table .ad-delete-button {
    background-color: #f44336;
    color: white;
    border: none;
}

.farmfaucet-ads-table .ad-delete-button:hover {
    background-color: #d32f2f;
}

/* Modal */
.farmfaucet-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.farmfaucet-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.farmfaucet-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.farmfaucet-modal-header h3 {
    margin: 0;
    font-size: 20px;
}

.farmfaucet-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.farmfaucet-modal-close:hover {
    color: #000;
}

.farmfaucet-modal-body {
    padding: 20px;
}

.farmfaucet-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.farmfaucet-modal-footer button {
    margin-left: 10px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .farmfaucet-ads-grid.columns-4,
    .farmfaucet-ads-grid.columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .farmfaucet-ads-grid.columns-4,
    .farmfaucet-ads-grid.columns-3,
    .farmfaucet-ads-grid.columns-2 {
        grid-template-columns: 1fr;
    }
    
    .farmfaucet-ad-item {
        flex-direction: column;
    }
    
    .farmfaucet-ad-item .ad-image {
        width: 100%;
        height: 160px;
    }
    
    .farmfaucet-ads-table {
        display: block;
        overflow-x: auto;
    }
    
    .farmfaucet-ad-create-container .form-info {
        flex-direction: column;
        gap: 10px;
    }
}
