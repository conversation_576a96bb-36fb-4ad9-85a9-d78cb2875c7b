/**
 * Referral System Frontend CSS
 */

/* Common Styles */
.farmfaucet-referral-notice {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    font-style: italic;
    color: #666;
    margin-bottom: 20px;
}

/* Referral Link */
.farmfaucet-referral-link-container {
    margin-bottom: 30px;
}

.farmfaucet-referral-link-container .referral-title {
    margin-bottom: 20px;
    text-align: center;
    font-size: 24px;
}

.referral-link-box {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.referral-link-input {
    display: flex;
    margin-bottom: 15px;
}

.referral-link-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
}

.referral-link-input .farmfaucet-button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.referral-link-input .farmfaucet-button:hover {
    background-color: #45a049;
}

.referral-link-input .farmfaucet-button.copied {
    background-color: #2196F3;
}

.referral-code {
    font-size: 14px;
    color: #666;
}

.referral-code-label {
    font-weight: bold;
    margin-right: 5px;
}

.referral-code-value {
    font-family: monospace;
    background-color: #f0f0f0;
    padding: 3px 6px;
    border-radius: 3px;
}

/* Referral Stats */
.referral-stats {
    margin-top: 30px;
}

.referral-stats h3 {
    margin-bottom: 15px;
    text-align: center;
    font-size: 20px;
}

.referral-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
}

.referral-stat-item {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.referral-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

/* Stats Grid Layout */
.farmfaucet-referral-stats-container {
    margin-bottom: 30px;
}

.farmfaucet-referral-stats-container .stats-title {
    margin-bottom: 20px;
    text-align: center;
    font-size: 24px;
}

.referral-stats-grid {
    display: grid;
    grid-gap: 20px;
}

.referral-stats-grid.columns-1 {
    grid-template-columns: 1fr;
}

.referral-stats-grid.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.referral-stats-grid.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.referral-stats-grid.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.referral-stats-grid .referral-stat-item {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.referral-stats-grid .stat-icon {
    margin-bottom: 15px;
}

.referral-stats-grid .stat-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: #4CAF50;
}

.referral-stats-grid .stat-content {
    flex: 1;
}

.referral-stats-grid .stat-footer {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

.referral-stats-grid .stat-active,
.referral-stats-grid .stat-converted {
    background-color: #e8f5e9;
    color: #4CAF50;
    padding: 3px 6px;
    border-radius: 3px;
}

.referral-stats-grid .stat-currency {
    background-color: #e3f2fd;
    color: #2196F3;
    padding: 3px 6px;
    border-radius: 3px;
}

.referral-stats-grid .conversion-bar {
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 5px;
}

.referral-stats-grid .conversion-progress {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 3px;
}

/* Stats List Layout */
.referral-stats-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.referral-stats-list .referral-stat-item {
    display: flex;
    align-items: center;
    text-align: left;
    padding: 15px;
}

.referral-stats-list .stat-icon {
    margin-right: 15px;
}

.referral-stats-list .stat-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #4CAF50;
}

.referral-stats-list .stat-content {
    flex: 1;
}

.referral-stats-list .stat-label {
    margin-bottom: 5px;
}

.referral-stats-list .stat-info {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* Leaderboard */
.farmfaucet-referral-leaderboard-container {
    margin-bottom: 30px;
}

.farmfaucet-referral-leaderboard-container .leaderboard-title {
    margin-bottom: 20px;
    text-align: center;
    font-size: 24px;
}

.leaderboard-period-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.leaderboard-period-tabs .period-tab {
    padding: 8px 15px;
    margin: 0 5px;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.leaderboard-period-tabs .period-tab:hover {
    background-color: #e0e0e0;
}

.leaderboard-period-tabs .period-tab.active {
    background-color: #4CAF50;
    color: white;
}

.referral-leaderboard {
    background-color: transparent;
    border-radius: 8px;
    overflow: hidden;
}

.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th,
.leaderboard-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.leaderboard-table th {
    background-color: transparent;
    font-weight: bold;
}

.leaderboard-table tr:last-child td {
    border-bottom: none;
}

.leaderboard-table tr:hover {
    background-color: transparent;
}

.leaderboard-table .rank-column {
    width: 80px;
    text-align: center;
}

.leaderboard-table .rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-color: #f0f0f0;
    color: #333;
    border-radius: 50%;
    font-weight: bold;
}

.leaderboard-table .top-1 .rank-badge {
    background-color: #ffd700;
    color: #333;
}

.leaderboard-table .top-2 .rank-badge {
    background-color: #c0c0c0;
    color: #333;
}

.leaderboard-table .top-3 .rank-badge {
    background-color: #cd7f32;
    color: #333;
}

.leaderboard-table .user-column {
    display: flex;
    align-items: center;
}

.leaderboard-table .user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.leaderboard-table .user-name {
    font-weight: bold;
}

.leaderboard-table .referrals-column,
.leaderboard-table .earnings-column {
    text-align: center;
}

.leaderboard-table .referrals-count,
.leaderboard-table .earnings-amount {
    font-weight: bold;
}

/* Responsive styles */
@media (max-width: 992px) {
    .referral-stats-grid.columns-4,
    .referral-stats-grid.columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .referral-link-input {
        flex-direction: column;
    }

    .referral-link-input input {
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .referral-link-input .farmfaucet-button {
        border-radius: 4px;
        width: 100%;
    }

    .referral-stats-grid.columns-4,
    .referral-stats-grid.columns-3,
    .referral-stats-grid.columns-2 {
        grid-template-columns: 1fr;
    }

    .leaderboard-period-tabs {
        flex-wrap: wrap;
    }

    .leaderboard-period-tabs .period-tab {
        margin-bottom: 10px;
    }

    .leaderboard-table th,
    .leaderboard-table td {
        padding: 10px;
    }

    .leaderboard-table .rank-column {
        width: 50px;
    }
}
