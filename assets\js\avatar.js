/**
 * Farm Faucet Avatar Functionality
 * 
 * Handles avatar display and upload functionality
 */
(function($) {
    $(document).ready(function() {
        // Initialize all editable avatars
        $('.farmfaucet-avatar-editable').each(function() {
            initEditableAvatar($(this));
        });
        
        /**
         * Initialize editable avatar
         * 
         * @param {jQuery} $container Avatar container
         */
        function initEditableAvatar($container) {
            var $img = $container.find('img');
            var $fileInput = $container.find('input[type="file"]');
            var $popup = $container.find('.farmfaucet-avatar-popup');
            var $successMessage = $container.find('.farmfaucet-avatar-success');
            var originalSrc = $img.attr('src');
            
            // Show popup when clicking on avatar
            $img.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Position popup relative to avatar
                var imgPos = $img.position();
                var imgWidth = $img.width();
                var imgHeight = $img.height();
                
                $popup.css({
                    top: imgPos.top + imgHeight + 10,
                    left: imgPos.left + (imgWidth / 2) - ($popup.width() / 2)
                }).fadeIn(200);
                
                // Close popup when clicking outside
                $(document).on('click.avatar-popup', function(e) {
                    if (!$(e.target).closest('.farmfaucet-avatar-popup').length && !$(e.target).closest('.farmfaucet-avatar-editable img').length) {
                        closePopup();
                    }
                });
            });
            
            // View image in larger size
            $popup.on('click', '.view-image', function(e) {
                e.preventDefault();
                
                // Create lightbox
                var $lightbox = $('<div class="farmfaucet-avatar-lightbox">' +
                    '<div class="lightbox-content">' +
                    '<img src="' + $img.attr('src') + '" alt="User Avatar">' +
                    '<button class="close-lightbox">&times;</button>' +
                    '</div>' +
                    '</div>');
                
                // Add lightbox to body
                $('body').append($lightbox);
                
                // Animate lightbox
                $lightbox.fadeIn(300);
                $lightbox.find('.lightbox-content').css({
                    transform: 'scale(0.8)',
                    opacity: 0
                }).animate({
                    transform: 'scale(1)',
                    opacity: 1
                }, 300);
                
                // Close lightbox when clicking close button or outside
                $lightbox.on('click', function(e) {
                    if ($(e.target).hasClass('farmfaucet-avatar-lightbox') || $(e.target).hasClass('close-lightbox')) {
                        $lightbox.fadeOut(300, function() {
                            $lightbox.remove();
                        });
                    }
                });
                
                // Close popup
                closePopup();
            });
            
            // Change picture
            $popup.on('click', '.change-picture', function(e) {
                e.preventDefault();
                $fileInput.click();
                closePopup();
            });
            
            // Handle file selection
            $fileInput.on('change', function() {
                var file = this.files[0];
                
                if (file) {
                    // Check file type
                    if (!file.type.match('image.*')) {
                        alert('Please select an image file');
                        return;
                    }
                    
                    // Check file size (max 2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('File size must be less than 2MB');
                        return;
                    }
                    
                    // Show preview
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $img.attr('src', e.target.result);
                        
                        // Show confirmation dialog after preview is loaded
                        setTimeout(function() {
                            if (confirm('Are you sure you want to use this image as your profile picture?')) {
                                // Upload the image
                                uploadAvatar(file, $container);
                            } else {
                                // Reset to original image if user cancels
                                $img.attr('src', originalSrc);
                            }
                        }, 500);
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            /**
             * Close popup
             */
            function closePopup() {
                $popup.fadeOut(200);
                $(document).off('click.avatar-popup');
            }
        }
        
        /**
         * Upload avatar
         * 
         * @param {File} file File to upload
         * @param {jQuery} $container Avatar container
         */
        function uploadAvatar(file, $container) {
            var $img = $container.find('img');
            var $successMessage = $container.find('.farmfaucet-avatar-success');
            var formData = new FormData();
            
            formData.append('action', 'farmfaucet_update_avatar');
            formData.append('nonce', farmfaucet_vars.avatar_nonce);
            formData.append('avatar', file);
            
            // Show loading state
            $container.addClass('uploading');
            
            // Send AJAX request
            $.ajax({
                url: farmfaucet_vars.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $container.removeClass('uploading');
                    
                    if (response.success) {
                        // Update all avatars on the page with the same user
                        $('.farmfaucet-avatar img, .farmfaucet-avatar-editable img').attr('src', response.data.avatar_url);
                        
                        // Show success message
                        $successMessage.fadeIn(300).delay(2000).fadeOut(300);
                    } else {
                        alert(response.data || 'Error updating avatar');
                        // Reset to original image
                        $img.attr('src', $img.data('original-src'));
                    }
                },
                error: function() {
                    $container.removeClass('uploading');
                    alert('Error communicating with server');
                    // Reset to original image
                    $img.attr('src', $img.data('original-src'));
                }
            });
        }
    });
})(jQuery);
