/**
 * Farm Faucet Admin Captcha Fix
 * 
 * This script fixes issues with captcha configuration in the admin panel.
 */
(function($) {
    'use strict';
    
    // Function to initialize captcha settings
    function initCaptchaSettings() {
        console.log('Initializing captcha settings');
        
        // Fix captcha type selection
        $('#farmfaucet-captcha-type').on('change', function() {
            var captchaType = $(this).val();
            console.log('Captcha type changed to:', captchaType);
            
            // Show the appropriate captcha configuration section
            $('.captcha-config-item').hide();
            $('#' + captchaType + '-config').show();
        });
        
        // Trigger change event to show the appropriate section
        $('#farmfaucet-captcha-type').trigger('change');
        
        // Fix captcha configuration in faucet form
        $('input[name="captcha_type"]').on('change', function() {
            var captchaType = $(this).val();
            console.log('Faucet captcha type changed to:', captchaType);
        });
    }
    
    // Function to fix faucet form captcha selection
    function fixFaucetFormCaptcha() {
        console.log('Fixing faucet form captcha selection');
        
        // Override the original editFaucet function if it exists
        if (typeof window.editFaucet === 'function') {
            var originalEditFaucet = window.editFaucet;
            
            window.editFaucet = function(faucetId) {
                // Call the original function
                originalEditFaucet(faucetId);
                
                // Fix captcha type selection
                $.ajax({
                    url: farmfaucet_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_get_faucet',
                        nonce: farmfaucet_admin.nonce,
                        faucet_id: faucetId
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            var faucet = response.data;
                            
                            // Set the captcha type
                            if (faucet.captcha_type) {
                                $('input[name="captcha_type"][value="' + faucet.captcha_type + '"]').prop('checked', true);
                            }
                        }
                    }
                });
            };
        }
        
        // Override the saveFaucet function if it exists
        if (typeof window.saveFaucet === 'function') {
            var originalSaveFaucet = window.saveFaucet;
            
            window.saveFaucet = function() {
                // Get the selected captcha type
                var captchaType = $('input[name="captcha_type"]:checked').val();
                console.log('Saving faucet with captcha type:', captchaType);
                
                // Call the original function
                originalSaveFaucet();
            };
        }
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Document ready, initializing admin captcha fix');
        
        // Initialize captcha settings
        initCaptchaSettings();
        
        // Fix faucet form captcha selection
        fixFaucetFormCaptcha();
    });
})(jQuery);
