<?php
/**
 * Farm Faucet Captcha Fix
 * 
 * This class handles the captcha configuration and ensures consistency
 * between different option names and captcha types.
 */
class Farmfaucet_Captcha_Fix
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        // Add hooks to normalize captcha option names
        add_filter('pre_update_option_farmfaucet_hcaptcha_site_key', [self::class, 'sync_hcaptcha_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_hcaptcha_sitekey', [self::class, 'sync_hcaptcha_site_key'], 10, 2);
        
        add_filter('pre_update_option_farmfaucet_recaptcha_site_key', [self::class, 'sync_recaptcha_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_recaptcha_sitekey', [self::class, 'sync_recaptcha_site_key'], 10, 2);
        
        add_filter('pre_update_option_farmfaucet_turnstile_site_key', [self::class, 'sync_turnstile_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_turnstile_sitekey', [self::class, 'sync_turnstile_site_key'], 10, 2);
        
        // Add hooks to normalize captcha secret option names
        add_filter('pre_update_option_farmfaucet_hcaptcha_secret', [self::class, 'sync_hcaptcha_secret_key'], 10, 2);
        add_filter('pre_update_option_farmfaucet_recaptcha_secret', [self::class, 'sync_recaptcha_secret_key'], 10, 2);
        add_filter('pre_update_option_farmfaucet_turnstile_secret', [self::class, 'sync_turnstile_secret_key'], 10, 2);
        
        // Add hooks to fix captcha display
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_captcha_fix_scripts']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_admin_captcha_fix_scripts']);
    }
    
    /**
     * Sync hCaptcha site key from site_key to sitekey
     */
    public static function sync_hcaptcha_sitekey($value, $old_value)
    {
        update_option('farmfaucet_hcaptcha_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync hCaptcha site key from sitekey to site_key
     */
    public static function sync_hcaptcha_site_key($value, $old_value)
    {
        update_option('farmfaucet_hcaptcha_site_key', $value);
        return $value;
    }
    
    /**
     * Sync reCAPTCHA site key from site_key to sitekey
     */
    public static function sync_recaptcha_sitekey($value, $old_value)
    {
        update_option('farmfaucet_recaptcha_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync reCAPTCHA site key from sitekey to site_key
     */
    public static function sync_recaptcha_site_key($value, $old_value)
    {
        update_option('farmfaucet_recaptcha_site_key', $value);
        return $value;
    }
    
    /**
     * Sync Turnstile site key from site_key to sitekey
     */
    public static function sync_turnstile_sitekey($value, $old_value)
    {
        update_option('farmfaucet_turnstile_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync Turnstile site key from sitekey to site_key
     */
    public static function sync_turnstile_site_key($value, $old_value)
    {
        update_option('farmfaucet_turnstile_site_key', $value);
        return $value;
    }
    
    /**
     * Sync hCaptcha secret key
     */
    public static function sync_hcaptcha_secret_key($value, $old_value)
    {
        return $value;
    }
    
    /**
     * Sync reCAPTCHA secret key
     */
    public static function sync_recaptcha_secret_key($value, $old_value)
    {
        return $value;
    }
    
    /**
     * Sync Turnstile secret key
     */
    public static function sync_turnstile_secret_key($value, $old_value)
    {
        return $value;
    }
    
    /**
     * Enqueue captcha fix scripts for frontend
     */
    public static function enqueue_captcha_fix_scripts()
    {
        wp_enqueue_script(
            'farmfaucet-captcha-fix',
            FARMFAUCET_URL . 'assets/js/captcha-fix.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(),
            true
        );
    }
    
    /**
     * Enqueue captcha fix scripts for admin
     */
    public static function enqueue_admin_captcha_fix_scripts()
    {
        wp_enqueue_script(
            'farmfaucet-admin-captcha-fix',
            FARMFAUCET_URL . 'assets/js/admin-captcha-fix.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(),
            true
        );
    }
    
    /**
     * Get captcha site key
     * 
     * @param string $captcha_type The captcha type (hcaptcha, recaptcha, turnstile)
     * @return string The captcha site key
     */
    public static function get_captcha_site_key($captcha_type)
    {
        switch ($captcha_type) {
            case 'hcaptcha':
                $site_key = get_option('farmfaucet_hcaptcha_sitekey', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_hcaptcha_site_key', '');
                }
                return $site_key;
                
            case 'recaptcha':
                $site_key = get_option('farmfaucet_recaptcha_sitekey', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_recaptcha_site_key', '');
                }
                return $site_key;
                
            case 'turnstile':
                $site_key = get_option('farmfaucet_turnstile_sitekey', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_turnstile_site_key', '');
                }
                return $site_key;
                
            default:
                return '';
        }
    }
}
