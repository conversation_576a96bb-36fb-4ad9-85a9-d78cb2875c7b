/**
 * Verificator JavaScript - Fixed Version
 */
(function($) {
    'use strict';

    // Global variables
    var activeVerificator = null;
    var verificatorInterval = null;
    var verificatorCompleted = false;

    // Initialize verificators
    function initVerificators() {
        if (typeof farmfaucet_verificator_data === 'undefined' || !farmfaucet_verificator_data.verificators) {
            console.log('Verificator data not found or invalid');
            return;
        }

        console.log('Initializing verificators:', farmfaucet_verificator_data.verificators);

        // Log debug info if available
        if (farmfaucet_verificator_data.debug) {
            console.log('Verificator debug info:', farmfaucet_verificator_data.debug);
        }

        // Check if verificator was already completed on this page
        verificatorCompleted = sessionStorage.getItem('farmfaucet_verificator_completed') === 'true';
        
        // Add a delay to ensure all elements are loaded
        setTimeout(function() {
            processVerificators();
        }, 1500);
    }

    // Process all verificators
    function processVerificators() {
        if (verificatorCompleted) {
            console.log('Verificator already completed on this page, skipping initialization');
            return;
        }

        $.each(farmfaucet_verificator_data.verificators, function(index, verificator) {
            try {
                setupVerificator(verificator);
            } catch (error) {
                console.error('Error setting up verificator:', error);
            }
        });
    }

    // Setup a single verificator
    function setupVerificator(verificator) {
        if (!verificator || !verificator.selector) {
            console.error('Invalid verificator data:', verificator);
            return;
        }

        console.log('Setting up verificator:', verificator.id, verificator.selector);

        // Try different selector variations to maximize compatibility
        var selector = verificator.selector;
        var selectorVariations = [
            selector,
            selector.trim(),
            selector.replace(/'/g, '"'),
            selector.replace(/"/g, "'")
        ];

        var elementFound = false;

        // Try each selector variation
        $.each(selectorVariations, function(i, currentSelector) {
            if (elementFound) return false; // Skip if already found

            try {
                var $elements = $(currentSelector);

                if ($elements.length > 0) {
                    console.log('Found elements for selector:', currentSelector, $elements.length);
                    elementFound = true;

                    // Add click handler to each element
                    $elements.on('click', function(e) {
                        console.log('Element clicked:', this);
                        e.preventDefault();
                        e.stopPropagation();

                        // Skip if verificator already completed
                        if (verificatorCompleted) {
                            console.log('Verificator already completed, ignoring click');
                            return;
                        }

                        // Store the original element for later use
                        verificator.originalElement = $(this);

                        // Disable the element during verification
                        $(this).addClass('farmfaucet-verificator-disabled');
                        $(this).css('pointer-events', 'none');

                        // Show verificator popup
                        showVerificator(verificator);
                    });
                }
            } catch (error) {
                console.error('Error with selector:', currentSelector, error);
            }
        });

        // If no elements found with jQuery, try document.querySelectorAll as a last resort
        if (!elementFound) {
            console.warn('No elements found for any variation of selector:', selector);

            try {
                var domElements = document.querySelectorAll(selector);
                if (domElements.length > 0) {
                    console.log('Found elements using document.querySelectorAll:', domElements.length);
                    elementFound = true;

                    // Convert to jQuery and add click handler
                    $(domElements).on('click', function(e) {
                        console.log('Element clicked (from querySelectorAll):', this);
                        e.preventDefault();
                        e.stopPropagation();

                        // Skip if verificator already completed
                        if (verificatorCompleted) {
                            console.log('Verificator already completed, ignoring click');
                            return;
                        }

                        // Store the original element for later use
                        verificator.originalElement = $(this);

                        // Disable the element during verification
                        $(this).addClass('farmfaucet-verificator-disabled');
                        $(this).css('pointer-events', 'none');

                        // Show verificator popup
                        showVerificator(verificator);
                    });
                }
            } catch (error) {
                console.error('Error with document.querySelectorAll:', error);
            }
        }

        if (!elementFound) {
            console.error('No elements found for any method with selector:', selector);
        }
    }

    // Show verificator popup
    function showVerificator(verificator) {
        if (activeVerificator !== null) {
            console.warn('Another verificator is already active');
            return;
        }

        activeVerificator = verificator;
        console.log('Showing verificator:', verificator);

        try {
            // Create overlay with opacity based on settings
            var overlayOpacity = verificator.overlay_opacity ? (verificator.overlay_opacity / 100) : 0.7;
            console.log('Overlay opacity:', overlayOpacity);

            var $overlay = $('<div class="farmfaucet-verificator-overlay"></div>');
            $overlay.css('background-color', 'rgba(0, 0, 0, ' + overlayOpacity + ')');

            // Create popup
            var $popup = $('<div class="farmfaucet-verificator-popup"></div>');
            $popup.css('border-radius', verificator.border_radius + 'px');
            $popup.css('background-color', verificator.background_color);

            // Set position based on settings
            var position = verificator.position || 'center';
            var margin = verificator.margin || 20;
            console.log('Position:', position, 'Margin:', margin);

            // Add position class
            $popup.addClass('position-' + position);

            // Set margin for corner positions
            if (position !== 'center') {
                if (position.includes('top')) {
                    $popup.css('top', margin + 'px');
                } else if (position.includes('bottom')) {
                    $popup.css('bottom', margin + 'px');
                }

                if (position.includes('left')) {
                    $popup.css('left', margin + 'px');
                } else if (position.includes('right')) {
                    $popup.css('right', margin + 'px');
                }
            }

            // Set animation style
            var style = verificator.style || 'fade';
            console.log('Animation style:', style);

            // Add animation class based on position and style
            if (style === 'slide' && position !== 'center') {
                $popup.addClass('position-' + position + ' animation-slide');
            } else {
                $popup.addClass('animation-' + style);
            }

            // Add text
            var $text = $('<div class="farmfaucet-verificator-text"></div>');
            $text.text(verificator.display_text);
            $popup.append($text);

            // Add progress container
            var $progressContainer = $('<div class="farmfaucet-verificator-progress-container"></div>');
            $progressContainer.css('border-radius', (verificator.border_radius / 2) + 'px');
            $popup.append($progressContainer);

            // Add progress bar
            var $progressBar = $('<div class="farmfaucet-verificator-progress-bar"></div>');
            $progressBar.css('background-color', verificator.bar_color);
            $progressContainer.append($progressBar);

            // Add popup to overlay
            $overlay.append($popup);

            // Add overlay to body
            $('body').append($overlay);

            console.log('Verificator popup added to DOM');

            // Start progress animation
            startProgress(verificator, $progressBar);
        } catch (error) {
            console.error('Error showing verificator:', error);
            // Reset active verificator in case of error
            activeVerificator = null;
        }
    }

    // Start progress animation
    function startProgress(verificator, $progressBar) {
        console.log('Starting progress animation, fill time:', verificator.fill_time);

        var progress = 0;
        var fillTime = verificator.fill_time * 1000; // Convert to milliseconds
        var interval = 50; // Update every 50ms
        var increment = (interval / fillTime) * 100;

        // Clear any existing interval
        if (verificatorInterval !== null) {
            clearInterval(verificatorInterval);
            verificatorInterval = null;
        }

        // Start interval
        verificatorInterval = setInterval(function() {
            progress += increment;
            $progressBar.css('width', progress + '%');

            // Check if complete
            if (progress >= 100) {
                console.log('Progress complete');
                clearInterval(verificatorInterval);
                verificatorInterval = null;
                showComplete(verificator);
            }
        }, interval);

        console.log('Progress interval started');
    }

    // Show completion animation
    function showComplete(verificator) {
        console.log('Showing completion animation');

        try {
            var $popup = $('.farmfaucet-verificator-popup');
            if ($popup.length === 0) {
                console.error('Popup element not found');
                return;
            }

            // Clear popup content
            $popup.empty();

            // Add complete content
            var $complete = $('<div class="farmfaucet-verificator-complete"></div>');
            var $check = $('<div class="farmfaucet-verificator-check"></div>');
            $check.css('background-color', verificator.bar_color);
            $complete.append($check);

            var $text = $('<div class="farmfaucet-verificator-text"></div>');
            $text.text(verificator.complete_text || 'Verification Complete');
            $complete.append($text);

            $popup.append($complete);

            console.log('Completion animation added to DOM');

            // Close after delay
            setTimeout(function() {
                console.log('Closing verificator after completion delay');
                closeVerificator();
            }, 1500);
        } catch (error) {
            console.error('Error showing completion:', error);
            // Try to close the verificator in case of error
            closeVerificator();
        }
    }

    // Close verificator
    function closeVerificator() {
        console.log('Closing verificator');

        try {
            var $overlay = $('.farmfaucet-verificator-overlay');
            if ($overlay.length === 0) {
                console.warn('Overlay element not found when closing');
                // Reset active verificator anyway
                activeVerificator = null;
                return;
            }

            // Add zoom out animation
            var $popup = $overlay.find('.farmfaucet-verificator-popup');
            if ($popup.length > 0) {
                $popup.css('animation', 'farmfaucet-verificator-zoom-out 0.5s ease forwards');
            } else {
                console.warn('Popup element not found when closing');
            }

            // Remove after animation
            setTimeout(function() {
                $overlay.remove();
                console.log('Overlay removed from DOM');

                // Re-enable the original element
                if (activeVerificator && activeVerificator.originalElement) {
                    console.log('Re-enabling original element');
                    activeVerificator.originalElement.removeClass('farmfaucet-verificator-disabled');
                    activeVerificator.originalElement.css('pointer-events', '');
                }

                // Reset active verificator
                activeVerificator = null;
                console.log('Verificator closed successfully');
            }, 500);
        } catch (error) {
            console.error('Error closing verificator:', error);

            // Force cleanup in case of error
            $('.farmfaucet-verificator-overlay').remove();

            // Re-enable the original element
            if (activeVerificator && activeVerificator.originalElement) {
                activeVerificator.originalElement.removeClass('farmfaucet-verificator-disabled');
                activeVerificator.originalElement.css('pointer-events', '');
            }

            // Reset active verificator
            activeVerificator = null;
        }
    }

    // Initialize on document ready
    $(document).ready(function() {
        console.log('Verificator script loaded - fixed version');
        initVerificators();
    });

})(jQuery);

