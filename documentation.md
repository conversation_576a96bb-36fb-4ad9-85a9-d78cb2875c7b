
# FarmFaucet Plugin Documentation

## Overview
FarmFaucet is an instant cryptocurrency faucet plugin for WordPress by ZpromoterZ. It allows users to claim crypto rewards (e.g., LTC or other currencies as set by the admin) directly to their FaucetPay account. The plugin includes several features such as:
- **hCaptcha Verification:** Ensures claims are made by real users.
- **FaucetPay API Integration:** Lets admins configure their FaucetPay API credentials.
- **Admin Panel Settings:** Configure hCaptcha keys, currency type, crypto amount, and cooldown timers.
- **Cooldown Timer:** Prevents repeated claims by enforcing a wait period.
- **Shortcode Support:** Embed the claim form on any page using a simple shortcode.
- **Modern UI Design:** The frontend claim form includes dynamic headers, custom styling, and notifications.

## File Structure




## File Structure
farmfaucet/ 
           ├── farmfaucet.php <-- Main plugin file (v2.1) 
           ├── uninstall.php <-- Uninstall script 
           ├── documentation.md <-- Plugin documentation 
           ├── readme.txt <-- Readme file 
           ├── includes/ │ 
                         ├── class-farmfaucet-security.php │ 
                         ├── class-farmfaucet-api.php │ 
                         ├── class-farmfaucet-admin.php │ 
                         ├── class-farmfaucet-frontend.php │ 
                         └── class-farmfaucet-logger.php 
           ├── languages/ │ 
                                      └── farmfaucet-en_US.mo 
           ├── templates/ │ 
                          └── claim-form.php 
           └── assets/
           ├── css/ │     
                    └── style.css 
           ├── images/ │ └── pickaxe-diamond.png └── js/ └── script.js



## Features
- **Instant Crypto Faucet:** Sends crypto rewards instantly to users' FaucetPay accounts.
- **hCaptcha Integration:** Uses hCaptcha to verify user authenticity.
- **Admin Configurable Options:** Set hCaptcha keys, FaucetPay API credentials, currency type, crypto amount, and cooldown timer.
- **User Notifications:** Dynamic headers and JavaScript notifications inform users of claim success.
- **Security Measures:** Input sanitization and nonce verification protect against XSS and CSRF.
- **Shortcode Implementation:** Easily embed the claim form on any page with the `[farmfaucet_claim]` shortcode.

## Installation
1. Upload the `farmfaucet` folder to your `/wp-content/plugins/` directory.
2. Activate the plugin via the WordPress admin dashboard.
3. Configure settings in the FarmFaucet admin panel.

## Uninstallation
When uninstalled, FarmFaucet removes all plugin options and custom data from your database.

## Author and Version
- **Author:** ZpromoterZ
- **Version:** 2.1



2. DOCUMENTATION.md (Technical Guide)
markdown
Copy
# Farmfaucet v2.0 Documentation

## Table of Contents
1. [Features Overview](#features)
2. [Installation Guide](#installation)
3. [Admin Configuration](#configuration)
4. [Security Protocols](#security)
5. [Troubleshooting](#troubleshooting)
6. [Version History](#history)
7. [Legal](#legal)

## <a name="features"></a>Key Features
- Instant Payouts: Direct to FaucetPay wallets
- Anti-Abuse Systems:
  - hCaptcha v2 verification
  - IP-based cooldown (client/server)
  - Transaction limits
- Customizable:
  - Payout amount (0.000001-10 crypto)
  - 20+ supported currencies
  - Cooldown (1-86400 seconds)
- Monitoring:
  - Error logging to /wp-content/farmfaucet-logs/
  - WordPress debug integration

## <a name="installation"></a>Installation
1. Requirements:
   - WordPress 5.6+
   - PHP 7.4+
   - SSL Certificate (HTTPS mandatory)

2. Steps:
   ```bash
   WordPress Admin → Plugins → Add New → Upload Plugin
Activate after upload

Configure under Farmfaucet admin menu

<a name="configuration"></a>Admin Settings
Field  Description  Default
hCaptcha Site Key  Public key for CAPTCHA  -
hCaptcha Secret Key  Private key (encrypted)  -
FaucetPay API  Merchant API key  -
Currency  LTC, BTC, DOGE etc.  LTC
Amount  Per-claim amount  0.001
Cooldown  Seconds between claims  3600
Shortcode Usage:

html
Copy
[farmfaucet]
Run HTML
<a name="security"></a>Security Measures
Data Protection:

AES-256 encryption for API keys

Nonce verification on all transactions

SQL injection prevention

Bot Protection:

hCaptcha score threshold: 0.7

IP rate limiting (10 claims/hour)

Browser fingerprinting

<a name="troubleshooting"></a>Troubleshooting
Issue  Solution
"Invalid API Key"  Regenerate FaucetPay API key
CAPTCHA not loading  Check domain whitelist in hCaptcha
Payout delay  Verify FaucetPay account balance
Cooldown errors  Clear browser localStorage
<a name="history"></a>Version History
Version  Date  Changes
2.0  2024-03-15  Added logging, enhanced security
1.1  2024-02-28  Mobile UI fixes
1.0  2024-01-10  Initial release
<a name="legal"></a>Legal Compliance
License: Proprietary (All rights reserved)

Redistribution: Requires written consent

Modifications: Forbidden without authorization

Liability: Not responsible for crypto losses

GDPR: Stores only transaction hashes

Copyright Notice:
© 2024 ZpromoterZ. This software is protected under international copyright treaties. Unauthorized distribution may result in legal action under DMCA and EUCD.

---

### 3. License Agreement
Create LICENSE.txt in plugin root:
```txt
FARMFAUCET END USER LICENSE AGREEMENT

1. GRANT OF LICENSE
ZpromoterZ grants you a non-exclusive, non-transferable right to use one copy of Farmfaucet on a single WordPress installation.

2. RESTRICTIONS
You may NOT:
- Redistribute, resell, or lease the code
- Remove copyright notices
- Use in cryptocurrency mixing services
- Decompile/reverse-engineer the software

3. TERMINATION
Violation of terms results in automatic termination of rights. Must delete all copies immediately.

4. DISCLAIMER
No warranty provided. Use at your own risk. Not responsible for lost funds or API key leaks.

Full EULA: https://example.com/farmfaucet-eula