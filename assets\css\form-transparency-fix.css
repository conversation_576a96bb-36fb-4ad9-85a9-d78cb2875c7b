/**
 * Form Transparency Fix
 * 
 * This CSS file fixes the transparency issues with forms and input fields.
 * It allows for complete transparency of form backgrounds and input fields.
 */

/* Make form backgrounds transparent when needed */
.farmfaucet-form.transparent-bg {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Make card backgrounds transparent when needed */
.farmfaucet-card.transparent-bg {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Make input fields transparent when needed */
.farmfaucet-form.transparent-inputs input[type="text"],
.farmfaucet-form.transparent-inputs input[type="email"],
.farmfaucet-form.transparent-inputs input[type="number"],
.farmfaucet-form.transparent-inputs input[type="password"],
.farmfaucet-form.transparent-inputs input[type="url"],
.farmfaucet-form.transparent-inputs input[type="tel"],
.farmfaucet-form.transparent-inputs textarea,
.farmfaucet-form.transparent-inputs select {
    background-color: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: inherit !important;
}

/* Make buttons transparent when needed */
.farmfaucet-form.transparent-buttons .farmfaucet-button,
.farmfaucet-form.transparent-buttons button {
    background-color: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: inherit !important;
}

/* Make all form elements transparent */
.farmfaucet-form.fully-transparent * {
    background-color: transparent !important;
}

/* Hide all card boxes when transparency is enabled */
.transparent-background .farmfaucet-card,
.transparent-background .farmfaucet-form-card,
.transparent-background .farmfaucet-content-card {
    display: none !important;
}

/* Specific fix for faucet forms */
.farmfaucet-faucet-form.transparent-bg {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Specific fix for faucet form inputs */
.farmfaucet-faucet-form.transparent-inputs input,
.farmfaucet-faucet-form.transparent-inputs select,
.farmfaucet-faucet-form.transparent-inputs textarea {
    background-color: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: inherit !important;
}

/* Specific fix for faucet form buttons */
.farmfaucet-faucet-form.transparent-buttons button,
.farmfaucet-faucet-form.transparent-buttons .farmfaucet-button {
    background-color: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: inherit !important;
}

/* Specific fix for currency maker form */
#farmfaucet-currency-modal .farmfaucet-modal-content {
    background-color: #fff;
}

/* Ensure color picker is visible */
.wp-picker-container,
.wp-picker-container * {
    background-color: #fff !important;
}

/* Ensure the color picker input is visible */
.wp-picker-container input[type="text"] {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
}

/* Fix for the color picker button */
.wp-color-result {
    background-color: #f7f7f7 !important;
    border: 1px solid #ccc !important;
}

/* Fix for the color picker dropdown */
.wp-picker-holder {
    background-color: #fff !important;
}

/* Fix for the iris picker */
.iris-picker {
    background-color: #fff !important;
    border: 1px solid #ccc !important;
}

/* Fix for the iris picker inputs */
.iris-picker input[type="text"] {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
}

/* Fix for the currency maker modal */
#farmfaucet-currency-modal {
    background-color: rgba(0, 0, 0, 0.5);
}

#farmfaucet-currency-modal .farmfaucet-modal-content {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

#farmfaucet-currency-modal .farmfaucet-modal-header {
    background-color: #4CAF50;
    color: white;
}

#farmfaucet-currency-modal .farmfaucet-modal-footer {
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

/* Fix for the currency maker form inputs */
#farmfaucet-currency-form input[type="text"],
#farmfaucet-currency-form input[type="number"],
#farmfaucet-currency-form input[type="url"],
#farmfaucet-currency-form select {
    background-color: #fff;
    border: 1px solid #ddd;
    color: #333;
    padding: 8px;
    border-radius: 4px;
    width: 100%;
}

/* Fix for the currency maker form toggle switch */
#farmfaucet-currency-form .toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

#farmfaucet-currency-form .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

#farmfaucet-currency-form .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

#farmfaucet-currency-form .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

#farmfaucet-currency-form input:checked + .toggle-slider {
    background-color: #4CAF50;
}

#farmfaucet-currency-form input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
