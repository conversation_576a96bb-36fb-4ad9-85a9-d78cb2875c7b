<?php
/**
 * Farm Faucet - Test Settings Save Functionality
 * 
 * This script tests the settings save functionality and identifies any issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Test Settings Save</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .step { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        .step h3 { margin-top: 0; color: #333; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; }
        .btn:hover { background: #45a049; }
        .test-form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Test Settings Save</h1>
    <p>This script will test the settings save functionality and identify any issues.</p>

<?php

// Test 1: Check if settings are registered
echo '<div class="step">';
echo '<h3>Step 1: Settings Registration Test</h3>';

$registered_settings = get_registered_settings();
$farmfaucet_settings = [];

foreach ($registered_settings as $setting => $args) {
    if (strpos($setting, 'farmfaucet_') === 0) {
        $farmfaucet_settings[$setting] = $args;
    }
}

if (!empty($farmfaucet_settings)) {
    echo '<div class="success"><p>✅ Found ' . count($farmfaucet_settings) . ' registered Farm Faucet settings</p></div>';
    
    echo '<table>';
    echo '<tr><th>Setting Name</th><th>Sanitize Callback</th><th>Current Value</th></tr>';
    
    foreach ($farmfaucet_settings as $setting => $args) {
        $current_value = get_option($setting, 'NOT SET');
        $callback = isset($args['sanitize_callback']) ? 
            (is_array($args['sanitize_callback']) ? 
                implode('::', $args['sanitize_callback']) : 
                $args['sanitize_callback']) : 
            'None';
        
        echo '<tr>';
        echo '<td>' . esc_html($setting) . '</td>';
        echo '<td>' . esc_html($callback) . '</td>';
        echo '<td>' . esc_html(is_string($current_value) ? $current_value : json_encode($current_value)) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<div class="error"><p>❌ No Farm Faucet settings found! This indicates a registration issue.</p></div>';
}

echo '</div>';

// Test 2: Test individual setting save
echo '<div class="step">';
echo '<h3>Step 2: Individual Setting Save Test</h3>';

if (isset($_POST['test_setting_save'])) {
    $test_value = sanitize_text_field($_POST['test_redirect_url']);
    
    echo '<div class="info"><p>Testing save with value: ' . esc_html($test_value) . '</p></div>';
    
    // Try to update the setting
    $result = update_option('farmfaucet_redirect_url', $test_value);
    
    if ($result) {
        echo '<div class="success"><p>✅ Setting saved successfully</p></div>';
        
        // Verify it was saved
        $saved_value = get_option('farmfaucet_redirect_url');
        if ($saved_value === $test_value) {
            echo '<div class="success"><p>✅ Setting verified: ' . esc_html($saved_value) . '</p></div>';
        } else {
            echo '<div class="error"><p>❌ Setting verification failed. Expected: ' . esc_html($test_value) . ', Got: ' . esc_html($saved_value) . '</p></div>';
        }
    } else {
        echo '<div class="error"><p>❌ Setting save failed</p></div>';
    }
}

echo '<div class="test-form">';
echo '<h4>Test Setting Save</h4>';
echo '<form method="post">';
echo '<p><label for="test_redirect_url">Test Redirect URL:</label><br>';
echo '<input type="url" id="test_redirect_url" name="test_redirect_url" value="https://example.com/test-' . time() . '" style="width: 300px; padding: 8px;">';
echo '</p>';
echo '<p><input type="submit" name="test_setting_save" value="Test Save" class="btn"></p>';
echo '</form>';
echo '</div>';

echo '</div>';

// Test 3: Test encryption functionality
echo '<div class="step">';
echo '<h3>Step 3: Encryption Functionality Test</h3>';

if (class_exists('Farmfaucet_Security')) {
    echo '<div class="success"><p>✅ Farmfaucet_Security class exists</p></div>';
    
    // Test encryption
    $test_api_key = 'test_api_key_' . time();
    
    try {
        $encrypted = Farmfaucet_Security::encrypt_api_key($test_api_key);
        echo '<div class="success"><p>✅ Encryption successful</p></div>';
        echo '<p><strong>Original:</strong> ' . esc_html($test_api_key) . '</p>';
        echo '<p><strong>Encrypted:</strong> ' . esc_html(substr($encrypted, 0, 50)) . '...</p>';
        
        // Test decryption
        $decrypted = Farmfaucet_Security::decrypt_api_key($encrypted);
        if ($decrypted === $test_api_key) {
            echo '<div class="success"><p>✅ Decryption successful</p></div>';
        } else {
            echo '<div class="error"><p>❌ Decryption failed. Expected: ' . esc_html($test_api_key) . ', Got: ' . esc_html($decrypted) . '</p></div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error"><p>❌ Encryption error: ' . esc_html($e->getMessage()) . '</p></div>';
    }
} else {
    echo '<div class="error"><p>❌ Farmfaucet_Security class not found</p></div>';
}

echo '</div>';

// Test 4: Test WordPress settings API
echo '<div class="step">';
echo '<h3>Step 4: WordPress Settings API Test</h3>';

if (isset($_POST['test_wp_settings'])) {
    echo '<div class="info"><p>Testing WordPress settings form submission...</p></div>';
    
    // Simulate settings form submission
    $test_settings = [
        'farmfaucet_redirect_url' => sanitize_text_field($_POST['wp_test_redirect_url']),
        'farmfaucet_daily_reset' => sanitize_text_field($_POST['wp_test_daily_reset']),
    ];
    
    foreach ($test_settings as $setting => $value) {
        $result = update_option($setting, $value);
        if ($result) {
            echo '<div class="success"><p>✅ ' . $setting . ' saved: ' . esc_html($value) . '</p></div>';
        } else {
            echo '<div class="error"><p>❌ ' . $setting . ' save failed</p></div>';
        }
    }
}

echo '<div class="test-form">';
echo '<h4>Test WordPress Settings Form</h4>';
echo '<form method="post" action="options.php">';
settings_fields('farmfaucet_settings');
echo '<table>';
echo '<tr><th>Setting</th><th>Value</th></tr>';
echo '<tr>';
echo '<td>Redirect URL</td>';
echo '<td><input type="url" name="farmfaucet_redirect_url" value="' . esc_attr(get_option('farmfaucet_redirect_url', '')) . '" style="width: 300px; padding: 8px;"></td>';
echo '</tr>';
echo '<tr>';
echo '<td>Daily Reset Time</td>';
echo '<td><input type="time" name="farmfaucet_daily_reset" value="' . esc_attr(get_option('farmfaucet_daily_reset', '')) . '" style="padding: 8px;"></td>';
echo '</tr>';
echo '</table>';
submit_button('Save Settings via WordPress API');
echo '</form>';
echo '</div>';

echo '</div>';

// Test 5: Check for common issues
echo '<div class="step">';
echo '<h3>Step 5: Common Issues Check</h3>';

$issues = [];

// Check if WordPress functions are available
if (!function_exists('register_setting')) {
    $issues[] = 'register_setting function not available';
}

if (!function_exists('add_settings_section')) {
    $issues[] = 'add_settings_section function not available';
}

if (!function_exists('add_settings_field')) {
    $issues[] = 'add_settings_field function not available';
}

// Check if admin class is properly initialized
if (!class_exists('Farmfaucet_Admin')) {
    $issues[] = 'Farmfaucet_Admin class not found';
}

// Check for PHP errors
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $recent_errors = file_get_contents($error_log);
    if (strpos($recent_errors, 'farmfaucet') !== false) {
        $issues[] = 'PHP errors found in error log related to farmfaucet';
    }
}

if (empty($issues)) {
    echo '<div class="success"><p>✅ No common issues detected</p></div>';
} else {
    echo '<div class="error"><p>❌ Issues detected:</p>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li>' . esc_html($issue) . '</li>';
    }
    echo '</ul></div>';
}

echo '</div>';

?>

<div class="step">
    <h3>Summary & Recommendations</h3>
    
    <div class="info">
        <h4>✅ What Was Fixed:</h4>
        <ul>
            <li><strong>Removed unnecessary settings:</strong> Payout Currency, Amount per Claim, and Cooldown Period</li>
            <li><strong>Fixed encryption method:</strong> Updated to use Farmfaucet_Security::encrypt_api_key()</li>
            <li><strong>Streamlined settings:</strong> Only essential global settings remain</li>
        </ul>
    </div>
    
    <div class="warning">
        <h4>⚠️ If Settings Still Don't Save:</h4>
        <ol>
            <li><strong>Check browser console</strong> for JavaScript errors</li>
            <li><strong>Verify WordPress permissions</strong> - ensure you have admin access</li>
            <li><strong>Check file permissions</strong> - wp-config.php should be writable</li>
            <li><strong>Disable other plugins</strong> temporarily to check for conflicts</li>
            <li><strong>Check PHP error logs</strong> for any fatal errors</li>
        </ol>
    </div>
    
    <div class="success">
        <h4>🎯 Current Settings Available:</h4>
        <ul>
            <li><strong>Captcha Settings:</strong> hCaptcha, reCAPTCHA, Turnstile site keys and secrets</li>
            <li><strong>FaucetPay API:</strong> API key for payment processing</li>
            <li><strong>Redirect URL:</strong> Post-claim redirect destination</li>
            <li><strong>Daily Reset Time:</strong> Automatic faucet reset time</li>
            <li><strong>Leaderboard Reset:</strong> Date for leaderboard reset</li>
        </ul>
        <p><strong>Note:</strong> Currency, amount, and cooldown settings are now configured per-faucet for better flexibility.</p>
    </div>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Go to Settings Tab</a>
    <a href="fix-appearance-settings.php" class="btn" style="background: #2196F3;">🔧 Run Database Fix</a>
</div>

</body>
</html>
