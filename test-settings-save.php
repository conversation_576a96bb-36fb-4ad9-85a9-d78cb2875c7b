<?php
/**
 * Farm Faucet - Settings Save Test
 * 
 * This script tests if the settings save issue is resolved
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load WordPress
if (!defined('ABSPATH')) {
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Settings Save Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Settings Save Test</h1>

<?php

echo '<div class="info">';
echo '<h3>🔍 Testing Settings Save</h3>';

// Test if Settings Manager is loaded
if (class_exists('Farmfaucet_Settings_Manager')) {
    echo '<div class="success">✅ Farmfaucet_Settings_Manager class is loaded</div>';
    
    // Test settings registration
    $all_settings = Farmfaucet_Settings_Manager::get_all_settings();
    if (!empty($all_settings)) {
        echo '<div class="success">✅ Found ' . count($all_settings) . ' registered settings</div>';
        
        // Test a simple setting update
        $test_result = Farmfaucet_Settings_Manager::safe_update_option('farmfaucet_captcha_type', 'hcaptcha');
        if ($test_result) {
            echo '<div class="success">✅ Settings save test successful</div>';
            
            // Verify the setting was saved
            $saved_value = Farmfaucet_Settings_Manager::safe_get_option('farmfaucet_captcha_type');
            if ($saved_value === 'hcaptcha') {
                echo '<div class="success">✅ Setting value verified: ' . esc_html($saved_value) . '</div>';
            } else {
                echo '<div class="error">❌ Setting value mismatch: ' . esc_html($saved_value) . '</div>';
            }
        } else {
            echo '<div class="error">❌ Settings save test failed</div>';
        }
    } else {
        echo '<div class="error">❌ No settings registered</div>';
    }
} else {
    echo '<div class="error">❌ Farmfaucet_Settings_Manager class not found</div>';
}

echo '</div>';

// Test TG Bot Builder
echo '<div class="info">';
echo '<h3>🤖 Testing TG Bot Builder</h3>';

if (class_exists('Farmfaucet_Tg_Bot_Builder')) {
    echo '<div class="success">✅ Farmfaucet_Tg_Bot_Builder class is loaded</div>';
    
    // Test if render_admin_page method exists
    if (method_exists('Farmfaucet_Tg_Bot_Builder', 'render_admin_page')) {
        echo '<div class="success">✅ render_admin_page method exists</div>';
    } else {
        echo '<div class="error">❌ render_admin_page method not found</div>';
    }
} else {
    echo '<div class="error">❌ Farmfaucet_Tg_Bot_Builder class not found</div>';
}

echo '</div>';

?>

<div style="background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #4CAF50;">
    <h3 style="color: #2e7d32; margin-top: 0;">🎯 Summary of Fixes Applied</h3>
    
    <div style="color: #2e7d32;">
        <h4>✅ Issues Fixed:</h4>
        <ul>
            <li><strong>Removed Duplicate Database Updaters:</strong> Deleted 14 duplicate database updater files</li>
            <li><strong>Centralized Settings Registration:</strong> Created Farmfaucet_Settings_Manager to handle all settings</li>
            <li><strong>Removed Settings Conflicts:</strong> Removed duplicate register_setting calls from all classes</li>
            <li><strong>Fixed TG Bot Builder:</strong> Cleaned up HTML code mixed in PHP file</li>
            <li><strong>Fixed Admin Class:</strong> Updated to use centralized settings manager</li>
            <li><strong>Removed Problematic Callbacks:</strong> Replaced encryption callbacks with safe functions</li>
        </ul>
        
        <h4>🔧 Technical Changes:</h4>
        <ul>
            <li><strong>Settings Manager:</strong> All settings now registered in one place with proper groups</li>
            <li><strong>Safe Callbacks:</strong> Using sanitize_text_field, esc_url_raw instead of custom encryption</li>
            <li><strong>Consistent Approach:</strong> All settings save through the same validated process</li>
            <li><strong>No Duplicates:</strong> Eliminated all duplicate files and implementations</li>
        </ul>
        
        <h4>🎉 Expected Results:</h4>
        <ul>
            <li>No more "website unable to handle request" errors</li>
            <li>TG Bot tab shows properly without "not available" error</li>
            <li>All settings save correctly without conflicts</li>
            <li>Plugin functions normally without server errors</li>
        </ul>
    </div>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Test Settings Tab</a>
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=tg-bot-builder'); ?>" class="btn" style="background: #2196F3;">🤖 Test TG Bot Tab</a>
</div>

</body>
</html>
