<?php

/**
 * Farmfaucet Task Completion Class
 *
 * Handles task completion settings and functionality
 */
class Farmfaucet_Task_Completion
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        // Register AJAX handlers
        add_action('wp_ajax_farmfaucet_update_task_faucet', [__CLASS__, 'ajax_update_task_faucet']);
    }

    /**
     * Render admin page
     */
    public static function render_admin_page()
    {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            return;
        }

        // Get all faucets
        $faucets = Farmfaucet_Logger::get_faucets();

        // Get task completion settings
        $task_faucets = self::get_task_faucets();



?>
        <div class="wrap farmfaucet-admin">
            <h1><?php echo esc_html__('Task Completion Settings', 'farmfaucet'); ?></h1>

            <div class="farmfaucet-admin-content">
                <div class="farmfaucet-card">
                    <h2><?php echo esc_html__('Faucets Included in Task Completion', 'farmfaucet'); ?></h2>
                    <p class="description"><?php echo esc_html__('Select which faucets should be included in task completion calculations. Only selected faucets will count towards the completion percentage displayed by the [farmfaucet_completion] shortcode.', 'farmfaucet'); ?></p>

                    <?php if (empty($faucets)): ?>
                        <p><?php echo esc_html__('No faucets found. Please create at least one faucet first.', 'farmfaucet'); ?></p>
                    <?php else: ?>
                        <table class="widefat striped">
                            <thead>
                                <tr>
                                    <th><?php echo esc_html__('Faucet Name', 'farmfaucet'); ?></th>
                                    <th><?php echo esc_html__('Include in Task Completion', 'farmfaucet'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($faucets as $faucet): ?>
                                    <?php
                                    $faucet_id = $faucet['id'];
                                    $is_included = isset($task_faucets[$faucet_id]) ? (bool)$task_faucets[$faucet_id]['is_included'] : true;
                                    ?>
                                    <tr>
                                        <td><?php echo esc_html($faucet['name']); ?></td>
                                        <td>
                                            <label class="switch">
                                                <input type="checkbox" class="task-faucet-toggle" data-faucet-id="<?php echo esc_attr($faucet_id); ?>" <?php checked($is_included); ?>>
                                                <span class="slider round"></span>
                                            </label>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                        <div class="farmfaucet-admin-actions">
                            <p class="description"><?php echo esc_html__('Changes are saved automatically.', 'farmfaucet'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="farmfaucet-card">
                    <h2><?php echo esc_html__('Task Completion Shortcode', 'farmfaucet'); ?></h2>
                    <p class="description"><?php echo esc_html__('Use this shortcode to display the task completion percentage on your site:', 'farmfaucet'); ?></p>
                    <div class="shortcode-preview-container">
                        <code id="task-completion-shortcode">[farmfaucet_completion]</code>
                        <button class="button copy-shortcode" data-target="task-completion-shortcode">
                            <span class="dashicons dashicons-clipboard"></span> <?php echo esc_html__('Copy', 'farmfaucet'); ?>
                        </button>
                    </div>

                    <div class="task-completion-customizer">
                        <h3><?php echo esc_html__('Customize Appearance', 'farmfaucet'); ?></h3>

                        <div class="customizer-form">
                            <div class="form-row">
                                <div class="form-field">
                                    <label for="tc-title"><?php echo esc_html__('Title', 'farmfaucet'); ?></label>
                                    <input type="text" id="tc-title" value="Task Completion" class="shortcode-param" data-param="title">
                                </div>

                                <div class="form-field">
                                    <label for="tc-color"><?php echo esc_html__('Bar Color', 'farmfaucet'); ?></label>
                                    <div class="color-grid-container">
                                        <div class="color-select-wrapper">
                                            <input type="hidden" id="tc-color" value="#4CAF50" class="shortcode-param" data-param="color">
                                            <div class="color-name-display">Green</div>
                                            <div class="color-preview" style="background-color: #4CAF50;"></div>
                                        </div>
                                        <div class="color-grid">
                                            <div class="color-swatch-option selected" data-color="#4CAF50" data-value="#4CAF50" data-name="Green" style="background-color: #4CAF50;"></div>
                                            <div class="color-swatch-option" data-color="#2271b1" data-value="#2271b1" data-name="Blue" style="background-color: #2271b1;"></div>
                                            <div class="color-swatch-option" data-color="#d63638" data-value="#d63638" data-name="Red" style="background-color: #d63638;"></div>
                                            <div class="color-swatch-option" data-color="#f56e28" data-value="#f56e28" data-name="Orange" style="background-color: #f56e28;"></div>
                                            <div class="color-swatch-option" data-color="#8c3db9" data-value="#8c3db9" data-name="Purple" style="background-color: #8c3db9;"></div>
                                            <div class="color-swatch-option" data-color="#1d2327" data-value="#1d2327" data-name="Black" style="background-color: #1d2327;"></div>
                                            <div class="color-swatch-option" data-color="#ffcc00" data-value="#ffcc00" data-name="Yellow" style="background-color: #ffcc00;"></div>
                                            <div class="color-swatch-option" data-color="#00a0d2" data-value="#00a0d2" data-name="Light Blue" style="background-color: #00a0d2;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-field">
                                    <label for="tc-height"><?php echo esc_html__('Bar Height', 'farmfaucet'); ?></label>
                                    <input type="range" id="tc-height" min="10" max="40" value="20" class="shortcode-param" data-param="height">
                                    <span class="range-value"><span id="tc-height-value">20</span>px</span>
                                </div>

                                <div class="form-field">
                                    <label for="tc-border-radius"><?php echo esc_html__('Border Radius', 'farmfaucet'); ?></label>
                                    <input type="range" id="tc-border-radius" min="0" max="20" value="10" class="shortcode-param" data-param="border_radius">
                                    <span class="range-value"><span id="tc-border-radius-value">10</span>px</span>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-field checkbox-field">
                                    <label>
                                        <input type="checkbox" id="tc-show-percentage" checked class="shortcode-param" data-param="show_percentage" data-type="boolean">
                                        <?php echo esc_html__('Show Percentage', 'farmfaucet'); ?>
                                    </label>
                                </div>

                                <div class="form-field checkbox-field">
                                    <label>
                                        <input type="checkbox" id="tc-show-fraction" class="shortcode-param" data-param="show_fraction" data-type="boolean">
                                        <?php echo esc_html__('Show Fraction', 'farmfaucet'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="task-completion-preview">
                            <h4><?php echo esc_html__('Preview', 'farmfaucet'); ?></h4>
                            <div class="preview-container">
                                <div class="preview-title" id="preview-title">Task Completion</div>
                                <div class="preview-progress-container" id="preview-container">
                                    <div class="preview-progress-bar" id="preview-bar" style="width: 65%;"></div>
                                    <div class="preview-progress-text" id="preview-text">65%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>
                        jQuery(document).ready(function($) {
                            // Update shortcode and preview when parameters change
                            $('.shortcode-param').on('input change', function() {
                                updateShortcode();
                                updatePreview();
                            });

                            // Copy shortcode to clipboard
                            $('.copy-shortcode').on('click', function() {
                                var targetId = $(this).data('target');
                                var shortcodeText = $('#' + targetId).text();

                                // Create temporary textarea to copy from
                                var $temp = $('<textarea>');
                                $('body').append($temp);
                                $temp.val(shortcodeText).select();
                                document.execCommand('copy');
                                $temp.remove();

                                // Show copied message
                                var $button = $(this);
                                var originalText = $button.html();
                                $button.html('<span class="dashicons dashicons-yes"></span> Copied!');
                                setTimeout(function() {
                                    $button.html(originalText);
                                }, 2000);
                            });

                            // Update range value displays
                            $('#tc-height').on('input', function() {
                                $('#tc-height-value').text($(this).val());
                            });

                            $('#tc-border-radius').on('input', function() {
                                $('#tc-border-radius-value').text($(this).val());
                            });

                            // Handle color swatch selection
                            $(document).on('click', '.color-swatch-option', function() {
                                var $this = $(this);
                                var value = $this.data('value');
                                var name = $this.data('name');
                                var color = $this.data('color');
                                var container = $this.closest('.color-grid-container');

                                // Update hidden input
                                container.find('input[type="hidden"]').val(value);

                                // Update visual elements
                                container.find('.color-swatch-option').removeClass('selected');
                                $this.addClass('selected');
                                container.find('.color-name-display').text(name);
                                container.find('.color-preview').css('background-color', color);

                                // Hide color grid
                                container.find('.color-grid').removeClass('active');

                                // Update preview and shortcode
                                updatePreview();
                                updateShortcode();
                            });

                            // Toggle color grid on preview click
                            $(document).on('click', '.color-preview, .color-select-wrapper', function() {
                                // Close any other open color grids first
                                $('.color-grid').not($(this).closest('.color-grid-container').find('.color-grid')).removeClass('active');

                                // Toggle this color grid
                                $(this).closest('.color-grid-container').find('.color-grid').toggleClass('active');
                                return false; // Prevent default dropdown
                            });

                            // Close color grid when clicking outside
                            $(document).on('click', function(e) {
                                if (!$(e.target).closest('.color-grid-container').length) {
                                    $('.color-grid').removeClass('active');
                                }
                            });

                            // Update shortcode function
                            function updateShortcode() {
                                var shortcode = '[farmfaucet_completion';

                                // Add parameters
                                $('.shortcode-param').each(function() {
                                    var param = $(this).data('param');
                                    var type = $(this).data('type') || 'text';
                                    var value;

                                    if (type === 'boolean') {
                                        value = $(this).is(':checked') ? 'yes' : 'no';
                                    } else {
                                        value = $(this).val();
                                    }

                                    // Only add non-default values
                                    if ((param === 'title' && value !== 'Task Completion') ||
                                        (param === 'color' && value !== '#2271b1') ||
                                        (param === 'height' && value !== '20') ||
                                        (param === 'border_radius' && value !== '10') ||
                                        (param === 'show_percentage' && value !== 'yes') ||
                                        (param === 'show_fraction' && value !== 'no')) {

                                        shortcode += ' ' + param + '="' + value + '"';
                                    }
                                });

                                shortcode += ']';
                                $('#task-completion-shortcode').text(shortcode);
                            }

                            // Update preview function
                            function updatePreview() {
                                // Update title
                                $('#preview-title').text($('#tc-title').val());

                                // Update bar color
                                $('#preview-bar').css('background-color', $('#tc-color').val());

                                // Update height
                                var height = $('#tc-height').val() + 'px';
                                $('#preview-container').css('height', height);

                                // Update border radius
                                var radius = $('#tc-border-radius').val() + 'px';
                                $('#preview-container').css('border-radius', radius);

                                // Update text
                                var text = '';
                                if ($('#tc-show-percentage').is(':checked')) {
                                    text += '65%';
                                }

                                if ($('#tc-show-fraction').is(':checked')) {
                                    if (text) text += ' - ';
                                    text += '13/20';
                                }

                                $('#preview-text').text(text);
                            }

                            // Initialize preview
                            updatePreview();
                        });
                    </script>

                    <style>
                        .shortcode-preview-container {
                            display: flex;
                            align-items: center;
                            margin: 15px 0;
                            background: #f9f9f9;
                            padding: 10px;
                            border-radius: 4px;
                            border: 1px solid #eee;
                        }

                        .shortcode-preview-container code {
                            flex: 1;
                            background: #f0f0f0;
                            padding: 8px 12px;
                            border-radius: 4px;
                            border: 1px solid #ddd;
                            font-family: monospace;
                            color: #333;
                        }

                        .copy-shortcode {
                            margin-left: 10px;
                            display: flex;
                            align-items: center;
                            gap: 5px;
                            background: #4CAF50 !important;
                            color: white !important;
                            border-color: #43A047 !important;
                        }

                        .copy-shortcode:hover {
                            background: #43A047 !important;
                            color: white !important;
                        }

                        .task-completion-customizer {
                            margin-top: 20px;
                            background: #f9f9f9;
                            padding: 15px;
                            border-radius: 8px;
                            border: 1px solid #eee;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                        }

                        .customizer-form {
                            margin-bottom: 20px;
                        }

                        .form-row {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 20px;
                            margin-bottom: 15px;
                        }

                        .form-field {
                            flex: 1;
                            min-width: 200px;
                        }

                        .form-field label {
                            display: block;
                            margin-bottom: 5px;
                            font-weight: 600;
                            color: #333;
                        }

                        .form-field input[type="text"],
                        .form-field input[type="number"] {
                            width: 100%;
                            padding: 8px;
                            border-radius: 4px;
                            border: 1px solid #ddd;
                        }

                        .form-field input[type="color"] {
                            width: 100%;
                            height: 40px;
                            padding: 0;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            cursor: pointer;
                        }

                        .checkbox-field {
                            display: flex;
                            align-items: center;
                        }

                        .checkbox-field label {
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            margin: 0;
                        }

                        .checkbox-field input[type="checkbox"] {
                            margin-right: 8px;
                        }

                        .range-value {
                            display: inline-block;
                            margin-left: 10px;
                            font-weight: 500;
                            color: #4CAF50;
                        }

                        .task-completion-preview {
                            background: white;
                            padding: 15px;
                            border-radius: 8px;
                            border: 1px solid #ddd;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                        }

                        .preview-container {
                            margin-top: 10px;
                        }

                        .preview-title {
                            font-weight: bold;
                            margin-bottom: 10px;
                            color: #333;
                        }

                        .preview-progress-container {
                            height: 20px;
                            background-color: #f0f0f0;
                            border-radius: 10px;
                            overflow: hidden;
                            position: relative;
                            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
                        }

                        .preview-progress-bar {
                            height: 100%;
                            background-color: #4CAF50;
                            width: 65%;
                            transition: width 0.3s ease, background-color 0.3s ease;
                        }

                        .preview-progress-text {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-weight: bold;
                            font-size: 0.9em;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                        }

                        /* Color selector styling */
                        .color-select-wrapper {
                            display: flex;
                            align-items: center;
                            background: #f0f0f0;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            padding: 8px 12px;
                            cursor: pointer;
                        }

                        .color-name-display {
                            flex: 1;
                            font-weight: 500;
                        }

                        .color-preview {
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            border: 1px solid rgba(0, 0, 0, 0.1);
                            margin-left: 10px;
                        }

                        /* Make sure the color grid is properly styled */
                        .color-grid {
                            display: none;
                            position: absolute;
                            top: 100%;
                            left: 0;
                            right: 0;
                            background: white;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            padding: 10px;
                            z-index: 9999;
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                            margin-top: 5px;
                            grid-template-columns: repeat(8, 1fr);
                            gap: 8px;
                            width: 320px;
                        }

                        .color-grid.active {
                            display: grid;
                        }

                        .color-swatch-option {
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            cursor: pointer;
                            border: 1px solid rgba(0, 0, 0, 0.1);
                            transition: all 0.2s ease;
                            margin: 0 auto;
                        }

                        .color-swatch-option:hover {
                            transform: scale(1.1);
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                        }

                        .color-swatch-option.selected {
                            box-shadow: 0 0 0 2px white, 0 0 0 4px #4CAF50;
                        }

                        .color-grid-container {
                            position: relative;
                        }

                        /* Heading styles */
                        .farmfaucet-card h3 {
                            color: #4CAF50;
                            border-bottom: 2px solid #e8f5e9;
                            padding-bottom: 8px;
                            margin-top: 20px;
                            font-weight: 600;
                        }

                        .task-completion-preview h4 {
                            color: #4CAF50;
                            margin-top: 0;
                            margin-bottom: 15px;
                            font-weight: 600;
                        }

                        /* Range input styling */
                        input[type="range"] {
                            -webkit-appearance: none;
                            width: 100%;
                            height: 8px;
                            border-radius: 5px;
                            background: #e0e0e0;
                            outline: none;
                        }

                        input[type="range"]::-webkit-slider-thumb {
                            -webkit-appearance: none;
                            appearance: none;
                            width: 18px;
                            height: 18px;
                            border-radius: 50%;
                            background: #4CAF50;
                            cursor: pointer;
                            border: 2px solid white;
                            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                        }

                        input[type="range"]::-moz-range-thumb {
                            width: 18px;
                            height: 18px;
                            border-radius: 50%;
                            background: #4CAF50;
                            cursor: pointer;
                            border: 2px solid white;
                            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                        }
                    </style>
                </div>


            </div>
        </div>



        <script>
            jQuery(document).ready(function($) {
                // Task faucet functionality
                $('.task-faucet-toggle').on('change', function() {
                    var faucetId = $(this).data('faucet-id');
                    var isIncluded = $(this).is(':checked') ? 1 : 0;

                    // Show loading indicator
                    $(this).closest('tr').addClass('updating');

                    // Send AJAX request
                    $.post(ajaxurl, {
                        action: 'farmfaucet_update_task_faucet',
                        nonce: '<?php echo wp_create_nonce('farmfaucet_admin_nonce'); ?>',
                        faucet_id: faucetId,
                        is_included: isIncluded
                    }, function(response) {
                        // Remove loading indicator
                        $('.updating').removeClass('updating');
                    });
                });


            });
        </script>

        <style>
            .farmfaucet-admin-content {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin-top: 20px;
            }

            .farmfaucet-card {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-radius: 4px;
                padding: 20px;
                box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
                flex: 1;
                min-width: 300px;
            }

            .farmfaucet-admin-actions {
                margin-top: 20px;
                padding-top: 10px;
                border-top: 1px solid #f0f0f0;
            }

            /* Toggle switch */
            .switch {
                position: relative;
                display: inline-block;
                width: 50px;
                height: 24px;
            }

            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }

            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
            }

            .slider:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                transition: .4s;
            }

            input:checked+.slider {
                background-color: #2271b1;
            }

            input:focus+.slider {
                box-shadow: 0 0 1px #2271b1;
            }

            input:checked+.slider:before {
                transform: translateX(26px);
            }

            .slider.round {
                border-radius: 24px;
            }

            .slider.round:before {
                border-radius: 50%;
            }

            /* Updating state */
            .updating {
                opacity: 0.7;
                pointer-events: none;
            }
        </style>
<?php
    }

    /**
     * AJAX handler for updating task faucet inclusion
     */
    public static function ajax_update_task_faucet()
    {
        // Check nonce
        if (!check_ajax_referer('farmfaucet_admin_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed', 'farmfaucet')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action', 'farmfaucet')]);
        }

        // Validate and sanitize data
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;
        $is_included = isset($_POST['is_included']) ? absint($_POST['is_included']) : 0;

        // Validate required fields
        if (empty($faucet_id)) {
            wp_send_json_error(['message' => __('Faucet ID is required', 'farmfaucet')]);
        }

        // Update task faucet
        $result = self::update_task_faucet($faucet_id, $is_included);

        if ($result) {
            wp_send_json_success(['message' => __('Task faucet updated successfully', 'farmfaucet')]);
        } else {
            wp_send_json_error(['message' => __('Failed to update task faucet', 'farmfaucet')]);
        }
    }

    /**
     * Get task faucets
     *
     * @return array Task faucets
     */
    public static function get_task_faucets()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_task_completion';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        if (!$table_exists) {
            return [];
        }

        // Get all task faucets
        $task_faucets = $wpdb->get_results("SELECT * FROM {$table_name}", ARRAY_A);

        // Index by faucet ID
        $indexed_task_faucets = [];
        foreach ($task_faucets as $task_faucet) {
            $indexed_task_faucets[$task_faucet['faucet_id']] = $task_faucet;
        }

        return $indexed_task_faucets;
    }

    /**
     * Update task faucet
     *
     * @param int $faucet_id Faucet ID
     * @param int $is_included Whether the faucet is included in task completion
     * @return bool Success or failure
     */
    public static function update_task_faucet($faucet_id, $is_included)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_task_completion';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        if (!$table_exists) {
            // Create the table
            $sql = "CREATE TABLE {$table_name} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                faucet_id mediumint(9) NOT NULL,
                is_included tinyint(1) NOT NULL DEFAULT 1,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY  (id),
                UNIQUE KEY faucet_id (faucet_id),
                KEY is_included (is_included)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

            $wpdb->query($sql);
        }

        // Check if the faucet already exists in the table
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE faucet_id = %d",
            $faucet_id
        ));

        if ($exists) {
            // Update existing record
            $result = $wpdb->update(
                $table_name,
                [
                    'is_included' => $is_included,
                    'updated_at' => current_time('mysql')
                ],
                ['faucet_id' => $faucet_id],
                ['%d', '%s'],
                ['%d']
            );
        } else {
            // Insert new record
            $result = $wpdb->insert(
                $table_name,
                [
                    'faucet_id' => $faucet_id,
                    'is_included' => $is_included,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['%d', '%d', '%s', '%s']
            );
        }

        return $result !== false;
    }

    /**
     * Check if a faucet is included in task completion
     *
     * @param int $faucet_id Faucet ID
     * @return bool True if included, false otherwise
     */
    public static function is_faucet_included($faucet_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_task_completion';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        if (!$table_exists) {
            return true; // Default to included if the table doesn't exist
        }

        // Check if the faucet exists in the table
        $is_included = $wpdb->get_var($wpdb->prepare(
            "SELECT is_included FROM {$table_name} WHERE faucet_id = %d",
            $faucet_id
        ));

        // If the faucet doesn't exist in the table, default to included
        if ($is_included === null) {
            return true;
        }

        return (bool)$is_included;
    }

    /**
     * Get included faucets
     *
     * @return array Array of included faucet IDs
     */
    public static function get_included_faucets()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_task_completion';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        if (!$table_exists) {
            // If the table doesn't exist, get all faucets
            $faucets = Farmfaucet_Logger::get_faucets();
            return array_column($faucets, 'id');
        }

        // Get all included faucets
        $included_faucets = $wpdb->get_results($wpdb->prepare(
            "SELECT faucet_id FROM {$table_name} WHERE is_included = %d",
            1
        ), ARRAY_A);

        // If no faucets are explicitly included, get all faucets
        if (empty($included_faucets)) {
            $faucets = Farmfaucet_Logger::get_faucets();
            return array_column($faucets, 'id');
        }

        return array_column($included_faucets, 'faucet_id');
    }
}
