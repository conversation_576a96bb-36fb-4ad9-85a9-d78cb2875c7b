/**
 * Farm Faucet - Admin Faucet Toggle (Fixed Version)
 * Handles the toggle switch functionality for enabling/disabling faucets
 * with improved error handling and user feedback
 */
jQuery(document).ready(function($) {
    // Track toggle operations in progress to prevent multiple clicks
    const togglesInProgress = {};
    
    // Handle toggle switch click
    $('.faucet-enabled-toggle').on('change', function() {
        const $toggle = $(this);
        const faucetId = $toggle.data('faucet-id');
        
        // Prevent multiple clicks on the same toggle
        if (togglesInProgress[faucetId]) {
            return false;
        }
        
        const isEnabled = $toggle.prop('checked') ? 1 : 0;
        const $statusLabel = $toggle.closest('.faucet-status-toggle').find('.faucet-status-label');
        const originalStatus = !isEnabled ? 'Enabled' : 'Disabled';
        
        // Show loading state
        $statusLabel.text(isEnabled ? 'Enabling...' : 'Disabling...');
        $toggle.prop('disabled', true);
        togglesInProgress[faucetId] = true;
        
        // Send AJAX request to toggle faucet status
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_toggle_faucet_status',
                faucet_id: faucetId,
                is_enabled: isEnabled,
                nonce: farmfaucet_admin_vars.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update status label
                    $statusLabel.text(isEnabled ? 'Enabled' : 'Disabled');
                    
                    // Show success message
                    farmfaucet_show_notification(response.data.message, 'success');
                } else {
                    // Revert toggle state
                    $toggle.prop('checked', !isEnabled);
                    $statusLabel.text(originalStatus);
                    
                    // Show error message
                    farmfaucet_show_notification(response.data.message || 'An error occurred', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                
                // Revert toggle state
                $toggle.prop('checked', !isEnabled);
                $statusLabel.text(originalStatus);
                
                // Show detailed error message
                farmfaucet_show_notification('Connection error: ' + error, 'error');
            },
            complete: function() {
                // Re-enable the toggle switch
                $toggle.prop('disabled', false);
                
                // Remove from in-progress tracking
                delete togglesInProgress[faucetId];
            }
        });
    });
    
    // Helper function to show notifications
    function farmfaucet_show_notification(message, type) {
        // Remove any existing notifications first
        $('.farmfaucet-notification').remove();
        
        const $notification = $('<div class="farmfaucet-notification"></div>')
            .addClass(type === 'error' ? 'farmfaucet-notification-error' : 'farmfaucet-notification-success')
            .text(message);
            
        // Add to page
        $('body').append($notification);
        
        // Show notification
        $notification.fadeIn(300);
        
        // Auto dismiss after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    // Add CSS for notifications if not already present
    if ($('#farmfaucet-notification-styles').length === 0) {
        $('head').append(`
            <style id="farmfaucet-notification-styles">
                .farmfaucet-notification {
                    position: fixed;
                    top: 50px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 4px;
                    color: white;
                    font-weight: 500;
                    z-index: 99999;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                    display: none;
                }
                .farmfaucet-notification-success {
                    background-color: #4CAF50;
                }
                .farmfaucet-notification-error {
                    background-color: #F44336;
                }
            </style>
        `);
    }
});
