<?php
if (!defined('ABSPATH')) exit;
?>
<div id="button-form-dialog" title="<?php esc_attr_e('Button Form', 'farmfaucet'); ?>" style="display:none;">
    <form id="button-form" class="farmfaucet-form">
        <input type="hidden" id="button-id" name="button_id" value="0">
        <input type="hidden" id="button-faucet-id" name="faucet_id" value="0">

        <h3><?php esc_html_e('Basic Settings', 'farmfaucet'); ?></h3>

        <div class="form-field">
            <label for="button-text"><?php esc_html_e('Button Text', 'farmfaucet'); ?> <span class="required">*</span></label>
            <input type="text" id="button-text" name="button_text" required>
            <p class="description"><?php esc_html_e('Text to display on the button.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="button-size"><?php esc_html_e('Button Size', 'farmfaucet'); ?></label>
            <select id="button-size" name="button_size">
                <option value="small"><?php esc_html_e('Small', 'farmfaucet'); ?></option>
                <option value="medium" selected><?php esc_html_e('Medium', 'farmfaucet'); ?></option>
                <option value="large"><?php esc_html_e('Large', 'farmfaucet'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('Select the size of the button.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="button-color"><?php esc_html_e('Button Color', 'farmfaucet'); ?></label>
            <select id="button-color" name="button_color">
                <option value="blue" selected><?php esc_html_e('Blue', 'farmfaucet'); ?></option>
                <option value="green"><?php esc_html_e('Green', 'farmfaucet'); ?></option>
                <option value="red"><?php esc_html_e('Red', 'farmfaucet'); ?></option>
                <option value="orange"><?php esc_html_e('Orange', 'farmfaucet'); ?></option>
                <option value="purple"><?php esc_html_e('Purple', 'farmfaucet'); ?></option>
                <option value="black"><?php esc_html_e('Black', 'farmfaucet'); ?></option>
                <option value="custom"><?php esc_html_e('Custom Color', 'farmfaucet'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('Select a predefined color or choose custom.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field custom-color-field" style="display:none;">
            <label for="button-color-hex"><?php esc_html_e('Custom Color', 'farmfaucet'); ?></label>
            <input type="color" id="button-color-hex" name="button_color_hex" value="#2271b1">
            <p class="description"><?php esc_html_e('Choose a custom color for the button.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="border-shape"><?php esc_html_e('Border Shape', 'farmfaucet'); ?></label>
            <select id="border-shape" name="border_shape">
                <option value="rounded" selected><?php esc_html_e('Rounded', 'farmfaucet'); ?></option>
                <option value="pill"><?php esc_html_e('Pill', 'farmfaucet'); ?></option>
                <option value="square"><?php esc_html_e('Square', 'farmfaucet'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('Select the border shape of the button.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="redirect-url"><?php esc_html_e('Redirect URL', 'farmfaucet'); ?></label>
            <input type="url" id="redirect-url" name="redirect_url" placeholder="https://example.com/page">
            <p class="description"><?php esc_html_e('Optional URL to redirect to when button is clicked.', 'farmfaucet'); ?></p>
        </div>

        <h3><?php esc_html_e('Button Lock Settings', 'farmfaucet'); ?></h3>

        <div class="form-field">
            <label><input type="checkbox" id="is-locked" name="is_locked" value="1"> <?php esc_html_e('Lock Button', 'farmfaucet'); ?></label>
            <p class="description"><?php esc_html_e('If checked, button will be locked until user completes required faucets.', 'farmfaucet'); ?></p>
        </div>

        <div class="lock-settings" style="display:none;">
            <div class="form-field">
                <label><?php esc_html_e('Required Faucets', 'farmfaucet'); ?></label>
                <div class="required-faucets-container">
                    <?php
                    // Get all faucets
                    $faucets = Farmfaucet_Logger::get_faucets();
                    if (!empty($faucets)) {
                        echo '<div class="checkbox-group">';
                        foreach ($faucets as $faucet) {
                            echo '<label><input type="checkbox" name="required_faucets[]" value="' . esc_attr($faucet['id']) . '"> ' .
                                esc_html($faucet['name']) . '</label>';
                        }
                        echo '</div>';
                    } else {
                        echo '<p>' . esc_html__('No faucets available.', 'farmfaucet') . '</p>';
                    }
                    ?>
                </div>
                <p class="description"><?php esc_html_e('Select which faucets must be completed before this button is unlocked.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="reset-minutes"><?php esc_html_e('Reset Time (minutes)', 'farmfaucet'); ?></label>
                <input type="number" id="reset-minutes" name="reset_minutes" min="0" value="0">
                <p class="description"><?php esc_html_e('Time in minutes before lock resets. 0 = never reset.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label><input type="checkbox" id="lock-faucet" name="lock_faucet" value="1"> <?php esc_html_e('Lock Parent Faucet', 'farmfaucet'); ?></label>
                <p class="description"><?php esc_html_e('If checked, the parent faucet will also be locked until requirements are met.', 'farmfaucet'); ?></p>
            </div>
        </div>

        <h3><?php esc_html_e('Milestone Settings', 'farmfaucet'); ?></h3>

        <div class="form-field">
            <label><input type="checkbox" id="milestone-enabled" name="milestone_enabled" value="1"> <?php esc_html_e('Enable Milestone Progress', 'farmfaucet'); ?></label>
            <p class="description"><?php esc_html_e('If checked, a milestone progress bar will be displayed.', 'farmfaucet'); ?></p>
        </div>

        <div class="milestone-settings" style="display:none;">
            <div class="form-field">
                <label for="milestone-type"><?php esc_html_e('Milestone Type', 'farmfaucet'); ?></label>
                <select id="milestone-type" name="milestone_type">
                    <option value="global" selected><?php esc_html_e('Global (All Faucets)', 'farmfaucet'); ?></option>
                    <option value="page_specific"><?php esc_html_e('Page Specific', 'farmfaucet'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('Select whether to track all faucets or specific pages.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field milestone-pages-field" style="display:none;">
                <label for="milestone-pages"><?php esc_html_e('Select Pages', 'farmfaucet'); ?></label>
                <select id="milestone-pages" name="milestone_pages[]" multiple class="farmfaucet-select2" style="width: 100%;">
                    <?php
                    $pages = get_pages(['sort_column' => 'post_title', 'sort_order' => 'ASC']);
                    foreach ($pages as $page) {
                        echo '<option value="' . esc_attr($page->ID) . '">' . esc_html($page->post_title) . '</option>';
                    }
                    ?>
                </select>
                <p class="description"><?php esc_html_e('Select pages to track for this milestone.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="milestone-count"><?php esc_html_e('Required Completions', 'farmfaucet'); ?></label>
                <input type="number" id="milestone-count" name="milestone_count" min="1" value="5">
                <p class="description"><?php esc_html_e('Number of completions required to reach 100%.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label><input type="checkbox" id="milestone-lock-faucet" name="milestone_lock_faucet" value="1"> <?php esc_html_e('Lock Until Complete', 'farmfaucet'); ?></label>
                <p class="description"><?php esc_html_e('If checked, the button will be locked until milestone is complete.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="milestone-display-style"><?php esc_html_e('Display Style', 'farmfaucet'); ?></label>
                <select id="milestone-display-style" name="milestone_display_style">
                    <option value="card" selected><?php esc_html_e('Card View', 'farmfaucet'); ?></option>
                    <option value="compact"><?php esc_html_e('Compact View', 'farmfaucet'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('Select how the milestone progress should be displayed.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label><input type="checkbox" id="milestone-transparent-bg" name="milestone_transparent_bg" value="1"> <?php esc_html_e('Transparent Background', 'farmfaucet'); ?></label>
                <p class="description"><?php esc_html_e('If checked, the milestone card will have a transparent background.', 'farmfaucet'); ?></p>
            </div>

            <div class="milestone-card-bg-options">
                <div class="form-field">
                    <label for="milestone-card-bg-style"><?php esc_html_e('Card Background Style', 'farmfaucet'); ?></label>
                    <select id="milestone-card-bg-style" name="milestone_card_bg_style">
                        <option value="solid" selected><?php esc_html_e('Solid Color', 'farmfaucet'); ?></option>
                        <option value="gradient"><?php esc_html_e('Gradient', 'farmfaucet'); ?></option>
                    </select>
                </div>

                <div class="form-field milestone-card-solid-option">
                    <label for="milestone-card-bg-color"><?php esc_html_e('Card Background Color', 'farmfaucet'); ?></label>
                    <input type="color" id="milestone-card-bg-color" name="milestone_card_bg_color" value="#FFFFFF">
                </div>

                <div class="form-field milestone-card-gradient-options" style="display:none;">
                    <label for="milestone-card-gradient-start"><?php esc_html_e('Gradient Start Color', 'farmfaucet'); ?></label>
                    <input type="color" id="milestone-card-gradient-start" name="milestone_card_gradient_start" value="#FFFFFF">
                </div>

                <div class="form-field milestone-card-gradient-options" style="display:none;">
                    <label for="milestone-card-gradient-end"><?php esc_html_e('Gradient End Color', 'farmfaucet'); ?></label>
                    <input type="color" id="milestone-card-gradient-end" name="milestone_card_gradient_end" value="#F5F5F5">
                </div>
            </div>

            <div class="form-field">
                <label for="milestone-bar-style"><?php esc_html_e('Progress Bar Style', 'farmfaucet'); ?></label>
                <select id="milestone-bar-style" name="milestone_bar_style">
                    <option value="solid" selected><?php esc_html_e('Solid Color', 'farmfaucet'); ?></option>
                    <option value="gradient"><?php esc_html_e('Gradient', 'farmfaucet'); ?></option>
                </select>
            </div>

            <div class="form-field milestone-bar-solid-option">
                <label for="milestone-bar-color"><?php esc_html_e('Progress Bar Color', 'farmfaucet'); ?></label>
                <input type="color" id="milestone-bar-color" name="milestone_bar_color" value="#4CAF50">
            </div>

            <div class="form-field milestone-bar-gradient-options" style="display:none;">
                <label for="milestone-gradient-start"><?php esc_html_e('Gradient Start Color', 'farmfaucet'); ?></label>
                <input type="color" id="milestone-gradient-start" name="milestone_gradient_start" value="#4CAF50">
            </div>

            <div class="form-field milestone-bar-gradient-options" style="display:none;">
                <label for="milestone-gradient-end"><?php esc_html_e('Gradient End Color', 'farmfaucet'); ?></label>
                <input type="color" id="milestone-gradient-end" name="milestone_gradient_end" value="#2196F3">
            </div>
        </div>

        <h3><?php esc_html_e('Countdown Settings', 'farmfaucet'); ?></h3>

        <div class="form-field">
            <label><input type="checkbox" id="countdown-enabled" name="countdown_enabled" value="1"> <?php esc_html_e('Enable Countdown', 'farmfaucet'); ?></label>
            <p class="description"><?php esc_html_e('If checked, a countdown will be displayed before the button is enabled.', 'farmfaucet'); ?></p>
        </div>

        <div class="countdown-settings" style="display:none;">
            <div class="form-field">
                <label for="countdown-seconds"><?php esc_html_e('Countdown Duration (seconds)', 'farmfaucet'); ?></label>
                <input type="number" id="countdown-seconds" name="countdown_seconds" min="1" value="60">
                <p class="description"><?php esc_html_e('Duration of the countdown in seconds.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label for="countdown-message"><?php esc_html_e('Countdown Message', 'farmfaucet'); ?></label>
                <input type="text" id="countdown-message" name="countdown_message" placeholder="Please wait...">
                <p class="description"><?php esc_html_e('Message to display during countdown.', 'farmfaucet'); ?></p>
            </div>

            <div class="form-field">
                <label><input type="checkbox" id="countdown-click-activation" name="countdown_click_activation" value="1"> <?php esc_html_e('Activate on Element Click', 'farmfaucet'); ?></label>
                <p class="description"><?php esc_html_e('If checked, countdown starts when user clicks on a specific element.', 'farmfaucet'); ?></p>
            </div>

            <div class="click-activation-settings" style="display:none;">
                <div class="form-field">
                    <label for="countdown-click-element-id"><?php esc_html_e('Element ID', 'farmfaucet'); ?></label>
                    <input type="text" id="countdown-click-element-id" name="countdown_click_element_id" placeholder="element-id">
                    <p class="description"><?php esc_html_e('ID of the element that triggers the countdown when clicked.', 'farmfaucet'); ?></p>
                </div>

                <div class="form-field">
                    <label for="countdown-pre-click-message"><?php esc_html_e('Pre-Click Message', 'farmfaucet'); ?></label>
                    <input type="text" id="countdown-pre-click-message" name="countdown_pre_click_message" placeholder="Click to start...">
                    <p class="description"><?php esc_html_e('Message to display before user clicks the element.', 'farmfaucet'); ?></p>
                </div>
            </div>
        </div>
    </form>
</div>