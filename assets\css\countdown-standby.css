/**
 * Farm Faucet - Countdown Standby CSS
 *
 * Styles for the countdown standby overlay
 */

.farmfaucet-standby-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    cursor: wait;
    display: flex;
    justify-content: center;
    align-items: center;
}

.farmfaucet-standby-message {
    background-color: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 80%;
    min-width: 300px;
}

.farmfaucet-standby-message h3 {
    margin-top: 0;
    color: #333;
    font-size: 22px;
    margin-bottom: 20px;
}

.farmfaucet-standby-timer {
    font-size: 28px;
    font-weight: bold;
    color: #4CAF50;
    margin: 15px 0;
    font-family: monospace;
}

body.farmfaucet-standby-active {
    overflow: hidden;
}

/* Cup and juice animation */
.farmfaucet-cup-container {
    width: 100px;
    height: 120px;
    margin: 0 auto 20px;
    position: relative;
}

.farmfaucet-cup {
    width: 80px;
    height: 100px;
    background-color: rgba(255, 255, 255, 0.7);
    border: 3px solid #4CAF50;
    border-radius: 5px 5px 40px 40px;
    position: relative;
    margin: 0 auto;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.farmfaucet-juice {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to bottom, #8BC34A, #4CAF50);
    transition: height 0.3s ease-in-out;
    border-radius: 0 0 37px 37px;
}
