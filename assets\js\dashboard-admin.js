/**
 * Farm Faucet Dashboard Admin JavaScript
 *
 * Handles admin-side dashboard functionality
 */

jQuery(document).ready(function($) {
    // Initialize earnings graph preview
    initEarningsGraphPreview();
    
    // Earnings graph height slider
    $('#farmfaucet_earnings_graph_default_height').on('input', function() {
        var height = $(this).val();
        $('#graph-height-value').text(height + 'px');
        $('.graph-preview-container').css('height', height + 'px');
        
        // Update the preview graph
        updateGraphPreview();
    });
    
    // Earnings graph width change
    $('#farmfaucet_earnings_graph_default_width').on('change', function() {
        var width = $(this).val();
        $('.graph-preview-container').css('width', width);
        
        // Update the preview graph
        updateGraphPreview();
    });
    
    // Earnings graph type change
    $('#farmfaucet_earnings_graph_default_type').on('change', function() {
        // Update the preview graph
        updateGraphPreview();
    });
    
    // Earnings graph color change
    $('#farmfaucet_earnings_graph_color').on('input', function() {
        // Update the preview graph
        updateGraphPreview();
    });
    
    /**
     * Initialize earnings graph preview
     */
    function initEarningsGraphPreview() {
        // Create sample data
        var sampleData = generateSampleData();
        
        // Create the preview chart
        createPreviewChart(sampleData);
    }
    
    /**
     * Update the graph preview
     */
    function updateGraphPreview() {
        // Create sample data
        var sampleData = generateSampleData();
        
        // Update the preview chart
        createPreviewChart(sampleData);
    }
    
    /**
     * Generate sample data for the preview chart
     * 
     * @return {Object} Sample data for the chart
     */
    function generateSampleData() {
        var type = $('#farmfaucet_earnings_graph_default_type').val() || 'line';
        var labels = [];
        var values = [];
        
        // Create sample data based on type
        if (type === 'line') {
            // Line chart data - smooth curve
            labels = ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7'];
            values = [0.00012500, 0.00025000, 0.00018750, 0.00031250, 0.00043750, 0.00037500, 0.00050000];
        } else {
            // Bar chart data - more varied
            labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
            values = [0.00087500, 0.00112500, 0.00075000, 0.00137500];
        }
        
        return {
            labels: labels,
            values: values
        };
    }
    
    /**
     * Create or update the preview chart
     * 
     * @param {Object} data Chart data
     */
    function createPreviewChart(data) {
        var $canvas = $('#earnings-graph-preview');
        
        // If canvas doesn't exist, return
        if (!$canvas.length) {
            return;
        }
        
        // Get settings
        var type = $('#farmfaucet_earnings_graph_default_type').val() || 'line';
        var color = $('#farmfaucet_earnings_graph_color').val() || '#4CAF50';
        
        // Convert hex color to rgba for background
        var rgbaColor = hexToRgba(color, 0.2);
        
        // Destroy existing chart if it exists
        if (window.previewChart) {
            window.previewChart.destroy();
        }
        
        // Create chart
        var ctx = $canvas[0].getContext('2d');
        window.previewChart = new Chart(ctx, {
            type: type,
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Earnings',
                    data: data.values,
                    backgroundColor: rgbaColor,
                    borderColor: color,
                    borderWidth: 2,
                    pointBackgroundColor: color,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 1,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.3,
                    barPercentage: 0.7,
                    categoryPercentage: 0.8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(8);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Earned: ' + context.raw.toFixed(8);
                            }
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    /**
     * Convert hex color to rgba
     * 
     * @param {string} hex Hex color code
     * @param {number} alpha Alpha value (0-1)
     * @return {string} RGBA color string
     */
    function hexToRgba(hex, alpha) {
        // Default to green if invalid hex
        if (!hex || !/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
            hex = '#4CAF50';
        }
        
        // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
        let shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
        hex = hex.replace(shorthandRegex, function(m, r, g, b) {
            return r + r + g + g + b + b;
        });
        
        let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) {
            return 'rgba(76, 175, 80, ' + alpha + ')';
        }
        
        let r = parseInt(result[1], 16);
        let g = parseInt(result[2], 16);
        let b = parseInt(result[3], 16);
        
        return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + alpha + ')';
    }
});
