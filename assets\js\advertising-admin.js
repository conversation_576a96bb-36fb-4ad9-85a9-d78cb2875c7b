/**
 * Advertising System Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initAdvertisingAdmin();
    });

    /**
     * Initialize Advertising Admin functionality
     */
    function initAdvertisingAdmin() {
        // View advertisement details
        $('.view-ad').on('click', function(e) {
            e.preventDefault();
            
            const adId = $(this).closest('tr').data('ad-id');
            viewAdDetails(adId);
        });
        
        // Approve advertisement
        $('.approve-ad').on('click', function(e) {
            e.preventDefault();
            
            const adId = $(this).closest('tr').data('ad-id');
            approveAd(adId);
        });
        
        // Reject advertisement
        $('.reject-ad').on('click', function(e) {
            e.preventDefault();
            
            const adId = $(this).closest('tr').data('ad-id');
            rejectAd(adId);
        });
        
        // Delete advertisement
        $('.delete-ad').on('click', function(e) {
            e.preventDefault();
            
            const adId = $(this).closest('tr').data('ad-id');
            deleteAd(adId);
        });
        
        // Close modal
        $('.farmfaucet-modal-close').on('click', function() {
            $('#farmfaucet-ad-modal').hide();
        });
        
        // Close modal when clicking outside
        $(window).on('click', function(event) {
            if ($(event.target).hasClass('farmfaucet-modal')) {
                $('.farmfaucet-modal').hide();
            }
        });
    }

    /**
     * View advertisement details
     * 
     * @param {number} adId Advertisement ID
     */
    function viewAdDetails(adId) {
        // Show loading state
        $('#ad-details-container').html('<p>Loading...</p>');
        $('#farmfaucet-ad-modal').show();
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetAdvertisingAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_ad_details',
                nonce: farmfaucetAdvertisingAdmin.nonce,
                ad_id: adId
            },
            success: function(response) {
                if (response.success) {
                    const ad = response.data;
                    
                    // Format dates
                    const createdDate = new Date(ad.created_at);
                    const startDate = new Date(ad.start_date);
                    const endDate = new Date(ad.end_date);
                    
                    // Build HTML
                    let html = '<div class="ad-details">';
                    
                    // Title
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Title</div>';
                    html += '<div class="ad-detail-value">' + ad.title + '</div>';
                    html += '</div>';
                    
                    // Description
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Description</div>';
                    html += '<div class="ad-detail-value">' + ad.description + '</div>';
                    html += '</div>';
                    
                    // URL
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">URL</div>';
                    html += '<div class="ad-detail-value"><a href="' + ad.url + '" target="_blank">' + ad.url + '</a></div>';
                    html += '</div>';
                    
                    // Image
                    if (ad.image_url) {
                        html += '<div class="ad-detail-item">';
                        html += '<div class="ad-detail-label">Image</div>';
                        html += '<div class="ad-detail-value">';
                        html += '<a href="' + ad.image_url + '" target="_blank">' + ad.image_url + '</a>';
                        html += '<img src="' + ad.image_url + '" class="ad-detail-image" alt="Advertisement Image">';
                        html += '</div>';
                        html += '</div>';
                    }
                    
                    // User
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">User</div>';
                    html += '<div class="ad-detail-value">' + ad.user_name + ' (ID: ' + ad.user_id + ')</div>';
                    html += '</div>';
                    
                    // Status
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Status</div>';
                    html += '<div class="ad-detail-value">' + ad.status.charAt(0).toUpperCase() + ad.status.slice(1) + '</div>';
                    html += '</div>';
                    
                    // Votes
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Votes</div>';
                    html += '<div class="ad-detail-value">' + ad.votes + '</div>';
                    html += '</div>';
                    
                    // Cost
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Cost</div>';
                    html += '<div class="ad-detail-value">' + ad.cost + '</div>';
                    html += '</div>';
                    
                    // Dates
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Created</div>';
                    html += '<div class="ad-detail-value">' + createdDate.toLocaleString() + '</div>';
                    html += '</div>';
                    
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">Start Date</div>';
                    html += '<div class="ad-detail-value">' + startDate.toLocaleString() + '</div>';
                    html += '</div>';
                    
                    html += '<div class="ad-detail-item">';
                    html += '<div class="ad-detail-label">End Date</div>';
                    html += '<div class="ad-detail-value">' + endDate.toLocaleString() + '</div>';
                    html += '</div>';
                    
                    html += '</div>'; // Close ad-details
                    
                    // Update modal content
                    $('#ad-details-container').html(html);
                } else {
                    // Show error message
                    $('#ad-details-container').html('<p>Error: ' + (response.data.message || 'Failed to load advertisement details.') + '</p>');
                }
            },
            error: function() {
                // Show error message
                $('#ad-details-container').html('<p>Error: Failed to load advertisement details.</p>');
            }
        });
    }

    /**
     * Approve an advertisement
     * 
     * @param {number} adId Advertisement ID
     */
    function approveAd(adId) {
        if (!confirm('Are you sure you want to approve this advertisement?')) {
            return;
        }
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetAdvertisingAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_approve_ad',
                nonce: farmfaucetAdvertisingAdmin.nonce,
                ad_id: adId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.approveSuccess);
                    
                    // Reload page
                    window.location.reload();
                } else {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.approveFailed);
                }
            },
            error: function() {
                alert(farmfaucetAdvertisingAdmin.i18n.error);
            }
        });
    }

    /**
     * Reject an advertisement
     * 
     * @param {number} adId Advertisement ID
     */
    function rejectAd(adId) {
        if (!confirm('Are you sure you want to reject this advertisement?')) {
            return;
        }
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetAdvertisingAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_reject_ad',
                nonce: farmfaucetAdvertisingAdmin.nonce,
                ad_id: adId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.rejectSuccess);
                    
                    // Reload page
                    window.location.reload();
                } else {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.rejectFailed);
                }
            },
            error: function() {
                alert(farmfaucetAdvertisingAdmin.i18n.error);
            }
        });
    }

    /**
     * Delete an advertisement
     * 
     * @param {number} adId Advertisement ID
     */
    function deleteAd(adId) {
        if (!confirm(farmfaucetAdvertisingAdmin.i18n.confirmDelete)) {
            return;
        }
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetAdvertisingAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_ad',
                nonce: farmfaucetAdvertisingAdmin.nonce,
                ad_id: adId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.deleteSuccess);
                    
                    // Remove row from table
                    $('tr[data-ad-id="' + adId + '"]').fadeOut(300, function() {
                        $(this).remove();
                    });
                } else {
                    alert(response.data.message || farmfaucetAdvertisingAdmin.i18n.deleteFailed);
                }
            },
            error: function() {
                alert(farmfaucetAdvertisingAdmin.i18n.error);
            }
        });
    }

})(jQuery);
