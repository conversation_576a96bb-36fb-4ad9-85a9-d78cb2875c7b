/**
 * Farm Faucet Button Update Consolidated Fix
 *
 * This script consolidates all button update fixes into a single file
 * to avoid conflicts between multiple implementations.
 */
(function($) {
    'use strict';

    // Store the original button form HTML
    var originalButtonFormHtml = '';

    $(document).ready(function() {
        console.log('Button Update Consolidated Fix loaded');

        // Store the original form HTML when the page loads
        if ($('#button-form-dialog').length) {
            originalButtonFormHtml = $('#button-form-dialog').html();
            console.log('Original button form HTML stored');

            // Initialize the button handlers
            initButtonHandlers();
        }
    });

    /**
     * Initialize button handlers
     */
    function initButtonHandlers() {
        // Override the edit button click handler
        $(document).off('click', '.edit-button').on('click', '.edit-button', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var buttonId = $(this).data('id');
            console.log('Editing button ID:', buttonId);

            // Reset the form to its original state
            $('#button-form-dialog').html(originalButtonFormHtml);

            // Show loading indicator
            $('#button-form-dialog').append('<div class="loading-overlay"><div class="loading-spinner">Loading...</div></div>');

            // Open the dialog
            $('#button-form-dialog').dialog('option', 'title', 'Edit Button');
            $('#button-form-dialog').dialog('open');

            // Set the button ID
            $('#button-id').val(buttonId);

            // Get the faucet ID from the parent container
            var faucetId = $(this).closest('.faucet-tab-content').data('faucet-id');
            $('#button-faucet-id').val(faucetId);

            // Get button data
            $.ajax({
                url: farmfaucet_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'farmfaucet_get_button',
                    nonce: farmfaucet_admin.nonce,
                    button_id: buttonId
                },
                success: function(response) {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();

                    if (response.success && response.data) {
                        var button = response.data;

                        // Log the button data for debugging
                        console.log('Button data loaded:', button);

                        // Populate form fields
                        $('#button-text').val(button.button_text || '');
                        $('#button-size').val(button.button_size || 'medium');
                        $('#button-color').val(button.button_color || 'blue');
                        $('#button-color-hex').val(button.button_color_hex || '');
                        $('#button-border').val(button.border_shape || 'rounded');
                        $('#button-redirect').val(button.redirect_url || '');

                        // Initialize color picker if it exists
                        if (typeof initColorPicker === 'function') {
                            initColorPicker();
                        }

                        // Set lock options
                        $('#button-is-locked').prop('checked', button.is_locked == 1);
                        $('#button-reset-minutes').val(button.reset_minutes || 0);
                        $('#button-lock-faucet').prop('checked', button.lock_faucet == 1);

                        // Toggle lock options visibility
                        toggleLockOptions();

                        // Check required faucets
                        if (button.required_faucets) {
                            var requiredFaucets = button.required_faucets.split(',');
                            $('.required-faucet-checkbox').each(function() {
                                $(this).prop('checked', requiredFaucets.includes($(this).val()));
                            });
                        }

                        // Set countdown options
                        if (button.countdown_enabled == 1) {
                            $('#button-countdown-enabled').prop('checked', true);
                            $('.countdown-options').show();

                            // Disable milestone and lock checkboxes
                            $('#button-milestone-enabled, #button-is-locked').prop('disabled', true);

                            // Set countdown seconds
                            $('#button-countdown-seconds').val(button.countdown_seconds || 60);

                            // Set countdown message
                            $('#button-countdown-message').val(button.countdown_message || '');

                            // Set click activation options
                            if (button.countdown_click_activation == 1) {
                                $('#button-countdown-click-activation').prop('checked', true);
                                $('.countdown-click-options').show();

                                // Set click element ID
                                $('#button-countdown-click-element-id').val(button.countdown_click_element_id || '');

                                // Set pre-click message
                                $('#button-countdown-pre-click-message').val(button.countdown_pre_click_message || '');
                            }

                            // Set standby mode
                            if (button.countdown_standby == 1) {
                                $('#button-countdown-standby').prop('checked', true);
                            } else {
                                $('#button-countdown-standby').prop('checked', false);
                            }

                            // Set captcha options
                            if (button.countdown_captcha == 1) {
                                $('#button-countdown-captcha').prop('checked', true);
                                $('.countdown-captcha-options').show();

                                // Set captcha type
                                $('#button-countdown-captcha-type').val(button.countdown_captcha_type || 'default');
                            } else {
                                $('#button-countdown-captcha').prop('checked', false);
                                $('.countdown-captcha-options').hide();
                            }
                        } else {
                            $('#button-countdown-enabled').prop('checked', false);
                            $('.countdown-options').hide();
                            $('.countdown-click-options').hide();
                            $('.countdown-captcha-options').hide();
                        }
                    } else {
                        alert('Error loading button data: ' + (response.data?.message || 'Unknown error'));
                        $('#button-form-dialog').dialog('close');
                    }
                },
                error: function() {
                    // Remove loading overlay
                    $('#button-form-dialog .loading-overlay').remove();
                    alert('Error loading button data');
                    $('#button-form-dialog').dialog('close');
                }
            });
        });

        // Add button click handler
        $(document).off('click', '.add-button').on('click', '.add-button', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var faucetId = $(this).data('faucet-id');
            console.log('Adding button for faucet ID:', faucetId);

            // Reset the form to its original state
            $('#button-form-dialog').html(originalButtonFormHtml);

            // Reset form fields
            $('#button-id').val('0');
            $('#button-faucet-id').val(faucetId);
            $('#button-text').val('');
            $('#button-size').val('medium');
            $('#button-color').val('blue');
            $('#button-color-hex').val('');
            $('#button-border').val('rounded');
            $('#button-redirect').val('');

            // Initialize color picker if it exists
            if (typeof initColorPicker === 'function') {
                initColorPicker();
            }

            // Reset lock options
            $('#button-is-locked').prop('checked', false);
            $('#button-reset-minutes').val('0');
            $('#button-lock-faucet').prop('checked', false);

            // Toggle lock options visibility
            toggleLockOptions();

            // Uncheck all required faucets
            $('.required-faucet-checkbox').prop('checked', false);

            // Open the dialog
            $('#button-form-dialog').dialog('option', 'title', 'Add New Button');
            $('#button-form-dialog').dialog('open');
        });

        // Toggle lock options visibility
        $(document).off('change', '#button-is-locked').on('change', '#button-is-locked', function() {
            toggleLockOptions();
        });

        // Toggle countdown options visibility
        $(document).off('change', '#button-countdown-enabled').on('change', '#button-countdown-enabled', function() {
            toggleCountdownOptions();
        });

        // Toggle countdown click options visibility
        $(document).off('change', '#button-countdown-click-activation').on('change', '#button-countdown-click-activation', function() {
            toggleCountdownClickOptions();
        });

        // Toggle captcha options visibility
        $(document).off('change', '#button-countdown-captcha').on('change', '#button-countdown-captcha', function() {
            toggleCaptchaOptions();
        });
    }

    /**
     * Toggle lock options visibility
     */
    function toggleLockOptions() {
        var isLocked = $('#button-is-locked').is(':checked');
        $('.lock-options').toggle(isLocked);
    }

    /**
     * Toggle countdown options visibility
     */
    function toggleCountdownOptions() {
        var isEnabled = $('#button-countdown-enabled').is(':checked');
        $('.countdown-options').toggle(isEnabled);

        // If countdown is enabled, disable milestone and lock options
        if (isEnabled) {
            $('#button-milestone-enabled').prop('checked', false).prop('disabled', true);
            $('#button-is-locked').prop('checked', false).prop('disabled', true);
            $('.milestone-options').hide();
            $('.lock-options').hide();
        } else {
            $('#button-milestone-enabled').prop('disabled', false);
            $('#button-is-locked').prop('disabled', false);
        }
    }

    /**
     * Toggle countdown click options visibility
     */
    function toggleCountdownClickOptions() {
        var isEnabled = $('#button-countdown-click-activation').is(':checked');
        $('.countdown-click-options').toggle(isEnabled);
    }

    /**
     * Toggle captcha options visibility
     */
    function toggleCaptchaOptions() {
        var isEnabled = $('#button-countdown-captcha').is(':checked');
        $('.countdown-captcha-options').toggle(isEnabled);
    }

    /**
     * Override the saveButton function
     */
    window.saveButton = function() {
        // Get form data
        var buttonId = $('#button-id').val();
        var isNew = buttonId === '0';
        var faucetId = $('#button-faucet-id').val();

        console.log('Saving button. ID:', buttonId, 'Is new:', isNew, 'Faucet ID:', faucetId);

        // Validate form
        var buttonText = $('#button-text').val().trim();
        if (!buttonText) {
            alert('Button text is required');
            return;
        }

        if (!faucetId || faucetId === '0') {
            alert('Faucet ID is missing. Please try again.');
            return;
        }

        // Get button color and hex value
        var buttonColor = $('#button-color').val();
        var buttonColorHex = $('#button-color-hex').val();

        // Create button data object
        var buttonData = {
            button_id: buttonId,
            faucet_id: faucetId,
            button_text: buttonText,
            button_size: $('#button-size').val(),
            button_color: buttonColor,
            button_color_hex: buttonColorHex,
            border_shape: $('#button-border').val(),
            redirect_url: $('#button-redirect').val(),

            // Lock options
            is_locked: $('#button-is-locked').is(':checked') ? 1 : 0,
            required_faucets: $('.required-faucet-checkbox:checked').map(function() { return $(this).val(); }).get(),
            reset_minutes: $('#button-reset-minutes').val(),
            lock_faucet: $('#button-lock-faucet').is(':checked') ? 1 : 0,

            // Countdown options
            countdown_enabled: $('#button-countdown-enabled').is(':checked') ? 1 : 0,
            countdown_seconds: $('#button-countdown-seconds').val(),
            countdown_message: $('#button-countdown-message').val(),
            countdown_click_activation: $('#button-countdown-click-activation').is(':checked') ? 1 : 0,
            countdown_click_element_id: $('#button-countdown-click-element-id').val(),
            countdown_pre_click_message: $('#button-countdown-pre-click-message').val(),
            countdown_standby: $('#button-countdown-standby').is(':checked') ? 1 : 0,

            // Captcha options
            countdown_captcha: $('#button-countdown-captcha').is(':checked') ? 1 : 0,
            countdown_captcha_type: $('#button-countdown-captcha-type').val()
        };

        // Log the button data for debugging
        console.log('Saving button with data:', buttonData);

        // Disable dialog buttons during save
        var $dialog = $('#button-form-dialog').parent();
        $dialog.find('.ui-dialog-buttonpane button').prop('disabled', true);

        // Add loading indicator
        var $buttonPane = $dialog.find('.ui-dialog-buttonpane');
        $buttonPane.append('<span class="saving-indicator">Saving...</span>');

        // Determine if we're creating or updating
        var action = isNew ? 'farmfaucet_create_button' : 'farmfaucet_update_button';

        // Create form data object
        var formData = {
            action: action,
            nonce: farmfaucet_admin.nonce
        };

        // Add button data to form data
        $.each(buttonData, function(key, value) {
            formData[key] = value;
        });

        // Send AJAX request
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                // Remove loading indicator
                $buttonPane.find('.saving-indicator').remove();
                $('#button-form-dialog .loading-overlay').remove();

                // Re-enable dialog buttons
                $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);

                if (response.success) {
                    console.log('Button saved successfully:', response);

                    // Close dialog
                    $('#button-form-dialog').dialog('close');

                    // Show success message
                    alert(isNew ? 'Button created successfully' : 'Button updated successfully');

                    // Reload the page to show updated button
                    location.reload();
                } else {
                    console.error('Error saving button:', response);
                    alert(response.data?.message || 'Error saving button');
                }
            },
            error: function(xhr, status, error) {
                // Remove loading indicator
                $buttonPane.find('.saving-indicator').remove();
                $('#button-form-dialog .loading-overlay').remove();

                // Re-enable dialog buttons
                $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);

                console.error('AJAX Error:', status, error);
                alert('Error saving button: ' + error);
            }
        });
    };

})(jQuery);
