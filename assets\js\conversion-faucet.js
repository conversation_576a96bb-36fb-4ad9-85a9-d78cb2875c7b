/**
 * Farm Faucet - Conversion Faucet JavaScript
 */

jQuery(document).ready(function($) {
    // Initialize conversion faucet functionality
    initConversionFaucet();

    /**
     * Initialize conversion faucet functionality
     */
    function initConversionFaucet() {
        // Calculate conversion amount when amount or target currency changes
        $('#conversion-amount, #target-currency').on('change input', function() {
            calculateConversionAmount();
        });

        // Enable submit button when captcha is completed
        window.enableConversionButton = function() {
            $('#convert-button').prop('disabled', false).attr('aria-disabled', 'false');
        };

        // Form submission handler
        $('#farmfaucet-conversion-form').on('submit', function(e) {
            e.preventDefault();

            // Validate form
            var amount = parseFloat($('#conversion-amount').val()) || 0;
            var minConversion = parseFloat($('#conversion-amount').attr('min')) || 0;
            var userBalance = parseFloat($('#conversion-amount').attr('max')) || 0;
            var targetCurrencyId = $('#target-currency').val();

            if (amount <= 0) {
                if (window.FarmfaucetNotifications) {
                    window.FarmfaucetNotifications.error('Please enter a valid amount');
                } else {
                    showError('Please enter a valid amount');
                }
                return false;
            }

            if (amount < minConversion) {
                if (window.FarmfaucetNotifications) {
                    window.FarmfaucetNotifications.error('Amount must be at least the minimum conversion amount');
                } else {
                    showError('Amount must be at least the minimum conversion amount');
                }
                return false;
            }

            if (amount > userBalance) {
                if (window.FarmfaucetNotifications) {
                    window.FarmfaucetNotifications.error('Amount cannot exceed your balance');
                } else {
                    showError('Amount cannot exceed your balance');
                }
                return false;
            }

            if (!targetCurrencyId) {
                if (window.FarmfaucetNotifications) {
                    window.FarmfaucetNotifications.error('Please select a currency to convert to');
                } else {
                    showError('Please select a currency to convert to');
                }
                return false;
            }

            // If all validations pass, show loading state
            $('#convert-button').prop('disabled', true).text('Processing...');

            // Show loading notification
            let loadingNotificationId = null;
            if (window.FarmfaucetNotifications) {
                loadingNotificationId = window.FarmfaucetNotifications.faucet.conversionStarted();
            }

            // Get form data
            var formData = new FormData(this);
            formData.append('action', 'process_conversion');
            formData.append('nonce', farmfaucet_ajax.nonce);

            // Send AJAX request
            $.ajax({
                url: farmfaucet_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        if (window.FarmfaucetNotifications) {
                            if (loadingNotificationId) {
                                window.FarmfaucetNotifications.hide(loadingNotificationId);
                            }
                            const fromAmount = $('#conversion-amount').val();
                            const fromCurrency = 'Source Currency'; // You might want to get this from the form
                            const toAmount = response.data.converted_amount || 'N/A';
                            const toCurrency = $('#target-currency option:selected').text();
                            window.FarmfaucetNotifications.faucet.conversionSuccess(fromAmount, fromCurrency, toAmount, toCurrency);
                        } else {
                            showSuccess(response.data.message);
                        }

                        // Update user balance
                        $('.balance').text(response.data.new_balance);

                        // Reset form
                        $('#farmfaucet-conversion-form')[0].reset();

                        // Reload captcha
                        if (typeof farmfaucetReloadCaptcha === 'function') {
                            farmfaucetReloadCaptcha();
                        }

                        // Disable button until captcha is completed again
                        $('#convert-button').prop('disabled', true).attr('aria-disabled', 'true');

                        // Update conversion history if available
                        loadConversionHistory();
                    } else {
                        // Show error notification
                        if (window.FarmfaucetNotifications) {
                            if (loadingNotificationId) {
                                window.FarmfaucetNotifications.hide(loadingNotificationId);
                            }
                            window.FarmfaucetNotifications.faucet.conversionError(response.data);
                        } else {
                            showError(response.data);
                        }

                        // Reset button
                        $('#convert-button').prop('disabled', false).text('CONVERT');

                        // Reload captcha
                        if (typeof farmfaucetReloadCaptcha === 'function') {
                            farmfaucetReloadCaptcha();
                        }
                    }
                },
                error: function() {
                    // Show error notification
                    if (window.FarmfaucetNotifications) {
                        if (loadingNotificationId) {
                            window.FarmfaucetNotifications.hide(loadingNotificationId);
                        }
                        window.FarmfaucetNotifications.faucet.conversionError('Connection error. Please try again.');
                    } else {
                        showError('Connection error. Please try again.');
                    }

                    // Reset button
                    $('#convert-button').prop('disabled', false).text('CONVERT');
                }
            });
        });

        // Load conversion history on page load
        loadConversionHistory();
    }

    /**
     * Calculate conversion amount based on input and selected currency
     */
    function calculateConversionAmount() {
        var amount = parseFloat($('#conversion-amount').val()) || 0;
        var selectedOption = $('#target-currency option:selected');
        var rate = parseFloat(selectedOption.data('rate')) || 0;
        var symbol = selectedOption.data('symbol') || '';
        var currencyType = selectedOption.data('type') || '';
        var currencyName = selectedOption.text() || '';

        if (!isNaN(amount) && !isNaN(rate)) {
            // Apply conversion rate
            var convertedAmount = amount * rate;

            // Format the amount with 8 decimal places
            $('.receive-amount').text(convertedAmount.toFixed(8) + ' ' + symbol);
            $('.receive-symbol').text(symbol);

            // Update conversion rate display
            $('.target-rate').text((rate).toFixed(8));
            $('.target-currency').text(symbol);

            // Update currency type notice
            if (currencyType === 'advertisement') {
                $('.currency-type-notice').html('<div class="ads-only-notice">Converting to <strong>' + currencyName + '</strong> (Advertisement Balance)</div>').show();
            } else {
                $('.currency-type-notice').html('').hide();
            }

            // Update hidden field for form submission
            $('#conversion-rate-field').val(rate.toFixed(8));
        }
    }

    /**
     * Load conversion history via AJAX
     */
    function loadConversionHistory() {
        var faucetId = $('.farmfaucet-conversion-history').data('faucet-id');

        if (!faucetId) return;

        $.ajax({
            url: farmfaucet_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_conversion_history',
                faucet_id: faucetId,
                nonce: farmfaucet_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('.conversion-history-container').html(response.data);
                } else {
                    $('.conversion-history-container').html('<div class="no-history">' + response.data + '</div>');
                }
            },
            error: function() {
                $('.conversion-history-container').html('<div class="error">Error loading conversion history</div>');
            }
        });
    }

    /**
     * Show success message
     */
    function showSuccess(message) {
        $('.farmfaucet-notification')
            .removeClass('error')
            .addClass('success')
            .html(message)
            .show();
    }

    /**
     * Show error message
     */
    function showError(message) {
        $('.farmfaucet-notification')
            .removeClass('success')
            .addClass('error')
            .html(message)
            .show();
    }
});
