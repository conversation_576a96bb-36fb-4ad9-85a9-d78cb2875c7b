<?php

/**
 * Telegram Bot Builder Page Class
 *
 * @package Farm Faucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Telegram Bot Builder Page Class
 */
class Farmfaucet_Tg_Bot_Builder_Page
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Add menu page
        add_action('admin_menu', array($this, 'add_menu_page'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // AJAX handlers
        add_action('wp_ajax_farmfaucet_get_bot', array($this, 'ajax_get_bot'));
        add_action('wp_ajax_farmfaucet_save_bot', array($this, 'ajax_save_bot'));
        add_action('wp_ajax_farmfaucet_delete_bot', array($this, 'ajax_delete_bot'));
        add_action('wp_ajax_farmfaucet_get_commands', array($this, 'ajax_get_commands'));
        add_action('wp_ajax_farmfaucet_get_command', array($this, 'ajax_get_command'));
        add_action('wp_ajax_farmfaucet_save_command', array($this, 'ajax_save_command'));
        add_action('wp_ajax_farmfaucet_delete_command', array($this, 'ajax_delete_command'));
    }

    /**
     * Add menu page
     */
    public function add_menu_page()
    {
        add_submenu_page(
            'farmfaucet-settings',
            __('Telegram Bot Builder', 'farmfaucet'),
            __('TG Bot Builder', 'farmfaucet'),
            'manage_options',
            'farmfaucet-tg-bot-builder',
            array($this, 'render_page')
        );
    }

    /**
     * Enqueue scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_scripts($hook)
    {
        // Only load on plugin page
        if ($hook !== 'farmfaucet_page_farmfaucet-tg-bot-builder') {
            return;
        }

        // Enqueue styles
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-modal',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-modal.css',
            array(),
            FARMFAUCET_VERSION
        );

        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-redesign',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-redesign.css',
            array('farmfaucet-tg-bot-builder-modal'),
            FARMFAUCET_VERSION
        );

        // Enqueue scripts
        wp_enqueue_script(
            'farmfaucet-tg-bot-builder-redesign-new',
            FARMFAUCET_URL . 'assets/js/tg-bot-builder-redesign-new.js',
            array('jquery'),
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Localize script
        wp_localize_script('farmfaucet-tg-bot-builder-redesign-new', 'farmfaucetTgBotBuilder', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_tg_bot_nonce'),
            'i18n' => array(
                'confirmDelete' => __('Are you sure you want to delete this bot? This action cannot be undone.', 'farmfaucet'),
                'confirmDeleteCommand' => __('Are you sure you want to delete this command? This action cannot be undone.', 'farmfaucet'),
                'errorOccurred' => __('An error occurred. Please try again.', 'farmfaucet'),
                'savingChanges' => __('Saving changes...', 'farmfaucet'),
                'changesSaved' => __('Changes saved successfully.', 'farmfaucet'),
                'testingToken' => __('Testing token...', 'farmfaucet'),
                'tokenValid' => __('Token is valid!', 'farmfaucet'),
                'tokenInvalid' => __('Token is invalid. Please check and try again.', 'farmfaucet'),
                'commandSaved' => __('Command saved successfully.', 'farmfaucet'),
                'commandDeleted' => __('Command deleted successfully.', 'farmfaucet')
            )
        ));
    }

    /**
     * Render page
     */
    public function render_page()
    {
        // Get all bots
        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');
        $bots = Farmfaucet_Tg_Bot_DB::get_bots();

?>
        <div class="wrap farmfaucet-admin-wrap">
            <h1><?php _e('Telegram Bot Builder', 'farmfaucet'); ?></h1>

            <div class="farmfaucet-admin-section tg-bot-builder-section">
                <div class="farmfaucet-admin-card">
                    <div class="card-header">
                        <h3><?php _e('Bot Management', 'farmfaucet'); ?></h3>
                    </div>
                    <div class="card-body">
                        <p class="description">
                            <?php _e('Create and manage your Telegram bots. You can create text bots for one-on-one interactions or chat bots for group moderation.', 'farmfaucet'); ?>
                        </p>

                        <!-- Bot Management Section -->
                        <div class="bot-management-section">
                            <div class="bot-management-header">
                                <button type="button" class="button button-primary add-new-bot">
                                    <span class="dashicons dashicons-plus-alt"></span> <?php _e('Add New Bot', 'farmfaucet'); ?>
                                </button>
                            </div>

                            <?php if (empty($bots)): ?>
                                <div class="no-bots-message">
                                    <div class="empty-state">
                                        <span class="dashicons dashicons-admin-site-alt3"></span>
                                        <h3><?php _e('No Telegram Bots Found', 'farmfaucet'); ?></h3>
                                        <p><?php _e('Create your first Telegram bot to get started with automated interactions.', 'farmfaucet'); ?></p>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Bot Tabs Navigation -->
                                <div class="bot-tabs-container">
                                    <div class="bot-tabs-nav">
                                        <?php foreach ($bots as $index => $bot): ?>
                                            <div class="bot-tab <?php echo $index === 0 ? 'active' : ''; ?>" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                <div class="bot-tab-inner">
                                                    <span class="bot-icon">
                                                        <span class="dashicons dashicons-admin-site-alt3"></span>
                                                    </span>
                                                    <span class="bot-name"><?php echo esc_html($bot['bot_name']); ?></span>
                                                    <span class="bot-status-indicator <?php echo $bot['is_active'] ? 'active' : 'inactive'; ?>"></span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Bot Content Panels -->
                                    <div class="bot-tabs-content">
                                        <?php foreach ($bots as $index => $bot): ?>
                                            <div class="bot-tab-content <?php echo $index === 0 ? 'active' : ''; ?>" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                <div class="bot-header">
                                                    <div class="bot-info">
                                                        <h3 class="bot-title"><?php echo esc_html($bot['bot_name']); ?></h3>
                                                        <div class="bot-meta">
                                                            <span class="bot-username">@<?php echo esc_html($bot['bot_username']); ?></span>
                                                            <span class="bot-type-badge">
                                                                <?php echo esc_html($bot['bot_type'] === 'text' ? __('Text Bot', 'farmfaucet') : __('Chat Bot', 'farmfaucet')); ?>
                                                            </span>
                                                            <span class="bot-status-badge <?php echo $bot['is_active'] ? 'active' : 'inactive'; ?>">
                                                                <?php echo $bot['is_active'] ? __('Active', 'farmfaucet') : __('Inactive', 'farmfaucet'); ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="bot-actions">
                                                        <button type="button" class="button edit-bot" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                            <span class="dashicons dashicons-edit"></span> <?php _e('Edit Bot', 'farmfaucet'); ?>
                                                        </button>
                                                        <button type="button" class="button button-link-delete delete-bot" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                            <span class="dashicons dashicons-trash"></span> <?php _e('Delete', 'farmfaucet'); ?>
                                                        </button>
                                                    </div>
                                                </div>

                                                <div class="bot-content-container">
                                                    <div class="bot-commands-panel">
                                                        <div class="commands-header">
                                                            <h4><?php _e('Bot Commands', 'farmfaucet'); ?></h4>
                                                            <button type="button" class="button add-new-command" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                                <span class="dashicons dashicons-plus-alt"></span> <?php _e('Add Command', 'farmfaucet'); ?>
                                                            </button>
                                                        </div>

                                                        <div class="commands-list" id="commands-list-<?php echo esc_attr($bot['id']); ?>">
                                                            <!-- Commands will be loaded here via AJAX -->
                                                            <div class="loading-commands">
                                                                <div class="spinner"></div>
                                                                <p><?php _e('Loading commands...', 'farmfaucet'); ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="bot-chat-panel">
                                                        <div class="chat-header">
                                                            <h4><?php _e('Test Your Bot', 'farmfaucet'); ?></h4>
                                                        </div>

                                                        <div class="telegram-chat-container">
                                                            <div class="telegram-chat-header">
                                                                <div class="telegram-chat-title">
                                                                    <span class="telegram-bot-name"><?php echo esc_html($bot['bot_name']); ?></span>
                                                                </div>
                                                            </div>

                                                            <div class="telegram-chat" id="telegram-chat-<?php echo esc_attr($bot['id']); ?>">
                                                                <!-- Welcome message -->
                                                                <div class="telegram-message bot-message">
                                                                    <div class="message-content">
                                                                        <?php _e('Welcome! This is a simulation of your Telegram bot. Type a command to test it.', 'farmfaucet'); ?>
                                                                    </div>
                                                                    <div class="message-time"><?php echo date('H:i'); ?></div>
                                                                </div>
                                                            </div>

                                                            <div class="telegram-chat-input">
                                                                <input type="text" class="telegram-message-input" placeholder="<?php _e('Type a command (e.g., /start)', 'farmfaucet'); ?>" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                                <button type="button" class="telegram-send-button" data-bot-id="<?php echo esc_attr($bot['id']); ?>">
                                                                    <span class="dashicons dashicons-arrow-right-alt"></span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Bot Command Section (Initially Hidden) -->
                        <div class="bot-commands-section" style="display: none;">
                            <div class="bot-commands-header">
                                <h3><?php _e('Bot Commands', 'farmfaucet'); ?> - <span class="current-bot-name"></span></h3>
                                <div class="bot-commands-actions">
                                    <button type="button" class="button add-new-command">
                                        <span class="dashicons dashicons-plus-alt"></span> <?php _e('Add New Command', 'farmfaucet'); ?>
                                    </button>
                                    <button type="button" class="button back-to-bots">
                                        <span class="dashicons dashicons-arrow-left-alt"></span> <?php _e('Back to Bots', 'farmfaucet'); ?>
                                    </button>
                                </div>
                            </div>

                            <div class="command-list-container">
                                <!-- Commands will be loaded here via AJAX -->
                                <div class="no-commands-message">
                                    <p><?php _e('No commands found for this bot. Add your first command to get started.', 'farmfaucet'); ?></p>
                                </div>

                                <div class="command-grid" style="display: none;">
                                    <!-- Command cards will be added here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    }

    /**
     * AJAX: Get Bot
     */
    public function ajax_get_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(array('message' => __('Bot ID is required.', 'farmfaucet')));
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Get bot
        $bot = Farmfaucet_Tg_Bot_DB::get_bot($bot_id);

        if (!$bot) {
            wp_send_json_error(array('message' => __('Bot not found.', 'farmfaucet')));
        }

        wp_send_json_success($bot);
    }

    /**
     * AJAX: Save Bot
     */
    public function ajax_save_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (
            !isset($_POST['bot_name']) || empty($_POST['bot_name']) ||
            !isset($_POST['bot_token']) || empty($_POST['bot_token']) ||
            !isset($_POST['bot_username']) || empty($_POST['bot_username']) ||
            !isset($_POST['bot_type']) || empty($_POST['bot_type'])
        ) {
            wp_send_json_error(array('message' => __('All fields are required.', 'farmfaucet')));
        }

        // Sanitize input
        $bot_data = array(
            'bot_name' => sanitize_text_field($_POST['bot_name']),
            'bot_token' => sanitize_text_field($_POST['bot_token']),
            'bot_username' => sanitize_text_field($_POST['bot_username']),
            'bot_type' => sanitize_text_field($_POST['bot_type']),
            'settings' => isset($_POST['settings']) ? sanitize_text_field($_POST['settings']) : '',
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        );

        // Check if we're updating or creating
        $bot_id = isset($_POST['bot_id']) ? intval($_POST['bot_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($bot_id > 0) {
            // Update existing bot
            $result = Farmfaucet_Tg_Bot_DB::update_bot($bot_id, $bot_data);
            $message = __('Bot updated successfully.', 'farmfaucet');
        } else {
            // Create new bot
            $result = Farmfaucet_Tg_Bot_DB::add_bot($bot_data);
            $bot_id = $result;
            $message = __('Bot created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(array('message' => __('Error saving bot. Please try again.', 'farmfaucet')));
        }

        wp_send_json_success(array(
            'message' => $message,
            'bot_id' => $bot_id
        ));
    }

    /**
     * AJAX: Delete Bot
     */
    public function ajax_delete_bot()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(array('message' => __('Bot ID is required.', 'farmfaucet')));
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete bot
        $result = Farmfaucet_Tg_Bot_DB::delete_bot($bot_id);

        if (!$result) {
            wp_send_json_error(array('message' => __('Error deleting bot. Please try again.', 'farmfaucet')));
        }

        wp_send_json_success(array(
            'message' => __('Bot deleted successfully.', 'farmfaucet')
        ));
    }

    /**
     * AJAX: Get Commands
     */
    public function ajax_get_commands()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (!isset($_POST['bot_id']) || empty($_POST['bot_id'])) {
            wp_send_json_error(array('message' => __('Bot ID is required.', 'farmfaucet')));
        }

        $bot_id = intval($_POST['bot_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Get commands
        $commands = Farmfaucet_Tg_Bot_DB::get_commands($bot_id);

        wp_send_json_success($commands);
    }

    /**
     * AJAX: Get Command
     */
    public function ajax_get_command()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (!isset($_POST['command_id']) || empty($_POST['command_id'])) {
            wp_send_json_error(array('message' => __('Command ID is required.', 'farmfaucet')));
        }

        $command_id = intval($_POST['command_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Get command
        $command = Farmfaucet_Tg_Bot_DB::get_command($command_id);

        if (!$command) {
            wp_send_json_error(array('message' => __('Command not found.', 'farmfaucet')));
        }

        wp_send_json_success($command);
    }

    /**
     * AJAX: Save Command
     */
    public function ajax_save_command()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (
            !isset($_POST['bot_id']) || empty($_POST['bot_id']) ||
            !isset($_POST['command_trigger']) || empty($_POST['command_trigger']) ||
            !isset($_POST['command_description']) || empty($_POST['command_description']) ||
            !isset($_POST['response_type']) || empty($_POST['response_type'])
        ) {
            wp_send_json_error(array('message' => __('All fields are required.', 'farmfaucet')));
        }

        // Sanitize input
        $command_data = array(
            'bot_id' => intval($_POST['bot_id']),
            'command_trigger' => sanitize_text_field($_POST['command_trigger']),
            'command_description' => sanitize_text_field($_POST['command_description']),
            'response_type' => sanitize_text_field($_POST['response_type']),
            'response_data' => isset($_POST['response_data']) ? sanitize_text_field($_POST['response_data']) : '',
            'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
        );

        // Check if we're updating or creating
        $command_id = isset($_POST['command_id']) ? intval($_POST['command_id']) : 0;

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        if ($command_id > 0) {
            // Update existing command
            $result = Farmfaucet_Tg_Bot_DB::update_command($command_id, $command_data);
            $message = __('Command updated successfully.', 'farmfaucet');
        } else {
            // Create new command
            $result = Farmfaucet_Tg_Bot_DB::add_command($command_data);
            $command_id = $result;
            $message = __('Command created successfully.', 'farmfaucet');
        }

        if (!$result) {
            wp_send_json_error(array('message' => __('Error saving command. Please try again.', 'farmfaucet')));
        }

        wp_send_json_success(array(
            'message' => $message,
            'command_id' => $command_id
        ));
    }

    /**
     * AJAX: Delete Command
     */
    public function ajax_delete_command()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'farmfaucet_tg_bot_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'farmfaucet')));
        }

        // Check required fields
        if (!isset($_POST['command_id']) || empty($_POST['command_id'])) {
            wp_send_json_error(array('message' => __('Command ID is required.', 'farmfaucet')));
        }

        $command_id = intval($_POST['command_id']);

        require_once(dirname(__FILE__) . '/class-farmfaucet-tg-bot-db.php');

        // Delete command
        $result = Farmfaucet_Tg_Bot_DB::delete_command($command_id);

        if (!$result) {
            wp_send_json_error(array('message' => __('Error deleting command. Please try again.', 'farmfaucet')));
        }

        wp_send_json_success(array(
            'message' => __('Command deleted successfully.', 'farmfaucet')
        ));
    }
}
