<?php
/**
 * Farm Faucet - Final Settings Save Test
 * 
 * This script tests if the settings save issue is finally resolved
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_config_path = dirname(__FILE__) . '/../../wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress configuration not found.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet - Final Settings Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .success { color: #4CAF50; background: #f0f8f0; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; border-radius: 4px; }
        .error { color: #f44336; background: #fdf0f0; padding: 15px; border-left: 4px solid #f44336; margin: 15px 0; border-radius: 4px; }
        .info { color: #2196F3; background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; border-radius: 4px; }
        .warning { color: #ff9800; background: #fff8f0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0; border-radius: 4px; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px 10px 0; }
        .btn:hover { background: #45a049; }
        .critical { background: #ffebee; border-left: 4px solid #f44336; padding: 20px; margin: 20px 0; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f5f5f5; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 Farm Faucet - Final Settings Save Test</h1>
    <p>Testing if the settings save issue has been resolved after fixing conflicting registrations.</p>

<?php

echo '<div class="info">';
echo '<h3>🔍 Root Cause Analysis Summary</h3>';
echo '<p><strong>ISSUE IDENTIFIED:</strong> Multiple classes were registering settings with the same group name <code>farmfaucet_settings</code>, causing conflicts.</p>';
echo '<p><strong>CLASSES INVOLVED:</strong></p>';
echo '<ul>';
echo '<li><strong>Admin Class:</strong> Main settings (kept as farmfaucet_settings)</li>';
echo '<li><strong>Advertising Class:</strong> Changed to farmfaucet_advertising_settings</li>';
echo '<li><strong>Currency Maker Class:</strong> Changed to farmfaucet_currency_settings</li>';
echo '<li><strong>Referral Class:</strong> Changed to farmfaucet_referral_settings</li>';
echo '<li><strong>TG Bot Builder Class:</strong> Changed to farmfaucet_tg_bot_settings</li>';
echo '</ul>';
echo '<p><strong>ADDITIONAL FIXES:</strong></p>';
echo '<ul>';
echo '<li>Removed problematic static method callback: <code>[\'Farmfaucet_Security\', \'validate_url\']</code></li>';
echo '<li>Removed encryption callbacks: <code>[$this, \'encrypt_api_key_setting\']</code></li>';
echo '<li>Removed encryption callback: <code>[$this, \'sanitize_captcha_secret\']</code></li>';
echo '</ul>';
echo '</div>';

// Test 1: Check current settings registration
echo '<div class="step">';
echo '<h3>📋 Step 1: Current Settings Registration</h3>';

$registered_settings = get_registered_settings();
$farmfaucet_main_settings = [];
$farmfaucet_other_settings = [];

foreach ($registered_settings as $setting => $data) {
    if (strpos($setting, 'farmfaucet_') === 0) {
        if (isset($data['group']) && $data['group'] === 'farmfaucet_settings') {
            $farmfaucet_main_settings[$setting] = $data;
        } else {
            $farmfaucet_other_settings[$setting] = $data;
        }
    }
}

echo '<h4>Main Settings Group (farmfaucet_settings):</h4>';
if (!empty($farmfaucet_main_settings)) {
    echo '<table>';
    echo '<tr><th>Setting Name</th><th>Sanitize Callback</th><th>Current Value</th></tr>';
    
    foreach ($farmfaucet_main_settings as $setting => $args) {
        $current_value = get_option($setting, 'NOT SET');
        $callback = isset($args['sanitize_callback']) ? 
            (is_array($args['sanitize_callback']) ? 
                implode('::', $args['sanitize_callback']) : 
                $args['sanitize_callback']) : 
            'None';
        
        echo '<tr>';
        echo '<td>' . esc_html($setting) . '</td>';
        echo '<td>' . esc_html($callback) . '</td>';
        echo '<td>' . esc_html(is_string($current_value) ? substr($current_value, 0, 50) : json_encode($current_value)) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '<div class="success"><p>✅ Found ' . count($farmfaucet_main_settings) . ' main settings</p></div>';
} else {
    echo '<div class="error"><p>❌ No main settings found!</p></div>';
}

echo '<h4>Other Settings Groups:</h4>';
if (!empty($farmfaucet_other_settings)) {
    echo '<div class="info"><p>ℹ️ Found ' . count($farmfaucet_other_settings) . ' settings in other groups (this is good - no conflicts)</p></div>';
} else {
    echo '<div class="warning"><p>⚠️ No other settings found - modules may not be loaded</p></div>';
}

echo '</div>';

// Test 2: Test settings save
echo '<div class="step">';
echo '<h3>💾 Step 2: Settings Save Test</h3>';

if (isset($_POST['test_final_save'])) {
    echo '<div class="info"><p>🧪 Testing settings save...</p></div>';
    
    $test_settings = [
        'farmfaucet_captcha_type' => 'hcaptcha',
        'farmfaucet_hcaptcha_sitekey' => 'test_site_key_' . time(),
        'farmfaucet_hcaptcha_secret' => 'test_secret_key_' . time(),
        'farmfaucet_faucetpay_api' => 'test_api_key_' . time(),
        'farmfaucet_redirect_url' => 'https://example.com/test-' . time(),
        'farmfaucet_daily_reset' => '00:00'
    ];
    
    $success_count = 0;
    $total_count = count($test_settings);
    
    foreach ($test_settings as $setting => $value) {
        try {
            $old_value = get_option($setting);
            $result = update_option($setting, $value);
            
            if ($result || get_option($setting) === $value) {
                echo '<div class="success"><p>✅ ' . esc_html($setting) . ' saved successfully</p></div>';
                $success_count++;
                
                // Restore old value
                if ($old_value !== false) {
                    update_option($setting, $old_value);
                } else {
                    delete_option($setting);
                }
            } else {
                echo '<div class="error"><p>❌ ' . esc_html($setting) . ' failed to save</p></div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><p>❌ ' . esc_html($setting) . ' error: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
    
    if ($success_count === $total_count) {
        echo '<div class="success critical">';
        echo '<h4>🎉 ALL SETTINGS SAVED SUCCESSFULLY!</h4>';
        echo '<p>The settings save issue has been completely resolved!</p>';
        echo '<p><strong>Success Rate:</strong> ' . $success_count . '/' . $total_count . ' (100%)</p>';
        echo '</div>';
    } else {
        echo '<div class="error critical">';
        echo '<h4>❌ Some Settings Still Failing</h4>';
        echo '<p><strong>Success Rate:</strong> ' . $success_count . '/' . $total_count . ' (' . round(($success_count / $total_count) * 100, 1) . '%)</p>';
        echo '</div>';
    }
} else {
    echo '<form method="post" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">';
    echo '<h4>Test All Main Settings Save</h4>';
    echo '<p>This will test saving all the main settings that were causing issues.</p>';
    echo '<p><input type="submit" name="test_final_save" value="Test Settings Save" class="btn"></p>';
    echo '</form>';
}

echo '</div>';

// Test 3: WordPress settings form test
echo '<div class="step">';
echo '<h3>🔧 Step 3: WordPress Settings Form Test</h3>';

echo '<div class="info">';
echo '<h4>Test the Actual Settings Form</h4>';
echo '<p>Use this form to test the actual WordPress settings API that your plugin uses:</p>';
echo '</div>';

echo '<form method="post" action="options.php" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">';
settings_fields('farmfaucet_settings');
echo '<table>';
echo '<tr><th>Setting</th><th>Current Value</th><th>Test Value</th></tr>';

$test_fields = [
    'farmfaucet_captcha_type' => ['type' => 'select', 'options' => ['hcaptcha', 'recaptcha', 'turnstile']],
    'farmfaucet_hcaptcha_sitekey' => ['type' => 'text'],
    'farmfaucet_hcaptcha_secret' => ['type' => 'text'],
    'farmfaucet_faucetpay_api' => ['type' => 'text'],
    'farmfaucet_redirect_url' => ['type' => 'url'],
    'farmfaucet_daily_reset' => ['type' => 'time']
];

foreach ($test_fields as $field => $config) {
    $current_value = get_option($field, '');
    echo '<tr>';
    echo '<td><strong>' . esc_html($field) . '</strong></td>';
    echo '<td>' . esc_html(substr($current_value, 0, 30)) . '</td>';
    echo '<td>';
    
    if ($config['type'] === 'select') {
        echo '<select name="' . esc_attr($field) . '">';
        foreach ($config['options'] as $option) {
            echo '<option value="' . esc_attr($option) . '" ' . selected($current_value, $option, false) . '>' . esc_html($option) . '</option>';
        }
        echo '</select>';
    } else {
        echo '<input type="' . esc_attr($config['type']) . '" name="' . esc_attr($field) . '" value="' . esc_attr($current_value) . '" style="width: 200px; padding: 5px;">';
    }
    
    echo '</td>';
    echo '</tr>';
}

echo '</table>';
submit_button('Save Settings via WordPress API');
echo '</form>';

echo '</div>';

?>

<div style="text-align: center; margin: 30px 0;">
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=settings'); ?>" class="btn">🚀 Test Settings Tab</a>
    <a href="<?php echo admin_url('admin.php?page=farmfaucet&tab=faucets'); ?>" class="btn" style="background: #2196F3;">🔧 Test Faucets Tab</a>
</div>

<div style="background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #4CAF50;">
    <h3 style="color: #2e7d32; margin-top: 0;">🎯 Final Resolution Summary</h3>
    
    <div style="color: #2e7d32;">
        <h4>✅ Issues Fixed:</h4>
        <ul>
            <li><strong>Settings Group Conflicts:</strong> Separated each module into its own settings group</li>
            <li><strong>Static Method Callbacks:</strong> Replaced problematic static callbacks with safe functions</li>
            <li><strong>Encryption Callbacks:</strong> Removed encryption callbacks causing server errors</li>
            <li><strong>Duplicate Registrations:</strong> Eliminated conflicts between multiple classes</li>
        </ul>
        
        <h4>🔧 Technical Changes:</h4>
        <ul>
            <li><strong>Admin Class:</strong> Uses farmfaucet_settings (main group)</li>
            <li><strong>Advertising:</strong> Uses farmfaucet_advertising_settings</li>
            <li><strong>Currency Maker:</strong> Uses farmfaucet_currency_settings</li>
            <li><strong>Referral System:</strong> Uses farmfaucet_referral_settings</li>
            <li><strong>TG Bot Builder:</strong> Uses farmfaucet_tg_bot_settings</li>
        </ul>
        
        <h4>🎉 Expected Results:</h4>
        <ul>
            <li>No more "website unable to handle request" errors</li>
            <li>All settings save correctly without conflicts</li>
            <li>API keys and captcha keys save properly</li>
            <li>Plugin functions normally without server errors</li>
        </ul>
    </div>
</div>

</body>
</html>
