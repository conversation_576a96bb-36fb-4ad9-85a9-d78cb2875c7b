<?php

/**
 * Farmfaucet Database Updater for Button Color Hex
 *
 * Adds the button_color_hex column to the buttons table
 */
class Farmfaucet_DB_Updater_Button_Color_Hex
{
    /**
     * Run the update to add the button_color_hex column
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

        // Check if the buttons table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add button_color_hex column if it doesn't exist
        if (!in_array('button_color_hex', $column_names)) {
            $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN button_color_hex varchar(50) DEFAULT '' AFTER button_color");
            error_log('Farmfaucet: Added button_color_hex column to buttons table');
        } else {
            // Update column size if it exists
            $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN button_color_hex varchar(50) DEFAULT ''");
            error_log('Farmfaucet: Updated button_color_hex column in buttons table');
        }
    }
}
