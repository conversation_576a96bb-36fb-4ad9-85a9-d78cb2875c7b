/**
 * Farm Faucet Admin Milestone Display Fix
 * 
 * Fixes the display of milestone card and compact views in the admin panel
 */

/* Fix for milestone display style options */
.milestone-display-style-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    margin-bottom: 20px;
}

.milestone-display-style-option {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    width: 200px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.milestone-display-style-option:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.milestone-display-style-option.selected {
    border-color: #2271b1;
    background-color: #f0f7fc;
}

.milestone-display-style-option input[type="radio"] {
    position: absolute;
    top: 10px;
    right: 10px;
    margin: 0;
    width: auto !important;
    height: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}

/* Style preview containers */
.style-preview {
    margin-bottom: 15px;
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 10px;
    background: #f9f9f9;
    min-height: 120px;
    overflow: hidden;
}

/* Card view preview */
.card-view-preview {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 10px;
    text-align: center;
}

.preview-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.preview-percentage {
    color: #4CAF50;
    font-weight: bold;
    margin-bottom: 10px;
}

.preview-progress-container {
    background: #f0f0f0;
    border-radius: 10px;
    height: 20px;
    margin-bottom: 10px;
    overflow: hidden;
    position: relative;
}

.preview-progress-bar {
    background: #4CAF50;
    height: 100%;
    width: 75%;
    border-radius: 10px;
}

.preview-tasks {
    text-align: left;
    margin-top: 10px;
}

.preview-task {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.preview-task-icon {
    color: #4CAF50;
    margin-right: 5px;
}

/* Compact view preview */
.compact-view-preview {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 10px;
}

.compact-view-preview .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.compact-view-preview .preview-progress-container {
    margin-bottom: 5px;
}

/* Style name and description */
.style-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.style-description {
    color: #666;
    font-size: 0.9em;
}

/* Fix for color pickers */
.wp-picker-container {
    display: inline-block;
}

.wp-color-result {
    margin: 0 6px 0 0 !important;
}

.wp-picker-input-wrap {
    display: inline-block !important;
    vertical-align: top;
}

.wp-picker-holder {
    position: absolute;
    z-index: 100;
}

/* Fix for checkbox styling */
.form-field input[type="checkbox"] {
    width: auto !important;
    height: auto !important;
    margin-right: 8px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

/* Fix for radio button styling */
.form-field input[type="radio"] {
    width: auto !important;
    height: auto !important;
    margin-right: 5px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

/* Fix for milestone appearance options */
.milestone-appearance-options {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.milestone-card-bg-options,
.milestone-bar-style-options {
    margin-top: 15px;
    padding: 10px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 5px;
}

.milestone-card-solid-color,
.milestone-card-gradient-colors,
.milestone-solid-color,
.milestone-gradient-colors {
    margin-top: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 5px;
}

/* Fix for color picker labels */
.color-picker-label {
    display: inline-block;
    width: 120px;
    margin-right: 10px;
}
