/**
 * Farm Faucet - <PERSON><PERSON>ton CSS
 *
 * Styles for the captcha button feature
 */

/* Captcha wrapper */
.farmfaucet-captcha-wrapper {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
    display: block;
    visibility: visible;
    opacity: 1;
}

/* Captcha message */
.farmfaucet-captcha-message {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

/* Captcha container */
.h-captcha-wrapper,
.g-recaptcha-wrapper,
.cf-turnstile-wrapper {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 15px 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Captcha elements */
.h-captcha,
.g-recaptcha,
.cf-turnstile {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 78px !important;
}

/* Button appear animation */
.farmfaucet-button-appear {
    animation: buttonAppear 0.5s ease-in-out;
}

@keyframes buttonAppear {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Main button wrapper */
.farmfaucet-main-button-wrapper {
    margin-top: 15px;
}
