/**
 * Farm Faucet - Master Faucet Form Handler
 * Handles ALL faucet form functionality including type switching, appearance settings, and saving
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Master Faucet Form Handler loaded');

        // Prevent WordPress color picker from initializing on our appearance section
        preventWordPressColorPicker();

        // Initialize all form handlers
        initFaucetTypeHandling();
        initColorCircles();
        initFormBackgroundHandlers();
        initFormValidation();
        initFaucetFormHandlers();
    });

    /**
     * Prevent WordPress color picker from interfering with our custom appearance section
     */
    function preventWordPressColorPicker() {
        // Remove wp-color-picker class from our appearance section inputs
        $(document).on('DOMNodeInserted', function() {
            $('.appearance-settings-container input[type="color"]').removeClass('wp-color-picker');
        });

        // Override wpColorPicker initialization for our section
        if ($.fn.wpColorPicker) {
            const originalWpColorPicker = $.fn.wpColorPicker;
            $.fn.wpColorPicker = function(options) {
                // Don't initialize color picker for our appearance section
                if ($(this).closest('.appearance-settings-container').length > 0) {
                    return this;
                }
                return originalWpColorPicker.call(this, options);
            };
        }
    }

    /**
     * Initialize faucet type handling
     */
    function initFaucetTypeHandling() {
        // Function to show/hide fields based on faucet type
        function toggleFaucetTypeFields() {
            const selectedType = $('#faucet-type').val();
            console.log('Selected faucet type:', selectedType);

            // Hide all type-specific fields
            $('.faucet-type-fields').hide();

            // Show fields for the selected type
            $('.' + selectedType + '-faucet-fields').show();

            // Update amount field ID based on type to avoid conflicts
            updateAmountFieldForType(selectedType);
        }

        // Update amount field to use type-specific IDs
        function updateAmountFieldForType(type) {
            const $stageAmount = $('#faucet-amount');
            const $dummyAmount = $('#dummy-amount');

            // Reset all amount fields
            $stageAmount.removeAttr('required');
            $dummyAmount.removeAttr('required');

            // Set the appropriate field as required
            if (type === 'stage') {
                $stageAmount.attr('required', true);
            } else if (type === 'dummy') {
                $dummyAmount.attr('required', true);
            }
        }

        // Initialize on page load
        toggleFaucetTypeFields();

        // Listen for changes to the faucet type dropdown
        $('#faucet-type').on('change', toggleFaucetTypeFields);
    }

    /**
     * Initialize color circle functionality
     */
    function initColorCircles() {
        $(document).on('click', '.color-circle', function() {
            const $circle = $(this);
            const color = $circle.data('color');
            const hex = $circle.data('hex');
            
            // Remove active class from all circles
            $('.color-circle').removeClass('active');
            
            // Add active class to clicked circle
            $circle.addClass('active');
            
            // Update hidden input
            $('#faucet-color').val(color);
            
            // Update related color fields
            updateRelatedColors(hex);
            
            console.log('Color selected:', color, hex);
        });
    }

    /**
     * Update related color fields when main color is changed
     */
    function updateRelatedColors(hex) {
        // Update border and button colors to match if they haven't been manually changed
        if ($('#faucet-border-color').val() === $('#faucet-border-color').data('original') || !$('#faucet-border-color').data('original')) {
            $('#faucet-border-color').val(hex).data('original', hex);
        }
        
        if ($('#faucet-button-color').val() === $('#faucet-button-color').data('original') || !$('#faucet-button-color').data('original')) {
            $('#faucet-button-color').val(hex).data('original', hex);
        }
        
        if ($('#faucet-text-color').val() === $('#faucet-text-color').data('original') || !$('#faucet-text-color').data('original')) {
            $('#faucet-text-color').val(hex).data('original', hex);
        }
    }

    /**
     * Initialize form background handlers
     */
    function initFormBackgroundHandlers() {
        // Handle transparent background toggle
        $(document).on('change', '#faucet-transparent-bg', function() {
            updateBackgroundOptions();
        });

        // Handle form transparent toggle
        $(document).on('change', '#faucet-form-transparent', function() {
            updateFormBackgroundOptions();
        });

        // Handle background style change
        $(document).on('change', 'input[name="bg_style"]', function() {
            updateBackgroundStyleOptions();
        });
    }

    /**
     * Update background options visibility
     */
    function updateBackgroundOptions() {
        if ($('#faucet-transparent-bg').is(':checked')) {
            $('.faucet-bg-options').slideUp();
        } else {
            $('.faucet-bg-options').slideDown();
            updateFormBackgroundOptions();
        }
    }

    /**
     * Update form background options visibility
     */
    function updateFormBackgroundOptions() {
        if ($('#faucet-form-transparent').is(':checked')) {
            $('#form-bg-color-field').slideUp().addClass('hidden');
        } else {
            if (!$('#faucet-transparent-bg').is(':checked')) {
                $('#form-bg-color-field').slideDown().removeClass('hidden');
            }
        }
    }

    /**
     * Update background style options
     */
    function updateBackgroundStyleOptions() {
        const style = $('input[name="bg_style"]:checked').val();
        
        if (style === 'gradient') {
            $('.bg-solid-option').hide();
            $('.bg-gradient-options').show();
        } else {
            $('.bg-solid-option').show();
            $('.bg-gradient-options').hide();
        }
    }

    /**
     * Initialize form validation
     */
    function initFormValidation() {
        // Validate amount field for decimal numbers
        $(document).on('input', '#faucet-amount', function() {
            validateAmountField($(this));
        });

        // Validate other numeric fields
        $(document).on('input', '#faucet-cooldown, #min-withdrawal', function() {
            validateNumericField($(this));
        });
    }

    /**
     * Validate amount field (allows decimals)
     */
    function validateAmountField($field) {
        const value = $field.val();
        const isValid = /^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0;
        
        if (!isValid && value !== '') {
            $field.addClass('invalid');
            showFieldError($field, 'Please enter a valid amount (e.g., 0.001, 1, 1.5)');
        } else {
            $field.removeClass('invalid');
            hideFieldError($field);
        }
        
        return isValid;
    }

    /**
     * Validate numeric field (integers only)
     */
    function validateNumericField($field) {
        const value = $field.val();
        const isValid = /^\d*$/.test(value) && parseInt(value) >= 0;
        
        if (!isValid && value !== '') {
            $field.addClass('invalid');
            showFieldError($field, 'Please enter a valid number');
        } else {
            $field.removeClass('invalid');
            hideFieldError($field);
        }
        
        return isValid;
    }

    /**
     * Show field error
     */
    function showFieldError($field, message) {
        const $error = $field.siblings('.field-error');
        if ($error.length === 0) {
            $field.after('<div class="field-error" style="color: #f44336; font-size: 12px; margin-top: 5px;">' + message + '</div>');
        } else {
            $error.text(message);
        }
    }

    /**
     * Hide field error
     */
    function hideFieldError($field) {
        $field.siblings('.field-error').remove();
    }

    /**
     * Initialize faucet form handlers
     */
    function initFaucetFormHandlers() {
        // Override the saveFaucet function
        window.saveFaucet = saveFaucetWithFormBackground;

        // Override the editFaucet function
        window.editFaucet = editFaucetWithAllData;

        // Handle faucet data loading
        $(document).on('farmfaucet:faucet_loaded', function(event, faucet) {
            loadFaucetAppearanceData(faucet);
        });
    }

    /**
     * Edit faucet with proper data loading for all types
     */
    function editFaucetWithAllData(faucetId) {
        console.log('Editing faucet with ID:', faucetId);

        // Reset the form first
        resetFaucetForm();

        // Set the faucet ID
        $('#faucet-id').val(faucetId);

        // Show loading indicator
        $('#faucet-form-dialog').append('<div class="loading-overlay"><div class="loading-spinner">Loading...</div></div>');

        // Open the dialog
        $('#faucet-form-dialog').dialog('option', 'title', 'Edit Faucet');
        $('#faucet-form-dialog').dialog('open');

        // Load faucet data via AJAX
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_faucet',
                nonce: farmfaucet_admin.nonce,
                faucet_id: faucetId
            },
            success: function(response) {
                // Remove loading indicator
                $('#faucet-form-dialog .loading-overlay').remove();

                console.log('Faucet data loaded:', response);

                if (response.success && response.data) {
                    loadFaucetData(response.data);
                } else {
                    alert(response.data?.message || 'Failed to load faucet data');
                }
            },
            error: function() {
                // Remove loading indicator
                $('#faucet-form-dialog .loading-overlay').remove();
                alert(farmfaucet_admin.strings.error);
            }
        });
    }

    /**
     * Reset faucet form to default state
     */
    function resetFaucetForm() {
        $('#faucet-form')[0].reset();
        $('#faucet-id').val('0');
        $('.color-circle').removeClass('active');
        $('.color-circle[data-color="green"]').addClass('active');
        $('#faucet-color').val('green');
        $('#faucet-type').val('stage').trigger('change');
    }

    /**
     * Save faucet with all settings properly handled
     */
    function saveFaucetWithFormBackground() {
        const faucetId = $('#faucet-id').val();
        const isNew = faucetId === '0';

        // Validate form before saving
        if (!validateForm()) {
            return false;
        }

        // Get basic form data
        const faucetType = $('#faucet-type').val() || 'stage';
        const captchaType = $('input[name="captcha_type"]:checked').val() || '';
        const isEnabled = $('#faucet-is-enabled').is(':checked') ? 1 : 0;

        // Get amount based on faucet type
        let amount = '0.001';
        if (faucetType === 'stage') {
            amount = $('#faucet-amount').val() || '0.001';
        } else if (faucetType === 'dummy') {
            amount = $('#dummy-amount').val() || '1';
        }

        // Prepare base form data
        const formData = {
            action: isNew ? 'farmfaucet_create_faucet' : 'farmfaucet_update_faucet',
            nonce: farmfaucet_admin.nonce,
            faucet_id: faucetId,
            name: $('#faucet-name').val(),
            faucet_type: faucetType,
            currency: $('#faucet-currency').val() || 'LTC',
            amount: amount,
            cooldown: $('#faucet-cooldown').val() || '3600',
            api_key: $('#faucet-api-key').val(),
            shortcode: $('#faucet-shortcode').val(),
            captcha_type: captchaType,
            is_enabled: isEnabled,

            // Complete appearance settings
            faucet_color: $('#faucet-color').val() || 'green',
            transparent_bg: $('#faucet-transparent-bg').is(':checked') ? 1 : 0,
            bg_style: $('input[name="bg_style"]:checked').val() || 'solid',
            bg_color: $('#faucet-bg-color').val() || '#f8fff8',
            bg_gradient_start: $('#faucet-gradient-start').val() || '#f8fff8',
            bg_gradient_end: $('#faucet-gradient-end').val() || '#e8f5e9',
            text_color: $('#faucet-text-color').val() || '#4CAF50',
            text_shadow: $('#faucet-text-shadow').val() || 'none',
            button_color: $('#faucet-button-color').val() || '#4CAF50',
            border_color: $('#faucet-border-color').val() || '#4CAF50',
            border_radius: $('#faucet-border-radius').val() || '8px',

            // Form background settings
            form_bg_color: $('#faucet-form-bg-color').val() || '#ffffff',
            form_transparent: $('#faucet-form-transparent').is(':checked') ? 1 : 0
        };

        // Add type-specific fields
        if (faucetType === 'dummy') {
            formData.currency_id = $('#dummy-currency-id').val() || 0;
            formData.view_style = $('#dummy-view-style').val() || 'default';
        } else if (faucetType === 'withdrawal') {
            formData.currency_id = $('#withdrawal-currency-id').val() || 0;
            formData.min_withdrawal = $('#min-withdrawal').val() || '1';
            formData.available_currencies = getSelectedCurrencies();
        } else if (faucetType === 'conversion') {
            formData.currency_id = $('#conversion-source-currency-id').val() || 0;
            formData.min_conversion = $('#min-conversion').val() || '1';
            formData.conversion_currencies = getSelectedConversionCurrencies();
            formData.ads_only_conversion = $('#ads-only-conversion').is(':checked') ? 1 : 0;
        }

        console.log('Saving faucet with data:', formData);

        // Show loading state
        $('#faucet-form-dialog').find('button').prop('disabled', true);

        // Send AJAX request
        $.ajax({
            url: farmfaucet_admin.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Faucet save response:', response);

                if (response.success) {
                    alert(isNew ? farmfaucet_admin.strings.create_success : farmfaucet_admin.strings.update_success);
                    $('#faucet-form-dialog').dialog('close');
                    location.reload(); // Reload to show updated faucet
                } else {
                    alert(response.data.message || farmfaucet_admin.strings.error);
                    $('#faucet-form-dialog').find('button').prop('disabled', false);
                }
            },
            error: function() {
                alert(farmfaucet_admin.strings.error);
                $('#faucet-form-dialog').find('button').prop('disabled', false);
            }
        });

        return true;
    }

    /**
     * Validate the entire form
     */
    function validateForm() {
        let isValid = true;

        // Validate required fields
        const requiredFields = ['#faucet-name', '#faucet-shortcode'];
        requiredFields.forEach(function(selector) {
            const $field = $(selector);
            if (!$field.val().trim()) {
                showFieldError($field, 'This field is required');
                isValid = false;
            }
        });

        // Validate amount field
        if (!validateAmountField($('#faucet-amount'))) {
            isValid = false;
        }

        // Validate cooldown field
        if (!validateNumericField($('#faucet-cooldown'))) {
            isValid = false;
        }

        return isValid;
    }

    /**
     * Load complete faucet data into form
     */
    function loadFaucetData(faucet) {
        // Set basic fields
        $('#faucet-name').val(faucet.name || '');
        $('#faucet-shortcode').val(faucet.shortcode || '');
        $('#faucet-cooldown').val(faucet.cooldown || '3600');
        $('#faucet-api-key').val('').attr('placeholder', faucet.api_key ? 'API key is set. Leave empty to keep current key.' : '');
        $('#faucet-is-enabled').prop('checked', faucet.is_enabled == 1);

        // Set faucet type and trigger change to show appropriate fields
        const faucetType = faucet.faucet_type || 'stage';
        $('#faucet-type').val(faucetType).trigger('change');

        // Set type-specific fields
        if (faucetType === 'stage') {
            $('#faucet-currency').val(faucet.currency || 'LTC');
            $('#faucet-amount').val(faucet.amount || '0.001');
        } else if (faucetType === 'dummy') {
            $('#dummy-currency-id').val(faucet.currency_id || 0);
            $('#dummy-amount').val(faucet.amount || '1');
            $('#dummy-view-style').val(faucet.view_style || 'default');
        } else if (faucetType === 'withdrawal') {
            $('#withdrawal-currency-id').val(faucet.currency_id || 0);
            $('#min-withdrawal').val(faucet.min_withdrawal || '1');

            // Set available currencies
            if (faucet.available_currencies) {
                const currencies = faucet.available_currencies.split(',');
                $('input[name="available_currencies[]"]').prop('checked', false);
                currencies.forEach(function(currency) {
                    $('input[name="available_currencies[]"][value="' + currency.trim() + '"]').prop('checked', true);
                });
            }
        } else if (faucetType === 'conversion') {
            $('#conversion-source-currency-id').val(faucet.currency_id || 0);
            $('#min-conversion').val(faucet.min_conversion || '1');
            $('#ads-only-conversion').prop('checked', faucet.ads_only_conversion == 1);

            // Set conversion currencies
            if (faucet.conversion_currencies) {
                const currencies = faucet.conversion_currencies.split(',');
                $('input[name="conversion_currencies[]"]').prop('checked', false);
                currencies.forEach(function(currency) {
                    $('input[name="conversion_currencies[]"][value="' + currency.trim() + '"]').prop('checked', true);
                });
            }
        }

        // Set captcha type
        if (faucet.captcha_type) {
            $('input[name="captcha_type"][value="' + faucet.captcha_type + '"]').prop('checked', true);
        }

        // Load appearance data
        loadFaucetAppearanceData(faucet);
    }

    /**
     * Load faucet appearance data when editing
     */
    function loadFaucetAppearanceData(faucet) {
        console.log('Loading appearance data:', faucet);

        // Set color circle
        if (faucet.faucet_color) {
            $('.color-circle').removeClass('active');
            $('.color-circle[data-color="' + faucet.faucet_color + '"]').addClass('active');
            $('#faucet-color').val(faucet.faucet_color);

            // Update related colors if they match the theme
            const colorHex = $('.color-circle[data-color="' + faucet.faucet_color + '"]').data('hex');
            if (colorHex) {
                updateRelatedColors(colorHex);
            }
        }

        // Set appearance settings
        $('#faucet-transparent-bg').prop('checked', faucet.transparent_bg == 1);
        $('input[name="bg_style"][value="' + (faucet.bg_style || 'solid') + '"]').prop('checked', true);
        $('#faucet-bg-color').val(faucet.bg_color || '#f8fff8');
        $('#faucet-gradient-start').val(faucet.bg_gradient_start || '#f8fff8');
        $('#faucet-gradient-end').val(faucet.bg_gradient_end || '#e8f5e9');
        $('#faucet-text-color').val(faucet.text_color || '#4CAF50');
        $('#faucet-text-shadow').val(faucet.text_shadow || 'none');
        $('#faucet-button-color').val(faucet.button_color || '#4CAF50');
        $('#faucet-border-color').val(faucet.border_color || '#4CAF50');
        $('#faucet-border-radius').val(faucet.border_radius || '8px');

        // Set form background fields
        $('#faucet-form-bg-color').val(faucet.form_bg_color || '#ffffff');
        $('#faucet-form-transparent').prop('checked', faucet.form_transparent == 1);

        // Update visibility based on loaded data
        updateBackgroundOptions();
        updateFormBackgroundOptions();
        updateBackgroundStyleOptions();

        // Remove any WordPress color picker elements that might have been added
        setTimeout(function() {
            $('.appearance-settings-container .wp-picker-container').remove();
            $('.appearance-settings-container .iris-picker').remove();
        }, 100);
    }

    /**
     * Get selected currencies for withdrawal faucets
     */
    function getSelectedCurrencies() {
        const currencies = [];
        $('input[name="available_currencies[]"]:checked').each(function() {
            currencies.push($(this).val());
        });
        return currencies;
    }

    /**
     * Get selected conversion currencies
     */
    function getSelectedConversionCurrencies() {
        const currencies = [];
        $('input[name="conversion_currencies[]"]:checked').each(function() {
            currencies.push($(this).val());
        });
        return currencies;
    }

})(jQuery);
