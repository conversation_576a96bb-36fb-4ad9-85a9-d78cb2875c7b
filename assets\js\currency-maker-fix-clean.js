/**
 * Farm Faucet - Currency Maker Fix
 * This script fixes issues with the currency maker functionality
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Currency Maker Fix Clean loaded');
        initCurrencyMakerFix();
    });

    /**
     * Initialize Currency Maker Fix
     */
    function initCurrencyMakerFix() {
        // Remove any existing event handlers to prevent duplicates
        $('#farmfaucet-save-currency').off('click');
        
        // Add our clean event handler
        $('#farmfaucet-save-currency').on('click', function(e) {
            e.preventDefault();
            saveCurrency();
        });
        
        // Initialize color picker if it exists
        if ($.fn.wpColorPicker) {
            $('.color-picker').wpColorPicker({
                change: function(event, ui) {
                    $(this).val(ui.color.toString());
                    console.log('Color picker changed to:', ui.color.toString());
                }
            });
        }
    }
    
    /**
     * Save currency
     */
    function saveCurrency() {
        // Get form data
        const currencyId = $('#currency-id').val();
        const isUpdate = currencyId !== '';
        
        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-currency');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');
        
        // Get color value directly from the input
        let colorValue = $('#currency-color').val();
        if (!colorValue || colorValue === '') {
            colorValue = '#4CAF50'; // Default color
        }
        
        console.log('Color value before submission:', colorValue);
        
        // Prepare form data
        const formData = {
            action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
            nonce: farmfaucetCurrencyMakerAdmin.nonce,
            name: $('#currency-name').val(),
            code: $('#currency-code').val(),
            symbol: $('#currency-symbol').val(),
            base_currency: $('#currency-base').val(),
            exchange_rate: $('#currency-rate').val(),
            color: colorValue,
            icon: $('#currency-icon').val(),
            currency_type: $('#currency-type').val(),
            is_active: $('#currency-active').is(':checked') ? 1 : 0
        };
        
        // Add currency ID if updating
        if (isUpdate) {
            formData.currency_id = currencyId;
        }
        
        console.log('Sending form data:', formData);
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Currency save response:', response);
                
                if (response.success) {
                    alert(response.data.message || (isUpdate ? 'Currency updated successfully' : 'Currency created successfully'));
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(response.data.message || (isUpdate ? 'Failed to update currency' : 'Failed to create currency'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $saveButton.prop('disabled', false).text(originalButtonText);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                alert('Error: ' + errorMessage);
            }
        });
    }
})(jQuery);
