/**
 * View Style Fix for Farm Faucet
 *
 * This CSS file fixes the view style issues in the Farm Faucet plugin.
 */

/* Default view style */
.farmfaucet-container.dummy-faucet.default-view {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color, #4CAF50);
}

.farmfaucet-container.dummy-faucet.default-view .farmfaucet-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.farmfaucet-container.dummy-faucet.default-view .farmfaucet-form {
    background-color: var(--form-bg-color, #ffffff);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Card view style */
.farmfaucet-container.dummy-faucet.card-view {
    max-width: 400px;
    margin: 2rem auto;
    padding: 0;
    border-radius: 15px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
}

.farmfaucet-container.dummy-faucet.card-view .farmfaucet-header {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--border-color, #4CAF50);
    color: white !important;
}

.farmfaucet-container.dummy-faucet.card-view .status-header,
.farmfaucet-container.dummy-faucet.card-view .reward-notice {
    color: white !important;
    background: transparent;
}

.farmfaucet-container.dummy-faucet.card-view .farmfaucet-form {
    background-color: var(--form-bg-color, #ffffff);
    padding: 1.5rem;
    border-radius: 0;
    box-shadow: none;
}

.farmfaucet-container.dummy-faucet.card-view .farmfaucet-claim-btn {
    border-radius: 8px;
}

/* Compact view style */
.farmfaucet-container.dummy-faucet.compact-view {
    max-width: 100%;
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color, #4CAF50);
}

.farmfaucet-container.dummy-faucet.compact-view .farmfaucet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.farmfaucet-container.dummy-faucet.compact-view .status-header {
    font-size: 1.2rem;
    margin: 0;
}

.farmfaucet-container.dummy-faucet.compact-view .reward-notice {
    padding: 0.5rem;
    margin: 0;
    font-size: 0.9rem;
}

.farmfaucet-container.dummy-faucet.compact-view .farmfaucet-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    background-color: var(--form-bg-color, #ffffff);
    padding: 1rem;
    border-radius: 8px;
}

.farmfaucet-container.dummy-faucet.compact-view .form-group {
    margin-bottom: 0;
}

.farmfaucet-container.dummy-faucet.compact-view .farmfaucet-captcha-container {
    flex: 1;
    min-width: 200px;
    margin: 0 !important;
}

.farmfaucet-container.dummy-faucet.compact-view .farmfaucet-claim-btn {
    flex: 0 0 auto;
    width: auto;
    min-width: 120px;
    padding: 10px 15px;
    font-size: 0.9rem;
}

/* Fix for form transparency */
.farmfaucet-container .farmfaucet-form.form-transparent {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Fix for form background color */
.farmfaucet-container[data-form-transparent="0"] .farmfaucet-form {
    background-color: var(--form-bg-color, #ffffff) !important;
}

.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Fix for transparent inputs */
.farmfaucet-container .transparent-input {
    background-color: transparent !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.farmfaucet-container .transparent-input:focus {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: var(--border-color, #4CAF50) !important;
}

/* Fix for transparent form elements */
.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form .form-group {
    background-color: transparent !important;
}

.farmfaucet-container[data-form-transparent="1"] .farmfaucet-form .reward-amount {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Fix for reward amount display */
.farmfaucet-container.dummy-faucet .reward-amount {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--button-color, #4CAF50);
    text-align: center;
    padding: 0.5rem;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Fix for captcha container */
.farmfaucet-container.dummy-faucet .farmfaucet-captcha-container {
    display: flex !important;
    justify-content: center !important;
    margin: 1rem 0 !important;
}

/* Fix for claim button */
.farmfaucet-container.dummy-faucet .farmfaucet-claim-btn {
    background-color: var(--button-color, #4CAF50) !important;
    border-color: var(--button-color, #4CAF50) !important;
}

/* Fix for disabled claim button */
.farmfaucet-container.dummy-faucet .farmfaucet-claim-btn:disabled {
    background-color: #cccccc !important;
    border-color: #bbbbbb !important;
    opacity: 0.7 !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .farmfaucet-container.dummy-faucet.compact-view .farmfaucet-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .farmfaucet-container.dummy-faucet.compact-view .reward-notice {
        margin-top: 0.5rem;
    }

    .farmfaucet-container.dummy-faucet.compact-view .farmfaucet-form {
        flex-direction: column;
    }

    .farmfaucet-container.dummy-faucet.compact-view .farmfaucet-claim-btn {
        width: 100%;
    }
}
