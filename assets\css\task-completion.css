/**
 * Farm Faucet Task Completion Styles
 */

.task-completion-section {
    margin-top: 20px;
}

.task-completion-section h1 {
    color: #2271b1;
    font-size: 24px;
    margin-bottom: 20px;
}

.farmfaucet-admin-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.farmfaucet-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    flex: 1;
    min-width: 300px;
}

.farmfaucet-card h2 {
    color: #2271b1;
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.farmfaucet-admin-actions {
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

/* Toggle switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #2271b1;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2271b1;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Table styling */
.widefat {
    border-spacing: 0;
    width: 100%;
    clear: both;
    margin: 0;
}

.widefat * {
    word-wrap: break-word;
}

.widefat td, 
.widefat th {
    padding: 8px 10px;
    vertical-align: middle;
}

.widefat thead th {
    font-weight: 600;
}

.widefat tbody th.check-column, 
.widefat tfoot th.check-column, 
.widefat thead th.check-column {
    padding: 11px 0 0 3px;
}

.widefat tbody th, 
.widefat thead th {
    font-weight: 600;
}

.widefat td {
    font-weight: 400;
}

.widefat tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

/* Database fix section */
.database-fix-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    margin-top: 20px;
    margin-bottom: 20px;
}

.database-fix-section h3 {
    color: #2271b1;
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 15px;
}

/* Updating indicator */
tr.updating {
    opacity: 0.7;
    position: relative;
}

tr.updating:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.5) url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg>') no-repeat center center;
    z-index: 1;
}
