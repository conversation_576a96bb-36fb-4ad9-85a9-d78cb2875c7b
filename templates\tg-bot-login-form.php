<?php

/**
 * Template for the Telegram Bot Login form
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Ensure WordPress functions are available
if (!function_exists('get_permalink')) {
    require_once(ABSPATH . 'wp-includes/link-template.php');
}

if (!function_exists('wp_lostpassword_url')) {
    require_once(ABSPATH . 'wp-includes/general-template.php');
}
?>

<div class="farmfaucet-tg-login-form">
    <h2 class="farmfaucet-tg-form-title"><?php esc_html_e('Login', 'farmfaucet'); ?></h2>

    <form method="post">
        <div class="farmfaucet-tg-form-group">
            <label for="username_or_email"><?php esc_html_e('Username, Email or Telegram Number', 'farmfaucet'); ?></label>
            <input type="text" name="username_or_email" id="username_or_email" required>
        </div>

        <div class="farmfaucet-tg-form-group">
            <label for="password"><?php esc_html_e('Password', 'farmfaucet'); ?></label>
            <input type="password" name="password" id="password" required>
        </div>

        <button type="submit" class="farmfaucet-tg-form-button"><?php echo esc_html($atts['button_text']); ?></button>
    </form>

    <div class="farmfaucet-tg-form-links">
        <?php
        // Get custom URLs from settings if available
        $password_reset_page = (int)get_option('farmfaucet_login_password_reset_url', 0);
        $register_page = (int)get_option('farmfaucet_login_register_url', 0);

        // Default URLs - use the custom URLs from attributes if provided
        $forgot_password_url = isset($atts['forgot_password_url']) ? $atts['forgot_password_url'] : '#';
        $signup_url = isset($atts['signup_url']) ? $atts['signup_url'] : '#';

        // If page IDs are set, use them
        if ($password_reset_page > 0) {
            $forgot_password_url = '?page_id=' . $password_reset_page;
        }

        if ($register_page > 0) {
            $signup_url = '?page_id=' . $register_page;
        }
        ?>
        <a href="<?php echo esc_url($forgot_password_url); ?>"><?php esc_html_e('Forgot Password?', 'farmfaucet'); ?></a>
        <a href="<?php echo esc_url($signup_url); ?>"><?php esc_html_e('Sign Up', 'farmfaucet'); ?></a>
    </div>
</div>