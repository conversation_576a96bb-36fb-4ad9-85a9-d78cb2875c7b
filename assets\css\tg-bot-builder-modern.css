/**
 * Telegram Bot Builder Modern UI CSS
 * A clean, modern interface for the Telegram Bot Builder
 */

/* General styles */
.farmfaucet-admin-section.tg-bot-builder-section {
    margin-top: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.farmfaucet-admin-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.farmfaucet-admin-card:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.farmfaucet-admin-card .card-header {
    background-color: #4CAF50;
    border-bottom: 3px solid #388E3C;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
}

.farmfaucet-admin-card .card-body {
    padding: 25px;
}

/* Bot cards */
.bot-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.bot-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.bot-card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.bot-card-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.bot-card-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.bot-card-body {
    padding: 20px;
    flex-grow: 1;
}

.bot-card-footer {
    padding: 15px 20px;
    background-color: #f9f9f9;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
}

.bot-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-top: 5px;
}

.bot-status.active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.bot-status.inactive {
    background-color: #ffebee;
    color: #c62828;
}

/* Bot actions */
.bot-actions {
    display: flex;
    gap: 10px;
}

.bot-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.bot-action-btn.edit {
    color: #2196F3;
}

.bot-action-btn.edit:hover {
    background-color: #e3f2fd;
}

.bot-action-btn.delete {
    color: #F44336;
}

.bot-action-btn.delete:hover {
    background-color: #ffebee;
}

.bot-action-btn.view {
    color: #4CAF50;
}

.bot-action-btn.view:hover {
    background-color: #e8f5e9;
}

/* Command list */
.command-list {
    margin-top: 20px;
}

.command-item {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
}

.command-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.command-name {
    font-weight: 500;
    color: #333;
}

.command-description {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

/* Flow builder */
.flow-builder {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;
    margin-top: 20px;
    position: relative;
}

.flow-node {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    width: 200px;
    position: absolute;
    cursor: move;
}

.flow-node-header {
    font-weight: 500;
    margin-bottom: 10px;
    color: #333;
}

.flow-node-content {
    font-size: 14px;
    color: #666;
}

.flow-node-message {
    background-color: #e3f2fd;
    border-left: 3px solid #2196F3;
}

.flow-node-command {
    background-color: #e8f5e9;
    border-left: 3px solid #4CAF50;
}

.flow-node-condition {
    background-color: #fff8e1;
    border-left: 3px solid #FFC107;
}

/* Telegram chat preview */
.telegram-preview {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.telegram-header {
    background-color: #5682a3;
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 500;
}

.telegram-chat {
    padding: 15px;
    background-color: #e6ebee;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.telegram-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 10px;
    position: relative;
}

.user-message {
    background-color: #fff;
    align-self: flex-end;
    border-top-right-radius: 0;
}

.bot-message {
    background-color: #d5f9ba;
    align-self: flex-start;
    border-top-left-radius: 0;
}

.message-content {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-time {
    font-size: 11px;
    color: #999;
    text-align: right;
}

.telegram-keyboard {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    align-self: center;
}

.telegram-button {
    background-color: #f0f0f0;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.telegram-button:hover {
    background-color: #e0e0e0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.fadeIn {
    animation-name: fadeIn;
}

/* Login Form Settings */
#login-form-settings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

#login-form-settings .form-field {
    margin-bottom: 15px;
}

#login-form-settings label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

#login-form-settings input[type="url"],
#login-form-settings input[type="text"],
#login-form-settings select {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    transition: all 0.2s ease;
}

#login-form-settings input[type="url"]:focus,
#login-form-settings input[type="text"]:focus,
#login-form-settings select:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    outline: none;
}

#login-form-settings .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.save-login-settings {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.save-login-settings:hover {
    background-color: #388E3C;
}

.save-login-settings:disabled {
    background-color: #9E9E9E;
    cursor: not-allowed;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bot-list {
        grid-template-columns: 1fr;
    }

    .telegram-preview {
        max-width: 100%;
    }

    #login-form-settings {
        grid-template-columns: 1fr;
    }
}
