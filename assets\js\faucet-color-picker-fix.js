/**
 * Farm Faucet Faucet Color Picker Fix
 *
 * Adds a color picker to the faucet edit form
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Faucet Color Picker Fix loaded');
        
        // Initialize the color picker when the page loads
        initFaucetColorPicker();
        
        // Initialize the color picker when the faucet dialog opens
        $(document).on('dialogopen', '#faucet-form-dialog', function() {
            setTimeout(initFaucetColorPicker, 100);
            setTimeout(initFaucetColorPicker, 500); // Run again after a delay to ensure it works
        });
        
        // Initialize the color picker when editing a faucet
        $(document).on('click', '.edit-faucet', function() {
            setTimeout(initFaucetColorPicker, 100);
            setTimeout(initFaucetColorPicker, 500);
        });
        
        // Initialize the color picker when creating a new faucet
        $(document).on('click', '#create-new-faucet', function() {
            setTimeout(initFaucetColorPicker, 100);
            setTimeout(initFaucetColorPicker, 500);
        });
    });
    
    /**
     * Initialize the faucet color picker
     */
    function initFaucetColorPicker() {
        // Check if the faucet form exists
        if (!$('#faucet-form').length) {
            return;
        }
        
        // Check if the color picker container already exists
        if ($('#faucet-form .color-grid-container').length) {
            return;
        }
        
        // Find the currency field to add the color picker after it
        var $currencyField = $('#faucet-form .form-field:has(#faucet-currency)');
        
        if (!$currencyField.length) {
            return;
        }
        
        // Create the color picker HTML
        var colorPickerHtml = `
            <div class="form-field">
                <label for="faucet-color">${farmfaucet_admin.strings.button_color || 'Button Color'}</label>
                <div class="color-grid-container">
                    <div class="color-select-wrapper">
                        <!-- Hidden input to store the color value -->
                        <input type="hidden" id="faucet-color" name="faucet_color" value="green">
                        <!-- Color name display -->
                        <div class="color-name-display">Green</div>
                        <!-- Color preview that opens the grid -->
                        <div class="color-preview" style="background-color: #4CAF50;"></div>
                    </div>
                    <div class="color-grid">
                        <div class="color-swatch-option selected" data-color="#4CAF50" data-value="green" data-name="Green" style="background-color: #4CAF50;"></div>
                        <div class="color-swatch-option" data-color="#2271b1" data-value="blue" data-name="Blue" style="background-color: #2271b1;"></div>
                        <div class="color-swatch-option" data-color="#d63638" data-value="red" data-name="Red" style="background-color: #d63638;"></div>
                        <div class="color-swatch-option" data-color="#f0c33c" data-value="yellow" data-name="Yellow" style="background-color: #f0c33c;"></div>
                        <div class="color-swatch-option" data-color="#9c27b0" data-value="purple" data-name="Purple" style="background-color: #9c27b0;"></div>
                        <div class="color-swatch-option" data-color="#ff5722" data-value="orange" data-name="Orange" style="background-color: #ff5722;"></div>
                        <div class="color-swatch-option" data-color="#607d8b" data-value="gray" data-name="Gray" style="background-color: #607d8b;"></div>
                        <div class="color-swatch-option" data-color="#009688" data-value="teal" data-name="Teal" style="background-color: #009688;"></div>
                    </div>
                </div>
                <p class="description">${farmfaucet_admin.strings.button_color_desc || 'Choose a color for the faucet buttons.'}</p>
            </div>
        `;
        
        // Insert the color picker after the currency field
        $currencyField.after(colorPickerHtml);
        
        // Style the color grid container
        $('.color-grid-container').css({
            'position': 'relative',
            'margin-bottom': '10px'
        });
        
        // Style color select wrapper
        $('.color-select-wrapper').css({
            'display': 'flex',
            'align-items': 'center',
            'background': '#f0f0f0',
            'border': '1px solid #ddd',
            'border-radius': '4px',
            'padding': '8px 12px',
            'cursor': 'pointer'
        });
        
        // Style color name display
        $('.color-name-display').css({
            'flex': '1',
            'font-weight': '500'
        });
        
        // Style color preview
        $('.color-preview').css({
            'width': '24px',
            'height': '24px',
            'border-radius': '50%',
            'border': '1px solid rgba(0, 0, 0, 0.1)',
            'margin-left': '10px'
        });
        
        // Style color grid
        $('.color-grid').css({
            'display': 'none',
            'position': 'absolute',
            'top': '100%',
            'left': '0',
            'right': '0',
            'background': 'white',
            'border': '1px solid #ddd',
            'border-radius': '4px',
            'padding': '10px',
            'z-index': '9999',
            'box-shadow': '0 4px 8px rgba(0,0,0,0.1)',
            'margin-top': '5px',
            'grid-template-columns': 'repeat(8, 1fr)',
            'gap': '8px',
            'width': '320px'
        });
        
        // Style color swatch options
        $('.color-swatch-option').css({
            'width': '32px',
            'height': '32px',
            'border-radius': '50%',
            'cursor': 'pointer',
            'border': '1px solid rgba(0, 0, 0, 0.1)',
            'transition': 'transform 0.2s ease'
        });
        
        // Style selected color swatch
        $('.color-swatch-option.selected').css({
            'box-shadow': '0 0 0 2px white, 0 0 0 4px #4CAF50'
        });
        
        // Add hover effect to color swatches
        $('.color-swatch-option').hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );
        
        // Toggle color grid when clicking on the color select wrapper
        $('.color-select-wrapper').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $container = $(this).closest('.color-grid-container');
            var $grid = $container.find('.color-grid');
            
            // Toggle grid display
            if ($grid.css('display') === 'none') {
                // Hide all other grids first
                $('.color-grid').hide().css('display', 'none');
                
                // Show this grid
                $grid.css('display', 'grid');
            } else {
                $grid.css('display', 'none');
            }
        });
        
        // Handle color swatch selection
        $('.color-swatch-option').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $this = $(this);
            var value = $this.data('value');
            var name = $this.data('name');
            var color = $this.data('color');
            var $container = $this.closest('.color-grid-container');
            
            console.log('Faucet color selected:', value, name, color);
            
            // Update hidden input
            $container.find('input[type="hidden"]').val(value);
            
            // Update visual elements
            $container.find('.color-swatch-option').removeClass('selected').css('box-shadow', 'none');
            $this.addClass('selected').css('box-shadow', '0 0 0 2px white, 0 0 0 4px #4CAF50');
            $container.find('.color-name-display').text(name);
            $container.find('.color-preview').css('background-color', color);
            
            // Hide color grid
            $container.find('.color-grid').css('display', 'none');
        });
        
        // Close color grid when clicking outside
        $(document).off('click.colorGrid').on('click.colorGrid', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').css('display', 'none');
            }
        });
        
        // Update the saveFaucet function to include the color value
        if (window.originalSaveFaucet === undefined) {
            window.originalSaveFaucet = window.saveFaucet;
            
            window.saveFaucet = function() {
                var faucetId = $('#faucet-id').val();
                var isNew = faucetId === '0';
                
                // Get selected captcha type
                var captchaType = $('input[name="captcha_type"]:checked').val() || '';
                
                var formData = {
                    action: isNew ? 'farmfaucet_create_faucet' : 'farmfaucet_update_faucet',
                    nonce: farmfaucet_admin.nonce,
                    faucet_id: faucetId,
                    name: $('#faucet-name').val(),
                    currency: $('#faucet-currency').val(),
                    amount: $('#faucet-amount').val(),
                    cooldown: $('#faucet-cooldown').val(),
                    api_key: $('#faucet-api-key').val(),
                    shortcode: $('#faucet-shortcode').val(),
                    captcha_type: captchaType,
                    faucet_color: $('#faucet-color').val() || 'green' // Add the color value
                };
                
                // Validate form
                if (!formData.name || !formData.shortcode) {
                    alert('Name and shortcode are required');
                    return;
                }
                
                // Log the data being sent
                console.log('Saving faucet with data:', formData);
                
                // Disable dialog buttons during save
                var $dialog = $('#faucet-form-dialog').parent();
                $dialog.find('.ui-dialog-buttonpane button').prop('disabled', true);
                
                // Add loading indicator
                var $buttonPane = $dialog.find('.ui-dialog-buttonpane');
                $buttonPane.append('<span class="saving-indicator">Saving...</span>');
                
                // Send AJAX request
                $.ajax({
                    url: farmfaucet_admin.ajax_url,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        // Remove loading indicator
                        $buttonPane.find('.saving-indicator').remove();
                        
                        // Re-enable dialog buttons
                        $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);
                        
                        if (response.success) {
                            // Close dialog
                            $('#faucet-form-dialog').dialog('close');
                            
                            // Show success message
                            alert(isNew ? farmfaucet_admin.strings.create_success : farmfaucet_admin.strings.update_success);
                            
                            // Reload the page to show updated faucet
                            location.reload();
                        } else {
                            alert(response.data.message || farmfaucet_admin.strings.error);
                        }
                    },
                    error: function() {
                        // Remove loading indicator
                        $buttonPane.find('.saving-indicator').remove();
                        
                        // Re-enable dialog buttons
                        $dialog.find('.ui-dialog-buttonpane button').prop('disabled', false);
                        
                        // Show error message
                        alert(farmfaucet_admin.strings.error);
                    }
                });
            };
        }
    }

})(jQuery);
