/**
 * Color Grid Fix JavaScript
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Color Grid Fix loaded');

        // Handle color swatch selection
        $(document).on('click', '.color-swatch-option', function() {
            var $this = $(this);
            var value = $this.data('value');
            var name = $this.data('name');
            var color = $this.data('color');
            var container = $this.closest('.color-grid-container');
            
            // Update hidden input
            container.find('input[type="hidden"]').val(value);
            
            // Update visual elements
            container.find('.color-swatch-option').removeClass('selected');
            $this.addClass('selected');
            container.find('.color-name-display').text(name);
            container.find('.color-preview').css('background-color', color);
            
            // Hide color grid
            container.find('.color-grid').removeClass('active');
            
            // Trigger change event on the hidden input
            container.find('input[type="hidden"]').trigger('change');
        });
        
        // Toggle color grid on preview click
        $(document).on('click', '.color-preview, .color-select-wrapper', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close any other open color grids first
            $('.color-grid').not($(this).closest('.color-grid-container').find('.color-grid')).removeClass('active');
            
            // Toggle this color grid
            $(this).closest('.color-grid-container').find('.color-grid').toggleClass('active');
            
            return false; // Prevent default dropdown
        });
        
        // Close color grid when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.color-grid-container').length) {
                $('.color-grid').removeClass('active');
            }
        });

        // Fix for color grid display in admin
        setTimeout(function() {
            $('.color-grid').each(function() {
                var $grid = $(this);
                if (!$grid.hasClass('grid-fixed')) {
                    $grid.css({
                        'display': 'none',
                        'grid-template-columns': 'repeat(8, 1fr)',
                        'gap': '8px',
                        'width': '320px',
                        'z-index': '9999'
                    });
                    
                    $grid.addClass('grid-fixed');
                    
                    // Make sure active grids display as grid
                    if ($grid.hasClass('active')) {
                        $grid.css('display', 'grid');
                    }
                }
            });
        }, 500);
    });

})(jQuery);
