<?php
/**
 * Template for advertisement creation form
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-ad-create-container">
    <h2 class="form-title"><?php echo esc_html($atts['title']); ?></h2>
    
    <?php if ($atts['show_cost'] || $atts['show_duration']) : ?>
        <div class="form-info">
            <?php if ($atts['show_cost']) : ?>
                <div class="cost-info">
                    <p><?php printf(esc_html__('Cost: %s', 'farmfaucet'), '<strong>' . esc_html($ad_cost) . '</strong>'); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($atts['show_duration']) : ?>
                <div class="duration-info">
                    <p><?php printf(esc_html__('Duration: %s days', 'farmfaucet'), '<strong>' . esc_html($ad_duration_days) . '</strong>'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <form id="farmfaucet-ad-create-form" class="farmfaucet-form">
        <div class="form-group">
            <label for="ad-title"><?php esc_html_e('Title', 'farmfaucet'); ?> <span class="required">*</span></label>
            <input type="text" id="ad-title" name="title" required maxlength="100">
            <p class="description"><?php esc_html_e('Enter a title for your advertisement (max 100 characters).', 'farmfaucet'); ?></p>
        </div>
        
        <div class="form-group">
            <label for="ad-description"><?php esc_html_e('Description', 'farmfaucet'); ?> <span class="required">*</span></label>
            <textarea id="ad-description" name="description" required rows="4"></textarea>
            <p class="description"><?php esc_html_e('Describe your advertisement. HTML is not allowed.', 'farmfaucet'); ?></p>
        </div>
        
        <div class="form-group">
            <label for="ad-url"><?php esc_html_e('URL', 'farmfaucet'); ?> <span class="required">*</span></label>
            <input type="url" id="ad-url" name="url" required>
            <p class="description"><?php esc_html_e('Enter the URL where users will be directed when they click your ad.', 'farmfaucet'); ?></p>
        </div>
        
        <?php if ($atts['show_image_upload']) : ?>
            <div class="form-group">
                <label for="ad-image-url"><?php esc_html_e('Image URL', 'farmfaucet'); ?></label>
                <input type="url" id="ad-image-url" name="image_url">
                <p class="description"><?php esc_html_e('Enter the URL of an image for your advertisement (optional).', 'farmfaucet'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($currencies)) : ?>
            <div class="form-group">
                <label for="ad-currency"><?php esc_html_e('Payment Currency', 'farmfaucet'); ?> <span class="required">*</span></label>
                <select id="ad-currency" name="currency_id" required>
                    <?php foreach ($currencies as $currency) : ?>
                        <option value="<?php echo esc_attr($currency['id']); ?>" data-balance="<?php echo esc_attr($user_balances[$currency['id']] ?? 0); ?>" data-symbol="<?php echo esc_attr($currency['symbol']); ?>">
                            <?php echo esc_html($currency['name'] . ' (' . $currency['symbol'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <p class="description">
                    <?php esc_html_e('Select the currency you want to pay with.', 'farmfaucet'); ?>
                    <span id="currency-balance-info"></span>
                </p>
            </div>
        <?php endif; ?>
        
        <div class="form-actions">
            <button type="submit" id="ad-submit" class="farmfaucet-button"><?php echo esc_html($atts['button_text']); ?></button>
            <div class="form-message" style="display: none;"></div>
        </div>
        
        <input type="hidden" name="action" value="farmfaucet_create_ad">
        <input type="hidden" name="nonce" value="<?php echo esc_attr(wp_create_nonce('farmfaucet-advertising-nonce')); ?>">
        <?php if (!empty($atts['redirect_url'])) : ?>
            <input type="hidden" name="redirect_url" value="<?php echo esc_url($atts['redirect_url']); ?>">
        <?php endif; ?>
    </form>
</div>

<script>
    (function() {
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('farmfaucet-ad-create-form');
            const messageContainer = form.querySelector('.form-message');
            const submitButton = document.getElementById('ad-submit');
            
            <?php if (!empty($currencies)) : ?>
            // Update balance info when currency changes
            const currencySelect = document.getElementById('ad-currency');
            const balanceInfo = document.getElementById('currency-balance-info');
            
            function updateBalanceInfo() {
                const selectedOption = currencySelect.options[currencySelect.selectedIndex];
                const balance = parseFloat(selectedOption.dataset.balance);
                const symbol = selectedOption.dataset.symbol;
                const cost = <?php echo esc_js($ad_cost); ?>;
                
                balanceInfo.innerHTML = ' ' + '<?php esc_html_e('Your balance:', 'farmfaucet'); ?> ' + 
                    '<strong>' + balance.toFixed(8) + ' ' + symbol + '</strong>';
                
                if (balance < cost) {
                    balanceInfo.innerHTML += ' <span class="insufficient-balance"><?php esc_html_e('(Insufficient balance)', 'farmfaucet'); ?></span>';
                    submitButton.disabled = true;
                } else {
                    submitButton.disabled = false;
                }
            }
            
            if (currencySelect) {
                currencySelect.addEventListener('change', updateBalanceInfo);
                updateBalanceInfo(); // Initial update
            }
            <?php endif; ?>
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = '<?php esc_html_e('Submitting...', 'farmfaucet'); ?>';
                
                // Collect form data
                const formData = new FormData(form);
                
                // Send AJAX request
                fetch(farmfaucetAdvertising.ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        messageContainer.className = 'form-message success';
                        messageContainer.textContent = data.data.message;
                        messageContainer.style.display = 'block';
                        
                        // Reset form
                        form.reset();
                        
                        // Redirect if URL is provided
                        const redirectUrl = form.querySelector('input[name="redirect_url"]');
                        if (redirectUrl && redirectUrl.value) {
                            setTimeout(function() {
                                window.location.href = redirectUrl.value;
                            }, 2000);
                        }
                    } else {
                        // Show error message
                        messageContainer.className = 'form-message error';
                        messageContainer.textContent = data.data.message;
                        messageContainer.style.display = 'block';
                    }
                    
                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<?php echo esc_js($atts['button_text']); ?>';
                    
                    <?php if (!empty($currencies)) : ?>
                    // Update balance info
                    updateBalanceInfo();
                    <?php endif; ?>
                })
                .catch(error => {
                    console.error('Error:', error);
                    
                    // Show error message
                    messageContainer.className = 'form-message error';
                    messageContainer.textContent = farmfaucetAdvertising.i18n.error;
                    messageContainer.style.display = 'block';
                    
                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<?php echo esc_js($atts['button_text']); ?>';
                });
            });
        });
    })();
</script>
