/**
 * Tel<PERSON><PERSON><PERSON> Builder Redesign CSS
 * Modern, clean interface with improved usability
 */

/* General styles */
.farmfaucet-admin-section.tg-bot-builder-section {
    margin-top: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.farmfaucet-admin-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.farmfaucet-admin-card .card-header {
    background-color: #4CAF50;
    border-bottom: 3px solid #388E3C;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.farmfaucet-admin-card .card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.farmfaucet-admin-card .card-body {
    padding: 25px;
    position: relative;
}

/* Bot Management Section */
.bot-management-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.farmfaucet-add-bot-button,
.bot-management-header .add-new-bot {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
    font-weight: 600;
    padding: 8px 16px;
    height: auto;
    line-height: 1.5;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.farmfaucet-add-bot-button:hover,
.bot-management-header .add-new-bot:hover {
    background: #3d9c40;
    border-color: #3d9c40;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Empty state */
.no-bots-message {
    text-align: center;
    padding: 60px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #ddd;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-state .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 24px;
    margin: 0 0 15px;
    color: #333;
}

.empty-state p {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
    max-width: 500px;
}

/* Bot Tabs */
.bot-tabs-container,
.bots-tabs-container {
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.bot-tabs-nav,
.bots-tabs-nav {
    display: flex;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    overflow-x: auto;
    scrollbar-width: thin;
}

.bot-tabs-nav::-webkit-scrollbar {
    height: 6px;
}

.bot-tabs-nav::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.bot-tab,
.bots-tabs-nav li {
    padding: 15px 20px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s ease;
    border-right: 1px solid #e0e0e0;
    position: relative;
    list-style: none;
}

.bot-tab.active,
.bots-tabs-nav li.ui-tabs-active {
    background-color: #fff;
    border-bottom: 3px solid #4CAF50;
}

.bots-tabs-nav li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
}

.bot-tab-inner {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bot-icon .dashicons {
    color: #4CAF50;
}

.bot-name {
    font-weight: 500;
    color: #333;
}

.bot-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.bot-status-indicator.active {
    background-color: #4CAF50;
}

.bot-status-indicator.inactive {
    background-color: #999;
}

/* Bot Content */
.bot-tabs-content,
.bot-tab-content {
    background-color: #fff;
}

.bot-tab-content,
.ui-tabs-panel {
    display: none;
    padding: 20px;
}

.bot-tab-content.active,
.ui-tabs-panel.ui-tabs-active {
    display: block;
}

.bot-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.bot-title {
    margin: 0 0 10px;
    font-size: 22px;
    color: #333;
}

.bot-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.bot-username {
    font-size: 14px;
    color: #666;
}

.bot-type-badge, .bot-status-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.bot-type-badge {
    background-color: #f0f0f0;
    color: #555;
}

.bot-status-badge {
    color: white;
}

.bot-status-badge.active {
    background-color: #4CAF50;
}

.bot-status-badge.inactive {
    background-color: #999;
}

.bot-actions {
    display: flex;
    gap: 10px;
}

/* Bot Content Container */
.bot-content-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 1200px) {
    .bot-content-container {
        grid-template-columns: 1fr;
    }
}

/* Commands Panel */
.bot-commands-panel, .bot-chat-panel {
    background-color: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

.commands-header, .chat-header {
    background-color: #f0f0f0;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
}

.commands-header h4, .chat-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.commands-list {
    padding: 15px;
    min-height: 200px;
}

.loading-commands {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
}

.loading-commands .spinner {
    background: url('../images/spinner.gif') no-repeat;
    background-size: 20px 20px;
    display: inline-block;
    visibility: visible;
    width: 20px;
    height: 20px;
    margin: 0 0 10px;
}

/* Telegram Chat */
.telegram-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 400px;
    background-color: #fff;
}

.telegram-chat-header {
    background-color: #5682a3;
    color: white;
    padding: 12px 15px;
    text-align: center;
}

.telegram-chat-title {
    font-weight: 600;
}

.telegram-chat {
    flex: 1;
    padding: 15px;
    background-color: #e6ebee;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.telegram-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 10px;
    position: relative;
}

.user-message {
    background-color: #fff;
    align-self: flex-end;
    border-top-right-radius: 0;
}

.bot-message {
    background-color: #d5f9ba;
    align-self: flex-start;
    border-top-left-radius: 0;
}

.message-content {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-time {
    font-size: 11px;
    color: #999;
    text-align: right;
}

.telegram-chat-input {
    display: flex;
    padding: 10px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.telegram-message-input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 15px;
    margin-right: 10px;
}

.telegram-send-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.telegram-send-button .dashicons {
    font-size: 18px;
}
