<?php
/**
 * Template for the Telegram Bot OTP verification form
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-tg-otp-form">
    <h2 class="farmfaucet-tg-form-title"><?php esc_html_e('Verify Your Telegram', 'farmfaucet'); ?></h2>
    
    <div class="otp-message">
        <?php esc_html_e('We\'ve sent a verification code to your Telegram. Please enter it below.', 'farmfaucet'); ?>
        <?php esc_html_e('Code expires in', 'farmfaucet'); ?> <span class="otp-timer" data-time="<?php echo esc_attr(get_option('farmfaucet_tg_otp_expiration', 5) * 60); ?>"><?php echo esc_html(get_option('farmfaucet_tg_otp_expiration', 5)); ?>:00</span>
    </div>
    
    <form method="post">
        <div class="farmfaucet-tg-form-group">
            <label for="otp_code"><?php esc_html_e('Verification Code', 'farmfaucet'); ?></label>
            <input type="text" name="otp_code" id="otp_code" class="farmfaucet-tg-otp-input" maxlength="6" required>
        </div>
        
        <input type="hidden" name="user_id" value="<?php echo esc_attr($user_id); ?>">
        
        <button type="submit" class="farmfaucet-tg-form-button"><?php esc_html_e('Verify', 'farmfaucet'); ?></button>
    </form>
    
    <div class="farmfaucet-tg-form-links">
        <a href="#" class="resend-otp" data-user-id="<?php echo esc_attr($user_id); ?>"><?php esc_html_e('Resend Code', 'farmfaucet'); ?></a>
    </div>
</div>
