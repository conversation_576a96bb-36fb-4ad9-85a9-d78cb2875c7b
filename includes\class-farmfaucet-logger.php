<?php
class Farmfaucet_Logger
{
    private static $instance;

    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        // Initialize hooks if needed
    }

    /**
     * Log messages to database
     * @param string $message
     * @param string $type (error|warning|info)
     * @param int $faucet_id The ID of the faucet (default: 0 for system logs)
     */
    public static function log($message, $type = 'info', $faucet_id = 0)
    {

        global $wpdb;

        $allowed_types = ['error', 'warning', 'info'];
        $type = in_array(strtolower($type), $allowed_types) ? $type : 'info';

        // Add contextual information
        $full_message = "[" . strtoupper($type) . "] " . $message;
        $full_message .= "\nContext: " . json_encode([
            'user_ip' => Farmfaucet_Security::get_user_ip(),
            'time' => date('Y-m-d H:i:s'),
            'plugin_version' => FARMFAUCET_VERSION,
            'faucet_id' => $faucet_id
        ]);

        $wpdb->insert(
            $wpdb->prefix . 'farmfaucet_logs',
            [
                'timestamp' => current_time('mysql'),
                'message' => sanitize_text_field($message),
                'type' => sanitize_text_field($type),
                'faucet_id' => absint($faucet_id)
            ],
            ['%s', '%s', '%s', '%d']
        );
    }

    /**
     * Retrieve logs from database
     * @param int $limit Number of logs to retrieve
     * @return array
     */


    public static function get_logs($limit = 100, $type = null, $faucet_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_logs';

        $query = "SELECT * FROM $table_name";
        $params = [];
        $where_clauses = [];

        if ($type) {
            $where_clauses[] = "type = %s";
            $params[] = sanitize_text_field($type);
        }

        if ($faucet_id !== null) {
            $where_clauses[] = "faucet_id = %d";
            $params[] = absint($faucet_id);
        }

        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(' AND ', $where_clauses);
        }

        $query .= " ORDER BY timestamp DESC LIMIT %d";
        $params[] = absint($limit);

        return $wpdb->get_results(
            $wpdb->prepare($query, $params),
            ARRAY_A
        );
    }

    /**
     * Get all faucets from database
     * @param bool $enabled_only Whether to return only enabled faucets
     * @return array
     */
    public static function get_faucets($enabled_only = false)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        $query = "SELECT * FROM $table_name";

        if ($enabled_only) {
            $query .= " WHERE is_enabled = 1";
        }

        $query .= " ORDER BY id ASC";

        return $wpdb->get_results($query, ARRAY_A);
    }

    /**
     * Get a specific faucet by ID
     * @param int $id Faucet ID
     * @return array|null
     */
    public static function get_faucet($id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", absint($id)),
            ARRAY_A
        );
    }

    /**
     * Get a faucet by shortcode
     * @param string $shortcode
     * @return array|null
     */
    public static function get_faucet_by_shortcode($shortcode)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE shortcode = %s", sanitize_text_field($shortcode)),
            ARRAY_A
        );
    }

    /**
     * Log error messages to database
     * @param string $message Error message
     * @param int $faucet_id The ID of the faucet (default: 0 for system logs)
     */
    public static function log_error($message, $faucet_id = 0)
    {
        self::log($message, 'error', $faucet_id);
    }

    /**
     * Clear all logs from database
     * @param int $faucet_id Optional faucet ID to clear logs for a specific faucet
     */
    public static function clear_logs($faucet_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_logs';

        if ($faucet_id !== null) {
            $wpdb->delete($table_name, ['faucet_id' => absint($faucet_id)], ['%d']);
        } else {
            $wpdb->query("TRUNCATE TABLE $table_name");
        }
    }

    /**
     * Create a new faucet
     * @param array $data Faucet data
     * @return int|false The ID of the inserted faucet or false on failure
     */
    public static function create_faucet($data)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        // Set is_enabled to 1 by default if not provided
        $is_enabled = isset($data['is_enabled']) ? absint($data['is_enabled']) : 1;

        // Set background styling defaults if not provided
        $transparent_bg = isset($data['transparent_bg']) ? absint($data['transparent_bg']) : 0;
        $bg_style = isset($data['bg_style']) ? sanitize_text_field($data['bg_style']) : 'solid';
        $bg_color = isset($data['bg_color']) ? sanitize_text_field($data['bg_color']) : '#f8fff8';
        $bg_gradient_start = isset($data['bg_gradient_start']) ? sanitize_text_field($data['bg_gradient_start']) : '#f8fff8';
        $bg_gradient_end = isset($data['bg_gradient_end']) ? sanitize_text_field($data['bg_gradient_end']) : '#e8f5e9';
        $text_color = isset($data['text_color']) ? sanitize_text_field($data['text_color']) : '#4CAF50';
        $text_shadow = isset($data['text_shadow']) ? sanitize_text_field($data['text_shadow']) : 'none';

        // Set faucet type default if not provided
        $faucet_type = isset($data['faucet_type']) ? sanitize_text_field($data['faucet_type']) : 'stage';

        // Ensure faucet_type is one of the allowed values
        if (!in_array($faucet_type, ['stage', 'dummy', 'withdrawal'])) {
            $faucet_type = 'stage';
        }

        // Prepare insert data
        $insert_data = [
            'name' => sanitize_text_field($data['name']),
            'faucet_type' => $faucet_type,
            'currency' => sanitize_text_field($data['currency']),
            'amount' => sanitize_text_field($data['amount']),
            'cooldown' => absint($data['cooldown']),
            'shortcode' => sanitize_text_field($data['shortcode']),
            'api_key' => sanitize_text_field($data['api_key'] ?? ''),
            'captcha_type' => sanitize_text_field($data['captcha_type'] ?? ''),
            'faucet_color' => sanitize_text_field($data['faucet_color'] ?? 'green'),
            'transparent_bg' => $transparent_bg,
            'bg_style' => $bg_style,
            'bg_color' => $bg_color,
            'bg_gradient_start' => $bg_gradient_start,
            'bg_gradient_end' => $bg_gradient_end,
            'text_color' => $text_color,
            'text_shadow' => $text_shadow,
            'is_enabled' => $is_enabled,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Add type-specific fields
        if ($faucet_type === 'dummy' || $faucet_type === 'withdrawal' || $faucet_type === 'conversion') {
            $insert_data['currency_id'] = isset($data['currency_id']) ? absint($data['currency_id']) : 0;
            $insert_data['view_style'] = isset($data['view_style']) ? sanitize_text_field($data['view_style']) : 'default';

            if ($faucet_type === 'withdrawal') {
                $insert_data['min_withdrawal'] = isset($data['min_withdrawal']) ? sanitize_text_field($data['min_withdrawal']) : '0';
                $insert_data['available_currencies'] = isset($data['available_currencies']) ? sanitize_text_field($data['available_currencies']) : '';
                $insert_data['ads_only_conversion'] = isset($data['ads_only_conversion']) ? absint($data['ads_only_conversion']) : 0;
            } else if ($faucet_type === 'conversion') {
                $insert_data['min_conversion'] = isset($data['min_conversion']) ? sanitize_text_field($data['min_conversion']) : '0';
                $insert_data['conversion_currencies'] = isset($data['conversion_currencies']) ? sanitize_text_field($data['conversion_currencies']) : '';
                $insert_data['ads_only_conversion'] = isset($data['ads_only_conversion']) ? absint($data['ads_only_conversion']) : 0;
            }
        }

        // Format types for wpdb->insert
        $format = [
            '%s', // name
            '%s', // faucet_type
            '%s', // currency
            '%s', // amount
            '%d', // cooldown
            '%s', // shortcode
            '%s', // api_key
            '%s', // captcha_type
            '%s', // faucet_color
            '%d', // transparent_bg
            '%s', // bg_style
            '%s', // bg_color
            '%s', // bg_gradient_start
            '%s', // bg_gradient_end
            '%s', // text_color
            '%s', // text_shadow
            '%d', // is_enabled
            '%s', // created_at
            '%s'  // updated_at
        ];

        // Add format for type-specific fields
        if ($faucet_type === 'dummy' || $faucet_type === 'withdrawal' || $faucet_type === 'conversion') {
            $format[] = '%d'; // currency_id
            $format[] = '%s'; // view_style

            if ($faucet_type === 'withdrawal') {
                $format[] = '%s'; // min_withdrawal
                $format[] = '%s'; // available_currencies
                $format[] = '%d'; // ads_only_conversion
            } else if ($faucet_type === 'conversion') {
                $format[] = '%s'; // min_conversion
                $format[] = '%s'; // conversion_currencies
                $format[] = '%d'; // ads_only_conversion
            }
        }

        $result = $wpdb->insert($table_name, $insert_data, $format);

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update an existing faucet
     * @param int $id Faucet ID
     * @param array $data Faucet data
     * @return bool Success or failure
     */
    public static function update_faucet($id, $data)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        // Build update data array dynamically
        $update_data = [
            'name' => sanitize_text_field($data['name']),
            'faucet_type' => sanitize_text_field($data['faucet_type'] ?? 'stage'),
            'currency' => sanitize_text_field($data['currency']),
            'amount' => sanitize_text_field($data['amount']),
            'cooldown' => absint($data['cooldown']),
            'shortcode' => sanitize_text_field($data['shortcode']),
            'api_key' => sanitize_text_field($data['api_key'] ?? ''),
            'captcha_type' => sanitize_text_field($data['captcha_type'] ?? ''),
            'faucet_color' => sanitize_text_field($data['faucet_color'] ?? 'green'),
            'updated_at' => current_time('mysql')
        ];

        // Build format array dynamically to match update_data
        $format = ['%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s'];

        // Add background styling fields if provided
        if (isset($data['transparent_bg'])) {
            $update_data['transparent_bg'] = absint($data['transparent_bg']);
            $format[] = '%d';
        }

        if (isset($data['bg_style'])) {
            $update_data['bg_style'] = sanitize_text_field($data['bg_style']);
            $format[] = '%s';
        }

        if (isset($data['bg_color'])) {
            $update_data['bg_color'] = sanitize_text_field($data['bg_color']);
            $format[] = '%s';
        }

        if (isset($data['bg_gradient_start'])) {
            $update_data['bg_gradient_start'] = sanitize_text_field($data['bg_gradient_start']);
            $format[] = '%s';
        }

        if (isset($data['bg_gradient_end'])) {
            $update_data['bg_gradient_end'] = sanitize_text_field($data['bg_gradient_end']);
            $format[] = '%s';
        }

        if (isset($data['text_color'])) {
            $update_data['text_color'] = sanitize_text_field($data['text_color']);
            $format[] = '%s';
        }

        if (isset($data['text_shadow'])) {
            $update_data['text_shadow'] = sanitize_text_field($data['text_shadow']);
            $format[] = '%s';
        }

        // Add button styling fields if provided
        if (isset($data['button_color'])) {
            $update_data['button_color'] = sanitize_text_field($data['button_color']);
            $format[] = '%s';
        }

        if (isset($data['border_color'])) {
            $update_data['border_color'] = sanitize_text_field($data['border_color']);
            $format[] = '%s';
        }

        if (isset($data['border_radius'])) {
            $update_data['border_radius'] = sanitize_text_field($data['border_radius']);
            $format[] = '%s';
        }

        if (isset($data['button_border_radius'])) {
            $update_data['button_border_radius'] = sanitize_text_field($data['button_border_radius']);
            $format[] = '%s';
        }

        if (isset($data['input_label_color'])) {
            $update_data['input_label_color'] = sanitize_text_field($data['input_label_color']);
            $format[] = '%s';
        }

        if (isset($data['input_placeholder_color'])) {
            $update_data['input_placeholder_color'] = sanitize_text_field($data['input_placeholder_color']);
            $format[] = '%s';
        }

        if (isset($data['form_bg_color'])) {
            $update_data['form_bg_color'] = sanitize_text_field($data['form_bg_color']);
            $format[] = '%s';
        }

        if (isset($data['form_transparent'])) {
            $update_data['form_transparent'] = absint($data['form_transparent']);
            $format[] = '%d';
        }

        // Add type-specific fields
        if (isset($data['currency_id'])) {
            $update_data['currency_id'] = absint($data['currency_id']);
            $format[] = '%d';
        }

        if (isset($data['min_withdrawal'])) {
            $update_data['min_withdrawal'] = sanitize_text_field($data['min_withdrawal']);
            $format[] = '%s';
        }

        if (isset($data['available_currencies'])) {
            $update_data['available_currencies'] = sanitize_text_field($data['available_currencies']);
            $format[] = '%s';
        }

        if (isset($data['conversion_currencies'])) {
            $update_data['conversion_currencies'] = sanitize_text_field($data['conversion_currencies']);
            $format[] = '%s';
        }

        if (isset($data['view_style'])) {
            $update_data['view_style'] = sanitize_text_field($data['view_style']);
            $format[] = '%s';
        }

        if (isset($data['ads_only_conversion'])) {
            $update_data['ads_only_conversion'] = absint($data['ads_only_conversion']);
            $format[] = '%d';
        }

        // Add is_enabled if it's set in the data
        if (isset($data['is_enabled'])) {
            $update_data['is_enabled'] = absint($data['is_enabled']);
            $format[] = '%d';
        }

        return $wpdb->update(
            $table_name,
            $update_data,
            ['id' => absint($id)],
            $format,
            ['%d']
        );
    }

    /**
     * Delete a faucet
     * @param int $id Faucet ID
     * @return bool Success or failure
     */
    public static function delete_faucet($id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        return $wpdb->delete(
            $table_name,
            ['id' => absint($id)],
            ['%d']
        );
    }

    /**
     * Get buttons for a specific faucet
     * @param int $faucet_id Faucet ID
     * @return array Array of buttons
     */
    public static function get_faucet_buttons($faucet_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $table_name WHERE faucet_id = %d ORDER BY id ASC", absint($faucet_id)),
            ARRAY_A
        );
    }

    /**
     * Get a specific button by ID
     * @param int $button_id Button ID
     * @return array|null Button data or null if not found
     */
    public static function get_button($button_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", absint($button_id)),
            ARRAY_A
        );
    }

    /**
     * Get a button by its shortcode
     * @param string $shortcode Button shortcode
     * @return array|null Button data or null if not found
     */
    public static function get_button_by_shortcode($shortcode)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE shortcode = %s", sanitize_text_field($shortcode)),
            ARRAY_A
        );
    }

    /**
     * Create a new button
     * @param array $data Button data
     * @return int|false The button ID on success, false on failure
     */
    public static function create_button($data)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        // Process required faucets if button is locked
        $is_locked = isset($data['is_locked']) ? absint($data['is_locked']) : 0;
        $required_faucets = '';
        $reset_minutes = 0;

        if ($is_locked && !empty($data['required_faucets'])) {
            if (is_array($data['required_faucets'])) {
                $required_faucets = implode(',', array_map('absint', $data['required_faucets']));
            } else {
                $required_faucets = sanitize_text_field($data['required_faucets']);
            }
            $reset_minutes = isset($data['reset_minutes']) ? absint($data['reset_minutes']) : 0;
        }

        // Process lock faucet option
        $lock_faucet = isset($data['lock_faucet']) ? absint($data['lock_faucet']) : 0;

        // Process milestone options
        $milestone_enabled = isset($data['milestone_enabled']) ? absint($data['milestone_enabled']) : 0;
        $milestone_type = isset($data['milestone_type']) ? sanitize_text_field($data['milestone_type']) : 'global';
        $milestone_count = isset($data['milestone_count']) ? absint($data['milestone_count']) : 0;
        $milestone_specific_faucet = isset($data['milestone_specific_faucet']) ? absint($data['milestone_specific_faucet']) : 0;
        $milestone_lock_faucet = isset($data['milestone_lock_faucet']) ? absint($data['milestone_lock_faucet']) : 0;

        // Process milestone card appearance options
        $milestone_display_style = isset($data['milestone_display_style']) ? sanitize_text_field($data['milestone_display_style']) : 'card';
        $milestone_transparent_bg = isset($data['milestone_transparent_bg']) ? absint($data['milestone_transparent_bg']) : 0;
        $milestone_card_bg_style = isset($data['milestone_card_bg_style']) ? sanitize_text_field($data['milestone_card_bg_style']) : 'solid';
        $milestone_card_bg_color = isset($data['milestone_card_bg_color']) ? sanitize_text_field($data['milestone_card_bg_color']) : '#FFFFFF';
        $milestone_card_gradient_start = isset($data['milestone_card_gradient_start']) ? sanitize_text_field($data['milestone_card_gradient_start']) : '#FFFFFF';
        $milestone_card_gradient_end = isset($data['milestone_card_gradient_end']) ? sanitize_text_field($data['milestone_card_gradient_end']) : '#F5F5F5';

        // Process milestone progress bar options
        $milestone_bar_style = isset($data['milestone_bar_style']) ? sanitize_text_field($data['milestone_bar_style']) : 'solid';
        $milestone_bar_color = isset($data['milestone_bar_color']) ? sanitize_text_field($data['milestone_bar_color']) : '#4CAF50';
        $milestone_gradient_start = isset($data['milestone_gradient_start']) ? sanitize_text_field($data['milestone_gradient_start']) : '#4CAF50';
        $milestone_gradient_end = isset($data['milestone_gradient_end']) ? sanitize_text_field($data['milestone_gradient_end']) : '#2196F3';
        $milestone_specific_count = isset($data['milestone_specific_count']) ? absint($data['milestone_specific_count']) : 0;

        // Process milestone pages (for page-specific milestone type)
        $milestone_pages = '';
        if ($milestone_type === 'page_specific' && !empty($data['milestone_pages'])) {
            if (is_array($data['milestone_pages'])) {
                $milestone_pages = wp_json_encode($data['milestone_pages']);
            } else {
                $milestone_pages = sanitize_text_field($data['milestone_pages']);
            }
        }

        // Process countdown standby option
        $countdown_standby = isset($data['countdown_standby']) ? absint($data['countdown_standby']) : 0;

        $result = $wpdb->insert(
            $table_name,
            [
                'faucet_id' => absint($data['faucet_id']),
                'button_text' => sanitize_text_field($data['button_text']),
                'button_size' => sanitize_text_field($data['button_size']),
                'button_color' => sanitize_text_field($data['button_color']),
                'button_color_hex' => sanitize_text_field($data['button_color_hex'] ?? ''),
                'border_shape' => sanitize_text_field($data['border_shape']),
                'redirect_url' => esc_url_raw($data['redirect_url'] ?? ''),
                'shortcode' => sanitize_text_field($data['shortcode']),
                'is_locked' => $is_locked,
                'required_faucets' => $required_faucets,
                'reset_minutes' => $reset_minutes,
                'lock_faucet' => $lock_faucet,
                'milestone_enabled' => $milestone_enabled,
                'milestone_type' => $milestone_type,
                'milestone_count' => $milestone_count,
                'milestone_specific_faucet' => $milestone_specific_faucet,
                'milestone_specific_count' => $milestone_specific_count,
                'milestone_lock_faucet' => $milestone_lock_faucet,
                'milestone_display_style' => $milestone_display_style,
                'milestone_transparent_bg' => $milestone_transparent_bg,
                'milestone_card_bg_style' => $milestone_card_bg_style,
                'milestone_card_bg_color' => $milestone_card_bg_color,
                'milestone_card_gradient_start' => $milestone_card_gradient_start,
                'milestone_card_gradient_end' => $milestone_card_gradient_end,
                'milestone_pages' => $milestone_pages,
                'milestone_bar_style' => $milestone_bar_style,
                'milestone_bar_color' => $milestone_bar_color,
                'milestone_gradient_start' => $milestone_gradient_start,
                'milestone_gradient_end' => $milestone_gradient_end,
                'countdown_standby' => $countdown_standby,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ],
            ['%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d', '%d', '%d', '%s', '%d', '%d', '%d', '%d', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s']
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update an existing button
     * @param int $id Button ID
     * @param array $data Button data
     * @return bool Success or failure
     */
    public static function update_button($id, $data)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        // Get the current button data to preserve any fields not being updated
        $current_button = self::get_button($id);
        if (!$current_button) {
            return false;
        }

        // Process required faucets if button is locked
        $is_locked = isset($data['is_locked']) ? (int)$data['is_locked'] : 0;
        $required_faucets = '';
        $reset_minutes = 0;

        if ($is_locked && !empty($data['required_faucets'])) {
            if (is_array($data['required_faucets'])) {
                $required_faucets = implode(',', array_map(function ($val) {
                    return (int)$val;
                }, $data['required_faucets']));
            } else {
                $required_faucets = $data['required_faucets'];
            }
            $reset_minutes = isset($data['reset_minutes']) ? (int)$data['reset_minutes'] : 0;
        }

        // Process lock faucet option
        $lock_faucet = isset($data['lock_faucet']) ? (int)$data['lock_faucet'] : 0;

        // Process milestone options
        $milestone_enabled = isset($data['milestone_enabled']) ? (int)$data['milestone_enabled'] : 0;
        $milestone_type = isset($data['milestone_type']) ? $data['milestone_type'] : 'global';
        $milestone_count = isset($data['milestone_count']) ? (int)$data['milestone_count'] : 0;
        $milestone_specific_faucet = isset($data['milestone_specific_faucet']) ? (int)$data['milestone_specific_faucet'] : 0;
        $milestone_specific_count = isset($data['milestone_specific_count']) ? (int)$data['milestone_specific_count'] : 0;
        $milestone_lock_faucet = isset($data['milestone_lock_faucet']) ? (int)$data['milestone_lock_faucet'] : 0;

        // Process milestone card appearance options
        $milestone_display_style = isset($data['milestone_display_style']) ? $data['milestone_display_style'] : 'card';
        $milestone_transparent_bg = isset($data['milestone_transparent_bg']) ? (int)$data['milestone_transparent_bg'] : 0;
        $milestone_card_bg_style = isset($data['milestone_card_bg_style']) ? $data['milestone_card_bg_style'] : 'solid';
        $milestone_card_bg_color = isset($data['milestone_card_bg_color']) ? $data['milestone_card_bg_color'] : '#FFFFFF';
        $milestone_card_gradient_start = isset($data['milestone_card_gradient_start']) ? $data['milestone_card_gradient_start'] : '#FFFFFF';
        $milestone_card_gradient_end = isset($data['milestone_card_gradient_end']) ? $data['milestone_card_gradient_end'] : '#F5F5F5';

        // Process milestone progress bar options
        $milestone_bar_style = isset($data['milestone_bar_style']) ? $data['milestone_bar_style'] : 'solid';
        $milestone_bar_color = isset($data['milestone_bar_color']) ? $data['milestone_bar_color'] : '#4CAF50';
        $milestone_gradient_start = isset($data['milestone_gradient_start']) ? $data['milestone_gradient_start'] : '#4CAF50';
        $milestone_gradient_end = isset($data['milestone_gradient_end']) ? $data['milestone_gradient_end'] : '#2196F3';

        // Process milestone pages (for page-specific milestone type)
        $milestone_pages = '{}'; // Default to empty JSON object
        if ($milestone_type === 'page_specific' && isset($data['milestone_pages'])) {
            if (is_array($data['milestone_pages'])) {
                $milestone_pages = json_encode($data['milestone_pages']);
            } elseif (is_string($data['milestone_pages']) && !empty($data['milestone_pages'])) {
                // Handle JSON string format
                $decoded = json_decode(stripslashes($data['milestone_pages']), true);
                if ($decoded !== null) {
                    $milestone_pages = stripslashes($data['milestone_pages']);
                }
            }
        }

        // Process countdown options
        $countdown_enabled = isset($data['countdown_enabled']) ? (int)$data['countdown_enabled'] : 0;
        $countdown_seconds = isset($data['countdown_seconds']) ? (int)$data['countdown_seconds'] : 60;
        $countdown_message = isset($data['countdown_message']) ? $data['countdown_message'] : '';
        $countdown_click_activation = isset($data['countdown_click_activation']) ? (int)$data['countdown_click_activation'] : 0;
        $countdown_click_element_id = isset($data['countdown_click_element_id']) ? $data['countdown_click_element_id'] : '';
        $countdown_pre_click_message = isset($data['countdown_pre_click_message']) ? $data['countdown_pre_click_message'] : '';
        $countdown_standby = isset($data['countdown_standby']) ? (int)$data['countdown_standby'] : 0;

        // Use the WordPress $wpdb->update function
        $result = $wpdb->update(
            $table_name,
            [
                'button_text' => isset($data['button_text']) ? $data['button_text'] : $current_button['button_text'],
                'button_size' => isset($data['button_size']) ? $data['button_size'] : $current_button['button_size'],
                'button_color' => isset($data['button_color']) ? $data['button_color'] : $current_button['button_color'],
                'button_color_hex' => isset($data['button_color_hex']) ? $data['button_color_hex'] : ($current_button['button_color_hex'] ?? ''),
                'border_shape' => isset($data['border_shape']) ? $data['border_shape'] : $current_button['border_shape'],
                'redirect_url' => isset($data['redirect_url']) ? $data['redirect_url'] : $current_button['redirect_url'],
                'is_locked' => $is_locked,
                'required_faucets' => $required_faucets,
                'reset_minutes' => $reset_minutes,
                'lock_faucet' => $lock_faucet,
                'milestone_enabled' => $milestone_enabled,
                'milestone_type' => $milestone_type,
                'milestone_count' => $milestone_count,
                'milestone_specific_faucet' => $milestone_specific_faucet,
                'milestone_specific_count' => $milestone_specific_count,
                'milestone_lock_faucet' => $milestone_lock_faucet,
                'milestone_display_style' => $milestone_display_style,
                'milestone_transparent_bg' => $milestone_transparent_bg,
                'milestone_card_bg_style' => $milestone_card_bg_style,
                'milestone_card_bg_color' => $milestone_card_bg_color,
                'milestone_card_gradient_start' => $milestone_card_gradient_start,
                'milestone_card_gradient_end' => $milestone_card_gradient_end,
                'milestone_pages' => $milestone_pages,
                'milestone_bar_style' => $milestone_bar_style,
                'milestone_bar_color' => $milestone_bar_color,
                'milestone_gradient_start' => $milestone_gradient_start,
                'milestone_gradient_end' => $milestone_gradient_end,
                'countdown_enabled' => $countdown_enabled,
                'countdown_seconds' => $countdown_seconds,
                'countdown_message' => $countdown_message,
                'countdown_click_activation' => $countdown_click_activation,
                'countdown_click_element_id' => $countdown_click_element_id,
                'countdown_pre_click_message' => $countdown_pre_click_message,
                'countdown_standby' => $countdown_standby,
                'updated_at' => current_time('mysql')
            ],
            ['id' => $id]
        );

        // Log the result for debugging
        self::log("Button update result: " . ($result !== false ? "Success" : "Failed") . " for button ID: $id", 'info');
        if ($result === false) {
            self::log("Database error: " . $wpdb->last_error, 'error');
        }

        return $result !== false;
    }

    /**
     * Delete a button
     * @param int $id Button ID
     * @return bool Success or failure
     */
    public static function delete_button($id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return $wpdb->delete(
            $table_name,
            ['id' => absint($id)],
            ['%d']
        );
    }

    /**
     * Count buttons for a specific faucet
     * @param int $faucet_id Faucet ID
     * @return int Number of buttons
     */
    public static function count_faucet_buttons($faucet_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return (int) $wpdb->get_var(
            $wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE faucet_id = %d", absint($faucet_id))
        );
    }

    /**
     * Get all buttons from database
     * @return array Array of all buttons
     */
    public static function get_buttons()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_buttons';

        return $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY id ASC",
            ARRAY_A
        );
    }

    /**
     * Record a faucet claim for leaderboard tracking
     * @param string $user_hash User hash
     * @param int $faucet_id Faucet ID
     * @param string $amount Claim amount
     * @param string $currency Claim currency
     * @return bool Success or failure
     */
    public static function record_claim($user_hash, $faucet_id, $amount, $currency)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_claims';

        // Record the claim
        $result = $wpdb->insert(
            $table_name,
            [
                'user_hash' => $user_hash,
                'faucet_id' => absint($faucet_id),
                'timestamp' => current_time('mysql'),
                'amount' => sanitize_text_field($amount),
                'currency' => sanitize_text_field($currency),
                'status' => 'success'
            ],
            ['%s', '%d', '%s', '%s', '%s', '%s']
        );

        // Update the leaderboard
        if ($result) {
            Farmfaucet_Users::record_completion($user_hash);
        }

        return $result !== false;
    }

    /**
     * Toggle faucet enabled/disabled status
     * @param int $id Faucet ID
     * @param int $is_enabled 1 for enabled, 0 for disabled
     * @return bool Success or failure
     */
    public static function toggle_faucet_status($id, $is_enabled)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_faucets';

        return $wpdb->update(
            $table_name,
            ['is_enabled' => $is_enabled ? 1 : 0, 'updated_at' => current_time('mysql')],
            ['id' => absint($id)],
            ['%d', '%s'],
            ['%d']
        );
    }
}
