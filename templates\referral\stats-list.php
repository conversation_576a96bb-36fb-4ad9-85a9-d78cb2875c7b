<?php
/**
 * Template for displaying referral statistics in list layout
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-referral-stats-container">
    <h2 class="stats-title"><?php echo esc_html($atts['title']); ?></h2>
    
    <div class="referral-stats-list">
        <?php if ($atts['show_referrals']) : ?>
            <div class="referral-stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-label"><?php esc_html_e('Total Referrals', 'farmfaucet'); ?></div>
                    <div class="stat-value"><?php echo esc_html($stats['total_referrals']); ?></div>
                    <div class="stat-info">
                        <span class="stat-active"><?php echo esc_html($stats['active_referrals']); ?> <?php esc_html_e('active', 'farmfaucet'); ?></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($atts['show_earnings']) : ?>
            <div class="referral-stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-money-alt"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-label"><?php esc_html_e('Total Earnings', 'farmfaucet'); ?></div>
                    <div class="stat-value">
                        <?php 
                        if (!empty($currency_info)) {
                            echo esc_html($stats['total_earnings']) . ' ' . esc_html($currency_info['symbol']);
                        } else {
                            echo esc_html($stats['total_earnings']);
                        }
                        ?>
                    </div>
                    <?php if (!empty($currency_info)) : ?>
                        <div class="stat-info">
                            <span class="stat-currency"><?php echo esc_html($currency_info['name']); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($atts['show_visits']) : ?>
            <div class="referral-stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-visibility"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-label"><?php esc_html_e('Total Visits', 'farmfaucet'); ?></div>
                    <div class="stat-value"><?php echo esc_html($stats['total_visits']); ?></div>
                    <div class="stat-info">
                        <span class="stat-converted"><?php echo esc_html($stats['converted_visits']); ?> <?php esc_html_e('converted', 'farmfaucet'); ?></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($atts['show_conversion']) : ?>
            <div class="referral-stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-label"><?php esc_html_e('Conversion Rate', 'farmfaucet'); ?></div>
                    <div class="stat-value"><?php echo esc_html($stats['conversion_rate']); ?>%</div>
                    <div class="stat-info">
                        <div class="conversion-bar">
                            <div class="conversion-progress" style="width: <?php echo esc_attr(min(100, $stats['conversion_rate'])); ?>%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
