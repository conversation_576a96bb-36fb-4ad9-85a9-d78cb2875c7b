/**
 * Simple CSS for captcha display
 */

/* Basic captcha container styling */
.farmfaucet-captcha-container {
    margin: 20px auto;
    padding: 10px;
    position: relative;
    min-height: 100px;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
}

/* Captcha wrapper styling */
.h-captcha-wrapper,
.g-recaptcha-wrapper {
    width: 100%;
    max-width: 300px;
    min-height: 78px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
}

/* Base captcha element styling */
.h-captcha,
.g-recaptcha {
    width: 100%;
    max-width: 300px;
    min-height: 78px;
    display: block;
    margin: 0 auto;
}

/* Make sure captcha iframes are visible */
.h-captcha iframe,
.g-recaptcha iframe,
iframe[src*="hcaptcha.com"],
iframe[src*="google.com/recaptcha"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 0 auto !important;
    z-index: 1 !important;
    position: relative !important;
    min-height: 70px !important;
}

/* Fix for captcha popup */
div[style*="z-index: 2000000000"],
div[style*="z-index: 2000000000"] iframe,
.hcaptcha-box,
.hcaptcha-box iframe,
.grecaptcha-overlay,
.grecaptcha-overlay iframe {
    z-index: 9999 !important;
    position: fixed !important;
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
}

/* Loading message styling */
.farmfaucet-captcha-loading {
    display: block;
    text-align: center;
    font-style: italic;
    color: #666;
    padding: 10px;
    margin: 10px 0;
    background-color: #f0f0f0;
    border-radius: 4px;
}

/* Error message styling */
.farmfaucet-error {
    color: #d63638;
    background-color: #ffeeee;
    border: 1px solid #ffcccc;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

/* Ensure claim button has rounded borders */
.farmfaucet-claim-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 25px !important;
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    margin-top: 20px;
}

.farmfaucet-claim-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
    border-radius: 25px !important;
}

/* Notification styling */
.farmfaucet-notification {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
    display: none;
}

.farmfaucet-notification.success {
    background-color: #eeffee;
    border: 1px solid #ccffcc;
    color: #00a32a;
}

.farmfaucet-notification.error {
    background-color: #ffeeee;
    border: 1px solid #ffcccc;
    color: #d63638;
}
