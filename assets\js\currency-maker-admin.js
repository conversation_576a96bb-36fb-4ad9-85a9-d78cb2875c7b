/**
 * Currency Maker Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initCurrencyMakerAdmin();
    });

    /**
     * Initialize Currency Maker Admin functionality
     */
    function initCurrencyMakerAdmin() {
        console.log('Initializing Currency Maker Admin with improved color picker handling');

        // Remove any existing event handlers to prevent duplicates
        $('#farmfaucet-add-currency').off('click');
        $('.farmfaucet-edit-currency-button').off('click');
        $('.farmfaucet-delete-currency-button').off('click');
        $('.farmfaucet-modal-close').off('click');
        $('#farmfaucet-save-currency').off('click');
        $('#farmfaucet-currency-form').off('submit');

        // Add event handler for the Add Currency button
        $('#farmfaucet-add-currency').on('click', function() {
            console.log('Add Currency button clicked');
            showCurrencyModal();
        });

        // Initialize color picker with proper event handling
        if ($.fn.wpColorPicker) {
            // First destroy any existing color pickers to avoid duplicates
            $('.color-picker').each(function() {
                if ($(this).hasClass('wp-color-picker')) {
                    $(this).wpColorPicker('destroy');
                }
            });

            // Initialize color picker with proper event handling
            $('.color-picker').wpColorPicker({
                defaultColor: '#4CAF50',
                change: function(event, ui) {
                    // Update the input value when color changes
                    $(this).val(ui.color.toString());
                    $(this).trigger('change');
                    console.log('Color picker changed to:', ui.color.toString());

                    // Store the color value in a data attribute for easy access
                    $(this).attr('data-color', ui.color.toString());
                },
                clear: function() {
                    // Set default color when cleared
                    $(this).val('#4CAF50');
                    $(this).attr('data-color', '#4CAF50');
                    $(this).trigger('change');
                    console.log('Color picker cleared, set to default: #4CAF50');
                }
            });

            // Initialize any color pickers that might be added dynamically
            $(document).on('focus', '.color-picker:not(.wp-color-picker)', function() {
                $(this).wpColorPicker({
                    defaultColor: '#4CAF50',
                    change: function(event, ui) {
                        $(this).val(ui.color.toString());
                        $(this).attr('data-color', ui.color.toString());
                        $(this).trigger('change');
                    },
                    clear: function() {
                        $(this).val('#4CAF50');
                        $(this).attr('data-color', '#4CAF50');
                        $(this).trigger('change');
                    }
                });
            });
        }

        // Add currency button click handler
        $('.farmfaucet-add-currency-button').on('click', function() {
            showCurrencyModal();
        });

        // Edit currency button click handler
        $('.farmfaucet-edit-currency-button').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            editCurrency(currencyId);
        });

        // Delete currency button click handler
        $('.farmfaucet-delete-currency-button').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            deleteCurrency(currencyId);
        });

        // Modal close button click handler
        $('.farmfaucet-modal-close').on('click', function() {
            closeCurrencyModal();
        });

        // Save currency button click handler
        $('#farmfaucet-save-currency').on('click', function() {
            saveCurrency();
        });

        // Close modal when clicking outside
        $(window).on('click', function(event) {
            if ($(event.target).hasClass('farmfaucet-modal')) {
                closeCurrencyModal();
            }
        });

        // Form submission handler
        $('#farmfaucet-currency-form').on('submit', function(e) {
            e.preventDefault();
            saveCurrency();
        });
    }

    /**
     * Show currency modal
     */
    function showCurrencyModal() {
        console.log('Showing currency modal');

        // Reset form
        $('#farmfaucet-currency-form')[0].reset();
        $('#currency-id').val('');

        // Reset color picker
        if ($.fn.wpColorPicker) {
            $('#currency-color').val('#4CAF50');
            $('#currency-color').attr('data-color', '#4CAF50');

            // If color picker is already initialized, update the color
            if ($('#currency-color').hasClass('wp-color-picker')) {
                $('#currency-color').wpColorPicker('color', '#4CAF50');
            } else {
                // Initialize color picker
                $('#currency-color').wpColorPicker({
                    defaultColor: '#4CAF50',
                    change: function(event, ui) {
                        $(this).val(ui.color.toString());
                        $(this).attr('data-color', ui.color.toString());
                    }
                });
            }
        }

        // Update modal title
        $('.modal-title').text('Add Currency');

        // Show modal
        $('#farmfaucet-currency-modal').fadeIn(300);
    }

    /**
     * Close currency modal
     */
    function closeCurrencyModal() {
        $('#farmfaucet-currency-modal').fadeOut(300);
    }

    /**
     * Edit currency
     *
     * @param {number} currencyId Currency ID to edit
     */
    function editCurrency(currencyId) {
        console.log('Editing currency with ID:', currencyId);

        // First try to get currency data via AJAX for more reliable data
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success && response.data && response.data.currency) {
                    const currency = response.data.currency;
                    console.log('Got currency data from AJAX:', currency);

                    // Populate form with data from AJAX
                    $('#currency-id').val(currency.id);
                    $('#currency-name').val(currency.name);
                    $('#currency-code').val(currency.code);
                    $('#currency-symbol').val(currency.symbol);
                    $('#currency-base').val(currency.base_currency);
                    $('#currency-rate').val(currency.exchange_rate);

                    // Set color - use the stored color or default to green
                    const colorValue = currency.color || '#4CAF50';
                    $('#currency-color').val(colorValue);
                    $('#currency-color').attr('data-color', colorValue);

                    // Update color picker
                    if ($.fn.wpColorPicker) {
                        $('#currency-color').wpColorPicker('color', colorValue);
                    }

                    // Set icon
                    $('#currency-icon').val(currency.icon || '');

                    // Set currency type
                    $('#currency-type').val(currency.currency_type || 'earnings');

                    // Set status
                    $('#currency-active').prop('checked', currency.is_active == 1);

                    // Update modal title
                    $('.modal-title').text('Edit Currency');

                    // Show modal
                    $('#farmfaucet-currency-modal').fadeIn(300);
                } else {
                    // Fallback to getting data from the table row
                    editCurrencyFromRow(currencyId);
                }
            },
            error: function() {
                // Fallback to getting data from the table row
                editCurrencyFromRow(currencyId);
            }
        });
    }

    /**
     * Edit currency using data from the table row (fallback method)
     *
     * @param {number} currencyId Currency ID to edit
     */
    function editCurrencyFromRow(currencyId) {
        console.log('Falling back to editing currency from table row');

        // Get currency data from the table row
        const $row = $('tr[data-currency-id="' + currencyId + '"]');

        if ($row.length === 0) {
            console.error('Currency row not found for ID:', currencyId);
            return;
        }

        // Set form values
        $('#currency-id').val(currencyId);
        $('#currency-name').val($row.find('td:eq(0)').text().trim());
        $('#currency-code').val($row.find('td:eq(1)').text().trim());
        $('#currency-symbol').val($row.find('td:eq(2)').text().trim());
        $('#currency-base').val($row.find('td:eq(3)').text().trim());
        $('#currency-rate').val(parseFloat($row.find('td:eq(4)').text().trim()));

        // Set color
        const color = $row.find('.color-preview').css('background-color');
        const hexColor = rgbToHex(color);
        $('#currency-color').val(hexColor);
        $('#currency-color').attr('data-color', hexColor);

        // Update color picker
        if ($.fn.wpColorPicker) {
            $('#currency-color').wpColorPicker('color', hexColor);
        }

        // Set icon
        const $icon = $row.find('.currency-icon');
        $('#currency-icon').val($icon.length > 0 ? $icon.attr('src') : '');

        // Set currency type
        const currencyType = $row.data('currency-type') || 'earnings';
        $('#currency-type').val(currencyType);

        // Set status
        const isActive = $row.find('.status-active').length > 0;
        $('#currency-active').prop('checked', isActive);

        // Update modal title
        $('.modal-title').text('Edit Currency');

        // Show modal
        $('#farmfaucet-currency-modal').fadeIn(300);
    }
    }

    /**
     * Delete currency
     *
     * @param {number} currencyId Currency ID to delete
     */
    function deleteCurrency(currencyId) {
        if (!confirm(farmfaucetCurrencyMakerAdmin.i18n.confirmDelete)) {
            return;
        }

        // Send AJAX request
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || farmfaucetCurrencyMakerAdmin.i18n.deleteSuccess);

                    // Remove the row from the table
                    $('tr[data-currency-id="' + currencyId + '"]').fadeOut(300, function() {
                        $(this).remove();

                        // Show "no currencies" message if table is empty
                        if ($('.farmfaucet-currencies-table tbody tr').length === 0) {
                            $('.farmfaucet-currencies-table').replaceWith(
                                '<p class="farmfaucet-no-currencies">' +
                                'No currencies found. Click the "Add Currency" button to create your first currency.' +
                                '</p>'
                            );
                        }
                    });
                } else {
                    alert(response.data.message || farmfaucetCurrencyMakerAdmin.i18n.deleteFailed);
                }
            },
            error: function() {
                alert(farmfaucetCurrencyMakerAdmin.i18n.error);
            }
        });
    }

    /**
     * Save currency
     */
    function saveCurrency() {
        console.log('Saving currency');

        // Validate form
        const $form = $('#farmfaucet-currency-form');

        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }

        // Get form data
        const currencyId = $('#currency-id').val();
        const isUpdate = currencyId !== '';

        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-currency');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');

        // Get color value - try multiple approaches to ensure we get a valid color
        let colorValue;

        // First try to get from data attribute (set by color picker)
        const $colorInput = $('#currency-color');
        if ($colorInput.attr('data-color')) {
            colorValue = $colorInput.attr('data-color');
            console.log('Got color from data-color attribute:', colorValue);
        }
        // Then try to get from the iris color picker directly
        else if ($colorInput.closest('.wp-picker-container').find('.wp-color-result').length) {
            const $colorResult = $colorInput.closest('.wp-picker-container').find('.wp-color-result');
            if ($colorResult.css('background-color')) {
                const rgbColor = $colorResult.css('background-color');
                colorValue = rgbToHex(rgbColor);
                console.log('Got color from color picker result:', colorValue);
            }
        }
        // Then try the input value
        else if ($colorInput.val() && $colorInput.val().startsWith('#')) {
            colorValue = $colorInput.val();
            console.log('Got color from input value:', colorValue);
        }
        // Finally, use default color
        else {
            colorValue = '#4CAF50';
            console.log('Using default color:', colorValue);
        }

        // Ensure we have a valid color
        if (!colorValue || colorValue === 'undefined' || colorValue === 'null') {
            colorValue = '#4CAF50';
            console.log('Color was invalid, using default:', colorValue);
        }

        console.log('Final color value before submission:', colorValue);
        $saveButton.prop('disabled', true).text('Saving...');

        const formData = {
            action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
            nonce: farmfaucetCurrencyMakerAdmin.nonce,
            name: $('#currency-name').val(),
            code: $('#currency-code').val(),
            symbol: $('#currency-symbol').val(),
            base_currency: $('#currency-base').val(),
            exchange_rate: $('#currency-rate').val(),
            color: colorValue,
            icon: $('#currency-icon').val(),
            currency_type: $('#currency-type').val(),
            is_active: $('#currency-active').is(':checked') ? 1 : 0
        };

        console.log('Sending form data:', formData);

        if (isUpdate) {
            formData.currency_id = currencyId;
        }

        // Send AJAX request
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || (isUpdate ? farmfaucetCurrencyMakerAdmin.i18n.updateSuccess : farmfaucetCurrencyMakerAdmin.i18n.createSuccess));

                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    console.error('Currency save error:', response);
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(response.data.message || (isUpdate ? farmfaucetCurrencyMakerAdmin.i18n.updateFailed : farmfaucetCurrencyMakerAdmin.i18n.createFailed));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $saveButton.prop('disabled', false).text(originalButtonText);

                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }

                alert(farmfaucetCurrencyMakerAdmin.i18n.error + ' - ' + errorMessage);
            }
        });
    }



    /**
     * Convert RGB color to HEX
     *
     * @param {string} rgb RGB color string (e.g., 'rgb(255, 0, 0)')
     * @return {string} HEX color string (e.g., '#ff0000')
     */
    function rgbToHex(rgb) {
        // Default to green if invalid
        if (!rgb) return '#4CAF50';

        // Extract RGB values
        const rgbMatch = rgb.match(/^rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i);
        if (!rgbMatch) return '#4CAF50';

        const r = parseInt(rgbMatch[1], 10).toString(16).padStart(2, '0');
        const g = parseInt(rgbMatch[2], 10).toString(16).padStart(2, '0');
        const b = parseInt(rgbMatch[3], 10).toString(16).padStart(2, '0');

        return `#${r}${g}${b}`;
    }
})(jQuery);
