<?php
/**
 * Template for displaying referral link
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="farmfaucet-referral-link-container">
    <h2 class="referral-title"><?php echo esc_html($atts['title']); ?></h2>
    
    <div class="referral-link-box">
        <div class="referral-link-input">
            <input type="text" id="farmfaucet-referral-link" value="<?php echo esc_url($referral_link); ?>" readonly>
            <button id="farmfaucet-copy-link" class="farmfaucet-button" data-copied-text="<?php echo esc_attr($atts['button_copied_text']); ?>">
                <?php echo esc_html($atts['button_text']); ?>
            </button>
        </div>
        
        <?php if ($atts['show_code']) : ?>
            <div class="referral-code">
                <span class="referral-code-label"><?php esc_html_e('Your Referral Code:', 'farmfaucet'); ?></span>
                <span class="referral-code-value"><?php echo esc_html($referral_code); ?></span>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($atts['show_stats'] && $stats) : ?>
        <div class="referral-stats">
            <h3><?php esc_html_e('Your Referral Stats', 'farmfaucet'); ?></h3>
            
            <div class="referral-stats-grid">
                <div class="referral-stat-item">
                    <div class="stat-value"><?php echo esc_html($stats['total_referrals']); ?></div>
                    <div class="stat-label"><?php esc_html_e('Total Referrals', 'farmfaucet'); ?></div>
                </div>
                
                <div class="referral-stat-item">
                    <div class="stat-value"><?php echo esc_html($stats['active_referrals']); ?></div>
                    <div class="stat-label"><?php esc_html_e('Active Referrals', 'farmfaucet'); ?></div>
                </div>
                
                <div class="referral-stat-item">
                    <div class="stat-value">
                        <?php 
                        if (!empty($currency_info)) {
                            echo esc_html($stats['total_earnings']) . ' ' . esc_html($currency_info['symbol']);
                        } else {
                            echo esc_html($stats['total_earnings']);
                        }
                        ?>
                    </div>
                    <div class="stat-label"><?php esc_html_e('Total Earnings', 'farmfaucet'); ?></div>
                </div>
                
                <div class="referral-stat-item">
                    <div class="stat-value"><?php echo esc_html($stats['conversion_rate']); ?>%</div>
                    <div class="stat-label"><?php esc_html_e('Conversion Rate', 'farmfaucet'); ?></div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    (function() {
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('farmfaucet-copy-link');
            const linkInput = document.getElementById('farmfaucet-referral-link');
            
            if (copyButton && linkInput) {
                copyButton.addEventListener('click', function() {
                    // Select the text
                    linkInput.select();
                    linkInput.setSelectionRange(0, 99999); // For mobile devices
                    
                    // Copy the text
                    document.execCommand('copy');
                    
                    // Change button text
                    const originalText = copyButton.textContent;
                    const copiedText = copyButton.dataset.copiedText;
                    
                    copyButton.textContent = copiedText;
                    copyButton.classList.add('copied');
                    
                    // Reset button text after 2 seconds
                    setTimeout(function() {
                        copyButton.textContent = originalText;
                        copyButton.classList.remove('copied');
                    }, 2000);
                });
            }
        });
    })();
</script>
