<?php
class Farmfaucet_Frontend
{
    private static $instance;

    public static function init()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        // Register the main shortcode
        add_shortcode('farmfaucet', [$this, 'render_claim_form']);

        // Register dynamic shortcodes for each faucet
        $this->register_dynamic_shortcodes();

        // Register button shortcode
        add_shortcode('farmfaucet_button', [$this, 'render_faucet_button']);

        // Register task completion percentage shortcode
        add_shortcode('farmfaucet_completion', [$this, 'render_completion_percentage']);

        // Register shortcodes list shortcode
        add_shortcode('farmfaucet_shortcodes', [$this, 'render_shortcodes_list']);

        // Register leaderboard and user profile shortcodes
        add_shortcode('farmfaucet_leaderboard', [$this, 'render_leaderboard']);
        add_shortcode('farmfaucet_username', [$this, 'render_username']);
        add_shortcode('farmfaucet_username_form', [$this, 'render_username_form']);
        add_shortcode('farmfaucet_avatar', [$this, 'render_avatar']);
        add_shortcode('farmfaucet_avatar_editable', [$this, 'render_avatar_editable']);

        // Register withdrawal and conversion history shortcodes
        add_shortcode('farmfaucet_withdrawal_history', [$this, 'render_withdrawal_history']);
        add_shortcode('farmfaucet_conversion_history', [$this, 'render_conversion_history']);

        // Register AJAX handlers for profile updates
        add_action('wp_ajax_farmfaucet_update_username', [$this, 'update_leaderboard_name']);
        add_action('wp_ajax_nopriv_farmfaucet_update_username', [$this, 'update_leaderboard_name']);
        add_action('wp_ajax_farmfaucet_update_avatar', [$this, 'update_avatar']);
        add_action('wp_ajax_nopriv_farmfaucet_update_avatar', [$this, 'update_avatar']);
        add_action('wp_ajax_farmfaucet_send_otp', [$this, 'send_otp']);
        add_action('wp_ajax_nopriv_farmfaucet_send_otp', [$this, 'send_otp']);

        // Register scripts and AJAX handlers
        add_action('wp_enqueue_scripts', [$this, 'enqueue_public_assets']);
        add_action('wp_ajax_process_claim', [$this, 'process_claim']);
        add_action('wp_ajax_nopriv_process_claim', [$this, 'process_claim']);

        // Register AJAX handlers for dummy faucets
        add_action('wp_ajax_process_dummy_claim', [$this, 'process_dummy_claim']);
        add_action('wp_ajax_nopriv_process_dummy_claim', [$this, 'process_dummy_claim']);

        // Register AJAX handlers for withdrawal faucets
        add_action('wp_ajax_process_withdrawal', [$this, 'process_withdrawal']);
        add_action('wp_ajax_get_withdrawal_history', [$this, 'get_withdrawal_history']);

        // Register AJAX handlers for conversion faucets
        add_action('wp_ajax_process_conversion', [$this, 'process_conversion']);
        add_action('wp_ajax_get_conversion_history', [$this, 'get_conversion_history']);
    }

    /**
     * Register dynamic shortcodes for each faucet
     */
    private function register_dynamic_shortcodes()
    {
        // Get all faucets
        $faucets = Farmfaucet_Logger::get_faucets();

        if (!empty($faucets)) {
            foreach ($faucets as $faucet) {
                // Skip the default shortcode as it's already registered
                if ($faucet['shortcode'] !== 'farmfaucet') {
                    add_shortcode($faucet['shortcode'], function () use ($faucet) {
                        return $this->render_claim_form(['id' => $faucet['id']]);
                    });
                }
            }
        }
    }

    public function enqueue_public_assets()
    {
        // Only load assets if we're on a page with our shortcode
        if (!$this->is_farmfaucet_page()) {
            return;
        }

        // Get captcha type
        $captcha_type = get_option('farmfaucet_captcha_type', 'hcaptcha');

        // Plugin assets - load CSS first
        wp_enqueue_style(
            'farmfaucet-style',
            FARMFAUCET_URL . 'assets/css/style.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load public CSS with milestone and completion styles
        wp_enqueue_style(
            'farmfaucet-public',
            FARMFAUCET_URL . 'assets/css/public.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load milestone-specific CSS
        wp_enqueue_style(
            'farmfaucet-milestone',
            FARMFAUCET_URL . 'assets/css/milestone.css',
            ['farmfaucet-style', 'farmfaucet-public'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load milestone compact view fix CSS
        wp_enqueue_style(
            'farmfaucet-milestone-compact-view-fix',
            FARMFAUCET_URL . 'assets/css/milestone-compact-view-fix.css',
            ['farmfaucet-milestone'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load leaderboard CSS
        wp_enqueue_style(
            'farmfaucet-leaderboard',
            FARMFAUCET_URL . 'assets/css/leaderboard.css',
            ['farmfaucet-style', 'farmfaucet-public'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load simplified captcha CSS
        wp_enqueue_style(
            'farmfaucet-captcha-simple',
            FARMFAUCET_URL . 'assets/css/captcha-simple.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load disabled faucet CSS
        wp_enqueue_style(
            'farmfaucet-disabled-faucet',
            FARMFAUCET_URL . 'assets/css/disabled-faucet.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load countdown standby CSS
        wp_enqueue_style(
            'farmfaucet-countdown-standby',
            FARMFAUCET_URL . 'assets/css/countdown-standby.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load captcha button CSS
        wp_enqueue_style(
            'farmfaucet-captcha-button',
            FARMFAUCET_URL . 'assets/css/captcha-button.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load form transparency CSS (consolidated)
        wp_enqueue_style(
            'farmfaucet-form-transparency-consolidated',
            FARMFAUCET_URL . 'assets/css/form-transparency-consolidated.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load notification system CSS
        wp_enqueue_style(
            'farmfaucet-notifications',
            FARMFAUCET_URL . 'assets/css/farmfaucet-notifications.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Always load jQuery first
        wp_enqueue_script('jquery');

        // Set script dependencies
        $script_deps = ['jquery'];

        // Load unified captcha script first - this is critical for captcha initialization
        wp_enqueue_script(
            'farmfaucet-captcha-unified',
            FARMFAUCET_URL . 'assets/js/captcha-unified.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            false // Load in header to ensure it's available early
        );
        $script_deps[] = 'farmfaucet-captcha-unified';

        // Also load the simplified captcha script for backward compatibility
        wp_enqueue_script(
            'farmfaucet-captcha-simple',
            FARMFAUCET_URL . 'assets/js/captcha-simple.js',
            ['jquery', 'farmfaucet-captcha-unified'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            false // Load in header to ensure it's available early
        );
        $script_deps[] = 'farmfaucet-captcha-simple';

        // Add inline script to ensure jQuery is available for captcha callbacks
        wp_add_inline_script('farmfaucet-captcha-simple', 'window.farmfaucetJQuery = jQuery; window.farmfaucetCaptchaWidgets = window.farmfaucetCaptchaWidgets || {};', 'before');

        // Add notification system script
        wp_enqueue_script(
            'farmfaucet-notifications',
            FARMFAUCET_URL . 'assets/js/farmfaucet-notifications.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Add form style fix script
        wp_enqueue_script(
            'farmfaucet-form-style-fix',
            FARMFAUCET_URL . 'assets/js/form-style-fix.js',
            ['jquery', 'farmfaucet-captcha-unified', 'farmfaucet-notifications'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Add view style fix CSS
        wp_enqueue_style(
            'farmfaucet-view-style-fix',
            FARMFAUCET_URL . 'assets/css/view-style-fix.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Get the global captcha type to determine which API to load first
        $captcha_type = get_option('farmfaucet_captcha_type', 'hcaptcha');

        // Load the selected captcha API first to prioritize it
        if ($captcha_type === 'hcaptcha') {
            // hCaptcha API - load in header without defer/async to ensure proper loading
            wp_enqueue_script(
                'hcaptcha',
                'https://js.hcaptcha.com/1/api.js?onload=farmfaucetHcaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                false // Load in header to ensure it's available early
            );
            $script_deps[] = 'hcaptcha';

            // Load other captcha APIs after the primary one
            wp_enqueue_script(
                'recaptcha',
                'https://www.google.com/recaptcha/api.js?onload=farmfaucetRecaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );

            wp_enqueue_script(
                'turnstile',
                'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=farmfaucetTurnstileLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );
        } elseif ($captcha_type === 'recaptcha') {
            // reCAPTCHA API - load in header without defer/async to ensure proper loading
            wp_enqueue_script(
                'recaptcha',
                'https://www.google.com/recaptcha/api.js?onload=farmfaucetRecaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                false // Load in header to ensure it's available early
            );
            $script_deps[] = 'recaptcha';

            // Load other captcha APIs after the primary one
            wp_enqueue_script(
                'hcaptcha',
                'https://js.hcaptcha.com/1/api.js?onload=farmfaucetHcaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );

            wp_enqueue_script(
                'turnstile',
                'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=farmfaucetTurnstileLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );
        } else {
            // Cloudflare Turnstile API - load in header without defer/async to ensure proper loading
            wp_enqueue_script(
                'turnstile',
                'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=farmfaucetTurnstileLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                false // Load in header to ensure it's available early
            );
            $script_deps[] = 'turnstile';

            // Load other captcha APIs after the primary one
            wp_enqueue_script(
                'hcaptcha',
                'https://js.hcaptcha.com/1/api.js?onload=farmfaucetHcaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );

            wp_enqueue_script(
                'recaptcha',
                'https://www.google.com/recaptcha/api.js?onload=farmfaucetRecaptchaLoad&render=explicit',
                ['jquery', 'farmfaucet-captcha-simple'],
                null,
                true // Load in footer to avoid conflicts
            );
        }

        // Now load our main script with the proper dependencies
        // Only include the primary captcha as a dependency to avoid conflicts
        wp_enqueue_script(
            'farmfaucet-script',
            FARMFAUCET_URL . 'assets/js/script.js',
            ['jquery', 'farmfaucet-captcha-simple', $script_deps[count($script_deps) - 1]],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the milestone messages script for dynamic progress messages
        wp_enqueue_script(
            'farmfaucet-milestone-messages',
            FARMFAUCET_URL . 'assets/js/milestone-messages.js',
            ['jquery', 'farmfaucet-script'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the milestone fix script to ensure proper display
        wp_enqueue_script(
            'farmfaucet-milestone-fix',
            FARMFAUCET_URL . 'assets/js/milestone-fix.js',
            ['jquery', 'farmfaucet-milestone-messages'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the progress bar fix script
        wp_enqueue_script(
            'farmfaucet-progress-bar-fix',
            FARMFAUCET_URL . 'assets/js/progress-bar-fix.js',
            ['jquery', 'farmfaucet-milestone-fix'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the milestone progress fix script
        wp_enqueue_script(
            'farmfaucet-milestone-progress-fix',
            FARMFAUCET_URL . 'assets/js/milestone-progress-fix.js',
            ['jquery', 'farmfaucet-progress-bar-fix'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the new milestone progress fix script
        wp_enqueue_script(
            'farmfaucet-milestone-progress-fix-new',
            FARMFAUCET_URL . 'assets/js/milestone-progress-fix-new.js',
            ['jquery', 'farmfaucet-milestone-progress-fix'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the consolidated faucet type fix script
        wp_enqueue_script(
            'farmfaucet-faucet-type-consolidated-fix',
            FARMFAUCET_URL . 'assets/js/faucet-type-consolidated-fix.js',
            ['jquery', 'farmfaucet-public'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the milestone compact view fix script
        wp_enqueue_script(
            'farmfaucet-milestone-compact-fix',
            FARMFAUCET_URL . 'assets/js/milestone-compact-fix.js',
            ['jquery', 'farmfaucet-milestone-progress-fix'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the captcha button script
        wp_enqueue_script(
            'farmfaucet-captcha-button',
            FARMFAUCET_URL . 'assets/js/captcha-button.js',
            ['jquery', 'farmfaucet-script'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Load the withdrawal faucet script
        wp_enqueue_script(
            'farmfaucet-withdrawal-faucet',
            FARMFAUCET_URL . 'assets/js/withdrawal-faucet.js',
            ['jquery', 'farmfaucet-script'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Localize the withdrawal faucet script
        wp_localize_script('farmfaucet-withdrawal-faucet', 'farmfaucetWithdrawal', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_withdrawal_nonce'),
            'i18n' => [
                'invalidAmount' => __('Please enter a valid amount', 'farmfaucet'),
                'belowMinimum' => __('Amount is below minimum withdrawal', 'farmfaucet'),
                'aboveMaximum' => __('Amount is above your balance', 'farmfaucet'),
                'emptyWallet' => __('Please enter a wallet address', 'farmfaucet'),
                'captchaRequired' => __('Please complete the captcha', 'farmfaucet'),
                'processing' => __('Processing your withdrawal...', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet'),
                'withdrawButtonText' => __('WITHDRAW', 'farmfaucet'),
                'convertButtonText' => __('CONVERT', 'farmfaucet')
            ]
        ]);

        // Load the withdrawal faucet CSS
        wp_enqueue_style(
            'farmfaucet-withdrawal-faucet',
            FARMFAUCET_URL . 'assets/css/withdrawal-faucet.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load the conversion faucet CSS
        wp_enqueue_style(
            'farmfaucet-conversion-faucet',
            FARMFAUCET_URL . 'assets/css/conversion-faucet.css',
            ['farmfaucet-style'],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Load the conversion faucet script
        wp_enqueue_script(
            'farmfaucet-conversion-faucet',
            FARMFAUCET_URL . 'assets/js/conversion-faucet.js',
            ['jquery', 'farmfaucet-script'],
            FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
            true
        );

        // Localize the conversion faucet script
        wp_localize_script('farmfaucet-conversion-faucet', 'farmfaucetConversion', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_conversion_nonce'),
            'i18n' => [
                'invalidAmount' => __('Please enter a valid amount', 'farmfaucet'),
                'belowMinimum' => __('Amount is below minimum conversion amount', 'farmfaucet'),
                'aboveMaximum' => __('Amount exceeds your balance', 'farmfaucet'),
                'noCurrency' => __('Please select a currency to convert to', 'farmfaucet'),
                'captchaRequired' => __('Please complete the captcha', 'farmfaucet'),
                'processing' => __('Processing your conversion...', 'farmfaucet'),
                'error' => __('An error occurred. Please try again.', 'farmfaucet'),
                'convertButtonText' => __('CONVERT', 'farmfaucet')
            ]
        ]);

        // Localization - make sure all necessary data is available
        wp_localize_script('farmfaucet-script', 'farmfaucet_vars', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('farmfaucet_claim_nonce'),
            'captcha_type' => get_option('farmfaucet_captcha_type', 'hcaptcha'),
            'hcaptcha_sitekey' => get_option('farmfaucet_hcaptcha_sitekey'),
            'recaptcha_sitekey' => get_option('farmfaucet_recaptcha_sitekey'),
            'turnstile_sitekey' => get_option('farmfaucet_turnstile_sitekey'),
            'redirect_url' => esc_url(get_option('farmfaucet_redirect_url')),
            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG,
            'translations' => [
                'complete_captcha' => esc_html__('Please complete the captcha', 'farmfaucet'),
                'invalid_email' => esc_html__('Please enter a valid email address', 'farmfaucet'),
                'processing' => esc_html__('Processing...', 'farmfaucet'),
                'success_message' => esc_html__('has been sent to your FaucetPay account!', 'farmfaucet'),
                'claim_failed' => esc_html__('Claim failed', 'farmfaucet'),
                'connection_error' => esc_html__('Connection error', 'farmfaucet'),
                'claim_button' => esc_html__('CLAIM NOW', 'farmfaucet')
            ]
        ]);
    }

    /**
     * Check if current page contains any farmfaucet shortcode
     *
     * @return bool True if page contains farmfaucet shortcode
     */
    private function is_farmfaucet_page()
    {
        // If we're in admin, don't check
        if (is_admin()) {
            return false;
        }

        // If we're in a REST API request, don't check
        if (defined('REST_REQUEST') && REST_REQUEST) {
            return false;
        }

        // Get current post
        global $post;
        if (!is_object($post)) {
            return false;
        }

        // Check for any farmfaucet shortcode
        try {
            // Check for main shortcode
            if (has_shortcode($post->post_content, 'farmfaucet')) {
                return true;
            }

            // Check for button shortcode
            if (has_shortcode($post->post_content, 'farmfaucet_button')) {
                return true;
            }

            // Check for dynamic shortcodes
            $faucets = Farmfaucet_Logger::get_faucets();
            if (is_array($faucets)) {
                foreach ($faucets as $faucet) {
                    if (isset($faucet['shortcode']) && has_shortcode($post->post_content, $faucet['shortcode'])) {
                        return true;
                    }
                }
            }

            // Check for button shortcodes
            $buttons = Farmfaucet_Logger::get_buttons();
            if (is_array($buttons)) {
                foreach ($buttons as $button) {
                    if (isset($button['shortcode']) && has_shortcode($post->post_content, $button['shortcode'])) {
                        return true;
                    }
                }
            }
        } catch (Exception $e) {
            // Log the error but don't break the page
            error_log('Farmfaucet error: ' . $e->getMessage());
            return false;
        }

        return false;
    }

    /**
     * Render the claim form for a specific faucet
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered claim form
     */
    public function render_claim_form($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'id' => 0, // Default to the first faucet if none specified
        ], $atts);

        // Get faucet data
        $faucet_id = absint($atts['id']);

        if ($faucet_id > 0) {
            // Get specific faucet
            $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        } else {
            // Try to get faucet by shortcode
            $current_shortcode = $this->get_current_shortcode();
            if ($current_shortcode) {
                $faucet = Farmfaucet_Logger::get_faucet_by_shortcode($current_shortcode);
            }

            // If still no faucet, get the first one
            if (empty($faucet)) {
                $faucets = Farmfaucet_Logger::get_faucets(true); // Only get enabled faucets
                $faucet = !empty($faucets) ? $faucets[0] : null;
            }
        }

        // If no faucet found, show error message
        if (empty($faucet)) {
            return '<div class="farmfaucet-error">' . esc_html__('Faucet not found', 'farmfaucet') . '</div>';
        }

        // Check if faucet is disabled
        if (isset($faucet['is_enabled']) && $faucet['is_enabled'] == 0) {
            return '<div class="farmfaucet-container farmfaucet-disabled" data-faucet-id="' . esc_attr($faucet['id']) . '">
                <div class="farmfaucet-header">
                    <h2 class="status-header">' . esc_html__('Unavailable', 'farmfaucet') . '</h2>
                    <div class="farmfaucet-disabled-message">' . esc_html__('This faucet is currently unavailable. Please check back later.', 'farmfaucet') . '</div>
                </div>
            </div>';
        }

        // Prepare faucet data for the template
        // Note: We pass the faucet directly to the template

        // Load the appropriate template based on faucet type
        $faucet_type = isset($faucet['faucet_type']) ? $faucet['faucet_type'] : 'stage';

        ob_start();
        switch ($faucet_type) {
            case 'dummy':
                include FARMFAUCET_DIR . 'templates/dummy-faucet.php';
                break;
            case 'withdrawal':
                include FARMFAUCET_DIR . 'templates/withdrawal-faucet.php';
                break;
            case 'conversion':
                include FARMFAUCET_DIR . 'templates/conversion-faucet.php';
                break;
            case 'stage':
            default:
                include FARMFAUCET_DIR . 'templates/claim-form.php';
                break;
        }
        return ob_get_clean();
    }

    /**
     * Get the current shortcode being used
     *
     * @return string|null The current shortcode or null if not found
     */
    private function get_current_shortcode()
    {
        global $shortcode_tags;

        // Get the current post content
        $post = get_post();
        if (!$post) return null;

        $content = $post->post_content;

        // Check for each registered shortcode
        foreach (array_keys($shortcode_tags) as $tag) {
            if (strpos($tag, 'farmfaucet') !== false && has_shortcode($content, $tag)) {
                return $tag;
            }
        }

        return null;
    }

    /**
     * Render a faucet button
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered button HTML
     */
    public function render_faucet_button($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'id' => 0, // Button ID
        ], $atts);

        $button_id = absint($atts['id']);

        if (empty($button_id)) {
            return '<div class="farmfaucet-error">' . esc_html__('Button ID is required', 'farmfaucet') . '</div>';
        }

        // Get button data
        $button = Farmfaucet_Logger::get_button($button_id);

        if (empty($button)) {
            return '<div class="farmfaucet-error">' . esc_html__('Button not found', 'farmfaucet') . '</div>';
        }

        // Get faucet data
        $faucet = Farmfaucet_Logger::get_faucet($button['faucet_id']);

        if (empty($faucet)) {
            return '<div class="farmfaucet-error">' . esc_html__('Faucet not found', 'farmfaucet') . '</div>';
        }

        // Check if faucet is disabled
        if (isset($faucet['is_enabled']) && $faucet['is_enabled'] == 0) {
            // Return a disabled button
            $button_color = '#cccccc'; // Grey color for disabled buttons
            $button_padding = $this->get_button_padding($button['button_size']);
            $border_radius = $button['border_shape'] === 'rounded' ? '20px' : '4px';

            return '<div class="farmfaucet-button-container" data-faucet-id="' . esc_attr($faucet['id']) . '" data-button-id="' . esc_attr($button['id']) . '">
                <a class="farmfaucet-button farmfaucet-button-disabled" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">
                    ' . esc_html__('Unavailable', 'farmfaucet') . '
                </a>
            </div>';
        }

        // Check if faucet is on cooldown
        $user_ip = Farmfaucet_Security::get_user_ip();
        $transient_key = 'farmfaucet_cooldown_' . $faucet['id'] . '_' . md5($user_ip);
        $on_cooldown = get_transient($transient_key);

        // Get remaining time if on cooldown
        $remaining_time = 0;
        if ($on_cooldown) {
            $remaining_time = $this->get_remaining_cooldown_time($transient_key, $faucet['cooldown']);
        }

        // Check if button is locked and if required faucets have been completed
        $is_locked = false;
        $lock_message = '';
        $lock_faucet = !empty($button['lock_faucet']) && $button['lock_faucet'] == 1;

        // Check if milestone is enabled
        $milestone_enabled = !empty($button['milestone_enabled']) && $button['milestone_enabled'] == 1;
        $milestone_type = !empty($button['milestone_type']) ? $button['milestone_type'] : 'global';
        $milestone_count = !empty($button['milestone_count']) ? intval($button['milestone_count']) : 5;
        $milestone_lock_faucet = !empty($button['milestone_lock_faucet']) && $button['milestone_lock_faucet'] == 1;
        $milestone_pages = !empty($button['milestone_pages']) ? $button['milestone_pages'] : '';

        // Check if countdown is enabled
        $countdown_enabled = !empty($button['countdown_enabled']) && $button['countdown_enabled'] == 1;
        $countdown_seconds = !empty($button['countdown_seconds']) ? intval($button['countdown_seconds']) : 60;
        $countdown_message = !empty($button['countdown_message']) ? $button['countdown_message'] : esc_html__('Please wait', 'farmfaucet');
        $countdown_click_activation = !empty($button['countdown_click_activation']) && $button['countdown_click_activation'] == 1;
        $countdown_click_element_id = !empty($button['countdown_click_element_id']) ? $button['countdown_click_element_id'] : '';
        $countdown_pre_click_message = !empty($button['countdown_pre_click_message']) ? $button['countdown_pre_click_message'] : esc_html__('Click to start countdown', 'farmfaucet');

        // Ensure only one feature is enabled at a time
        if ($milestone_enabled) {
            $lock_faucet = false;
            $countdown_enabled = false;
        } else if ($countdown_enabled) {
            $lock_faucet = false;
            $milestone_enabled = false;
        } else if ($lock_faucet) {
            $milestone_enabled = false;
            $countdown_enabled = false;
        }

        // If countdown is enabled, we'll render a countdown timer instead of a button
        if ($countdown_enabled) {
            return $this->render_countdown_button(
                $button,
                $faucet,
                $countdown_seconds,
                $countdown_message,
                $countdown_click_activation,
                $countdown_click_element_id,
                $countdown_pre_click_message
            );
        }

        // If milestone is enabled, we'll render a progress bar instead of a button
        if ($milestone_enabled) {
            $milestone_lock_faucet = !empty($button['milestone_lock_faucet']) && $button['milestone_lock_faucet'] == 1;

            if ($milestone_type === 'global') {
                return $this->render_milestone_progress(
                    $button,
                    $faucet,
                    $milestone_count,
                    $milestone_lock_faucet
                );
            } else if ($milestone_type === 'page_specific') {
                return $this->render_page_milestone_progress(
                    $button,
                    $faucet,
                    $milestone_pages,
                    $milestone_lock_faucet
                );
            }
        }

        if (!empty($button['is_locked']) && !empty($button['required_faucets'])) {
            $required_faucets = explode(',', $button['required_faucets']);
            $completed_faucets = [];
            $missing_faucets = [];

            // Check each required faucet
            foreach ($required_faucets as $req_faucet_id) {
                $req_faucet = Farmfaucet_Logger::get_faucet($req_faucet_id);
                if (!$req_faucet) continue;

                // Check if this faucet has been completed
                $req_transient_key = 'farmfaucet_cooldown_' . $req_faucet_id . '_' . md5($user_ip);
                $req_on_cooldown = get_transient($req_transient_key);

                if ($req_on_cooldown) {
                    $completed_faucets[] = $req_faucet_id;
                } else {
                    $missing_faucets[] = $req_faucet;
                }
            }

            // If not all required faucets are completed, button is locked
            if (count($completed_faucets) < count($required_faucets)) {
                $is_locked = true;

                // Create lock message
                $lock_message = esc_html__('Complete these faucets first:', 'farmfaucet') . ' ';
                $faucet_names = [];
                foreach ($missing_faucets as $missing) {
                    $faucet_names[] = $missing['name'];
                }
                $lock_message .= implode(', ', $faucet_names);

                // If lock_faucet is enabled, we need to check if the current faucet should be locked
                if ($lock_faucet && !$on_cooldown) {
                    // Set a flag to indicate the faucet should be locked
                    $lock_message .= ' ' . esc_html__('(This faucet is locked until requirements are met)', 'farmfaucet');
                }
            } else if (!empty($button['reset_minutes']) && $button['reset_minutes'] > 0) {
                // All faucets completed, but check if we need to reset after a time period
                $completion_key = 'farmfaucet_button_completed_' . $button_id . '_' . md5($user_ip);
                $completion_time = get_transient($completion_key);

                if (!$completion_time) {
                    // Mark as completed now
                    set_transient($completion_key, time(), $button['reset_minutes'] * 60);
                }
            }
        }

        // Button style attributes
        $button_color_hex = isset($button['button_color_hex']) ? $button['button_color_hex'] : '';
        $button_color = $this->get_button_color($button['button_color'], $button_color_hex);
        $button_padding = $this->get_button_padding($button['button_size']);
        $border_radius = $button['border_shape'] === 'rounded' ? '20px' : '4px';

        // Button classes
        $button_classes = 'farmfaucet-button';
        // Cooldown takes precedence over locked state
        if ($on_cooldown) {
            $button_classes .= ' farmfaucet-button-disabled';
        } elseif ($is_locked) {
            $button_classes .= ' farmfaucet-button-locked';
        }

        // Button URL - either redirect URL or faucet page
        $button_url = !empty($button['redirect_url']) ? esc_url($button['redirect_url']) : '#';

        // Make sure the URL is properly formatted
        if ($button_url !== '#' && strpos($button_url, 'http') !== 0) {
            $button_url = 'https://' . ltrim($button_url, '/');
        }

        // Button HTML
        $output = '<div class="farmfaucet-button-container" data-faucet-id="' . esc_attr($faucet['id']) . '" data-button-id="' . esc_attr($button['id']) . '">';

        if ($on_cooldown) {
            // Disabled button with timer
            $output .= '<a class="' . esc_attr($button_classes) . '" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">';
            $output .= '<span class="farmfaucet-cooldown-timer" data-seconds="' . esc_attr($remaining_time) . '">' . $this->format_time($remaining_time) . '</span>';
            $output .= '</a>';
        } else if ($is_locked) {
            // Locked button - only show lock icon without displaying which faucets need to be completed
            $output .= '<a class="' . esc_attr($button_classes) . '" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">';
            $output .= '<span class="farmfaucet-lock-icon">🔒</span> ' . esc_html($button['button_text']);
            $output .= '</a>';
        } else {
            // Active button
            $output .= '<a href="' . esc_url($button_url) . '" class="' . esc_attr($button_classes) . '" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">';
            $output .= esc_html($button['button_text']);
            $output .= '</a>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Get remaining cooldown time in seconds
     *
     * @param string $transient_key Transient key
     * @param int $cooldown_period Total cooldown period in seconds
     * @return int Remaining time in seconds
     */
    private function get_remaining_cooldown_time($transient_key, $cooldown_period)
    {
        // Check if we have a cached value for this request
        static $cached_times = [];

        if (isset($cached_times[$transient_key])) {
            return $cached_times[$transient_key];
        }

        // First try to get the transient timeout directly from WordPress options
        $transient_timeout = get_option('_transient_timeout_' . $transient_key);

        if (!$transient_timeout) {
            // If no timeout found, but we know the cooldown period, calculate based on that
            if ($cooldown_period > 0) {
                // This is a fallback in case the transient exists but the timeout option doesn't
                $transient_value = get_transient($transient_key);
                if ($transient_value) {
                    // If we have a value but no timeout, estimate based on cooldown period
                    // This is not precise but better than showing 0
                    $cached_times[$transient_key] = $cooldown_period;
                    return $cooldown_period;
                }
            }
            $cached_times[$transient_key] = 0;
            return 0;
        }

        $current_time = time();
        $remaining_time = $transient_timeout - $current_time;
        $result = max(0, $remaining_time);

        // Cache the result for this request
        $cached_times[$transient_key] = $result;

        return $result;
    }

    /**
     * Format time in seconds to human-readable format
     *
     * @param int $seconds Time in seconds
     * @return string Formatted time string
     */
    private function format_time($seconds)
    {
        $seconds = max(0, $seconds);

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
        } else {
            return sprintf('%02d:%02d', $minutes, $secs);
        }
    }

    /**
     * Get button color based on color name or hex value
     *
     * @param string $color_name Color name or 'custom'
     * @param string $color_hex Hex color value for custom colors
     * @return string CSS color value
     */
    private function get_button_color($color_name, $color_hex = '')
    {
        $colors = [
            'blue' => '#2271b1',
            'green' => '#00a32a',
            'red' => '#d63638',
            'orange' => '#f56e28',
            'purple' => '#8c3db9',
            'black' => '#1d2327',
            'pink' => '#e91e63',
            'violet' => '#9c27b0',
            'deep-purple' => '#673ab7',
            'indigo' => '#3f51b5',
            'light-blue' => '#03a9f4',
            'teal' => '#009688',
            'light-green' => '#4caf50',
            'lime' => '#8bc34a',
            'yellow-green' => '#cddc39',
            'yellow' => '#ffeb3b',
            'amber' => '#ffc107',
            'deep-orange' => '#ff9800'
        ];

        // If this is a custom color and we have a hex value, use it
        if ($color_name === 'custom' && !empty($color_hex) && preg_match('/^#[0-9a-f]{3,6}$/i', $color_hex)) {
            return $color_hex;
        }

        // If the color name itself is a hex code, return it directly
        if (preg_match('/^#[0-9a-f]{3,6}$/i', $color_name)) {
            return $color_name;
        }

        return isset($colors[$color_name]) ? $colors[$color_name] : '#2271b1';
    }

    /**
     * Get button padding based on size
     *
     * @param string $size Button size
     * @return string CSS padding value
     */
    private function get_button_padding($size)
    {
        $sizes = [
            'small' => '4px 8px',
            'medium' => '8px 16px',
            'large' => '12px 24px'
        ];

        return isset($sizes[$size]) ? $sizes[$size] : '8px 16px';
    }

    /**
     * Extract and verify captcha response
     *
     * @param string $nonce_action The nonce action to use for error responses
     */
    private function verify_captcha($nonce_action = 'farmfaucet_claim_nonce')
    {
        // Get captcha type from the form or fallback to global setting
        $captcha_type = isset($_POST['captcha_type']) ? sanitize_text_field($_POST['captcha_type']) : get_option('farmfaucet_captcha_type', 'hcaptcha');

        // Validate captcha type
        if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
        }

        $api = new Farmfaucet_API();

        // Validate captcha based on type
        try {
            if ($captcha_type === 'hcaptcha') {
                // Validate hCaptcha
                if (!isset($_POST['h-captcha-response'])) {
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing hCaptcha response', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing hCaptcha response', 'farmfaucet'));
                    }
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['h-captcha-response']);
                $captcha_secret = get_option('farmfaucet_hcaptcha_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty hCaptcha secret key");
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing hCaptcha API key. Please contact the site administrator.', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing hCaptcha API key. Please contact the site administrator.', 'farmfaucet'));
                    }
                    return;
                }

                $response = wp_remote_post('https://hcaptcha.com/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("hCaptcha verification error: " . $response->get_error_message());
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("hCaptcha verification failed: " . wp_remote_retrieve_body($response));
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }
            } else if ($captcha_type === 'recaptcha') {
                // Validate reCAPTCHA
                if (!isset($_POST['g-recaptcha-response'])) {
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing reCAPTCHA response', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing reCAPTCHA response', 'farmfaucet'));
                    }
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['g-recaptcha-response']);
                $captcha_secret = get_option('farmfaucet_recaptcha_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty reCAPTCHA secret key");
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing reCAPTCHA API key. Please contact the site administrator.', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing reCAPTCHA API key. Please contact the site administrator.', 'farmfaucet'));
                    }
                    return;
                }

                $response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("reCAPTCHA verification error: " . $response->get_error_message());
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("reCAPTCHA verification failed: " . wp_remote_retrieve_body($response));
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }
            } else if ($captcha_type === 'turnstile') {
                // Validate Cloudflare Turnstile
                if (!isset($_POST['cf-turnstile-response'])) {
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing Cloudflare Turnstile response', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing Cloudflare Turnstile response', 'farmfaucet'));
                    }
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['cf-turnstile-response']);
                $captcha_secret = get_option('farmfaucet_turnstile_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty Turnstile secret key");
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Missing Cloudflare Turnstile API key. Please contact the site administrator.', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Missing Cloudflare Turnstile API key. Please contact the site administrator.', 'farmfaucet'));
                    }
                    return;
                }

                $response = wp_remote_post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("Turnstile verification error: " . $response->get_error_message());
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("Turnstile verification failed: " . wp_remote_retrieve_body($response));
                    if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                        wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    }
                    return;
                }
            } else {
                // Unknown captcha type
                Farmfaucet_Logger::log_error("Invalid captcha type: $captcha_type");
                if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                    wp_send_json_error(['message' => __('Invalid captcha configuration', 'farmfaucet')]);
                } else {
                    wp_send_json_error(__('Invalid captcha configuration', 'farmfaucet'));
                }
                return;
            }
        } catch (Exception $e) {
            Farmfaucet_Logger::log_error("Captcha verification exception: " . $e->getMessage());
            if ($nonce_action === 'farmfaucet_withdrawal_nonce') {
                wp_send_json_error(['message' => __('Captcha verification failed', 'farmfaucet')]);
            } else {
                wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
            }
            return;
        }
    }

    /**
     * Process a dummy faucet claim
     */
    public function process_dummy_claim()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_claim_nonce')) {
            wp_send_json_error(__('Invalid request', 'farmfaucet'));
        }

        // Check if user is logged in
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            wp_send_json_error(__('You must be logged in to claim rewards', 'farmfaucet'));
        }

        // Get verification code
        $verification_code = isset($_POST['verification_code']) ? sanitize_text_field($_POST['verification_code']) : '';
        $expected_code = isset($_POST['expected_code']) ? sanitize_text_field($_POST['expected_code']) : '';

        if (empty($verification_code) || empty($expected_code) || $verification_code !== $expected_code) {
            wp_send_json_error(__('Invalid verification code', 'farmfaucet'));
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get faucet data
        $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        if (empty($faucet)) {
            wp_send_json_error(__('Invalid faucet', 'farmfaucet'));
        }

        // Check faucet type - only process dummy faucets here
        if (!isset($faucet['faucet_type']) || $faucet['faucet_type'] !== 'dummy') {
            wp_send_json_error(__('Invalid faucet type', 'farmfaucet'));
        }

        // Get currency ID
        $currency_id = isset($faucet['currency_id']) ? absint($faucet['currency_id']) : 0;
        if (empty($currency_id)) {
            wp_send_json_error(__('No currency configured for this faucet', 'farmfaucet'));
        }

        // Verify captcha
        $this->verify_captcha();

        // Check for cooldown
        $user_ip = Farmfaucet_Security::get_user_ip();
        $transient_key = 'farmfaucet_cooldown_' . $faucet['id'] . '_' . md5($user_ip);
        $cooldown_active = get_transient($transient_key);

        if ($cooldown_active) {
            wp_send_json_error(__('Cooldown period is still active', 'farmfaucet'));
        }

        // Process the claim - credit user's balance
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();
            $amount = floatval($faucet['amount']);

            // Update user's balance
            $result = $currency_maker->update_user_currency_balance($user_id, $currency_id, $amount);

            if ($result) {
                // Log transaction
                Farmfaucet_Logger::log(
                    sprintf(
                        __('User ID %d completed the task and received %s of currency ID %d from %s', 'farmfaucet'),
                        $user_id,
                        $amount,
                        $currency_id,
                        $faucet['name']
                    ),
                    'transaction',
                    $faucet_id
                );

                // Set the cooldown transient
                set_transient($transient_key, true, $faucet['cooldown']);

                // Update milestone counters
                $this->update_milestone_counters($faucet_id);

                wp_send_json_success();
            } else {
                wp_send_json_error(__('Failed to update balance', 'farmfaucet'));
            }
        } else {
            wp_send_json_error(__('Currency system is not available', 'farmfaucet'));
        }
    }

    /**
     * Process a withdrawal request
     */
    public function process_withdrawal()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_withdrawal_nonce')) {
            wp_send_json_error(['message' => __('Invalid request', 'farmfaucet')]);
        }

        // Check if user is logged in
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            wp_send_json_error(['message' => __('You must be logged in to withdraw funds', 'farmfaucet')]);
        }

        // Get withdrawal amount
        $withdrawal_amount = isset($_POST['withdrawal_amount']) ? floatval($_POST['withdrawal_amount']) : 0;
        if ($withdrawal_amount <= 0) {
            wp_send_json_error(['message' => __('Invalid withdrawal amount', 'farmfaucet')]);
        }

        // Get withdrawal mode
        $withdrawal_mode = isset($_POST['withdrawal_mode']) ? sanitize_text_field($_POST['withdrawal_mode']) : 'crypto';

        if ($withdrawal_mode === 'crypto') {
            // Get withdrawal currency
            $withdrawal_currency = isset($_POST['withdrawal_currency']) ? sanitize_text_field($_POST['withdrawal_currency']) : '';
            if (empty($withdrawal_currency)) {
                wp_send_json_error(['message' => __('Please select a withdrawal currency', 'farmfaucet')]);
            }

            // Get wallet address
            $wallet_address = isset($_POST['wallet_address']) ? sanitize_text_field($_POST['wallet_address']) : '';
            if (empty($wallet_address)) {
                wp_send_json_error(['message' => __('Please enter a wallet address or FaucetPay email', 'farmfaucet')]);
            }
        } else if ($withdrawal_mode === 'convert') {
            // Get target currency ID for conversion
            $convert_currency_id = isset($_POST['convert_currency']) ? intval($_POST['convert_currency']) : 0;
            if (empty($convert_currency_id)) {
                wp_send_json_error(['message' => __('Please select a currency to convert to', 'farmfaucet')]);
            }
        } else {
            wp_send_json_error(['message' => __('Invalid withdrawal mode', 'farmfaucet')]);
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get faucet data
        $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        if (empty($faucet)) {
            wp_send_json_error(__('Invalid faucet', 'farmfaucet'));
        }

        // Check faucet type - only process withdrawal faucets here
        if (!isset($faucet['faucet_type']) || $faucet['faucet_type'] !== 'withdrawal') {
            wp_send_json_error(__('Invalid faucet type', 'farmfaucet'));
        }

        // Get currency ID
        $currency_id = isset($faucet['currency_id']) ? absint($faucet['currency_id']) : 0;
        if (empty($currency_id)) {
            wp_send_json_error(__('No currency configured for this faucet', 'farmfaucet'));
        }

        // Check minimum withdrawal amount
        $min_withdrawal = isset($faucet['min_withdrawal']) ? floatval($faucet['min_withdrawal']) : 0;
        if ($withdrawal_amount < $min_withdrawal) {
            wp_send_json_error(sprintf(__('Minimum withdrawal amount is %s', 'farmfaucet'), $min_withdrawal));
        }

        // Verify captcha
        $this->verify_captcha('farmfaucet_withdrawal_nonce');

        // Process the withdrawal
        if (class_exists('Farmfaucet_Currency_Maker')) {
            $currency_maker = Farmfaucet_Currency_Maker::init();

            // Check user's balance
            $user_balance = $currency_maker->get_user_currency_balance($user_id, $currency_id);
            if ($user_balance < $withdrawal_amount) {
                wp_send_json_error(['message' => __('Insufficient balance', 'farmfaucet')]);
            }

            // Get currency data for conversion
            $currency = $currency_maker->get_currency($currency_id);
            if (empty($currency)) {
                wp_send_json_error(['message' => __('Currency not found', 'farmfaucet')]);
            }

            // Deduct from user's balance
            $result = $currency_maker->update_user_currency_balance($user_id, $currency_id, -$withdrawal_amount);

            if ($result) {
                if ($withdrawal_mode === 'crypto') {
                    // Calculate the equivalent amount in the withdrawal currency
                    $base_currency = $currency['base_currency'];
                    $exchange_rate = $currency['exchange_rate'];

                    // Convert to base currency first
                    $base_amount = $withdrawal_amount * $exchange_rate;

                    // If withdrawal currency is different from base currency, convert using real exchange rates
                    if ($withdrawal_currency !== $base_currency) {
                        if (class_exists('Farmfaucet_Exchange_Rates')) {
                            $exchange_rates = Farmfaucet_Exchange_Rates::init();
                            $converted_amount = $exchange_rates->convert_amount($base_amount, $base_currency, $withdrawal_currency);

                            if ($converted_amount !== false) {
                                $base_amount = $converted_amount;
                            } else {
                                // Fallback to old conversion if exchange rate fails
                                $base_amount = $this->convert_currency($base_amount, $base_currency, $withdrawal_currency);
                            }
                        } else {
                            // Fallback to old conversion if exchange rates class not available
                            $base_amount = $this->convert_currency($base_amount, $base_currency, $withdrawal_currency);
                        }
                    }

                    // Create a withdrawal record
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'farmfaucet_withdrawals';

                    $wpdb->insert(
                        $table_name,
                        [
                            'user_id' => $user_id,
                            'faucet_id' => $faucet_id,
                            'currency_id' => $currency_id,
                            'amount' => $withdrawal_amount,
                            'withdrawal_currency' => $withdrawal_currency,
                            'wallet_address' => $wallet_address,
                            'status' => 'pending',
                            'created_at' => current_time('mysql')
                        ],
                        [
                            '%d', // user_id
                            '%d', // faucet_id
                            '%d', // currency_id
                            '%f', // amount
                            '%s', // withdrawal_currency
                            '%s', // wallet_address
                            '%s', // status
                            '%s'  // created_at
                        ]
                    );

                    $withdrawal_id = $wpdb->insert_id;

                    // Process the withdrawal through FaucetPay API
                    $api = new Farmfaucet_API();
                    $payment_result = $api->send_payment(
                        $wallet_address,
                        $base_amount,
                        $withdrawal_currency,
                        $faucet_id // Pass faucet ID to use faucet-specific API key if available
                    );

                    if ($payment_result['success']) {
                        // Update withdrawal status to completed
                        $wpdb->update(
                            $table_name,
                            ['status' => 'completed'],
                            ['id' => $withdrawal_id],
                            ['%s'],
                            ['%d']
                        );

                        // Log transaction
                        Farmfaucet_Logger::log(
                            sprintf(
                                __('User ID %d withdrew %s of currency ID %d (%s %s) to %s', 'farmfaucet'),
                                $user_id,
                                $withdrawal_amount,
                                $currency_id,
                                $base_amount,
                                $withdrawal_currency,
                                $wallet_address
                            ),
                            'withdrawal',
                            $faucet_id
                        );

                        wp_send_json_success(['message' => sprintf(__('%s %s has been sent to your wallet!', 'farmfaucet'), number_format($base_amount, 8), $withdrawal_currency)]);
                    } else {
                        // Keep withdrawal status as pending for admin approval
                        Farmfaucet_Logger::log(
                            sprintf(
                                __('Withdrawal failed for user ID %d: %s', 'farmfaucet'),
                                $user_id,
                                $payment_result['message'] ?? 'Unknown error'
                            ),
                            'error',
                            $faucet_id
                        );

                        wp_send_json_error(['message' => __('Withdrawal request has been submitted for manual approval', 'farmfaucet')]);
                    }
                } else if ($withdrawal_mode === 'convert') {
                    // Get target currency
                    $target_currency = $currency_maker->get_currency($convert_currency_id);
                    if (empty($target_currency)) {
                        wp_send_json_error(['message' => __('Target currency not found', 'farmfaucet')]);
                    }

                    // Calculate conversion rate
                    $source_rate = $currency['exchange_rate'];
                    $target_rate = $target_currency['exchange_rate'];

                    // Convert to base currency first, then to target currency
                    $base_amount = $withdrawal_amount * $source_rate;
                    $converted_amount = $base_amount / $target_rate;

                    // Add to user's target currency balance
                    $add_result = $currency_maker->update_user_currency_balance($user_id, $convert_currency_id, $converted_amount);

                    if ($add_result) {
                        // Log the conversion
                        Farmfaucet_Logger::log(
                            sprintf(
                                __('User ID %d converted %s %s to %s %s', 'farmfaucet'),
                                $user_id,
                                number_format($withdrawal_amount, 8),
                                $currency['code'],
                                number_format($converted_amount, 8),
                                $target_currency['code']
                            ),
                            'conversion',
                            $faucet_id
                        );

                        wp_send_json_success([
                            'message' => sprintf(
                                __('Successfully converted %s %s to %s %s!', 'farmfaucet'),
                                number_format($withdrawal_amount, 8),
                                $currency['code'],
                                number_format($converted_amount, 8),
                                $target_currency['code']
                            )
                        ]);
                    } else {
                        // If conversion failed, refund the source currency
                        $currency_maker->update_user_currency_balance($user_id, $currency_id, $withdrawal_amount);

                        wp_send_json_error(['message' => __('Failed to convert currency', 'farmfaucet')]);
                    }
                }
            } else {
                wp_send_json_error(['message' => __('Failed to process withdrawal', 'farmfaucet')]);
            }
        } else {
            wp_send_json_error(['message' => __('Currency system is not available', 'farmfaucet')]);
        }
    }

    /**
     * Get withdrawal history for a user (AJAX handler)
     */
    public function get_withdrawal_history()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_withdrawal_nonce')) {
            wp_send_json_error(['message' => __('Invalid request', 'farmfaucet')]);
        }

        // Check if user is logged in
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            wp_send_json_error(__('You must be logged in to view withdrawal history', 'farmfaucet'));
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get withdrawal history
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_withdrawals';

        $withdrawals = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE user_id = %d AND faucet_id = %d ORDER BY created_at DESC LIMIT 10",
                $user_id,
                $faucet_id
            ),
            ARRAY_A
        );

        // Format the withdrawal history as HTML
        $html = '';
        if (empty($withdrawals)) {
            $html = '<div class="no-history">' . __('No withdrawal history found.', 'farmfaucet') . '</div>';
        } else {
            $html = '<table class="withdrawal-history-table">
                <thead>
                    <tr>
                        <th>' . __('Date', 'farmfaucet') . '</th>
                        <th>' . __('Amount', 'farmfaucet') . '</th>
                        <th>' . __('Currency', 'farmfaucet') . '</th>
                        <th>' . __('Status', 'farmfaucet') . '</th>
                    </tr>
                </thead>
                <tbody>';

            foreach ($withdrawals as $withdrawal) {
                $status_class = '';
                switch ($withdrawal['status']) {
                    case 'pending':
                        $status_class = 'pending';
                        break;
                    case 'completed':
                        $status_class = 'completed';
                        break;
                    case 'failed':
                        $status_class = 'failed';
                        break;
                }

                $html .= '<tr>
                    <td>' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($withdrawal['created_at'])) . '</td>
                    <td>' . number_format($withdrawal['amount'], 8) . '</td>
                    <td>' . esc_html($withdrawal['withdrawal_currency']) . '</td>
                    <td><span class="withdrawal-status ' . $status_class . '">' . ucfirst(esc_html($withdrawal['status'])) . '</span></td>
                </tr>';
            }

            $html .= '</tbody></table>';
        }

        wp_send_json_success(['html' => $html]);
    }

    /**
     * Render withdrawal history shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_withdrawal_history($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'faucet_id' => 0,
            'title' => __('Withdrawal History', 'farmfaucet'),
            'limit' => 10,
            'show_empty' => 'yes',
        ], $atts);

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-info">' . __('Please log in to view your withdrawal history.', 'farmfaucet') . '</div>';
        }

        $user_id = get_current_user_id();
        $faucet_id = absint($atts['faucet_id']);

        // If no faucet ID is provided, show a message
        if (empty($faucet_id)) {
            return '<div class="farmfaucet-info">' . __('Please specify a faucet ID to view withdrawal history.', 'farmfaucet') . '</div>';
        }

        // Get withdrawal history
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_withdrawals';
        $limit = absint($atts['limit']);

        $withdrawals = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE user_id = %d AND faucet_id = %d ORDER BY created_at DESC LIMIT %d",
                $user_id,
                $faucet_id,
                $limit
            ),
            ARRAY_A
        );

        // Start building output
        $output = '<div class="farmfaucet-withdrawal-history" data-faucet-id="' . esc_attr($faucet_id) . '">';

        // Add title if provided
        if (!empty($atts['title'])) {
            $output .= '<h3>' . esc_html($atts['title']) . '</h3>';
        }

        // Add history container
        $output .= '<div class="withdrawal-history-container">';

        // Format the withdrawal history as HTML
        if (empty($withdrawals)) {
            if ($atts['show_empty'] === 'yes') {
                $output .= '<div class="no-history">' . __('No withdrawal history found.', 'farmfaucet') . '</div>';
            }
        } else {
            $output .= '<table class="withdrawal-history-table">
                <thead>
                    <tr>
                        <th>' . __('Date', 'farmfaucet') . '</th>
                        <th>' . __('Amount', 'farmfaucet') . '</th>
                        <th>' . __('Currency', 'farmfaucet') . '</th>
                        <th>' . __('Status', 'farmfaucet') . '</th>
                    </tr>
                </thead>
                <tbody>';

            foreach ($withdrawals as $withdrawal) {
                $status_class = '';
                switch ($withdrawal['status']) {
                    case 'pending':
                        $status_class = 'pending';
                        break;
                    case 'completed':
                        $status_class = 'completed';
                        break;
                    case 'failed':
                        $status_class = 'failed';
                        break;
                }

                $output .= '<tr>
                    <td>' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($withdrawal['created_at'])) . '</td>
                    <td>' . number_format($withdrawal['amount'], 8) . '</td>
                    <td>' . esc_html($withdrawal['withdrawal_currency']) . '</td>
                    <td><span class="withdrawal-status ' . $status_class . '">' . ucfirst(esc_html($withdrawal['status'])) . '</span></td>
                </tr>';
            }

            $output .= '</tbody></table>';
        }

        $output .= '</div>'; // Close withdrawal-history-container
        $output .= '</div>'; // Close farmfaucet-withdrawal-history

        return $output;
    }

    /**
     * Render conversion history shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_conversion_history($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'faucet_id' => 0,
            'title' => __('Conversion History', 'farmfaucet'),
            'limit' => 10,
            'show_empty' => 'yes',
        ], $atts);

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return '<div class="farmfaucet-info">' . __('Please log in to view your conversion history.', 'farmfaucet') . '</div>';
        }

        $user_id = get_current_user_id();
        $faucet_id = absint($atts['faucet_id']);

        // If no faucet ID is provided, show a message
        if (empty($faucet_id)) {
            return '<div class="farmfaucet-info">' . __('Please specify a faucet ID to view conversion history.', 'farmfaucet') . '</div>';
        }

        // Get conversion history
        global $wpdb;
        $table_name = $wpdb->prefix . 'farmfaucet_conversions';
        $limit = absint($atts['limit']);

        $conversions = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT c.*,
                 sc.symbol as source_symbol, sc.name as source_name,
                 tc.symbol as target_symbol, tc.name as target_name
                 FROM $table_name c
                 LEFT JOIN {$wpdb->prefix}farmfaucet_currencies sc ON c.source_currency_id = sc.id
                 LEFT JOIN {$wpdb->prefix}farmfaucet_currencies tc ON c.target_currency_id = tc.id
                 WHERE c.user_id = %d AND c.faucet_id = %d
                 ORDER BY c.created_at DESC
                 LIMIT %d",
                $user_id,
                $faucet_id,
                $limit
            ),
            ARRAY_A
        );

        // Start building output
        $output = '<div class="farmfaucet-conversion-history" data-faucet-id="' . esc_attr($faucet_id) . '">';

        // Add title if provided
        if (!empty($atts['title'])) {
            $output .= '<h3>' . esc_html($atts['title']) . '</h3>';
        }

        // Add history container
        $output .= '<div class="conversion-history-container">';

        // Format the conversion history as HTML
        if (empty($conversions)) {
            if ($atts['show_empty'] === 'yes') {
                $output .= '<div class="no-history">' . __('No conversion history found.', 'farmfaucet') . '</div>';
            }
        } else {
            $output .= '<table class="conversion-history-table">
                <thead>
                    <tr>
                        <th>' . __('Date', 'farmfaucet') . '</th>
                        <th>' . __('From', 'farmfaucet') . '</th>
                        <th>' . __('To', 'farmfaucet') . '</th>
                        <th>' . __('Rate', 'farmfaucet') . '</th>
                        <th>' . __('Status', 'farmfaucet') . '</th>
                    </tr>
                </thead>
                <tbody>';

            foreach ($conversions as $conversion) {
                $status_class = '';
                switch ($conversion['status']) {
                    case 'pending':
                        $status_class = 'pending';
                        break;
                    case 'completed':
                        $status_class = 'completed';
                        break;
                    case 'failed':
                        $status_class = 'failed';
                        break;
                }

                $output .= '<tr>
                    <td>' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($conversion['created_at'])) . '</td>
                    <td>' . number_format($conversion['source_amount'], 8) . ' ' . esc_html($conversion['source_symbol']) . '</td>
                    <td>' . number_format($conversion['target_amount'], 8) . ' ' . esc_html($conversion['target_symbol']) . '</td>
                    <td>' . number_format($conversion['conversion_rate'], 8) . '</td>
                    <td><span class="conversion-status ' . $status_class . '">' . ucfirst(esc_html($conversion['status'])) . '</span></td>
                </tr>';
            }

            $output .= '</tbody></table>';
        }

        $output .= '</div>'; // Close conversion-history-container
        $output .= '</div>'; // Close farmfaucet-conversion-history

        return $output;
    }

    /**
     * Helper function to convert between currencies
     *
     * @param float $amount Amount to convert
     * @param string $from_currency Currency to convert from
     * @param string $to_currency Currency to convert to
     * @return float Converted amount
     */
    private function convert_currency($amount, $from_currency, $to_currency)
    {
        // In a real implementation, you would use an exchange rate API
        // For simplicity, we'll use a fixed conversion rate here
        $conversion_rates = [
            'BTC' => 1.0,
            'LTC' => 0.01,
            'DOGE' => 0.000001,
            'ETH' => 0.05,
            'TRX' => 0.000005,
            'BCH' => 0.005
        ];

        // Convert to BTC first
        $btc_amount = $amount;
        if ($from_currency !== 'BTC') {
            $btc_amount = $amount / $conversion_rates[$from_currency];
        }

        // Convert from BTC to target currency
        if ($to_currency === 'BTC') {
            return $btc_amount;
        } else {
            return $btc_amount * $conversion_rates[$to_currency];
        }
    }

    public function process_claim()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_claim_nonce')) {
            wp_send_json_error(__('Invalid request', 'farmfaucet'));
        }

        // Validate email
        $user_email = sanitize_email($_POST['user_email']);
        if (!is_email($user_email)) {
            wp_send_json_error(__('Invalid email address', 'farmfaucet'));
        }

        // Get faucet ID
        $faucet_id = isset($_POST['faucet_id']) ? absint($_POST['faucet_id']) : 0;

        // Get faucet data
        $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
        if (empty($faucet)) {
            wp_send_json_error(__('Invalid faucet', 'farmfaucet'));
        }

        // Check faucet type - only process stage faucets here
        if (isset($faucet['faucet_type']) && $faucet['faucet_type'] !== 'stage') {
            wp_send_json_error(__('Invalid faucet type', 'farmfaucet'));
        }

        // Get captcha type from the form or fallback to global setting
        $captcha_type = isset($_POST['captcha_type']) ? sanitize_text_field($_POST['captcha_type']) : get_option('farmfaucet_captcha_type', 'hcaptcha');

        // Validate captcha type
        if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
        }

        $api = new Farmfaucet_API();

        // Validate captcha based on type
        try {
            if ($captcha_type === 'hcaptcha') {
                // Validate hCaptcha
                if (!isset($_POST['h-captcha-response'])) {
                    wp_send_json_error(__('Missing hCaptcha response', 'farmfaucet'));
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['h-captcha-response']);
                $captcha_secret = get_option('farmfaucet_hcaptcha_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty hCaptcha secret key for faucet ID: $faucet_id");
                    wp_send_json_error(__('Missing hCaptcha API key. Please contact the site administrator.', 'farmfaucet'));
                    return;
                }

                $response = wp_remote_post('https://hcaptcha.com/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("hCaptcha verification error: " . $response->get_error_message());
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("hCaptcha verification failed: " . wp_remote_retrieve_body($response));
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }
            } else if ($captcha_type === 'recaptcha') {
                // Validate reCAPTCHA
                if (!isset($_POST['g-recaptcha-response'])) {
                    wp_send_json_error(__('Missing reCAPTCHA response', 'farmfaucet'));
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['g-recaptcha-response']);
                $captcha_secret = get_option('farmfaucet_recaptcha_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty reCAPTCHA secret key for faucet ID: $faucet_id");
                    wp_send_json_error(__('Missing reCAPTCHA API key. Please contact the site administrator.', 'farmfaucet'));
                    return;
                }

                $response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("reCAPTCHA verification error: " . $response->get_error_message());
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("reCAPTCHA verification failed: " . wp_remote_retrieve_body($response));
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }
            } else if ($captcha_type === 'turnstile') {
                // Validate Cloudflare Turnstile
                if (!isset($_POST['cf-turnstile-response'])) {
                    wp_send_json_error(__('Missing Cloudflare Turnstile response', 'farmfaucet'));
                    return;
                }

                $captcha_response = sanitize_text_field($_POST['cf-turnstile-response']);
                $captcha_secret = get_option('farmfaucet_turnstile_secret');

                // Decrypt the secret key
                $captcha_secret = $api->decrypt_api_key($captcha_secret);

                if (empty($captcha_secret)) {
                    Farmfaucet_Logger::log_error("Empty Turnstile secret key for faucet ID: $faucet_id");
                    wp_send_json_error(__('Missing Cloudflare Turnstile API key. Please contact the site administrator.', 'farmfaucet'));
                    return;
                }

                $response = wp_remote_post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
                    'body' => [
                        'secret' => $captcha_secret,
                        'response' => $captcha_response
                    ]
                ]);

                if (is_wp_error($response)) {
                    Farmfaucet_Logger::log_error("Turnstile verification error: " . $response->get_error_message());
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }

                $body = json_decode(wp_remote_retrieve_body($response));
                if (!$body || !isset($body->success) || $body->success !== true) {
                    Farmfaucet_Logger::log_error("Turnstile verification failed: " . wp_remote_retrieve_body($response));
                    wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
                    return;
                }
            } else {
                // Unknown captcha type
                Farmfaucet_Logger::log_error("Invalid captcha type: $captcha_type");
                wp_send_json_error(__('Invalid captcha configuration', 'farmfaucet'));
                return;
            }
        } catch (Exception $e) {
            Farmfaucet_Logger::log_error("Captcha verification exception: " . $e->getMessage());
            wp_send_json_error(__('Captcha verification failed', 'farmfaucet'));
            return;
        }

        // Check cooldown - now specific to this faucet
        $user_ip = Farmfaucet_Security::get_user_ip();
        $transient_key = 'farmfaucet_cooldown_' . $faucet_id . '_' . md5($user_ip);

        if (get_transient($transient_key)) {
            wp_send_json_error(__('Claim cooldown active', 'farmfaucet'));
        }

        // Check for rate limiting
        $rate_limit_key = 'farmfaucet_rate_limit_' . md5($user_ip);
        $rate_limit_count = get_transient($rate_limit_key);

        if ($rate_limit_count && $rate_limit_count >= 5) {
            wp_send_json_error(__('Too many attempts. Please try again later.', 'farmfaucet'));
        }

        // Process payment with faucet-specific API key
        $api = new Farmfaucet_API();
        $payment_result = $api->send_payment(
            $user_email,
            $faucet['amount'],
            $faucet['currency'],
            $faucet_id // Pass faucet ID to use faucet-specific API key if available
        );

        // Update rate limiting
        if ($rate_limit_count) {
            set_transient($rate_limit_key, $rate_limit_count + 1, 300); // 5 minutes
        } else {
            set_transient($rate_limit_key, 1, 300); // 5 minutes
        }

        if ($payment_result['success']) {
            // Log transaction with faucet ID
            Farmfaucet_Logger::log(
                sprintf(
                    __('%s completed the task and received %s %s from %s', 'farmfaucet'),
                    sanitize_email($user_email),
                    $faucet['amount'],
                    $faucet['currency'],
                    $faucet['name']
                ),
                'transaction',
                $faucet_id
            );

            // Set the cooldown transient
            set_transient($transient_key, true, $faucet['cooldown']);

            // Update milestone counters
            $this->update_milestone_counters($faucet_id);

            // Record claim for leaderboard tracking
            $user_hash = md5($user_ip);
            Farmfaucet_Logger::record_claim(
                $user_hash,
                $faucet_id,
                $faucet['amount'],
                $faucet['currency']
            );

            wp_send_json_success();
        } else {
            wp_send_json_error($payment_result['message']);
        }
    }

    /**
     * Render the task completion percentage
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered completion percentage HTML
     */
    public function render_completion_percentage($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'title' => esc_html__('Task Completion', 'farmfaucet'),
            'show_percentage' => 'yes',
            'show_fraction' => 'no',
            'color' => '#2271b1',
            'height' => '20',
            'border_radius' => '10',
        ], $atts);

        // Get all faucets
        $faucets = Farmfaucet_Logger::get_faucets();

        if (empty($faucets)) {
            return '<div class="farmfaucet-error">' . esc_html__('No faucets found', 'farmfaucet') . '</div>';
        }

        // Get included faucets if the task completion class exists
        $included_faucet_ids = [];
        if (class_exists('Farmfaucet_Task_Completion')) {
            $included_faucet_ids = Farmfaucet_Task_Completion::get_included_faucets();

            // Filter faucets to only include those in the task completion list
            $filtered_faucets = [];
            foreach ($faucets as $faucet) {
                if (in_array($faucet['id'], $included_faucet_ids)) {
                    $filtered_faucets[] = $faucet;
                }
            }
            $faucets = $filtered_faucets;
        }

        // Count total active faucets
        $total_faucets = count($faucets);

        if ($total_faucets === 0) {
            return '<div class="farmfaucet-error">' . esc_html__('No faucets available', 'farmfaucet') . '</div>';
        }

        // Count completed faucets
        $completed_faucets = 0;
        $user_ip = Farmfaucet_Security::get_user_ip();

        foreach ($faucets as $faucet) {
            $transient_key = 'farmfaucet_cooldown_' . $faucet['id'] . '_' . md5($user_ip);
            if (get_transient($transient_key)) {
                $completed_faucets++;
            }
        }

        // Calculate percentage
        $percentage = ($total_faucets > 0) ? floor(($completed_faucets / $total_faucets) * 100) : 0;

        // Prepare display text
        $display_text = '';

        if ($atts['show_percentage'] === 'yes') {
            $display_text .= $percentage . '%';
        }

        if ($atts['show_fraction'] === 'yes') {
            if (!empty($display_text)) {
                $display_text .= ' - ';
            }
            $display_text .= $completed_faucets . '/' . $total_faucets;
        }

        // Prepare styles
        $bar_height = absint($atts['height']) . 'px';
        $border_radius = absint($atts['border_radius']) . 'px';
        $bar_color = sanitize_hex_color($atts['color']) ?: '#2271b1';

        // Generate unique ID for this progress bar
        $unique_id = 'farmfaucet-progress-' . uniqid();

        // Build HTML output with animation
        $output = '<div class="farmfaucet-completion-container">';

        if (!empty($atts['title'])) {
            $output .= '<h4 class="farmfaucet-completion-title">' . esc_html($atts['title']) . '</h4>';
        }

        $output .= '<div class="farmfaucet-progress-bar-container" style="height: ' . esc_attr($bar_height) . '; border-radius: ' . esc_attr($border_radius) . '; background-color: #f0f0f0; overflow: hidden; position: relative;">';
        $output .= '<div id="' . esc_attr($unique_id) . '" class="farmfaucet-progress-bar" data-progress="' . esc_attr($percentage) . '" style="width: 0%; height: 100%; background-color: ' . esc_attr($bar_color) . '; position: absolute; left: 0; top: 0; transition: width 1.5s ease-in-out;">';
        $output .= '</div>';

        if (!empty($display_text)) {
            $output .= '<div class="farmfaucet-progress-text" style="position: absolute; width: 100%; text-align: center; color: #333; font-weight: bold; line-height: ' . esc_attr($bar_height) . ';">' . esc_html($display_text) . '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';

        // Animation is now handled by the global script.js

        return $output;
    }

    /**
     * Render a list of available shortcodes
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered shortcodes list HTML
     */


    public function render_shortcodes_list($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'title' => esc_html__('Available Shortcodes', 'farmfaucet'),
            'show_descriptions' => 'yes',
        ], $atts);

        $output = '<div class="farmfaucet-shortcodes-container">';

        if (!empty($atts['title'])) {
            $output .= '<h3 class="farmfaucet-shortcodes-title">' . esc_html($atts['title']) . '</h3>';
        }

        $output .= '<div class="farmfaucet-shortcodes-list">';

        // Add main shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays the default faucet claim form', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Add user profile shortcodes section
        $output .= '<h4>' . esc_html__('User Profile Shortcodes', 'farmfaucet') . '</h4>';

        // Add completion percentage shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_completion]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays a progress bar showing task completion percentage', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';



        // Add username shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_username]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays the user\'s leaderboard name', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Add username form shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_username_form]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays a form for users to update their leaderboard name', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Add avatar shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_avatar]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays the user\'s avatar (view-only)', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Add editable avatar shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_avatar_editable]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays the user\'s avatar with the ability to update it', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Add leaderboard shortcode
        $output .= '<div class="farmfaucet-shortcode-item">';
        $output .= '<code>[farmfaucet_leaderboard]</code>';

        if ($atts['show_descriptions'] === 'yes') {
            $output .= '<span class="farmfaucet-shortcode-description">' . esc_html__('Displays a leaderboard of top users based on faucet completions', 'farmfaucet') . '</span>';
        }

        $output .= '</div>';

        // Get all faucets
        $faucets = Farmfaucet_Logger::get_faucets();

        if (!empty($faucets)) {
            $output .= '<h4>' . esc_html__('Faucet Shortcodes', 'farmfaucet') . '</h4>';

            foreach ($faucets as $faucet) {
                if ($faucet['shortcode'] !== 'farmfaucet') {
                    $output .= '<div class="farmfaucet-shortcode-item">';
                    $output .= '<code>[' . esc_html($faucet['shortcode']) . ']</code>';

                    if ($atts['show_descriptions'] === 'yes') {
                        $output .= '<span class="farmfaucet-shortcode-description">' .
                            sprintf(esc_html__('Displays the "%s" faucet claim form', 'farmfaucet'), esc_html($faucet['name'])) .
                            '</span>';
                    }

                    $output .= '</div>';
                }
            }
        }

        // Get all buttons
        $buttons = Farmfaucet_Logger::get_buttons();

        if (!empty($buttons)) {
            $output .= '<h4>' . esc_html__('Button Shortcodes', 'farmfaucet') . '</h4>';

            foreach ($buttons as $button) {
                $output .= '<div class="farmfaucet-shortcode-item">';
                $output .= '<code>[farmfaucet_button id="' . esc_html($button['id']) . '"]</code>';

                if ($atts['show_descriptions'] === 'yes') {
                    // Get faucet name
                    $faucet = Farmfaucet_Logger::get_faucet($button['faucet_id']);
                    $faucet_name = $faucet ? $faucet['name'] : esc_html__('Unknown', 'farmfaucet');

                    $output .= '<span class="farmfaucet-shortcode-description">' .
                        sprintf(
                            esc_html__('Displays the "%s" button for the "%s" faucet', 'farmfaucet'),
                            esc_html($button['button_text']),
                            esc_html($faucet_name)
                        ) .
                        '</span>';
                }

                $output .= '</div>';
            }
        }

        $output .= '</div>'; // End shortcodes list
        $output .= '</div>'; // End container

        return $output;
    }

    /**
     * Update milestone counters when a faucet is completed
     *
     * @param int $faucet_id The ID of the completed faucet
     */
    private function update_milestone_counters($faucet_id)
    {
        $user_ip = Farmfaucet_Security::get_user_ip();

        // Always increment the total faucet claims counter
        $total_claims_key = 'farmfaucet_total_claims_' . md5($user_ip);
        $total_claims_count = get_transient($total_claims_key) ?: 0;
        $total_claims_count++;

        // Store the updated count with a long expiration (30 days)
        set_transient($total_claims_key, $total_claims_count, 30 * 86400); // 86400 seconds = 1 day

        // For backward compatibility, also update the global completion counter
        $global_completion_key = 'farmfaucet_global_completion_' . md5($user_ip);
        $global_completion_count = get_transient($global_completion_key) ?: 0;
        $global_completion_count++;

        // Store the updated count with a long expiration (30 days)
        set_transient($global_completion_key, $global_completion_count, 30 * 86400); // 86400 seconds = 1 day

        // Increment the specific faucet completion counter
        $specific_completion_key = 'farmfaucet_specific_completion_' . $faucet_id . '_' . md5($user_ip);
        $specific_completion_count = get_transient($specific_completion_key) ?: 0;
        $specific_completion_count++;

        // Store the updated count with a long expiration (30 days)
        set_transient($specific_completion_key, $specific_completion_count, 30 * 86400); // 86400 seconds = 1 day
    }

    /**
     * Render milestone progress bar
     *
     * @param array $button Button data
     * @param array $faucet Faucet data
     * @param int $milestone_count Global completion count required
     * @param bool $milestone_lock_faucet Whether to lock the faucet until milestone is completed
     * @return string HTML output
     */
    private function render_milestone_progress($button, $faucet, $milestone_count, $milestone_lock_faucet = false)
    {
        $user_ip = Farmfaucet_Security::get_user_ip();

        // Track total faucet claims count
        $total_claims_key = 'farmfaucet_total_claims_' . md5($user_ip);
        $total_claims_count = get_transient($total_claims_key) ?: 0;

        // For backward compatibility, also check the global completion counter
        $global_completion_key = 'farmfaucet_global_completion_' . md5($user_ip);
        $global_completion_count = get_transient($global_completion_key) ?: 0;

        // Use the higher of the two counts to ensure users don't lose progress
        $user_completion_count = max($total_claims_count, $global_completion_count);

        // Calculate progress percentage
        $overall_percentage = min(100, floor(($user_completion_count / $milestone_count) * 100));

        // Generate unique ID for this progress bar
        $unique_id = 'farmfaucet-milestone-' . uniqid();

        // Get progress bar style and colors
        $bar_style = isset($button['milestone_bar_style']) ? $button['milestone_bar_style'] : 'solid';
        $border_radius = $button['border_shape'] === 'rounded' ? '20px' : '4px';

        // Set background style based on bar style
        if ($bar_style === 'gradient' && !empty($button['milestone_gradient_start']) && !empty($button['milestone_gradient_end'])) {
            $bar_background = 'linear-gradient(to right, ' . esc_attr($button['milestone_gradient_start']) . ', ' . esc_attr($button['milestone_gradient_end']) . ')';
        } else {
            // Default to solid color
            $bar_color = !empty($button['milestone_bar_color']) ? $button['milestone_bar_color'] : $this->get_button_color($button['button_color']);
            $bar_background = $bar_color;
        }

        // Get milestone card appearance settings
        $display_style = isset($button['milestone_display_style']) ? $button['milestone_display_style'] : 'card';
        $transparent_bg = isset($button['milestone_transparent_bg']) && $button['milestone_transparent_bg'] == 1;
        $card_bg_style = isset($button['milestone_card_bg_style']) ? $button['milestone_card_bg_style'] : 'solid';
        $card_bg_color = isset($button['milestone_card_bg_color']) ? $button['milestone_card_bg_color'] : '#ffffff';
        $card_gradient_start = isset($button['milestone_card_gradient_start']) ? $button['milestone_card_gradient_start'] : '#ffffff';
        $card_gradient_end = isset($button['milestone_card_gradient_end']) ? $button['milestone_card_gradient_end'] : '#f5f5f5';

        // Prepare milestone text
        $milestone_text = sprintf(
            __('Complete %d faucet claims to unlock', 'farmfaucet'),
            $milestone_count
        );

        // Determine if milestone is completed
        $is_completed = ($overall_percentage >= 100);
        $completion_class = $is_completed ? 'farmfaucet-milestone-complete' : '';

        // Add transparent class if needed
        $transparent_class = $transparent_bg ? 'transparent-bg' : '';

        // Add display style class
        $style_class = $display_style === 'compact' ? 'compact-view' : 'card-view';

        // Set card background style
        $card_background = '';
        if (!$transparent_bg) {
            if ($card_bg_style === 'gradient' && !empty($card_gradient_start) && !empty($card_gradient_end)) {
                $card_background = 'background: linear-gradient(to bottom, ' . esc_attr($card_gradient_start) . ', ' . esc_attr($card_gradient_end) . ');';
            } else {
                $card_background = 'background: ' . esc_attr($card_bg_color) . ';';
            }
        } else {
            $card_background = 'background: transparent; box-shadow: none;';
        }

        // Get message change interval (default to 60 seconds if not set)
        $message_interval = isset($button['message_interval']) ? absint($button['message_interval']) : 60;

        // Build HTML output with animation
        $output = '<div class="farmfaucet-milestone-container ' . $style_class . ' ' . $transparent_class . ' ' . $completion_class . '" data-button-id="' . esc_attr($button['id']) . '" data-faucet-id="' . esc_attr($faucet['id']) . '" data-message-interval="' . esc_attr($message_interval) . '" style="' . $card_background . '">';

        // Different layout based on display style
        if ($display_style === 'compact') {
            // Compact view layout

            // Header with title only
            $output .= '<div class="farmfaucet-milestone-header">';
            $output .= '<h4 class="farmfaucet-milestone-title">' . esc_html($button['button_text']) . '</h4>';
            $output .= '</div>';

            // Progress bar with percentage inside
            $output .= '<div class="farmfaucet-milestone-progress-container" style="box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);">';
            $output .= '<div id="' . esc_attr($unique_id) . '" class="farmfaucet-milestone-progress-bar" data-progress="' . esc_attr($overall_percentage) . '" style="width: 0%; background: ' . esc_attr($bar_background) . ';">';
            $output .= '</div>';
            $output .= '<div class="farmfaucet-milestone-progress-text" style="position: absolute; width: 100%; text-align: center; color: #fff; font-weight: bold; line-height: 24px; font-size: 0.9em; text-shadow: 0 1px 2px rgba(0,0,0,0.3); z-index: 5; left: 0; top: 0; height: 100%; display: flex; align-items: center; justify-content: center; pointer-events: none; margin: 0; padding: 0;">' . $overall_percentage . '%</div>';
            $output .= '</div>';

            // Dynamic summary text (clickable to toggle dropdown)
            $output .= '<div class="farmfaucet-milestone-summary">';
            if ($is_completed) {
                $output .= esc_html__('All tasks completed!', 'farmfaucet');
            } else {
                $remaining = $milestone_count - $user_completion_count;
                $percentage_left = 100 - $overall_percentage;
                $output .= sprintf(
                    esc_html__('You need %d more claims (%d%%) to complete this challenge.', 'farmfaucet'),
                    $remaining,
                    $percentage_left
                );
            }
            // Add a down arrow to indicate it's clickable
            $output .= '<span class="milestone-dropdown-arrow">▼</span>';
            $output .= '</div>';

            // Hidden task list (will be shown via JavaScript)
            $output .= '<div class="farmfaucet-milestone-task-list">';
            $output .= '<h5 class="farmfaucet-milestone-task-heading">' . esc_html__('Tasks to Complete:', 'farmfaucet') . '</h5>';

            // Task list items
            $output .= '<ul class="farmfaucet-milestone-tasks">';

            // Completion task
            $task_class = ($overall_percentage >= 100) ? 'farmfaucet-task-complete' : 'farmfaucet-task-incomplete';
            $task_icon = ($overall_percentage >= 100) ? '✓' : '○';
            $output .= '<li class="' . $task_class . '">';
            $output .= '<span class="farmfaucet-task-icon">' . $task_icon . '</span>';
            $output .= '<span class="farmfaucet-task-text">' . sprintf(esc_html__('Complete %d faucet claims', 'farmfaucet'), $milestone_count) . '</span>';
            $output .= '<span class="farmfaucet-task-progress">' . $user_completion_count . '/' . $milestone_count . '</span>';
            $output .= '</li>';

            $output .= '</ul>';
            $output .= '</div>'; // End task list

        } else {
            // Card view layout (original)

            // Milestone title (button text)
            $output .= '<h4 class="farmfaucet-milestone-title">' . esc_html($button['button_text']) . '</h4>';

            // Add milestone text to output
            $output .= '<div class="farmfaucet-milestone-description">' . esc_html($milestone_text) . '</div>';

            // Main progress section
            $output .= '<div class="farmfaucet-milestone-progress-section">';

            // Progress percentage display
            $output .= '<div class="farmfaucet-milestone-percentage-display">';
            $output .= '<span class="farmfaucet-milestone-percentage">' . $overall_percentage . '%</span>';
            $output .= '<span class="farmfaucet-milestone-complete-text">' . esc_html__('Complete!', 'farmfaucet') . '</span>';
            $output .= '</div>';

            // Progress bar with percentage inside
            $output .= '<div class="farmfaucet-milestone-progress-container" style="height: 30px; border-radius: ' . esc_attr($border_radius) . '; background-color: #f0f0f0; overflow: hidden; position: relative; margin: 10px 0; width: 100%; box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);">';
            $output .= '<div id="' . esc_attr($unique_id) . '" class="farmfaucet-milestone-progress-bar" data-progress="' . esc_attr($overall_percentage) . '" style="width: 0%; height: 100%; background: ' . esc_attr($bar_background) . '; position: absolute; left: 0; top: 0; transition: width 1.5s ease-in-out;">';
            $output .= '</div>';
            // Add percentage text inside the progress bar
            $output .= '<div class="farmfaucet-milestone-progress-text">' . $overall_percentage . '%</div>';
            $output .= '</div>';

            $output .= '</div>'; // End progress section

            // Task list section
            $output .= '<div class="farmfaucet-milestone-task-list">';
            $output .= '<h5 class="farmfaucet-milestone-task-heading">' . esc_html__('Tasks to Complete:', 'farmfaucet') . '</h5>';

            // Task list items
            $output .= '<ul class="farmfaucet-milestone-tasks">';

            // Completion task
            $task_class = ($overall_percentage >= 100) ? 'farmfaucet-task-complete' : 'farmfaucet-task-incomplete';
            $task_icon = ($overall_percentage >= 100) ? '✓' : '○';
            $output .= '<li class="' . $task_class . '">';
            $output .= '<span class="farmfaucet-task-icon">' . $task_icon . '</span>';
            $output .= '<span class="farmfaucet-task-text">' . sprintf(esc_html__('Complete %d faucet claims', 'farmfaucet'), $milestone_count) . '</span>';
            $output .= '<span class="farmfaucet-task-progress">' . $user_completion_count . '/' . $milestone_count . '</span>';
            $output .= '</li>';

            $output .= '</ul>';
            $output .= '</div>'; // End task list
        }

        // No need for inline JavaScript anymore as it's handled by milestone-messages.js

        // Check if faucet should be locked until milestone is complete
        if ($milestone_lock_faucet && !$is_completed) {
            // Show locked message
            $output .= '<div class="farmfaucet-milestone-locked-message">';
            $output .= '<p>' . esc_html__('This faucet is locked until you complete the milestone.', 'farmfaucet') . '</p>';
            $output .= '</div>';
        } else {
            // Add the actual button (hidden until milestone is complete)
            $output .= '<div class="farmfaucet-milestone-button-container" style="' . ($is_completed ? 'display: block;' : 'display: none;') . '">';
            $output .= '<a href="' . esc_url($button['redirect_url']) . '" class="farmfaucet-milestone-button farmfaucet-button-appear">' . esc_html($button['button_text']) . '</a>';
            $output .= '</div>';
        }

        $output .= '</div>'; // End milestone container

        return $output;
    }

    /**
     * Render page-specific milestone progress bar
     *
     * @param array $button Button data
     * @param array $faucet Faucet data
     * @param string $milestone_pages JSON string of page requirements
     * @param bool $milestone_lock_faucet Whether to lock the faucet until milestone is completed
     * @return string HTML output
     */
    private function render_page_milestone_progress($button, $faucet, $milestone_pages, $milestone_lock_faucet = false)
    {
        $user_ip = Farmfaucet_Security::get_user_ip();

        // Parse milestone pages
        $pages = [];
        if (!empty($milestone_pages)) {
            try {
                $pages = json_decode($milestone_pages, true);
            } catch (Exception $e) {
                // Log error but continue with empty pages
                Farmfaucet_Logger::log_error("Error parsing milestone pages: " . $e->getMessage());
            }
        }

        if (empty($pages)) {
            return '<div class="farmfaucet-error">' . esc_html__('No page requirements configured', 'farmfaucet') . '</div>';
        }

        // Track page visits
        $this->track_page_visit();

        // Generate unique ID for this progress bar
        $unique_id = 'farmfaucet-milestone-' . uniqid();

        // Get progress bar style and colors
        $bar_style = isset($button['milestone_bar_style']) ? $button['milestone_bar_style'] : 'solid';
        $border_radius = $button['border_shape'] === 'rounded' ? '20px' : '4px';

        // Set background style based on bar style
        if ($bar_style === 'gradient' && !empty($button['milestone_gradient_start']) && !empty($button['milestone_gradient_end'])) {
            $bar_background = 'linear-gradient(to right, ' . esc_attr($button['milestone_gradient_start']) . ', ' . esc_attr($button['milestone_gradient_end']) . ')';
        } else {
            // Default to solid color
            $bar_color = !empty($button['milestone_bar_color']) ? $button['milestone_bar_color'] : $this->get_button_color($button['button_color']);
            $bar_background = $bar_color;
        }

        // Calculate overall progress
        $total_required = 0;
        $total_completed = 0;
        $page_progress = [];

        foreach ($pages as $page_id => $page) {
            $page_url = isset($page['url']) ? esc_url_raw($page['url']) : '';
            $page_name = isset($page['name']) ? sanitize_text_field($page['name']) : 'Page';
            $page_count = isset($page['count']) ? intval($page['count']) : 1;

            // Skip invalid pages
            if (empty($page_url)) continue;

            // Get completion count for this page
            $page_key = 'farmfaucet_page_visit_' . md5($page_url) . '_' . md5($user_ip);
            $page_visits = get_transient($page_key) ?: 0;

            // Add to totals
            $total_required += $page_count;
            $total_completed += min($page_visits, $page_count);

            // Store progress for this page
            $page_progress[$page_id] = [
                'name' => $page_name,
                'url' => $page_url,
                'count' => $page_count,
                'completed' => $page_visits,
                'percentage' => min(100, floor(($page_visits / $page_count) * 100))
            ];
        }

        // Calculate overall percentage
        $overall_percentage = $total_required > 0
            ? min(100, floor(($total_completed / $total_required) * 100))
            : 0;

        // Determine if milestone is completed
        $is_completed = ($overall_percentage >= 100);
        $completion_class = $is_completed ? 'farmfaucet-milestone-complete' : '';

        // Get milestone card appearance settings
        $display_style = isset($button['milestone_display_style']) && !empty($button['milestone_display_style']) ? $button['milestone_display_style'] : 'card';
        // Validate display style to ensure it's either 'card' or 'compact'
        if ($display_style !== 'card' && $display_style !== 'compact') {
            $display_style = 'card'; // Default to card if invalid value
        }
        $transparent_bg = isset($button['milestone_transparent_bg']) && $button['milestone_transparent_bg'] == 1;
        $card_bg_style = isset($button['milestone_card_bg_style']) ? $button['milestone_card_bg_style'] : 'solid';
        $card_bg_color = isset($button['milestone_card_bg_color']) ? $button['milestone_card_bg_color'] : '#ffffff';
        $card_gradient_start = isset($button['milestone_card_gradient_start']) ? $button['milestone_card_gradient_start'] : '#ffffff';
        $card_gradient_end = isset($button['milestone_card_gradient_end']) ? $button['milestone_card_gradient_end'] : '#f5f5f5';

        // Add transparent class if needed
        $transparent_class = $transparent_bg ? 'transparent-bg' : '';

        // Add display style class
        $style_class = $display_style === 'compact' ? 'compact-view' : 'card-view';

        // Set card background style
        $card_background = '';
        if (!$transparent_bg) {
            if ($card_bg_style === 'gradient' && !empty($card_gradient_start) && !empty($card_gradient_end)) {
                $card_background = 'background: linear-gradient(to bottom, ' . esc_attr($card_gradient_start) . ', ' . esc_attr($card_gradient_end) . ');';
            } else {
                $card_background = 'background: ' . esc_attr($card_bg_color) . ';';
            }
        } else {
            $card_background = 'background: transparent; box-shadow: none;';
        }

        // Get message change interval (default to 60 seconds if not set)
        $message_interval = isset($button['message_interval']) ? absint($button['message_interval']) : 60;

        // Build HTML output with animation
        $output = '<div class="farmfaucet-milestone-container ' . $style_class . ' ' . $transparent_class . ' ' . $completion_class . '" data-button-id="' . esc_attr($button['id']) . '" data-faucet-id="' . esc_attr($faucet['id']) . '" data-message-interval="' . esc_attr($message_interval) . '" style="' . $card_background . '">';

        // Different layout based on display style
        if ($display_style === 'compact') {
            // Compact view layout

            // Header with title and percentage
            $output .= '<div class="farmfaucet-milestone-header">';
            $output .= '<h4 class="farmfaucet-milestone-title">' . esc_html($button['button_text']) . '</h4>';
            $output .= '<div class="farmfaucet-milestone-percentage-display">';
            $output .= '<span class="farmfaucet-milestone-percentage">' . $overall_percentage . '%</span>';
            $output .= '<span class="farmfaucet-milestone-complete-text">' . esc_html__('Complete!', 'farmfaucet') . '</span>';
            $output .= '</div>';
            $output .= '</div>';

            // Progress bar with percentage inside
            $output .= '<div class="farmfaucet-milestone-progress-container">';
            $output .= '<div id="' . esc_attr($unique_id) . '" class="farmfaucet-milestone-progress-bar" data-progress="' . esc_attr($overall_percentage) . '" style="width: 0%; background: ' . esc_attr($bar_background) . ';">';
            $output .= '</div>';
            $output .= '<div class="farmfaucet-milestone-progress-text" style="position: absolute; width: 100%; text-align: center; color: #fff; font-weight: bold; line-height: 24px; font-size: 0.9em; text-shadow: 0 1px 2px rgba(0,0,0,0.3); z-index: 5; left: 0; top: 0; height: 100%; display: flex; align-items: center; justify-content: center; pointer-events: none; margin: 0; padding: 0;">' . $overall_percentage . '%</div>';
            $output .= '</div>';

            // Dynamic summary text (clickable to toggle dropdown)
            $output .= '<div class="farmfaucet-milestone-summary">';
            if ($is_completed) {
                $output .= esc_html__('All tasks completed!', 'farmfaucet');
            } else {
                // Count remaining pages but use it only for internal logic
                $remaining_pages = count(array_filter($page_progress, function ($page) {
                    return $page['percentage'] < 100;
                }));
                $output .= sprintf(
                    esc_html__('You are %d%% complete', 'farmfaucet'),
                    $overall_percentage
                );
            }
            // Add a down arrow to indicate it's clickable
            $output .= '<span class="milestone-dropdown-arrow">▼</span>';
            $output .= '</div>';

            // No dropdown toggle needed anymore as the entire milestone is clickable

            // Hidden task list (will be shown via JavaScript)
            $output .= '<div class="farmfaucet-milestone-task-list">';
            $output .= '<h5 class="farmfaucet-milestone-task-heading">' . esc_html__('Pages to Visit:', 'farmfaucet') . '</h5>';

            // Task list items
            $output .= '<ul class="farmfaucet-milestone-tasks">';
        } else {
            // Card view layout (original)

            // Milestone title (button text)
            $output .= '<h4 class="farmfaucet-milestone-title">' . esc_html($button['button_text']) . '</h4>';

            // Main progress section
            $output .= '<div class="farmfaucet-milestone-progress-section">';

            // Progress percentage display
            $output .= '<div class="farmfaucet-milestone-percentage-display">';
            $output .= '<span class="farmfaucet-milestone-percentage">' . $overall_percentage . '%</span>';
            $output .= '<span class="farmfaucet-milestone-complete-text">' . esc_html__('Complete!', 'farmfaucet') . '</span>';
            $output .= '</div>';

            // Progress bar with percentage inside
            $output .= '<div class="farmfaucet-milestone-progress-container" style="height: 30px; border-radius: ' . esc_attr($border_radius) . '; background-color: #f0f0f0; overflow: hidden; position: relative; margin: 10px 0; width: 100%;">';
            $output .= '<div id="' . esc_attr($unique_id) . '" class="farmfaucet-milestone-progress-bar" data-progress="' . esc_attr($overall_percentage) . '" style="width: 0%; height: 100%; background: ' . esc_attr($bar_background) . '; position: absolute; left: 0; top: 0; transition: width 1.5s ease-in-out;">';
            $output .= '</div>';
            // Add percentage text inside the progress bar
            $output .= '<div class="farmfaucet-milestone-progress-text" style="position: absolute; width: 100%; text-align: center; color: #fff; font-weight: bold; line-height: 30px; font-size: 1.1em; text-shadow: 0 1px 2px rgba(0,0,0,0.3); z-index: 5; left: 0; top: 0; height: 100%; display: flex; align-items: center; justify-content: center; pointer-events: none; margin: 0; padding: 0;">' . $overall_percentage . '%</div>';
            $output .= '</div>';

            $output .= '</div>'; // End progress section

            // Task list section
            $output .= '<div class="farmfaucet-milestone-task-list">';
            $output .= '<h5 class="farmfaucet-milestone-task-heading">' . esc_html__('Pages to Visit:', 'farmfaucet') . '</h5>';

            // Task list items
            $output .= '<ul class="farmfaucet-milestone-tasks">';
        }

        // Individual page progress
        foreach ($page_progress as $page_id => $page) {
            $page_task_class = ($page['percentage'] >= 100) ? 'farmfaucet-task-complete' : 'farmfaucet-task-incomplete';
            $page_task_icon = ($page['percentage'] >= 100) ? '✓' : '○';

            $output .= '<li class="' . $page_task_class . '">';
            $output .= '<span class="farmfaucet-task-icon">' . $page_task_icon . '</span>';
            $output .= '<span class="farmfaucet-task-text">' . esc_html($page['name']) . '</span>';
            $output .= '<span class="farmfaucet-task-progress">' . $page['completed'] . '/' . $page['count'] . '</span>';

            // Only show visit button for incomplete tasks
            if ($page['percentage'] < 100) {
                $output .= '<a href="' . esc_url($page['url']) . '" class="farmfaucet-task-action" target="_blank">' .
                    esc_html__('Visit', 'farmfaucet') .
                    '</a>';
            }

            $output .= '</li>';
        }

        $output .= '</ul>';
        $output .= '</div>'; // End task list

        // Add CSS for the milestone display (already added in the global milestone function)

        // Add page-specific CSS
        $output .= '<style>
            .farmfaucet-task-action {
                margin-left: 10px;
                background: #f0f0f0;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 0.8em;
                text-decoration: none;
                color: #333;
                transition: all 0.2s ease;
            }
            .farmfaucet-task-action:hover {
                background: #e0e0e0;
                color: #000;
            }
            .farmfaucet-milestone-button-container {
                margin-top: 20px;
                text-align: center;
                display: none;
            }
            .farmfaucet-milestone-complete .farmfaucet-milestone-button-container {
                display: block;
            }
            .farmfaucet-milestone-button {
                display: inline-block;
                padding: 10px 20px;
                background-color: ' . esc_attr($this->get_button_color($button['button_color'])) . ';
                color: #fff;
                text-decoration: none;
                border-radius: ' . esc_attr($border_radius) . ';
                font-weight: bold;
                transition: all 0.3s ease;
                border: none;
                cursor: pointer;
                font-size: 16px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }
            .farmfaucet-milestone-button:hover {
                opacity: 0.9;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            .farmfaucet-milestone-locked-message {
                background-color: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                text-align: center;
                border: 1px solid #f5c6cb;
            }
            .farmfaucet-milestone-locked-message p {
                margin: 0;
                font-weight: bold;
            }
        </style>';

        // No need for inline JavaScript anymore as it's handled by milestone-messages.js

        // Check if faucet should be locked until milestone is complete
        if ($milestone_lock_faucet && !$is_completed) {
            // Show locked message
            $output .= '<div class="farmfaucet-milestone-locked-message">';
            $output .= '<p>' . esc_html__('This faucet is locked until you complete the milestone.', 'farmfaucet') . '</p>';
            $output .= '</div>';
        } else {
            // Add the actual button (hidden until milestone is complete)
            $output .= '<div class="farmfaucet-milestone-button-container" style="' . ($is_completed ? 'display: block;' : 'display: none;') . '">';
            $output .= '<a href="' . esc_url($button['redirect_url']) . '" class="farmfaucet-milestone-button farmfaucet-button-appear">' . esc_html($button['button_text']) . '</a>';
            $output .= '</div>';
        }

        $output .= '</div>'; // End milestone container

        return $output;
    }

    /**
     * Track page visit for milestone tracking
     */
    private function track_page_visit()
    {
        // Get current page URL
        $current_url = $this->get_current_page_url();

        if (empty($current_url)) {
            return;
        }

        $user_ip = Farmfaucet_Security::get_user_ip();
        $page_key = 'farmfaucet_page_visit_' . md5($current_url) . '_' . md5($user_ip);

        // Get current visit count
        $visit_count = get_transient($page_key) ?: 0;
        $visit_count++;

        // Store the updated count with a long expiration (30 days)
        set_transient($page_key, $visit_count, 30 * 86400); // 86400 seconds = 1 day
    }

    /**
     * Get the current page URL
     *
     * @return string Current page URL
     */
    private function get_current_page_url()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = isset($_SERVER['HTTP_HOST']) ? sanitize_text_field($_SERVER['HTTP_HOST']) : '';
        $uri = isset($_SERVER['REQUEST_URI']) ? sanitize_text_field($_SERVER['REQUEST_URI']) : '';

        if (empty($host)) {
            return '';
        }

        return $protocol . '://' . $host . $uri;
    }

    /**
     * Render the user's username form
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered username form HTML
     */
    public function render_username_form($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'button_text' => esc_html__('Save', 'farmfaucet'),
            'placeholder' => esc_html__('Enter your display name', 'farmfaucet'),
            'label' => esc_html__('Change your display name', 'farmfaucet'),
            'success_message' => esc_html__('Display name updated successfully!', 'farmfaucet'),
            'toggle_mode' => 'no',
            'require_otp' => 'yes',
            'otp_label' => esc_html__('Enter OTP sent to your Telegram', 'farmfaucet'),
            'otp_placeholder' => esc_html__('Enter OTP code', 'farmfaucet'),
        ], $atts);

        // Get user IP for identification
        $user_ip = Farmfaucet_Security::get_user_ip();
        $user_hash = md5($user_ip);

        // Get or create user
        $user = Farmfaucet_Users::get_or_create_user($user_hash);

        // Get current display name
        $display_name = !empty($user['display_name']) ? $user['display_name'] : '';

        // Generate unique ID for this form
        $form_id = 'farmfaucet-username-form-' . uniqid();

        // Build HTML
        $output = '<div class="farmfaucet-username-container">';

        // Current display name with edit button
        $output .= '<div class="farmfaucet-current-username">';
        $output .= '<div class="farmfaucet-current-name-display">' . esc_html($display_name) . '</div>';
        $output .= '<button type="button" class="farmfaucet-username-edit-btn">' . esc_html__('Edit', 'farmfaucet') . '</button>';
        $output .= '</div>';

        // Form (initially hidden)
        $output .= '<div class="farmfaucet-username-form">';

        // Form label
        $output .= '<label class="farmfaucet-username-label" for="username-' . esc_attr($form_id) . '">' . esc_html($atts['label']) . '</label>';

        // Form
        $output .= '<form id="' . esc_attr($form_id) . '" class="farmfaucet-username-form-inner">';

        // Username input
        $output .= '<div class="farmfaucet-username-input-container">';
        $output .= '<input type="text" id="username-' . esc_attr($form_id) . '" name="username" value="' . esc_attr($display_name) . '" placeholder="' . esc_attr($atts['placeholder']) . '" class="farmfaucet-username-input" maxlength="50" required>';
        $output .= '</div>';

        // OTP field (if required)
        if ($atts['require_otp'] === 'yes') {
            $output .= '<div class="farmfaucet-otp-container">';
            $output .= '<label class="farmfaucet-otp-label" for="otp-' . esc_attr($form_id) . '">' . esc_html($atts['otp_label']) . '</label>';
            $output .= '<div class="farmfaucet-otp-input-container">';
            $output .= '<input type="text" id="otp-' . esc_attr($form_id) . '" name="otp_code" placeholder="' . esc_attr($atts['otp_placeholder']) . '" class="farmfaucet-otp-input" maxlength="6" required>';
            $output .= '<button type="button" class="farmfaucet-send-otp-button">' . esc_html__('Send OTP', 'farmfaucet') . '</button>';
            $output .= '</div>';
            $output .= '<div class="farmfaucet-otp-message"></div>';
            $output .= '</div>';
        }

        // Submit button and cancel link
        $output .= '<div class="farmfaucet-username-submit-container">';
        $output .= '<a href="#" class="farmfaucet-username-cancel-link">' . esc_html__('Cancel', 'farmfaucet') . '</a>';
        $output .= '<button type="submit" class="farmfaucet-username-submit">' . esc_html($atts['button_text']) . '</button>';
        $output .= '</div>';

        // Hidden nonce field for security
        $output .= wp_nonce_field('farmfaucet_username_nonce', 'username_nonce', true, false);

        // Success message (initially hidden)
        $output .= '<div class="farmfaucet-username-success" style="display: none;">' . esc_html($atts['success_message']) . '</div>';

        $output .= '</form>';
        $output .= '</div>'; // End form container

        $output .= '</div>'; // End container

        // Enqueue username form script and styles (improved version)
        wp_enqueue_script('farmfaucet-username-form', FARMFAUCET_URL . 'assets/js/username-form-improved.js', ['jquery'], FARMFAUCET_VERSION . '.' . time(), true);
        wp_enqueue_style('farmfaucet-username-form', FARMFAUCET_URL . 'assets/css/username-form-fix.css', [], FARMFAUCET_VERSION . '.' . time());

        // Enqueue transparency fix CSS
        wp_enqueue_style('farmfaucet-dashboard-transparent-fix', FARMFAUCET_URL . 'assets/css/dashboard-transparent-fix.css', ['farmfaucet-username-form'], FARMFAUCET_VERSION . '.' . time());

        // Enqueue faucet appearance fix CSS
        wp_enqueue_style('farmfaucet-appearance-fix', FARMFAUCET_URL . 'assets/css/faucet-appearance-fix.css', ['farmfaucet-style'], FARMFAUCET_VERSION . '.' . time());

        // Enqueue form background fix JavaScript
        wp_enqueue_script('farmfaucet-form-bg-fix', FARMFAUCET_URL . 'assets/js/faucet-form-bg-fix.js', ['jquery'], FARMFAUCET_VERSION . '.' . time(), true);

        // Add username nonce to script
        wp_localize_script('farmfaucet-username-form', 'farmfaucet_vars', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'username_nonce' => wp_create_nonce('farmfaucet_username_nonce')
        ]);

        return $output;
    }

    /**
     * Render the user's username
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered username HTML
     */
    public function render_username($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'default' => esc_html__('Anonymous', 'farmfaucet'),
        ], $atts);

        // Check if user is logged in with WordPress
        if (function_exists('is_user_logged_in') && is_user_logged_in() && function_exists('get_current_user_id') && function_exists('get_userdata')) {
            $user_id = get_current_user_id();
            $user_data = get_userdata($user_id);

            if ($user_data) {
                // Return the WordPress display name (not username)
                $display_name = !empty($user_data->display_name) ? $user_data->display_name : $user_data->user_login;
                return '<span class="farmfaucet-username">' . esc_html($display_name) . '</span>';
            }
        }

        // Fallback to the old method for non-WordPress users
        // Get user IP for identification
        $user_hash = md5(Farmfaucet_Security::get_user_ip());

        // Get or create user
        $user = Farmfaucet_Users::get_or_create_user($user_hash);

        // Get display name from user data
        $display_name = !empty($user['display_name']) ? $user['display_name'] : $atts['default'];

        // Return the username in a span that inherits parent styling
        return '<span class="farmfaucet-username">' . esc_html($display_name) . '</span>';
    }

    /**
     * Render the leaderboard name form
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered form HTML
     */
    public function render_leaderboard_name_form($atts = [])
    {
        // Add toggle mode to the attributes
        $atts['toggle_mode'] = 'yes';

        // Use the improved username form
        return $this->render_username_form($atts);
    }

    /**
     * AJAX handler for sending OTP
     */
    public function send_otp()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_username_nonce')) {
            wp_send_json_error(__('Invalid request', 'farmfaucet'));
            return;
        }

        // Generate a random 6-digit OTP
        $otp = sprintf('%06d', mt_rand(100000, 999999));

        // Store OTP in user session
        if (!isset($_SESSION)) {
            session_start();
        }

        $_SESSION['farmfaucet_otp'] = $otp;
        $_SESSION['farmfaucet_otp_time'] = time();

        // Get user information
        $user_hash = md5(Farmfaucet_Security::get_user_ip());
        $user = Farmfaucet_Users::get_or_create_user($user_hash);

        // Check if user has Telegram ID
        if (empty($user['telegram_id'])) {
            // Try to get WordPress user if logged in
            if (function_exists('is_user_logged_in') && is_user_logged_in() && function_exists('get_current_user_id')) {
                $user_id = get_current_user_id();
                $telegram_id = get_user_meta($user_id, 'farmfaucet_telegram_id', true);

                if (!empty($telegram_id)) {
                    // Send OTP via Telegram
                    $result = $this->send_telegram_otp($telegram_id, $otp);

                    if ($result) {
                        wp_send_json_success(['message' => __('OTP sent to your Telegram', 'farmfaucet')]);
                    } else {
                        wp_send_json_error(__('Failed to send OTP to your Telegram. Please try again.', 'farmfaucet'));
                    }
                } else {
                    wp_send_json_error(__('No Telegram account linked. Please connect your Telegram account first.', 'farmfaucet'));
                }
            } else {
                wp_send_json_error(__('No Telegram account linked. Please connect your Telegram account first.', 'farmfaucet'));
            }
        } else {
            // Send OTP via Telegram
            $result = $this->send_telegram_otp($user['telegram_id'], $otp);

            if ($result) {
                wp_send_json_success(['message' => __('OTP sent to your Telegram', 'farmfaucet')]);
            } else {
                wp_send_json_error(__('Failed to send OTP to your Telegram. Please try again.', 'farmfaucet'));
            }
        }
    }

    /**
     * Send OTP via Telegram
     *
     * @param string $telegram_id Telegram user ID
     * @param string $otp OTP code
     * @return bool Success status
     */
    private function send_telegram_otp($telegram_id, $otp)
    {
        // Get Telegram bot token
        $bot_token = get_option('farmfaucet_telegram_bot_token', '');

        if (empty($bot_token)) {
            return false;
        }

        // Prepare message
        $message = __('Your OTP code for username change is:', 'farmfaucet') . ' ' . $otp;
        $message .= "\n\n" . __('This code will expire in 5 minutes.', 'farmfaucet');

        // Send message via Telegram API
        $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
        $data = [
            'chat_id' => $telegram_id,
            'text' => $message,
            'parse_mode' => 'HTML'
        ];

        $response = wp_remote_post($url, [
            'body' => $data,
            'timeout' => 15
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        return isset($body['ok']) && $body['ok'] === true;
    }

    /**
     * Verify OTP code
     *
     * @param string $otp_code OTP code to verify
     * @return bool Verification result
     */
    private function verify_otp($otp_code)
    {
        if (!isset($_SESSION)) {
            session_start();
        }

        // Check if OTP exists in session
        if (!isset($_SESSION['farmfaucet_otp']) || !isset($_SESSION['farmfaucet_otp_time'])) {
            return false;
        }

        $stored_otp = $_SESSION['farmfaucet_otp'];
        $otp_time = $_SESSION['farmfaucet_otp_time'];

        // Check if OTP has expired (5 minutes)
        if (time() - $otp_time > 300) {
            // Clear expired OTP
            unset($_SESSION['farmfaucet_otp']);
            unset($_SESSION['farmfaucet_otp_time']);
            return false;
        }

        // Verify OTP
        if ($otp_code === $stored_otp) {
            // Clear used OTP
            unset($_SESSION['farmfaucet_otp']);
            unset($_SESSION['farmfaucet_otp_time']);
            return true;
        }

        return false;
    }

    /**
     * AJAX handler for updating leaderboard name
     */
    public function update_leaderboard_name()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_username_nonce')) {
            wp_send_json_error(__('Invalid request', 'farmfaucet'));
            return;
        }

        // Validate username - check both possible parameter names
        $username = '';
        if (isset($_POST['display_name'])) {
            $username = sanitize_text_field($_POST['display_name']);
        } elseif (isset($_POST['username'])) {
            $username = sanitize_text_field($_POST['username']);
        }

        if (empty($username)) {
            wp_send_json_error(__('Display name cannot be empty', 'farmfaucet'));
            return;
        }

        if (strlen($username) > 50) {
            wp_send_json_error(__('Display name is too long (maximum 50 characters)', 'farmfaucet'));
            return;
        }

        // Verify OTP if provided
        if (isset($_POST['otp_code']) && !empty($_POST['otp_code'])) {
            $otp_code = sanitize_text_field($_POST['otp_code']);

            if (!$this->verify_otp($otp_code)) {
                wp_send_json_error(__('Invalid or expired OTP code. Please request a new OTP.', 'farmfaucet'));
                return;
            }
        }

        // Get user IP for identification
        $user_hash = md5(Farmfaucet_Security::get_user_ip());

        // Check if the display name is already taken by another user
        if (Farmfaucet_Users::is_display_name_taken($username, $user_hash)) {
            wp_send_json_error(__('This display name is already taken. Please choose another one.', 'farmfaucet'));
            return;
        }

        // Update the user's display name in the Farm Faucet database
        $result = Farmfaucet_Users::update_display_name($user_hash, $username);

        if (!$result) {
            wp_send_json_error(__('Failed to update display name', 'farmfaucet'));
            return;
        }

        // Also update WordPress user profile if logged in
        Farmfaucet_Users::update_wordpress_user($user_hash, $username, '');

        wp_send_json_success();
    }

    /**
     * Render the leaderboard
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered leaderboard HTML
     */
    public function render_leaderboard($atts = [])
    {
        // Extract attributes
        $atts = shortcode_atts([
            'title' => esc_html__('Top Users', 'farmfaucet'),
            'limit' => 15,
            'show_rank' => 'yes',
            'show_avatar' => 'yes',
            'show_completions' => 'yes',
        ], $atts);

        // Get top users
        $users = Farmfaucet_Users::get_top_users($atts['limit']);

        if (empty($users)) {
            return '<div class="farmfaucet-leaderboard-empty">' . esc_html__('No users found', 'farmfaucet') . '</div>';
        }

        // Start building the leaderboard
        $output = '<div class="farmfaucet-leaderboard">';

        // Add title if provided
        if (!empty($atts['title'])) {
            $output .= '<h3 class="farmfaucet-leaderboard-title">' . esc_html($atts['title']) . '</h3>';
        }

        // Start the leaderboard list
        $output .= '<ul class="farmfaucet-leaderboard-list">';

        // Add each user to the leaderboard
        foreach ($users as $index => $user) {
            $rank = $index + 1;
            $display_name = !empty($user['display_name']) ? $user['display_name'] : 'User' . substr(md5($user['user_hash']), 0, 6);
            $avatar_url = !empty($user['profile_picture']) ? $user['profile_picture'] : Farmfaucet_Users::get_default_avatar($user['user_hash']);
            $completions = $user['total_completions'];

            // Determine rank class
            $rank_class = '';
            $rank_effect = '';

            if ($rank === 1) {
                $rank_class = 'farmfaucet-rank-gold';
                $rank_effect = '<div class="farmfaucet-gold-sparkle"></div>';
            } elseif ($rank === 2) {
                $rank_class = 'farmfaucet-rank-silver';
                $rank_effect = '<div class="farmfaucet-silver-sparkle"></div>';
            } elseif ($rank === 3) {
                $rank_class = 'farmfaucet-rank-bronze';
                $rank_effect = '<div class="farmfaucet-bronze-sparkle"></div>';
            }

            // Build the user item
            $output .= '<li class="farmfaucet-leaderboard-item ' . esc_attr($rank_class) . '">';

            // Show rank if enabled
            if ($atts['show_rank'] === 'yes') {
                $output .= '<div class="farmfaucet-leaderboard-rank">' . esc_html($rank) . '</div>';
            }

            // Show avatar if enabled
            if ($atts['show_avatar'] === 'yes') {
                $output .= '<div class="farmfaucet-leaderboard-avatar">';
                $output .= '<img src="' . esc_url($avatar_url) . '" alt="' . esc_attr($display_name) . '" />';
                $output .= '</div>';
            }

            // Show user name with special effects for top 3
            $output .= '<div class="farmfaucet-leaderboard-name">';
            $output .= '<span class="farmfaucet-leaderboard-name-text">' . esc_html($display_name) . '</span>';
            $output .= $rank_effect;
            $output .= '</div>';

            // Show completions if enabled
            if ($atts['show_completions'] === 'yes') {
                $output .= '<div class="farmfaucet-leaderboard-completions">';
                $output .= '<span class="farmfaucet-leaderboard-completions-count">' . esc_html($completions) . '</span>';
                $output .= '<span class="farmfaucet-leaderboard-completions-label">' . esc_html__('completions', 'farmfaucet') . '</span>';
                $output .= '</div>';
            }

            $output .= '</li>';
        }

        $output .= '</ul>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render the user's avatar
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered avatar HTML
     */
    public function render_avatar($atts = [])
    {
        // Get the global avatar size setting
        $default_size = get_option('farmfaucet_avatar_size', 50);

        // Extract attributes
        $atts = shortcode_atts([
            'size' => $default_size,
            'border' => 'yes',
            'border_color' => '#ddd',
            'border_width' => 2,
        ], $atts);

        // Get user IP for identification
        $user_hash = md5(Farmfaucet_Security::get_user_ip());

        // Get or create user
        $user = Farmfaucet_Users::get_or_create_user($user_hash);

        // Get avatar URL
        $avatar_url = !empty($user['profile_picture']) ? $user['profile_picture'] : Farmfaucet_Users::get_default_avatar($user_hash);

        // Get the global avatar size setting
        $global_size = get_option('farmfaucet_avatar_size', 50);

        // Build inline styles - use the global size if no size is specified
        $size = isset($atts['size']) && !empty($atts['size']) ? absint($atts['size']) : $global_size;
        $border = $atts['border'] === 'yes' ? true : false;
        $border_color = sanitize_hex_color($atts['border_color']) ?: '#ddd';
        $border_width = absint($atts['border_width']);

        $styles = 'width: ' . $size . 'px; ';
        $styles .= 'height: ' . $size . 'px; ';
        $styles .= 'border-radius: 50%; ';
        $styles .= 'overflow: hidden; ';

        if ($border) {
            $styles .= 'border: ' . $border_width . 'px solid ' . $border_color . '; ';
        }

        // Build HTML
        $output = '<div class="farmfaucet-avatar" style="' . esc_attr($styles) . '">';
        $output .= '<img src="' . esc_url($avatar_url) . '" alt="' . esc_attr__('User Avatar', 'farmfaucet') . '" style="width: 100%; height: 100%; object-fit: cover;">';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render the user's editable avatar
     *
     * @param array $atts Shortcode attributes
     * @return string The rendered editable avatar HTML
     */
    public function render_avatar_editable($atts = [])
    {
        // Get the global avatar size setting
        $default_size = get_option('farmfaucet_avatar_size', 50);

        // Extract attributes
        $atts = shortcode_atts([
            'size' => $default_size,
            'border' => 'yes',
            'border_color' => '#ddd',
            'border_width' => 2,
            'success_message' => esc_html__('Avatar updated!', 'farmfaucet'),
        ], $atts);

        // Get user IP for identification
        $user_hash = md5(Farmfaucet_Security::get_user_ip());

        // Get or create user
        $user = Farmfaucet_Users::get_or_create_user($user_hash);

        // Get avatar URL
        $avatar_url = !empty($user['profile_picture']) ? $user['profile_picture'] : Farmfaucet_Users::get_default_avatar($user_hash);

        // Build inline styles - use the global size if no size is specified
        $size = isset($atts['size']) && !empty($atts['size']) ? absint($atts['size']) : $default_size;
        $border = $atts['border'] === 'yes' ? true : false;
        $border_color = sanitize_hex_color($atts['border_color']) ?: '#ddd';
        $border_width = absint($atts['border_width']);

        $styles = 'width: ' . $size . 'px; height: ' . $size . 'px;';
        if ($border) {
            $styles .= ' border: ' . $border_width . 'px solid ' . $border_color . ';';
        }

        // Generate unique ID for this avatar
        $avatar_id = 'farmfaucet-avatar-' . uniqid();

        // Build HTML
        $output = '<div id="' . esc_attr($avatar_id) . '" class="farmfaucet-avatar-editable">';
        $output .= '<img src="' . esc_url($avatar_url) . '" alt="' . esc_attr__('User Avatar', 'farmfaucet') . '" style="' . esc_attr($styles) . '" data-original-src="' . esc_url($avatar_url) . '">';
        $output .= '<input type="file" accept="image/*">';

        // Add popup menu
        $output .= '<div class="farmfaucet-avatar-popup">';
        $output .= '<ul>';
        $output .= '<li class="view-image"><i class="fas fa-search-plus"></i>' . esc_html__('View Image', 'farmfaucet') . '</li>';
        $output .= '<li class="change-picture"><i class="fas fa-camera"></i>' . esc_html__('Change Picture', 'farmfaucet') . '</li>';
        $output .= '</ul>';
        $output .= '</div>';

        $output .= '<div class="farmfaucet-avatar-success"><i class="fas fa-check"></i></div>';
        $output .= '</div>';

        // Enqueue avatar script and styles
        wp_enqueue_script('farmfaucet-avatar', FARMFAUCET_URL . 'assets/js/avatar.js', ['jquery'], FARMFAUCET_VERSION, true);
        wp_enqueue_style('farmfaucet-avatar', FARMFAUCET_URL . 'assets/css/avatar.css', [], FARMFAUCET_VERSION);

        // Add avatar nonce to script
        wp_localize_script('farmfaucet-avatar', 'farmfaucet_vars', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'avatar_nonce' => wp_create_nonce('farmfaucet_avatar_nonce')
        ]);

        return $output;
    }

    /**
     * AJAX handler for updating avatar
     */
    public function update_avatar()
    {
        // Verify nonce
        if (!Farmfaucet_Security::verify_nonce($_POST['nonce'], 'farmfaucet_avatar_nonce')) {
            wp_send_json_error(__('Invalid request', 'farmfaucet'));
            return;
        }

        // Check if file was uploaded
        if (empty($_FILES['avatar'])) {
            wp_send_json_error(__('No file uploaded', 'farmfaucet'));
            return;
        }

        // Get the uploaded file
        $file = $_FILES['avatar'];

        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('File upload failed', 'farmfaucet'));
            return;
        }

        // Validate file type
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowed_types)) {
            wp_send_json_error(__('Invalid file type. Only JPG, PNG, and GIF are allowed', 'farmfaucet'));
            return;
        }

        // Validate file size (max 2MB)
        if ($file['size'] > 2 * 1024 * 1024) {
            wp_send_json_error(__('File size exceeds 2MB limit', 'farmfaucet'));
            return;
        }

        // Get WordPress upload directory
        $upload_dir = wp_upload_dir();

        // Create farmfaucet directory if it doesn't exist
        $farmfaucet_dir = $upload_dir['basedir'] . '/farmfaucet';
        if (!file_exists($farmfaucet_dir)) {
            mkdir($farmfaucet_dir, 0755);
        }

        // Create avatars directory if it doesn't exist
        $avatars_dir = $farmfaucet_dir . '/avatars';
        if (!file_exists($avatars_dir)) {
            mkdir($avatars_dir, 0755);
        }

        // Generate unique filename
        $user_hash = md5(Farmfaucet_Security::get_user_ip());
        $filename = $user_hash . '-' . time() . '-' . sanitize_file_name($file['name']);
        $filepath = $avatars_dir . '/' . $filename;

        // Move the uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            wp_send_json_error(__('Failed to save the uploaded file', 'farmfaucet'));
            return;
        }

        // Generate the URL for the uploaded file
        $avatar_url = $upload_dir['baseurl'] . '/farmfaucet/avatars/' . $filename;

        // Update user profile in the Farm Faucet system
        $result = Farmfaucet_Users::update_profile_picture($user_hash, $avatar_url);

        if (!$result) {
            wp_send_json_error(__('Failed to update profile', 'farmfaucet'));
            return;
        }

        // Also update WordPress user profile if logged in
        Farmfaucet_Users::update_wordpress_user($user_hash, '', $avatar_url);

        // Return success with the avatar URL
        wp_send_json_success([
            'avatar_url' => $avatar_url
        ]);
    }

    /**
     * Render a countdown button
     *
     * @param array $button Button data
     * @param array $faucet Faucet data
     * @param int $countdown_seconds Countdown time in seconds
     * @param string $countdown_message Message to display next to the countdown
     * @param bool $countdown_click_activation Whether to activate countdown on element click
     * @param string $countdown_click_element_id ID of the element to click to activate countdown
     * @param string $countdown_pre_click_message Message to display before element is clicked
     * @return string The rendered countdown button HTML
     */
    public function render_countdown_button($button, $faucet, $countdown_seconds, $countdown_message, $countdown_click_activation, $countdown_click_element_id, $countdown_pre_click_message)
    {
        // Check if standby mode is enabled
        $standby_enabled = isset($button['countdown_standby']) && $button['countdown_standby'] == 1;

        // Check if captcha is enabled
        $captcha_enabled = isset($button['countdown_captcha']) && $button['countdown_captcha'] == 1;

        // Get captcha type (default to global setting if not specified)
        $captcha_type = !empty($button['countdown_captcha_type']) ? $button['countdown_captcha_type'] : 'default';
        if ($captcha_type === 'default') {
            $captcha_type = get_option('farmfaucet_captcha_type', 'hcaptcha');
        }

        // Validate captcha type
        if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
            $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
        }
        // Button style attributes
        $button_color = $this->get_button_color($button['button_color']);
        $button_padding = $this->get_button_padding($button['button_size']);
        $border_radius = $button['border_shape'] === 'rounded' ? '20px' : '4px';

        // Button URL - either redirect URL or faucet page
        $button_url = !empty($button['redirect_url']) ? esc_url($button['redirect_url']) : '#';

        // Make sure the URL is properly formatted
        if ($button_url !== '#' && strpos($button_url, 'http') !== 0) {
            $button_url = 'https://' . ltrim($button_url, '/');
        }

        // Generate unique ID for this countdown
        $countdown_id = 'farmfaucet-countdown-' . uniqid();
        $button_id = 'farmfaucet-button-' . uniqid();

        // Start building output
        $output = '<div class="farmfaucet-countdown-container" data-faucet-id="' . esc_attr($faucet['id']) . '" data-button-id="' . esc_attr($button['id']) . '">';

        // Countdown timer container
        $output .= '<div class="farmfaucet-countdown-timer-container" id="' . esc_attr($countdown_id) . '-container">';

        // If click activation is enabled, show pre-click message
        if ($countdown_click_activation) {
            $output .= '<div class="farmfaucet-countdown-pre-click">';
            $output .= '<span class="farmfaucet-countdown-message">' . esc_html($countdown_pre_click_message) . '</span>';
            $output .= '</div>';
        } else {
            // Otherwise show the countdown immediately
            $output .= '<div class="farmfaucet-countdown-active">';
            $output .= '<span class="farmfaucet-countdown-message">' . esc_html($countdown_message) . '</span> ';
            $output .= '<span class="farmfaucet-countdown-timer" id="' . esc_attr($countdown_id) . '">' . gmdate('i:s', $countdown_seconds) . '</span>';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Button container (initially hidden)
        $output .= '<div class="farmfaucet-button-wrapper" id="' . esc_attr($button_id) . '-wrapper" style="display: none;">';

        // If captcha is enabled, show captcha first
        if ($captcha_enabled) {
            // Generate unique ID for captcha
            $captcha_id = 'farmfaucet-captcha-' . uniqid();

            // Get captcha site key based on type
            $captcha_sitekey = '';
            if ($captcha_type === 'hcaptcha') {
                $captcha_sitekey = get_option('farmfaucet_hcaptcha_sitekey', '');
            } else if ($captcha_type === 'recaptcha') {
                $captcha_sitekey = get_option('farmfaucet_recaptcha_sitekey', '');
            } else if ($captcha_type === 'turnstile') {
                $captcha_sitekey = get_option('farmfaucet_turnstile_sitekey', '');
            }

            // Captcha container
            $output .= '<div class="farmfaucet-captcha-wrapper" id="' . esc_attr($captcha_id) . '-wrapper">';
            $output .= '<div class="farmfaucet-captcha-message">' . esc_html__('Please complete the captcha to continue', 'farmfaucet') . '</div>';

            // Render the appropriate captcha
            if ($captcha_type === 'hcaptcha') {
                $output .= '<div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center; margin: 15px 0;">';
                $output .= '<div id="' . esc_attr($captcha_id) . '" class="h-captcha farmfaucet-captcha" data-sitekey="' . esc_attr($captcha_sitekey) . '" data-callback="farmfaucetCountdownCaptchaCallback" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                $output .= '</div>';
            } else if ($captcha_type === 'recaptcha') {
                $output .= '<div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center; margin: 15px 0;">';
                $output .= '<div id="' . esc_attr($captcha_id) . '" class="g-recaptcha farmfaucet-captcha" data-sitekey="' . esc_attr($captcha_sitekey) . '" data-callback="farmfaucetCountdownCaptchaCallback" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                $output .= '</div>';
            } else if ($captcha_type === 'turnstile') {
                $output .= '<div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center; margin: 15px 0;">';
                $output .= '<div id="' . esc_attr($captcha_id) . '" class="cf-turnstile farmfaucet-captcha" data-sitekey="' . esc_attr($captcha_sitekey) . '" data-callback="farmfaucetCountdownCaptchaCallback" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                $output .= '</div>';
            }

            $output .= '</div>';

            // Main button (initially hidden)
            $output .= '<div class="farmfaucet-main-button-wrapper" id="' . esc_attr($button_id) . '-main-wrapper" style="display: none;">';
            $output .= '<a href="' . esc_url($button_url) . '" class="farmfaucet-button" id="' . esc_attr($button_id) . '" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">';
            $output .= esc_html($button['button_text']);
            $output .= '</a>';
            $output .= '</div>';
        } else {
            // No captcha, just show the button
            $output .= '<a href="' . esc_url($button_url) . '" class="farmfaucet-button" id="' . esc_attr($button_id) . '" style="background-color: ' . esc_attr($button_color) . '; padding: ' . esc_attr($button_padding) . '; border-radius: ' . esc_attr($border_radius) . ';">';
            $output .= esc_html($button['button_text']);
            $output .= '</a>';
        }

        $output .= '</div>';

        // Add JavaScript for countdown functionality
        $button_id_js = htmlspecialchars($button['id'], ENT_QUOTES, 'UTF-8');
        $faucet_id_js = htmlspecialchars($faucet['id'], ENT_QUOTES, 'UTF-8');
        $countdown_id_js = htmlspecialchars($countdown_id, ENT_QUOTES, 'UTF-8');
        $button_id_js_element = htmlspecialchars($button_id, ENT_QUOTES, 'UTF-8');
        $countdown_message_js = htmlspecialchars($countdown_message, ENT_QUOTES, 'UTF-8');
        $click_activation = $countdown_click_activation ? 'true' : 'false';

        // Add standby mode JavaScript
        $standby_js = $standby_enabled ? 'true' : 'false';

        // Add captcha flag to JavaScript
        $captcha_js = $captcha_enabled ? 'true' : 'false';
        $captcha_type_js = json_encode($captcha_type);

        $output .= '<script>
            // Global callback function for captcha
            window.farmfaucetCountdownCaptchaCallback = function(token) {
                console.log("Captcha callback triggered with token: " + (token ? token.substring(0, 10) + "..." : "empty"));

                // Find the main button wrapper
                var mainButtonWrapper = document.getElementById("' . $button_id_js_element . '-main-wrapper");
                if (mainButtonWrapper) {
                    // Hide captcha, show main button
                    var captchaWrapper = mainButtonWrapper.previousElementSibling;
                    if (captchaWrapper && captchaWrapper.classList.contains("farmfaucet-captcha-wrapper")) {
                        captchaWrapper.style.display = "none";
                    }
                    mainButtonWrapper.style.display = "block";

                    // Add animation class to button
                    var mainButton = document.getElementById("' . $button_id_js_element . '");
                    if (mainButton) {
                        mainButton.classList.add("farmfaucet-button-appear");
                    }
                }
            };

            document.addEventListener("DOMContentLoaded", function() {
                var countdownContainer = document.getElementById("' . $countdown_id_js . '-container");
                var buttonWrapper = document.getElementById("' . $button_id_js_element . '-wrapper");
                var countdownTimer = document.getElementById("' . $countdown_id_js . '");
                var initialCountdown = ' . intval($countdown_seconds) . ';
                var countdownSeconds = initialCountdown;
                var countdownActive = false;
                var countdownInterval;
                var storageKey = "farmfaucet_countdown_' . $button_id_js . '_' . $faucet_id_js . '";
                var standbyEnabled = ' . $standby_js . ';
                var captchaEnabled = ' . $captcha_js . ';
                var captchaType = ' . $captcha_type_js . ';
                var standbyOverlay = null;

                // Format time function (converts seconds to MM:SS format)
                function formatTime(seconds) {
                    var minutes = Math.floor(seconds / 60);
                    var remainingSeconds = seconds % 60;
                    return minutes.toString().padStart(2, "0") + ":" + remainingSeconds.toString().padStart(2, "0");
                }

                // Always reset countdown on page refresh
                localStorage.removeItem(storageKey);

                // Check if there\'s a saved countdown state
                function checkSavedState() {
                    try {
                        var savedState = localStorage.getItem(storageKey);
                        if (savedState) {
                            var state = JSON.parse(savedState);
                            var now = Math.floor(Date.now() / 1000);

                            // If the saved state is still valid
                            if (state.expiry > now) {
                                // Calculate remaining time
                                countdownSeconds = state.expiry - now;

                                // If countdown is complete, show the button
                                if (countdownSeconds <= 0) {
                                    showButton();
                                    return;
                                }

                                // Otherwise start the countdown with remaining time
                                startCountdown();
                                return;
                            } else {
                                // Expired state, remove it and restart countdown
                                localStorage.removeItem(storageKey);

                                // Reset countdown to initial value
                                countdownSeconds = initialCountdown;

                                // Start fresh countdown if not click activation
                                if (' . $click_activation . ' === false) {
                                    startCountdown();
                                }
                                return;
                            }
                        }
                    } catch (e) {
                        console.error("Error checking saved countdown state:", e);
                        localStorage.removeItem(storageKey);
                    }

                    // No valid saved state, start fresh
                    if (' . $click_activation . ' === false) {
                        startCountdown();
                    }
                }

                function showButton() {
                    // Hide countdown
                    countdownContainer.style.display = "none";

                    // Show button wrapper
                    buttonWrapper.style.display = "block";

                    // If captcha is enabled, show captcha first
                    if (captchaEnabled) {
                        // Find captcha wrapper
                        var captchaWrappers = buttonWrapper.querySelectorAll(".farmfaucet-captcha-wrapper");
                        if (captchaWrappers.length > 0) {
                            // Show captcha wrapper
                            captchaWrappers[0].style.display = "block";

                            // Initialize captcha if not already initialized
                            var captchaElements = captchaWrappers[0].querySelectorAll(".farmfaucet-captcha");
                            if (captchaElements.length > 0) {
                                var captchaElement = captchaElements[0];

                                // Try to render captcha based on type
                                try {
                                    if (captchaType === "hcaptcha" && typeof hcaptcha !== "undefined") {
                                        hcaptcha.render(captchaElement.id, {
                                            sitekey: captchaElement.getAttribute("data-sitekey"),
                                            callback: window.farmfaucetCountdownCaptchaCallback
                                        });
                                    } else if (captchaType === "recaptcha" && typeof grecaptcha !== "undefined") {
                                        grecaptcha.render(captchaElement.id, {
                                            sitekey: captchaElement.getAttribute("data-sitekey"),
                                            callback: window.farmfaucetCountdownCaptchaCallback
                                        });
                                    } else if (captchaType === "turnstile" && typeof turnstile !== "undefined") {
                                        turnstile.render(captchaElement.id, {
                                            sitekey: captchaElement.getAttribute("data-sitekey"),
                                            callback: window.farmfaucetCountdownCaptchaCallback
                                        });
                                    }
                                } catch (e) {
                                    console.error("Error rendering captcha:", e);
                                }
                            }
                        }
                    } else {
                        // No captcha, just show the button with animation
                        var button = document.getElementById("' . $button_id_js_element . '");
                        if (button) {
                            button.classList.add("farmfaucet-button-appear");
                        }
                    }

                    // Clear saved state
                    localStorage.removeItem(storageKey);
                }

                // Create standby overlay function
                function createStandbyOverlay() {
                    if (!standbyEnabled) return;

                    // Calculate half time for standby mode (minimum 5 seconds)
                    var standbyDuration = Math.max(5, Math.floor(initialCountdown / 2));
                    var standbyEndTime = Math.floor(Date.now() / 1000) + standbyDuration;

                    // Create overlay element
                    standbyOverlay = document.createElement("div");
                    standbyOverlay.className = "farmfaucet-standby-overlay";

                    // Create message container
                    var messageContainer = document.createElement("div");
                    messageContainer.className = "farmfaucet-standby-message";

                    // Create heading
                    var heading = document.createElement("h3");
                    heading.textContent = "Please Wait";
                    messageContainer.appendChild(heading);

                    // Create cup animation container
                    var cupContainer = document.createElement("div");
                    cupContainer.className = "farmfaucet-cup-container";

                    // Create cup element
                    var cup = document.createElement("div");
                    cup.className = "farmfaucet-cup";

                    // Create juice element
                    var juice = document.createElement("div");
                    juice.className = "farmfaucet-juice";
                    juice.style.height = "0%";
                    cup.appendChild(juice);

                    // Add cup to container
                    cupContainer.appendChild(cup);
                    messageContainer.appendChild(cupContainer);

                    // Create timer display
                    var timerDisplay = document.createElement("div");
                    timerDisplay.className = "farmfaucet-standby-timer";
                    timerDisplay.id = "farmfaucet-standby-timer-" + Math.random().toString(36).substr(2, 9);
                    timerDisplay.textContent = formatTime(standbyDuration);
                    messageContainer.appendChild(timerDisplay);

                    // Create message
                    var message = document.createElement("p");
                    message.textContent = "The page is locked until the countdown completes";
                    messageContainer.appendChild(message);

                    // Add message container to overlay
                    standbyOverlay.appendChild(messageContainer);

                    // Add to body
                    document.body.appendChild(standbyOverlay);

                    // Prevent scrolling
                    document.body.style.overflow = "hidden";
                    document.body.classList.add("farmfaucet-standby-active");

                    // Add event listeners to block all interactions
                    standbyOverlay.addEventListener("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }, true);

                    standbyOverlay.addEventListener("touchstart", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }, true);

                    standbyOverlay.addEventListener("mousedown", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }, true);

                    standbyOverlay.addEventListener("keydown", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }, true);

                    // Update timer display and juice animation
                    var timerElement = document.getElementById(timerDisplay.id);
                    var juiceElement = standbyOverlay.querySelector(".farmfaucet-juice");

                    if (timerElement) {
                        var remainingStandbyTime = standbyDuration;
                        var standbyInterval = setInterval(function() {
                            remainingStandbyTime--;

                            // Update timer text
                            if (timerElement) {
                                timerElement.textContent = formatTime(remainingStandbyTime);
                            }

                            // Update juice height
                            if (juiceElement) {
                                var percentComplete = 100 - (remainingStandbyTime / standbyDuration * 100);
                                juiceElement.style.height = percentComplete + "%";
                            }

                            // Check if standby time is complete
                            if (remainingStandbyTime <= 0) {
                                clearInterval(standbyInterval);
                                removeStandbyOverlay();
                            }
                        }, 1000);
                    }
                }

                // Remove standby overlay function
                function removeStandbyOverlay() {
                    if (!standbyEnabled || !standbyOverlay) return;

                    // Remove overlay
                    if (standbyOverlay && standbyOverlay.parentNode) {
                        standbyOverlay.parentNode.removeChild(standbyOverlay);
                    }

                    // Restore scrolling
                    document.body.style.overflow = "";
                    document.body.classList.remove("farmfaucet-standby-active");

                    standbyOverlay = null;
                }

                function startCountdown() {
                    if (countdownActive) return;
                    countdownActive = true;

                    // Create standby overlay if enabled
                    createStandbyOverlay();

                    // Save the countdown state
                    var now = Math.floor(Date.now() / 1000);
                    var expiry = now + countdownSeconds;
                    try {
                        localStorage.setItem(storageKey, JSON.stringify({
                            expiry: expiry,
                            buttonId: "' . $button_id_js . '",
                            faucetId: "' . $faucet_id_js . '"
                        }));
                    } catch (e) {
                        console.error("Error saving countdown state:", e);
                    }

                    // If click activation is enabled, update the display
                    if (' . $click_activation . ') {
                        countdownContainer.innerHTML = "<div class=\"farmfaucet-countdown-active\"><span class=\"farmfaucet-countdown-message\">' . $countdown_message_js . '</span> <span class=\"farmfaucet-countdown-timer\" id=\"' . $countdown_id_js . '\">" + formatTime(countdownSeconds) + "</span></div>";
                        countdownTimer = document.getElementById("' . $countdown_id_js . '");
                    } else if (!countdownTimer) {
                        // Make sure we have a valid reference to the countdown timer element
                        countdownTimer = document.getElementById("' . $countdown_id_js . '");
                        if (!countdownTimer) {
                            console.error("Countdown timer element not found");
                            return;
                        }
                    }

                    // Update the timer display immediately
                    countdownTimer.textContent = formatTime(countdownSeconds);

                    countdownInterval = setInterval(function() {
                        countdownSeconds--;

                        // Update the saved state every 5 seconds
                        if (countdownSeconds % 5 === 0) {
                            var now = Math.floor(Date.now() / 1000);
                            var expiry = now + countdownSeconds;
                            try {
                                localStorage.setItem(storageKey, JSON.stringify({
                                    expiry: expiry,
                                    buttonId: "' . $button_id_js . '",
                                    faucetId: "' . $faucet_id_js . '"
                                }));
                            } catch (e) {
                                console.error("Error updating countdown state:", e);
                            }
                        }

                        if (countdownSeconds <= 0) {
                            // Stop the countdown
                            clearInterval(countdownInterval);

                            // Remove standby overlay if enabled
                            removeStandbyOverlay();

                            showButton();
                        } else {
                            // Update the countdown display
                            countdownTimer.textContent = formatTime(countdownSeconds);
                        }
                    }, 1000);
                }

                // Initialize the countdown
                checkSavedState();

                function formatTime(seconds) {
                    var minutes = Math.floor(seconds / 60);
                    var secs = seconds % 60;

                    if (minutes > 0) {
                        return (minutes < 10 ? "0" + minutes : minutes) + ":" + (secs < 10 ? "0" + secs : secs);
                    } else {
                        return "00:" + (secs < 10 ? "0" + secs : secs);
                    }
                }

                // Check for saved state first
                checkSavedState();

                // If click activation is enabled and no saved state triggered a countdown, set up the click handler
                if (' . ($countdown_click_activation ? 'true' : 'false') . ' && !countdownActive) {
                    var clickElement = null;
                    var clickSelector = "' . esc_html($countdown_click_element_id) . '";

                    // First try to get the element by ID
                    if (clickSelector) {
                        clickElement = document.getElementById(clickSelector);

                        // If not found by ID, try as a class selector
                        if (!clickElement) {
                            var elements = document.getElementsByClassName(clickSelector.replace(/^\./, ""));
                            if (elements.length > 0) {
                                clickElement = elements[0];
                            }
                        }

                        // If still not found, try as a CSS selector
                        if (!clickElement) {
                            try {
                                var elements = document.querySelectorAll(clickSelector);
                                if (elements.length > 0) {
                                    clickElement = elements[0];
                                }
                            } catch (e) {
                                console.error("Invalid CSS selector:", clickSelector);
                            }
                        }
                    }

                    // If element exists, use it, otherwise use the countdown container
                    if (clickElement) {
                        console.log("Found click element:", clickElement);
                        clickElement.style.cursor = "pointer";
                        clickElement.addEventListener("click", function(e) {
                            console.log("Click element clicked, starting countdown");
                            if (e.target.tagName.toLowerCase() === "a") {
                                e.preventDefault();
                            }
                            startCountdown();
                        });
                    } else {
                        console.log("Click element not found, using countdown container");
                        // Fallback if the specified element doesn\'t exist
                        countdownContainer.style.cursor = "pointer";
                        countdownContainer.addEventListener("click", function(e) {
                            e.preventDefault();
                            startCountdown();
                        });
                    }
                }
            });
        </script>';

        // Add CSS for the countdown button
        $output .= '<style>
            .farmfaucet-countdown-container {
                font-family: inherit;
                color: inherit;
                margin: 15px 0;
            }

            .farmfaucet-countdown-timer-container {
                background-color: #f5f5f5;
                border-radius: 8px;
                padding: 12px 15px;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 50px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                width: 100%;
            }

            .farmfaucet-countdown-message {
                font-weight: 500;
                margin-right: 8px;
            }

            .farmfaucet-countdown-timer {
                font-weight: bold;
                font-family: monospace;
                font-size: 1.2em;
                color: #e91e63;
            }

            .farmfaucet-countdown-pre-click {
                cursor: pointer;
                text-align: center;
                width: 100%;
            }

            .farmfaucet-countdown-active {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
            }

            .farmfaucet-button-appear {
                animation: farmfaucetButtonAppear 0.5s ease-in-out;
            }

            @keyframes farmfaucetButtonAppear {
                0% {
                    opacity: 0;
                    transform: scale(0.8);
                }
                70% {
                    opacity: 1;
                    transform: scale(1.05);
                }
                100% {
                    transform: scale(1);
                }
            }
        </style>';

        $output .= '</div>'; // End countdown container

        return $output;
    }
}
