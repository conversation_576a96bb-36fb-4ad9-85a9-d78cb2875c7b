<?php

/**
 * Farmfaucet Installer Class
 *
 * Handles installation and upgrade processes for the Farmfaucet plugin
 */
class Farmfaucet_Installer
{
    /**
     * Check if the plugin is properly installed
     *
     * @return bool True if properly installed, false otherwise
     */
    public static function is_properly_installed()
    {
        global $wpdb;

        // Check if required tables exist
        $tables = [
            $wpdb->prefix . 'farmfaucet_logs',
            $wpdb->prefix . 'farmfaucet_faucets',
            $wpdb->prefix . 'farmfaucet_buttons',
            $wpdb->prefix . 'farmfaucet_users',
            $wpdb->prefix . 'farmfaucet_leaderboard',
            $wpdb->prefix . 'farmfaucet_claims'
        ];

        $missing_tables = false;

        foreach ($tables as $table) {
            try {
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
                if (!$table_exists) {
                    $missing_tables = true;
                    error_log("Farmfaucet missing table: {$table}");
                }
            } catch (Exception $e) {
                error_log('Farmfaucet installation check error: ' . $e->getMessage());
                $missing_tables = true;
            }
        }

        // Check if required options exist if WordPress functions are available
        $missing_options = false;
        if (function_exists('get_option')) {
            $options = [
                'farmfaucet_captcha_type',
                'farmfaucet_currency',
                'farmfaucet_cooldown'
            ];

            foreach ($options as $option) {
                if (get_option($option) === false) {
                    $missing_options = true;
                    error_log("Farmfaucet missing option: {$option}");
                }
            }
        }

        return !$missing_tables && !$missing_options;
    }

    /**
     * Install the plugin
     *
     * @return void
     */
    public static function install()
    {
        try {
            // Trigger the activation hook manually
            Farmfaucet_Security::activate_plugin();
        } catch (Exception $e) {
            error_log('Farmfaucet installation error: ' . $e->getMessage());
        }
    }

    /**
     * Check and install if needed
     *
     * @return void
     */
    public static function check_and_install()
    {
        try {
            if (!self::is_properly_installed()) {
                self::install();
            }
        } catch (Exception $e) {
            error_log('Farmfaucet installation check error: ' . $e->getMessage());
        }
    }
}
