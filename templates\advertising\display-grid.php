<?php
/**
 * Template for displaying advertisements in grid layout
 *
 * @package Farmfaucet
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get current user ID
$current_user_id = get_current_user_id();

// Get max votes per day
$max_votes = get_option('farmfaucet_ad_max_votes', 3);

// Count user votes today
$user_votes_today = is_user_logged_in() ? $this->count_user_votes_today($current_user_id) : 0;

// Check if user can vote
$can_vote = is_user_logged_in() && $user_votes_today < $max_votes;

// Get vote reward
$vote_reward = get_option('farmfaucet_ad_vote_reward', 0.1);

// Get columns
$columns = intval($atts['columns']);
if ($columns < 1 || $columns > 4) {
    $columns = 3;
}
?>

<div class="farmfaucet-ads-container">
    <?php if ($can_vote) : ?>
        <div class="farmfaucet-ads-info">
            <p><?php printf(esc_html__('You have %1$d/%2$d votes remaining today. Earn %3$s for each vote!', 'farmfaucet'), 
                $max_votes - $user_votes_today, 
                $max_votes,
                '<strong>' . esc_html($vote_reward) . '</strong>'
            ); ?></p>
        </div>
    <?php elseif (is_user_logged_in()) : ?>
        <div class="farmfaucet-ads-info">
            <p><?php esc_html_e('You have used all your votes for today. Come back tomorrow!', 'farmfaucet'); ?></p>
        </div>
    <?php else : ?>
        <div class="farmfaucet-ads-info">
            <p><?php esc_html_e('Log in to vote on advertisements and earn rewards!', 'farmfaucet'); ?></p>
        </div>
    <?php endif; ?>

    <div class="farmfaucet-ads-grid columns-<?php echo esc_attr($columns); ?>">
        <?php foreach ($ads as $ad) : 
            // Check if user has voted for this ad
            $has_voted = is_user_logged_in() ? $this->has_user_voted($ad['id'], $current_user_id) : false;
        ?>
            <div class="farmfaucet-ad-card" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                <?php if ($atts['show_image'] && !empty($ad['image_url'])) : ?>
                    <div class="ad-image">
                        <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow">
                            <img src="<?php echo esc_url($ad['image_url']); ?>" alt="<?php echo esc_attr($ad['title']); ?>">
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="ad-content">
                    <h3 class="ad-title">
                        <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow">
                            <?php echo esc_html($ad['title']); ?>
                        </a>
                    </h3>
                    
                    <?php if ($atts['show_description']) : 
                        $description = $ad['description'];
                        $max_length = intval($atts['description_length']);
                        
                        if (strlen($description) > $max_length) {
                            $description = substr($description, 0, $max_length) . '...';
                        }
                    ?>
                        <div class="ad-description">
                            <?php echo wp_kses_post($description); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="ad-meta">
                        <?php if ($atts['show_votes']) : ?>
                            <div class="ad-votes">
                                <span class="votes-count"><?php echo esc_html($ad['votes']); ?></span>
                                <span class="votes-label"><?php echo _n('vote', 'votes', $ad['votes'], 'farmfaucet'); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="ad-actions">
                            <a href="<?php echo esc_url($ad['url']); ?>" target="_blank" rel="nofollow" class="ad-visit-button">
                                <?php esc_html_e('Visit', 'farmfaucet'); ?>
                            </a>
                            
                            <?php if ($can_vote && !$has_voted) : ?>
                                <button class="ad-vote-button" data-ad-id="<?php echo esc_attr($ad['id']); ?>">
                                    <?php esc_html_e('Vote', 'farmfaucet'); ?>
                                </button>
                            <?php elseif ($has_voted) : ?>
                                <span class="ad-voted"><?php esc_html_e('Voted', 'farmfaucet'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
