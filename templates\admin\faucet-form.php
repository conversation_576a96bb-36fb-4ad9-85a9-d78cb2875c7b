<?php
if (!defined('ABSPATH')) exit;
?>
<form id="faucet-form" class="farmfaucet-form">
    <input type="hidden" id="faucet-id" name="faucet_id" value="0">

    <h3><?php esc_html_e('Basic Settings', 'farmfaucet'); ?></h3>

    <div class="form-field">
        <label for="faucet-name"><?php esc_html_e('Faucet Name', 'farmfaucet'); ?> <span class="required">*</span></label>
        <input type="text" id="faucet-name" name="name" required>
        <p class="description"><?php esc_html_e('Enter a name for your faucet.', 'farmfaucet'); ?></p>
    </div>

    <div class="form-field">
        <label for="faucet-shortcode"><?php esc_html_e('Shortcode', 'farmfaucet'); ?> <span class="required">*</span></label>
        <input type="text" id="faucet-shortcode" name="shortcode" required>
        <p class="description"><?php esc_html_e('Enter a unique shortcode identifier (no spaces or special characters).', 'farmfaucet'); ?></p>
    </div>

    <div class="form-field">
        <label for="faucet-type"><?php esc_html_e('Faucet Type', 'farmfaucet'); ?></label>
        <select id="faucet-type" name="faucet_type">
            <option value="stage"><?php esc_html_e('Stage Faucet (Default)', 'farmfaucet'); ?></option>
            <option value="dummy"><?php esc_html_e('Dummy Faucet', 'farmfaucet'); ?></option>
            <option value="withdrawal"><?php esc_html_e('Withdrawal Faucet', 'farmfaucet'); ?></option>
            <option value="conversion"><?php esc_html_e('Conversion Faucet', 'farmfaucet'); ?></option>
        </select>
        <p class="description"><?php esc_html_e('Select the type of faucet.', 'farmfaucet'); ?></p>
    </div>

    <div class="form-field">
        <label for="faucet-cooldown"><?php esc_html_e('Cooldown (seconds)', 'farmfaucet'); ?></label>
        <input type="number" id="faucet-cooldown" name="cooldown" min="0" value="3600">
        <p class="description"><?php esc_html_e('Time in seconds before a user can claim again.', 'farmfaucet'); ?></p>
    </div>

    <div class="form-field">
        <label for="faucet-api-key"><?php esc_html_e('API Key', 'farmfaucet'); ?></label>
        <input type="text" id="faucet-api-key" name="api_key">
        <p class="description"><?php esc_html_e('Optional API key for this specific faucet.', 'farmfaucet'); ?></p>
    </div>

    <h3><?php esc_html_e('Faucet Type Settings', 'farmfaucet'); ?></h3>

    <!-- Fields specific to stage faucets -->
    <div class="faucet-type-fields stage-faucet-fields">
        <div class="form-field">
            <label for="faucet-currency"><?php esc_html_e('Currency', 'farmfaucet'); ?></label>
            <select id="faucet-currency" name="currency">
                <option value="BTC"><?php esc_html_e('Bitcoin (BTC)', 'farmfaucet'); ?></option>
                <option value="ETH"><?php esc_html_e('Ethereum (ETH)', 'farmfaucet'); ?></option>
                <option value="LTC" selected><?php esc_html_e('Litecoin (LTC)', 'farmfaucet'); ?></option>
                <option value="DOGE"><?php esc_html_e('Dogecoin (DOGE)', 'farmfaucet'); ?></option>
                <option value="BCH"><?php esc_html_e('Bitcoin Cash (BCH)', 'farmfaucet'); ?></option>
                <option value="TRX"><?php esc_html_e('Tron (TRX)', 'farmfaucet'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('Select the cryptocurrency for this faucet.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="faucet-amount"><?php esc_html_e('Reward Amount', 'farmfaucet'); ?></label>
            <input type="text" id="faucet-amount" name="amount" value="0.001">
            <p class="description"><?php esc_html_e('Amount of cryptocurrency to reward per claim.', 'farmfaucet'); ?></p>
        </div>
    </div>

    <!-- Fields specific to dummy faucets -->
    <div class="faucet-type-fields dummy-faucet-fields" style="display:none;">
        <div class="form-field">
            <label for="dummy-currency-id"><?php esc_html_e('Created Currency', 'farmfaucet'); ?></label>
            <select id="dummy-currency-id" name="currency_id">
                <?php
                // Get created currencies
                if (class_exists('Farmfaucet_Currency_Maker')) {
                    $currency_maker = Farmfaucet_Currency_Maker::init();
                    $currencies = $currency_maker->get_currencies(true);

                    if (!empty($currencies)) {
                        foreach ($currencies as $currency) {
                            echo '<option value="' . esc_attr($currency['id']) . '">' .
                                esc_html($currency['name']) . ' (' . esc_html($currency['symbol']) . ')</option>';
                        }
                    } else {
                        echo '<option value="0" disabled>' . esc_html__('No currencies created yet', 'farmfaucet') . '</option>';
                    }
                } else {
                    echo '<option value="0" disabled>' . esc_html__('Currency Maker not available', 'farmfaucet') . '</option>';
                }
                ?>
            </select>
            <p class="description"><?php esc_html_e('Select the created currency to credit to user balance.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="dummy-amount"><?php esc_html_e('Reward Amount', 'farmfaucet'); ?></label>
            <input type="text" id="dummy-amount" name="amount" value="1">
            <p class="description"><?php esc_html_e('Amount of created currency to reward per claim.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="dummy-view-style"><?php esc_html_e('View Style', 'farmfaucet'); ?></label>
            <select id="dummy-view-style" name="view_style">
                <option value="default"><?php esc_html_e('Default', 'farmfaucet'); ?></option>
                <option value="card"><?php esc_html_e('Card View', 'farmfaucet'); ?></option>
                <option value="compact"><?php esc_html_e('Compact View', 'farmfaucet'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('Select the display style for this faucet.', 'farmfaucet'); ?></p>
        </div>
    </div>

    <!-- Fields specific to withdrawal faucets -->
    <div class="faucet-type-fields withdrawal-faucet-fields" style="display:none;">
        <div class="form-field">
            <label for="withdrawal-currency-id"><?php esc_html_e('Created Currency to Withdraw', 'farmfaucet'); ?></label>
            <select id="withdrawal-currency-id" name="currency_id" class="farmfaucet-select">
                <?php
                // Get created currencies
                if (class_exists('Farmfaucet_Currency_Maker')) {
                    $currency_maker = Farmfaucet_Currency_Maker::init();
                    $currencies = $currency_maker->get_currencies(true);

                    if (!empty($currencies)) {
                        foreach ($currencies as $currency) {
                            $currency_type = isset($currency['currency_type']) ? $currency['currency_type'] : 'earnings';
                            $type_label = $currency_type === 'advertisement' ? ' (Advertisement)' : ' (Earnings)';
                            echo '<option value="' . esc_attr($currency['id']) . '" data-type="' . esc_attr($currency_type) . '">' .
                                esc_html($currency['name']) . ' (' . esc_html($currency['symbol']) . ')' . esc_html($type_label) . '</option>';
                        }
                    } else {
                        echo '<option value="0" disabled>' . esc_html__('No currencies created yet', 'farmfaucet') . '</option>';
                    }
                } else {
                    echo '<option value="0" disabled>' . esc_html__('Currency Maker not available', 'farmfaucet') . '</option>';
                }
                ?>
            </select>
            <p class="description"><?php esc_html_e('Select the created currency that users can withdraw.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="min-withdrawal"><?php esc_html_e('Minimum Withdrawal', 'farmfaucet'); ?></label>
            <input type="text" id="min-withdrawal" name="min_withdrawal" value="1" class="farmfaucet-input">
            <p class="description"><?php esc_html_e('Minimum amount users must withdraw.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label><?php esc_html_e('Available Cryptocurrencies for Withdrawal', 'farmfaucet'); ?></label>
            <div class="vertical-checkbox-group">
                <label><input type="checkbox" name="available_currencies[]" value="BTC" checked> <?php esc_html_e('Bitcoin (BTC)', 'farmfaucet'); ?></label>
                <label><input type="checkbox" name="available_currencies[]" value="ETH" checked> <?php esc_html_e('Ethereum (ETH)', 'farmfaucet'); ?></label>
                <label><input type="checkbox" name="available_currencies[]" value="LTC" checked> <?php esc_html_e('Litecoin (LTC)', 'farmfaucet'); ?></label>
                <label><input type="checkbox" name="available_currencies[]" value="DOGE" checked> <?php esc_html_e('Dogecoin (DOGE)', 'farmfaucet'); ?></label>
                <label><input type="checkbox" name="available_currencies[]" value="BCH" checked> <?php esc_html_e('Bitcoin Cash (BCH)', 'farmfaucet'); ?></label>
                <label><input type="checkbox" name="available_currencies[]" value="TRX" checked> <?php esc_html_e('Tron (TRX)', 'farmfaucet'); ?></label>
            </div>
            <p class="description"><?php esc_html_e('Select which cryptocurrencies users can withdraw to.', 'farmfaucet'); ?></p>
        </div>
    </div>

    <!-- Fields specific to conversion faucets -->
    <div class="faucet-type-fields conversion-faucet-fields" style="display:none;">
        <div class="form-field">
            <label for="conversion-source-currency-id"><?php esc_html_e('Source Currency (From)', 'farmfaucet'); ?></label>
            <select id="conversion-source-currency-id" name="currency_id" class="farmfaucet-select">
                <?php
                // Get created currencies
                if (class_exists('Farmfaucet_Currency_Maker')) {
                    $currency_maker = Farmfaucet_Currency_Maker::init();
                    $currencies = $currency_maker->get_currencies(true);

                    if (!empty($currencies)) {
                        foreach ($currencies as $currency) {
                            $currency_type = isset($currency['currency_type']) ? $currency['currency_type'] : 'earnings';
                            $type_label = $currency_type === 'advertisement' ? ' (Advertisement)' : ' (Earnings)';
                            echo '<option value="' . esc_attr($currency['id']) . '" data-type="' . esc_attr($currency_type) . '">' .
                                esc_html($currency['name']) . ' (' . esc_html($currency['symbol']) . ')' . esc_html($type_label) . '</option>';
                        }
                    } else {
                        echo '<option value="0" disabled>' . esc_html__('No currencies created yet', 'farmfaucet') . '</option>';
                    }
                } else {
                    echo '<option value="0" disabled>' . esc_html__('Currency Maker not available', 'farmfaucet') . '</option>';
                }
                ?>
            </select>
            <p class="description"><?php esc_html_e('Select the source currency that users will convert from.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label for="min-conversion"><?php esc_html_e('Minimum Conversion Amount', 'farmfaucet'); ?></label>
            <input type="text" id="min-conversion" name="min_withdrawal" value="1" class="farmfaucet-input">
            <p class="description"><?php esc_html_e('Minimum amount users must convert.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label><?php esc_html_e('Available Currencies for Conversion', 'farmfaucet'); ?></label>
            <div class="vertical-checkbox-group conversion-currencies">
                <?php
                // Get created currencies for conversion
                if (class_exists('Farmfaucet_Currency_Maker')) {
                    $currency_maker = Farmfaucet_Currency_Maker::init();
                    $currencies = $currency_maker->get_currencies(true);

                    if (!empty($currencies)) {
                        foreach ($currencies as $currency) {
                            $currency_type = isset($currency['currency_type']) ? $currency['currency_type'] : 'earnings';
                            $type_label = $currency_type === 'advertisement' ? ' (Advertisement)' : ' (Earnings)';
                            $is_ad = $currency_type === 'advertisement';
                            echo '<label class="' . ($is_ad ? 'ad-currency' : '') . '">';
                            echo '<input type="checkbox" name="conversion_currencies[]" value="' . esc_attr($currency['id']) . '" ' . ($is_ad ? 'checked' : '') . '> ';
                            echo esc_html($currency['name']) . ' (' . esc_html($currency['symbol']) . ')' . esc_html($type_label);
                            echo '</label>';
                        }
                    } else {
                        echo '<p>' . esc_html__('No currencies available for conversion', 'farmfaucet') . '</p>';
                    }
                } else {
                    echo '<p>' . esc_html__('Currency Maker not available', 'farmfaucet') . '</p>';
                }
                ?>
            </div>
            <p class="description"><?php esc_html_e('Select which currencies users can convert to.', 'farmfaucet'); ?></p>
        </div>

        <div class="form-field">
            <label>
                <input type="checkbox" id="ads-only-conversion" name="ads_only_conversion" value="1" checked>
                <?php esc_html_e('Advertisement Balance Only', 'farmfaucet'); ?>
            </label>
            <p class="description"><?php esc_html_e('If checked, users can only convert to advertisement balance currencies.', 'farmfaucet'); ?></p>
        </div>
    </div>

    <h3><?php esc_html_e('Security Settings', 'farmfaucet'); ?></h3>

    <!-- Captcha settings -->
    <div class="form-field">
        <label><?php esc_html_e('Captcha Type', 'farmfaucet'); ?></label>
        <div class="captcha-options">
            <?php
            // Get global captcha settings - check both possible option names
            $global_hcaptcha_site_key = get_option('farmfaucet_hcaptcha_site_key', '');
            if (empty($global_hcaptcha_site_key)) {
                $global_hcaptcha_site_key = get_option('farmfaucet_hcaptcha_sitekey', '');
            }

            $global_recaptcha_site_key = get_option('farmfaucet_recaptcha_site_key', '');
            if (empty($global_recaptcha_site_key)) {
                $global_recaptcha_site_key = get_option('farmfaucet_recaptcha_sitekey', '');
            }

            $global_turnstile_site_key = get_option('farmfaucet_turnstile_site_key', '');
            if (empty($global_turnstile_site_key)) {
                $global_turnstile_site_key = get_option('farmfaucet_turnstile_sitekey', '');
            }

            // Determine which captchas are available
            $hcaptcha_available = !empty($global_hcaptcha_site_key);
            $recaptcha_available = !empty($global_recaptcha_site_key);
            $turnstile_available = !empty($global_turnstile_site_key);

            // Default to global setting
            $global_captcha_type = get_option('farmfaucet_captcha_type', 'hcaptcha');
            ?>

            <label class="<?php echo !$hcaptcha_available ? 'disabled' : ''; ?>">
                <input type="radio" name="captcha_type" value="hcaptcha" <?php echo ($global_captcha_type === 'hcaptcha') ? 'checked' : ''; ?> <?php echo !$hcaptcha_available ? 'disabled' : ''; ?>>
                <?php esc_html_e('hCaptcha', 'farmfaucet'); ?>
                <?php if (!$hcaptcha_available) : ?>
                    <span class="not-configured"><?php esc_html_e('(Not configured)', 'farmfaucet'); ?></span>
                <?php endif; ?>
            </label>

            <label class="<?php echo !$recaptcha_available ? 'disabled' : ''; ?>">
                <input type="radio" name="captcha_type" value="recaptcha" <?php echo ($global_captcha_type === 'recaptcha') ? 'checked' : ''; ?> <?php echo !$recaptcha_available ? 'disabled' : ''; ?>>
                <?php esc_html_e('reCAPTCHA', 'farmfaucet'); ?>
                <?php if (!$recaptcha_available) : ?>
                    <span class="not-configured"><?php esc_html_e('(Not configured)', 'farmfaucet'); ?></span>
                <?php endif; ?>
            </label>

            <label class="<?php echo !$turnstile_available ? 'disabled' : ''; ?>">
                <input type="radio" name="captcha_type" value="turnstile" <?php echo ($global_captcha_type === 'turnstile') ? 'checked' : ''; ?> <?php echo !$turnstile_available ? 'disabled' : ''; ?>>
                <?php esc_html_e('Cloudflare Turnstile', 'farmfaucet'); ?>
                <?php if (!$turnstile_available) : ?>
                    <span class="not-configured"><?php esc_html_e('(Not configured)', 'farmfaucet'); ?></span>
                <?php endif; ?>
            </label>
        </div>
        <p class="description"><?php esc_html_e('Select the captcha type for this faucet.', 'farmfaucet'); ?></p>
    </div>

    <!-- Faucet enabled/disabled toggle -->
    <div class="form-field">
        <label><input type="checkbox" id="faucet-is-enabled" name="is_enabled" value="1" checked> <?php esc_html_e('Enable Faucet', 'farmfaucet'); ?></label>
        <p class="description"><?php esc_html_e('If unchecked, the faucet will be disabled and show as unavailable.', 'farmfaucet'); ?></p>
    </div>

    <!-- Appearance Settings -->
    <h3><?php esc_html_e('Appearance Settings', 'farmfaucet'); ?></h3>

    <div class="appearance-settings-container">
        <!-- Primary Color Selection -->
        <div class="appearance-section">
            <h4><?php esc_html_e('Primary Color', 'farmfaucet'); ?></h4>
            <div class="form-field">
                <label for="faucet-color"><?php esc_html_e('Faucet Color Theme', 'farmfaucet'); ?></label>
                <div class="color-picker-container">
                    <input type="hidden" id="faucet-color" name="faucet_color" value="green">
                    <div class="color-circles">
                        <div class="color-circle active" data-color="green" data-hex="#4CAF50" style="background-color: #4CAF50;" title="Green"></div>
                        <div class="color-circle" data-color="blue" data-hex="#2196F3" style="background-color: #2196F3;" title="Blue"></div>
                        <div class="color-circle" data-color="red" data-hex="#f44336" style="background-color: #f44336;" title="Red"></div>
                        <div class="color-circle" data-color="purple" data-hex="#9c27b0" style="background-color: #9c27b0;" title="Purple"></div>
                        <div class="color-circle" data-color="orange" data-hex="#ff9800" style="background-color: #ff9800;" title="Orange"></div>
                        <div class="color-circle" data-color="teal" data-hex="#009688" style="background-color: #009688;" title="Teal"></div>
                    </div>
                </div>
                <p class="description"><?php esc_html_e('Select the primary color theme for this faucet.', 'farmfaucet'); ?></p>
            </div>
        </div>

        <!-- Custom Colors -->
        <div class="appearance-section">
            <h4><?php esc_html_e('Custom Colors', 'farmfaucet'); ?></h4>
            <div class="color-grid">
                <div class="form-field">
                    <label for="faucet-border-color"><?php esc_html_e('Border Color', 'farmfaucet'); ?></label>
                    <input type="color" id="faucet-border-color" name="border_color" value="#4CAF50">
                </div>
                <div class="form-field">
                    <label for="faucet-button-color"><?php esc_html_e('Button Color', 'farmfaucet'); ?></label>
                    <input type="color" id="faucet-button-color" name="button_color" value="#4CAF50">
                </div>
                <div class="form-field">
                    <label for="faucet-text-color"><?php esc_html_e('Text Color', 'farmfaucet'); ?></label>
                    <input type="color" id="faucet-text-color" name="text_color" value="#4CAF50">
                </div>
            </div>
        </div>

        <!-- Shape & Style -->
        <div class="appearance-section">
            <h4><?php esc_html_e('Shape & Style', 'farmfaucet'); ?></h4>
            <div class="style-grid">
                <div class="form-field">
                    <label for="faucet-border-radius"><?php esc_html_e('Border Radius', 'farmfaucet'); ?></label>
                    <select id="faucet-border-radius" name="border_radius">
                        <option value="0"><?php esc_html_e('Square (0px)', 'farmfaucet'); ?></option>
                        <option value="4px"><?php esc_html_e('Slightly Rounded (4px)', 'farmfaucet'); ?></option>
                        <option value="8px" selected><?php esc_html_e('Rounded (8px)', 'farmfaucet'); ?></option>
                        <option value="16px"><?php esc_html_e('Very Rounded (16px)', 'farmfaucet'); ?></option>
                        <option value="24px"><?php esc_html_e('Pill Shape (24px)', 'farmfaucet'); ?></option>
                        <option value="50%"><?php esc_html_e('Circular (50%)', 'farmfaucet'); ?></option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="faucet-text-shadow"><?php esc_html_e('Text Shadow', 'farmfaucet'); ?></label>
                    <select id="faucet-text-shadow" name="text_shadow">
                        <option value="none" selected><?php esc_html_e('None', 'farmfaucet'); ?></option>
                        <option value="1px 1px 2px rgba(0,0,0,0.3)"><?php esc_html_e('Light', 'farmfaucet'); ?></option>
                        <option value="2px 2px 4px rgba(0,0,0,0.5)"><?php esc_html_e('Medium', 'farmfaucet'); ?></option>
                        <option value="3px 3px 6px rgba(0,0,0,0.7)"><?php esc_html_e('Heavy', 'farmfaucet'); ?></option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Settings -->
    <div class="appearance-section">
        <h4><?php esc_html_e('Background Settings', 'farmfaucet'); ?></h4>

        <div class="form-field">
            <label>
                <input type="checkbox" id="faucet-transparent-bg" name="transparent_bg" value="1">
                <?php esc_html_e('Transparent Container Background', 'farmfaucet'); ?>
            </label>
            <p class="description"><?php esc_html_e('Make the main faucet container background transparent.', 'farmfaucet'); ?></p>
        </div>

        <div class="faucet-bg-options">
            <div class="form-field">
                <label><?php esc_html_e('Background Style', 'farmfaucet'); ?></label>
                <div class="radio-group">
                    <label><input type="radio" name="bg_style" value="solid" checked> <?php esc_html_e('Solid Color', 'farmfaucet'); ?></label>
                    <label><input type="radio" name="bg_style" value="gradient"> <?php esc_html_e('Gradient', 'farmfaucet'); ?></label>
                </div>
            </div>

            <div class="bg-solid-option">
                <div class="color-grid">
                    <div class="form-field">
                        <label for="faucet-bg-color"><?php esc_html_e('Container Background', 'farmfaucet'); ?></label>
                        <input type="color" id="faucet-bg-color" name="bg_color" value="#f8fff8">
                    </div>
                    <div class="form-field" id="form-bg-color-field">
                        <label for="faucet-form-bg-color"><?php esc_html_e('Form Background', 'farmfaucet'); ?></label>
                        <input type="color" id="faucet-form-bg-color" name="form_bg_color" value="#ffffff">
                    </div>
                </div>

                <div class="form-field">
                    <label>
                        <input type="checkbox" id="faucet-form-transparent" name="form_transparent" value="1">
                        <?php esc_html_e('Transparent Form Background', 'farmfaucet'); ?>
                    </label>
                    <p class="description"><?php esc_html_e('Make the form background completely transparent.', 'farmfaucet'); ?></p>
                </div>
            </div>

            <div class="bg-gradient-options" style="display:none;">
                <div class="color-grid">
                    <div class="form-field">
                        <label for="faucet-gradient-start"><?php esc_html_e('Gradient Start', 'farmfaucet'); ?></label>
                        <input type="color" id="faucet-gradient-start" name="bg_gradient_start" value="#f8fff8">
                    </div>
                    <div class="form-field">
                        <label for="faucet-gradient-end"><?php esc_html_e('Gradient End', 'farmfaucet'); ?></label>
                        <input type="color" id="faucet-gradient-end" name="bg_gradient_end" value="#e8f5e9">
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</form>