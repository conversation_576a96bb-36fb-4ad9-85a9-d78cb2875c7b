/**
 * Farm Faucet - Improved Faucet Form CSS
 */

/* Faucet Form */
.farmfaucet-form .form-field {
    margin-bottom: 15px;
}

.farmfaucet-form label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px !important;
}

.farmfaucet-form input[type="text"],
.farmfaucet-form input[type="number"],
.farmfaucet-form select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 14px !important;
}

.farmfaucet-form .description {
    font-size: 12px !important;
    color: #666;
    margin-top: 3px;
}

.farmfaucet-form .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.farmfaucet-form .checkbox-group label {
    font-weight: normal;
    display: flex;
    align-items: center;
    margin-right: 15px;
    font-size: 13px;
}

.farmfaucet-form .checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

/* Vertical Checkbox Group */
.farmfaucet-form .vertical-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.farmfaucet-form .vertical-checkbox-group label {
    font-weight: normal;
    display: flex;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #eee;
    margin: 0;
    font-size: 13px;
}

.farmfaucet-form .vertical-checkbox-group label:last-child {
    border-bottom: none;
}

.farmfaucet-form .vertical-checkbox-group label:hover {
    background-color: #f0f0f0;
}

.farmfaucet-form .vertical-checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Conversion Settings */
.farmfaucet-form .conversion-settings {
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.farmfaucet-form .toggle-field {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.farmfaucet-form .toggle-label {
    margin-left: 10px;
    font-weight: 500;
    font-size: 14px;
}

.farmfaucet-form .conversion-currencies .ad-currency {
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 3px solid #4CAF50;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Faucet Type Fields */
.faucet-type-fields {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #ddd;
    margin-bottom: 20px;
}

.faucet-type-fields h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

/* Faucet Form Dialog */
#faucet-form-dialog {
    max-width: 800px;
    width: 100%;
    padding: 0;
}

#faucet-form-dialog .ui-dialog-titlebar {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 0;
    padding: 15px;
}

#faucet-form-dialog .ui-dialog-content {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

#faucet-form-dialog h3 {
    font-size: 16px;
    margin: 20px 0 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
    color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #faucet-form-dialog {
        max-width: 95%;
    }

    .farmfaucet-form .checkbox-group {
        flex-direction: column;
    }
}
