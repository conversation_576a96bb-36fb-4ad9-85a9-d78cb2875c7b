/**
 * Farm Faucet Username Form Functionality
 *
 * Handles username form display and submission
 */
(function($) {
    $(document).ready(function() {
        // Initialize all username forms
        $('.farmfaucet-username-container').each(function() {
            initUsernameForm($(this));
        });

        /**
         * Initialize username form
         *
         * @param {jQuery} $container Form container
         */
        function initUsernameForm($container) {
            var $currentDisplay = $container.find('.farmfaucet-current-username');
            var $form = $container.find('.farmfaucet-username-form');
            var $editBtn = $container.find('.farmfaucet-username-edit-btn');
            var $cancelBtn = $container.find('.farmfaucet-username-cancel-link');
            var $input = $container.find('.farmfaucet-username-input');
            var $submitBtn = $container.find('.farmfaucet-username-submit');
            var $successMsg = $container.find('.farmfaucet-username-success');
            var currentName = $container.find('.farmfaucet-current-name-display').text().trim();

            // Initially hide the form and success message
            $form.hide();
            $successMsg.hide();

            // Show form when edit button is clicked
            $editBtn.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Fade out current display and fade in form
                $currentDisplay.fadeOut(200, function() {
                    // Reset form state
                    $input.val(currentName);
                    $input.prop('disabled', false);
                    $submitBtn.prop('disabled', false).text('Save');
                    $successMsg.hide();

                    // Show form and focus input
                    $form.fadeIn(200, function() {
                        $input.focus();

                        // Select all text in the input
                        $input[0].setSelectionRange(0, $input.val().length);
                    });
                });
            });

            // Hide form when cancel is clicked
            $cancelBtn.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Hide success message if visible
                $successMsg.hide();

                // Fade out form and fade in current display
                $form.fadeOut(200, function() {
                    $currentDisplay.fadeIn(200);
                });
            });

            // Handle form submission
            $container.find('form').on('submit', function(e) {
                e.preventDefault();

                var newName = $input.val().trim();

                // Validate input
                if (newName === '') {
                    alert('Please enter a display name');
                    $input.focus();
                    return;
                }

                if (newName === currentName) {
                    // No change, just hide the form
                    $form.fadeOut(200, function() {
                        $currentDisplay.fadeIn(200);
                    });
                    return;
                }

                // Show confirmation dialog
                if (!confirm('Are you sure you want to change your display name to "' + newName + '"?')) {
                    return;
                }

                // Disable form during submission
                $input.prop('disabled', true);
                $submitBtn.prop('disabled', true).text('Saving...');

                // Send AJAX request
                $.ajax({
                    url: farmfaucet_vars.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'farmfaucet_update_username',
                        nonce: farmfaucet_vars.username_nonce,
                        display_name: newName
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update current name display
                            currentName = newName;
                            $container.find('.farmfaucet-current-name-display').text(newName);

                            // Update all other instances of the username on the page
                            $('.farmfaucet-username').text(newName);

                            // Show success message
                            $successMsg.fadeIn(300);

                            // Hide form and show current display after a delay
                            setTimeout(function() {
                                $successMsg.fadeOut(300);
                                $form.fadeOut(200, function() {
                                    $currentDisplay.fadeIn(200);
                                });
                            }, 2000);
                        } else {
                            alert(response.data || 'Error updating display name');

                            // Re-enable form
                            $input.prop('disabled', false);
                            $submitBtn.prop('disabled', false).text('Save');
                        }
                    },
                    error: function() {
                        alert('Error communicating with server');

                        // Re-enable form
                        $input.prop('disabled', false);
                        $submitBtn.prop('disabled', false).text('Save');
                    }
                });
            });

            // Prevent form submission on Enter key in input field
            $input.on('keydown', function(e) {
                if (e.keyCode === 13) { // Enter key
                    e.preventDefault();
                    $submitBtn.click();
                }
            });
        }
    });
})(jQuery);
