/**
 * Captcha debug script
 * This script helps diagnose captcha loading issues
 */
(function() {
    'use strict';
    
    // Wait for the page to be fully loaded
    window.addEventListener('load', function() {
        console.log('Captcha debug script loaded');
        
        // Check if farmfaucet_vars is available
        if (window.farmfaucet_vars) {
            console.log('Farmfaucet vars available:');
            console.log('- Captcha type:', window.farmfaucet_vars.captcha_type);
            console.log('- hCaptcha sitekey configured:', !!window.farmfaucet_vars.hcaptcha_sitekey);
            console.log('- reCAPTCHA sitekey configured:', !!window.farmfaucet_vars.recaptcha_sitekey);
        } else {
            console.log('Farmfaucet vars not available');
        }
        
        // Check if captcha APIs are available
        console.log('hCaptcha API available:', typeof window.hcaptcha !== 'undefined');
        console.log('reCAPTCHA API available:', typeof window.grecaptcha !== 'undefined');
        
        // Check if captcha containers exist
        const hcaptchaContainers = document.querySelectorAll('.h-captcha');
        const recaptchaContainers = document.querySelectorAll('.g-recaptcha');
        console.log('hCaptcha containers found:', hcaptchaContainers.length);
        console.log('reCAPTCHA containers found:', recaptchaContainers.length);
        
        // Check if captcha loading messages exist
        const loadingMessages = document.querySelectorAll('.farmfaucet-captcha-loading');
        console.log('Captcha loading messages found:', loadingMessages.length);
        
        // Check if captcha widgets have been initialized
        if (window.farmfaucetCaptchaWidgets) {
            console.log('Captcha widgets initialized:', Object.keys(window.farmfaucetCaptchaWidgets).length);
            console.log('Widget IDs:', Object.keys(window.farmfaucetCaptchaWidgets));
        } else {
            console.log('Captcha widgets not initialized');
        }
        
        // Check if callback functions are defined
        console.log('hCaptcha callback defined:', typeof window.hCaptchaCallback === 'function');
        console.log('reCAPTCHA callback defined:', typeof window.recaptchaCallback === 'function');
        
        // Check if initialization function is defined
        console.log('Captcha initialization function defined:', typeof window.farmfaucetInitCaptchas === 'function');
        
        // Check if initialization flag is set
        console.log('Captchas initialized flag:', window.farmfaucetCaptchasInitialized);
        
        // Try to fix any visible issues
        setTimeout(function() {
            // If loading messages still exist but APIs are available, try to remove them
            if (loadingMessages.length > 0) {
                if ((typeof window.hcaptcha !== 'undefined' && window.farmfaucet_vars.captcha_type === 'hcaptcha') ||
                    (typeof window.grecaptcha !== 'undefined' && window.farmfaucet_vars.captcha_type === 'recaptcha')) {
                    console.log('Removing stale loading messages');
                    loadingMessages.forEach(function(msg) {
                        msg.style.display = 'none';
                    });
                }
            }
            
            // Make sure captcha containers are visible
            const captchaContainers = document.querySelectorAll('.farmfaucet-captcha-container');
            captchaContainers.forEach(function(container) {
                container.style.display = 'flex';
                container.style.visibility = 'visible';
                container.style.opacity = '1';
            });
            
            // Make sure captcha elements are visible
            const captchaElements = document.querySelectorAll('.h-captcha, .g-recaptcha');
            captchaElements.forEach(function(element) {
                element.style.display = 'block';
                element.style.visibility = 'visible';
                element.style.opacity = '1';
            });
            
            // Make sure captcha iframes are visible
            const captchaIframes = document.querySelectorAll('iframe[src*="hcaptcha.com"], iframe[src*="google.com/recaptcha"]');
            captchaIframes.forEach(function(iframe) {
                iframe.style.display = 'block';
                iframe.style.visibility = 'visible';
                iframe.style.opacity = '1';
            });
            
            console.log('Applied visibility fixes to captcha elements');
        }, 3000);
    });
})();
