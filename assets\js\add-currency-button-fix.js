/**
 * Add Currency Button Fix
 * 
 * This is a very simple, targeted fix for the "Add Currency" button in the Currency Maker.
 * It only focuses on making the button work properly.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Add Currency Button Fix loaded');
        initAddCurrencyButton();
    });

    /**
     * Initialize Add Currency Button
     */
    function initAddCurrencyButton() {
        console.log('Initializing Add Currency Button Fix');
        
        // Add event handler for the Add Currency button
        $('.farmfaucet-add-currency-button').off('click').on('click', function() {
            console.log('Add Currency button clicked');
            showCurrencyModal();
        });

        // Add event handler for the modal close button
        $('.farmfaucet-modal-close').off('click').on('click', function() {
            console.log('Modal close button clicked');
            closeCurrencyModal();
        });

        // Add event handler for the save button
        $('#farmfaucet-save-currency').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Save Currency button clicked');
            saveCurrency();
        });

        // Add event handler for the form submission
        $('#farmfaucet-currency-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            saveCurrency();
        });

        // Close modal when clicking outside
        $(window).off('click.currencyModal').on('click.currencyModal', function(event) {
            if ($(event.target).hasClass('farmfaucet-modal')) {
                closeCurrencyModal();
            }
        });

        // Initialize color picker
        if ($.fn.wpColorPicker) {
            console.log('Initializing color picker');
            $('.color-picker').wpColorPicker();
        }
    }

    /**
     * Show currency modal
     */
    function showCurrencyModal() {
        console.log('Showing currency modal');
        
        // Reset form
        $('#farmfaucet-currency-form')[0].reset();
        $('#currency-id').val('');
        
        // Update modal title
        $('.modal-title').text('Add Currency');
        
        // Show modal
        $('#farmfaucet-currency-modal').fadeIn(300);
    }

    /**
     * Close currency modal
     */
    function closeCurrencyModal() {
        $('#farmfaucet-currency-modal').fadeOut(300);
    }

    /**
     * Save currency
     */
    function saveCurrency() {
        console.log('Saving currency');
        
        // Validate form
        const $form = $('#farmfaucet-currency-form');
        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }

        // Get form data
        const currencyId = $('#currency-id').val();
        const isUpdate = currencyId !== '';
        
        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-currency');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');

        // Get color value
        let colorValue = $('#currency-color').val();
        if (!colorValue || colorValue === 'undefined' || colorValue === 'null') {
            colorValue = '#4CAF50';
        }
        
        // Prepare form data
        const formData = {
            action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
            nonce: farmfaucetCurrencyMakerAdmin.nonce,
            name: $('#currency-name').val(),
            code: $('#currency-code').val(),
            symbol: $('#currency-symbol').val(),
            base_currency: $('#currency-base').val(),
            exchange_rate: $('#currency-rate').val(),
            color: colorValue,
            icon: $('#currency-icon').val(),
            currency_type: $('#currency-type').val(),
            is_active: $('#currency-active').is(':checked') ? 1 : 0
        };
        
        // Add currency ID if updating
        if (isUpdate) {
            formData.currency_id = currencyId;
        }
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    alert(isUpdate ? 'Currency updated successfully.' : 'Currency created successfully.');
                    window.location.reload();
                } else {
                    console.error('Currency save error:', response);
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(isUpdate ? 'Failed to update currency. Please try again.' : 'Failed to create currency. Please try again.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                $saveButton.prop('disabled', false).text(originalButtonText);
                alert('An error occurred. Please try again.');
            }
        });
    }
})(jQuery);
