<?php

/**
 * Farmfaucet Database Updater for Faucet Appearance
 *
 * Handles database updates for the faucet appearance fields
 */
class Farmfaucet_DB_Updater_Faucet_Appearance
{
    /**
     * Run the update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if the faucets table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        if (!$table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add button_color column if it doesn't exist
        if (!in_array('button_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN button_color varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER text_shadow");
        }

        // Add border_color column if it doesn't exist
        if (!in_array('border_color', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN border_color varchar(50) NOT NULL DEFAULT '#4CAF50' AFTER button_color");
        }

        // Add border_radius column if it doesn't exist
        if (!in_array('border_radius', $column_names)) {
            $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN border_radius varchar(20) NOT NULL DEFAULT '8px' AFTER border_color");
        }

        // Update existing faucets to set default values
        $wpdb->query("UPDATE {$faucets_table} SET button_color = '#4CAF50', border_color = '#4CAF50', border_radius = '8px' WHERE button_color IS NULL OR button_color = ''");
    }
}
