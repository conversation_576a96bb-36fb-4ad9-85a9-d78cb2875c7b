<?php
/**
 * Farm Faucet Captcha Handler
 * 
 * This class handles captcha configuration and rendering across all faucet types.
 */
class Farmfaucet_Captcha_Handler
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        // Add hooks to normalize captcha option names
        add_filter('pre_update_option_farmfaucet_hcaptcha_site_key', [self::class, 'sync_hcaptcha_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_hcaptcha_sitekey', [self::class, 'sync_hcaptcha_site_key'], 10, 2);
        
        add_filter('pre_update_option_farmfaucet_recaptcha_site_key', [self::class, 'sync_recaptcha_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_recaptcha_sitekey', [self::class, 'sync_recaptcha_site_key'], 10, 2);
        
        add_filter('pre_update_option_farmfaucet_turnstile_site_key', [self::class, 'sync_turnstile_sitekey'], 10, 2);
        add_filter('pre_update_option_farmfaucet_turnstile_sitekey', [self::class, 'sync_turnstile_site_key'], 10, 2);
        
        // Add hooks to normalize captcha secret option names
        add_filter('pre_update_option_farmfaucet_hcaptcha_secret', [self::class, 'sync_hcaptcha_secret_key'], 10, 2);
        add_filter('pre_update_option_farmfaucet_recaptcha_secret', [self::class, 'sync_recaptcha_secret_key'], 10, 2);
        add_filter('pre_update_option_farmfaucet_turnstile_secret', [self::class, 'sync_turnstile_secret_key'], 10, 2);
        
        // Add hooks to enqueue scripts
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_scripts']);
    }
    
    /**
     * Sync hCaptcha site key from site_key to sitekey
     */
    public static function sync_hcaptcha_sitekey($value, $old_value)
    {
        update_option('farmfaucet_hcaptcha_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync hCaptcha site key from sitekey to site_key
     */
    public static function sync_hcaptcha_site_key($value, $old_value)
    {
        update_option('farmfaucet_hcaptcha_site_key', $value);
        return $value;
    }
    
    /**
     * Sync reCAPTCHA site key from site_key to sitekey
     */
    public static function sync_recaptcha_sitekey($value, $old_value)
    {
        update_option('farmfaucet_recaptcha_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync reCAPTCHA site key from sitekey to site_key
     */
    public static function sync_recaptcha_site_key($value, $old_value)
    {
        update_option('farmfaucet_recaptcha_site_key', $value);
        return $value;
    }
    
    /**
     * Sync Turnstile site key from site_key to sitekey
     */
    public static function sync_turnstile_sitekey($value, $old_value)
    {
        update_option('farmfaucet_turnstile_sitekey', $value);
        return $value;
    }
    
    /**
     * Sync Turnstile site key from sitekey to site_key
     */
    public static function sync_turnstile_site_key($value, $old_value)
    {
        update_option('farmfaucet_turnstile_site_key', $value);
        return $value;
    }
    
    /**
     * Sync hCaptcha secret key
     */
    public static function sync_hcaptcha_secret_key($value, $old_value)
    {
        update_option('farmfaucet_hcaptcha_secret_key', $value);
        return $value;
    }
    
    /**
     * Sync reCAPTCHA secret key
     */
    public static function sync_recaptcha_secret_key($value, $old_value)
    {
        update_option('farmfaucet_recaptcha_secret_key', $value);
        return $value;
    }
    
    /**
     * Sync Turnstile secret key
     */
    public static function sync_turnstile_secret_key($value, $old_value)
    {
        update_option('farmfaucet_turnstile_secret_key', $value);
        return $value;
    }
    
    /**
     * Enqueue scripts
     */
    public static function enqueue_scripts()
    {
        wp_enqueue_script(
            'farmfaucet-captcha-unified',
            FARMFAUCET_URL . 'assets/js/captcha-unified.js',
            ['jquery'],
            FARMFAUCET_VERSION . '.' . time(),
            true
        );
    }
    
    /**
     * Get captcha site key
     * 
     * @param string $captcha_type The captcha type (hcaptcha, recaptcha, turnstile)
     * @return string The captcha site key
     */
    public static function get_captcha_site_key($captcha_type)
    {
        switch ($captcha_type) {
            case 'hcaptcha':
                $site_key = get_option('farmfaucet_hcaptcha_site_key', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_hcaptcha_sitekey', '');
                }
                return $site_key;
                
            case 'recaptcha':
                $site_key = get_option('farmfaucet_recaptcha_site_key', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_recaptcha_sitekey', '');
                }
                return $site_key;
                
            case 'turnstile':
                $site_key = get_option('farmfaucet_turnstile_site_key', '');
                if (empty($site_key)) {
                    $site_key = get_option('farmfaucet_turnstile_sitekey', '');
                }
                return $site_key;
                
            default:
                return '';
        }
    }
    
    /**
     * Get captcha secret key
     * 
     * @param string $captcha_type The captcha type (hcaptcha, recaptcha, turnstile)
     * @return string The captcha secret key
     */
    public static function get_captcha_secret_key($captcha_type)
    {
        switch ($captcha_type) {
            case 'hcaptcha':
                $secret_key = get_option('farmfaucet_hcaptcha_secret', '');
                if (empty($secret_key)) {
                    $secret_key = get_option('farmfaucet_hcaptcha_secret_key', '');
                }
                return $secret_key;
                
            case 'recaptcha':
                $secret_key = get_option('farmfaucet_recaptcha_secret', '');
                if (empty($secret_key)) {
                    $secret_key = get_option('farmfaucet_recaptcha_secret_key', '');
                }
                return $secret_key;
                
            case 'turnstile':
                $secret_key = get_option('farmfaucet_turnstile_secret', '');
                if (empty($secret_key)) {
                    $secret_key = get_option('farmfaucet_turnstile_secret_key', '');
                }
                return $secret_key;
                
            default:
                return '';
        }
    }
    
    /**
     * Check if captcha is configured
     * 
     * @param string $captcha_type The captcha type (hcaptcha, recaptcha, turnstile)
     * @return bool Whether the captcha is configured
     */
    public static function is_captcha_configured($captcha_type)
    {
        $site_key = self::get_captcha_site_key($captcha_type);
        $secret_key = self::get_captcha_secret_key($captcha_type);
        
        return !empty($site_key) && !empty($secret_key);
    }
    
    /**
     * Render captcha
     * 
     * @param array $faucet The faucet data
     * @return string The captcha HTML
     */
    public static function render_captcha($faucet)
    {
        $output = '';
        
        try {
            // Get captcha type from faucet-specific setting or fallback to global settings
            $captcha_type = !empty($faucet['captcha_type']) ? $faucet['captcha_type'] : get_option('farmfaucet_captcha_type', 'hcaptcha');
            
            // Validate captcha type
            if (!in_array($captcha_type, ['hcaptcha', 'recaptcha', 'turnstile'])) {
                $captcha_type = 'hcaptcha'; // Default to hCaptcha if invalid
            }
            
            // Get captcha site keys
            $hcaptcha_sitekey = self::get_captcha_site_key('hcaptcha');
            $recaptcha_sitekey = self::get_captcha_site_key('recaptcha');
            $turnstile_sitekey = self::get_captcha_site_key('turnstile');
            
            // Generate a unique ID for this captcha
            $captcha_id = 'farmfaucet-captcha-' . $faucet['id'] . '-' . uniqid();
            
            // Add hidden field for captcha type
            $output .= '<input type="hidden" name="captcha_type" value="' . esc_attr($captcha_type) . '">';
            
            // Render the appropriate captcha
            if ($captcha_type === 'hcaptcha') {
                if (empty($hcaptcha_sitekey)) {
                    $output .= '<div class="farmfaucet-error">' . esc_html__('hCaptcha site key not configured', 'farmfaucet') . '</div>';
                } else {
                    $output .= '<div class="h-captcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">';
                    $output .= '<div id="' . $captcha_id . '" class="h-captcha farmfaucet-captcha" data-sitekey="' . esc_attr($hcaptcha_sitekey) . '" data-faucet-id="' . esc_attr($faucet['id']) . '" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                    $output .= '</div>';
                }
            } elseif ($captcha_type === 'recaptcha') {
                if (empty($recaptcha_sitekey)) {
                    $output .= '<div class="farmfaucet-error">' . esc_html__('reCAPTCHA site key not configured', 'farmfaucet') . '</div>';
                } else {
                    $output .= '<div class="g-recaptcha-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">';
                    $output .= '<div id="' . $captcha_id . '" class="g-recaptcha farmfaucet-captcha" data-sitekey="' . esc_attr($recaptcha_sitekey) . '" data-faucet-id="' . esc_attr($faucet['id']) . '" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                    $output .= '</div>';
                }
            } elseif ($captcha_type === 'turnstile') {
                if (empty($turnstile_sitekey)) {
                    $output .= '<div class="farmfaucet-error">' . esc_html__('Cloudflare Turnstile site key not configured', 'farmfaucet') . '</div>';
                } else {
                    $output .= '<div class="cf-turnstile-wrapper" style="display: flex; visibility: visible; opacity: 1; justify-content: center;">';
                    $output .= '<div id="' . $captcha_id . '" class="cf-turnstile farmfaucet-captcha" data-sitekey="' . esc_attr($turnstile_sitekey) . '" data-faucet-id="' . esc_attr($faucet['id']) . '" style="display: block; visibility: visible; opacity: 1; min-height: 78px;"></div>';
                    $output .= '</div>';
                }
            }
        } catch (Exception $e) {
            $output .= '<div class="farmfaucet-error">' . esc_html__('Error loading captcha', 'farmfaucet') . '</div>';
        }
        
        return $output;
    }
}
