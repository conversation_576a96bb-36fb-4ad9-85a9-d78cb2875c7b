/* ============================================= */
/* MILESTONE STYLES - MOVED TO milestone.css */
/* ============================================= */
/* All milestone styles have been moved to milestone.css to avoid duplication */

/* ============================================= */
/* INLINE SHORTCODE STYLES */
/* ============================================= */

/* Inline shortcode styles */
.farmfaucet-user-email-inline,
.farmfaucet-user-username-inline,
.farmfaucet-user-display-name-inline,
.farmfaucet-total-earned-inline,
.farmfaucet-joined-date-inline {
    display: inline;
    font-size: inherit;
    color: inherit;
    word-break: break-word;
}

/* Inline avatar styles */
.farmfaucet-user-avatar-inline {
    display: inline-block;
    vertical-align: middle;
}

.farmfaucet-user-avatar-inline img.circular-avatar {
    border-radius: 50%;
    border: 2px solid #4CAF50;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Faucet button styles */
.farmfaucet-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 5px 0;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Countdown standby overlay */
.farmfaucet-standby-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    z-index: 999999;
    cursor: wait;
}
