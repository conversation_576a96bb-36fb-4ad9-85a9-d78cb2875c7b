/**
 * Farm Faucet - Button Color Fix
 *
 * This stylesheet fixes issues with the button color picker
 */

/* Color picker container */
.color-grid-container {
    position: relative;
    margin-bottom: 15px;
}

/* Color select wrapper */
.color-select-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

/* Color name display */
.color-name-display {
    flex-grow: 1;
    padding: 5px 10px;
    font-weight: 500;
}

/* Color preview */
.color-preview {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
}

/* Color grid */
.color-grid {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    max-width: 300px;
}

/* Color swatch options */
.color-swatch-option {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin: 5px;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-swatch-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-swatch-option.selected {
    border: 2px solid #000;
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px #4CAF50;
}

/* Custom color input */
.custom-color-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

#button-custom-color {
    width: 100%;
    height: 30px;
    margin-top: 5px;
    cursor: pointer;
}

/* Fix for dialog positioning */
.ui-dialog {
    z-index: 100000 !important;
}

/* Fix for color grid positioning */
.ui-dialog .color-grid {
    max-width: 100%;
}

/* Fix for color picker in button form */
#button-form .color-grid-container {
    max-width: 100%;
}

/* Make sure the color preview is visible */
#button-form .color-preview {
    min-width: 30px;
}

/* Fix for custom color input */
#button-form #button-custom-color {
    padding: 0;
    border: 1px solid #ddd;
}

/* Fix for color name display */
#button-form .color-name-display {
    min-width: 80px;
}
