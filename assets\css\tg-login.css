/**
 * Tel<PERSON><PERSON>gin CSS
 */

/* Container styles */
.farmfaucet-tg-login-container,
.farmfaucet-tg-signup-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Button styles */
.farmfaucet-tg-login-button,
.farmfaucet-tg-signup-button {
    display: block;
    width: 100%;
    padding: 12px 20px;
    background-color: #0088cc; /* Telegram blue */
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
}

.farmfaucet-tg-login-button:hover,
.farmfaucet-tg-signup-button:hover {
    background-color: #006699;
}

/* Form styles */
.farmfaucet-tg-login-form,
.farmfaucet-tg-signup-form {
    margin-top: 20px;
    transition: opacity 0.5s ease;
}

.farmfaucet-form-group {
    margin-bottom: 15px;
}

.farmfaucet-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.farmfaucet-form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.farmfaucet-form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.farmfaucet-form-actions {
    margin-top: 20px;
}

.farmfaucet-tg-login-submit,
.farmfaucet-tg-signup-submit,
.farmfaucet-tg-verify-submit {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.farmfaucet-tg-login-submit:hover,
.farmfaucet-tg-signup-submit:hover,
.farmfaucet-tg-verify-submit:hover {
    background-color: #45a049;
}

/* Verification section */
.farmfaucet-tg-verification {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 4px;
    border-left: 4px solid #0088cc;
}

.farmfaucet-tg-get-code {
    color: #0088cc;
    text-decoration: underline;
    cursor: pointer;
}

.farmfaucet-tg-verification-timer {
    margin-top: 10px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

/* Message styles */
.farmfaucet-tg-login-message,
.farmfaucet-tg-signup-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

.farmfaucet-success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.farmfaucet-error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.farmfaucet-info {
    background-color: #d9edf7;
    color: #31708f;
    border: 1px solid #bce8f1;
}

/* Responsive styles */
@media (max-width: 768px) {
    .farmfaucet-tg-login-container,
    .farmfaucet-tg-signup-container {
        max-width: 100%;
        padding: 15px;
    }
}
