/**
 * Farm Faucet - Telegram Bot Builder Fix
 * This script fixes issues with the Telegram Bot Builder functionality
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Telegram Bot Builder Fix Clean loaded');
        initTgBotBuilderFix();
    });

    /**
     * Initialize Telegram Bot Builder Fix
     */
    function initTgBotBuilderFix() {
        // Remove any existing event handlers to prevent duplicates
        $('#farmfaucet-save-bot').off('click');
        $('#test-bot-token').off('click');
        
        // Add our clean event handlers
        $('#farmfaucet-save-bot').on('click', function(e) {
            e.preventDefault();
            saveBot();
        });
        
        $('#test-bot-token').on('click', function(e) {
            e.preventDefault();
            testBotToken();
        });
    }
    
    /**
     * Save bot
     */
    function saveBot() {
        // Get form data
        const botId = $('#bot-id').val();
        const botName = $('#bot-name').val();
        const botToken = $('#bot-token').val();
        const botUsername = $('#bot-username').val();
        const botType = $('#bot-type').val();
        const isActive = $('#bot-active').is(':checked') ? 1 : 0;
        
        // Validate required fields
        if (!botName) {
            alert('Please enter a bot name');
            return;
        }
        
        if (!botToken) {
            alert('Please enter a bot token');
            return;
        }
        
        if (!botUsername) {
            alert('Please enter a bot username');
            return;
        }
        
        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-bot');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');
        
        // Prepare form data
        const formData = {
            action: 'farmfaucet_save_bot',
            nonce: farmfaucetTgBotBuilder.nonce,
            bot_id: botId,
            bot_name: botName,
            bot_token: botToken,
            bot_username: botUsername.replace('@', ''), // Remove @ if present
            bot_type: botType,
            is_active: isActive,
            webhook_url: $('#webhook-url').val() || '',
            settings: JSON.stringify({
                welcome_message: $('#welcome-message').val() || 'Welcome to my bot!',
                help_message: $('#help-message').val() || 'Here are the available commands:',
                error_message: $('#error-message').val() || 'Sorry, I don\'t understand that command.'
            })
        };
        
        console.log('Sending bot data:', formData);
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Bot save response:', response);
                
                if (response.success) {
                    alert(response.data.message || (botId ? 'Bot updated successfully' : 'Bot created successfully'));
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(response.data.message || (botId ? 'Failed to update bot' : 'Failed to create bot'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $saveButton.prop('disabled', false).text(originalButtonText);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                alert('Error: ' + errorMessage);
            }
        });
    }
    
    /**
     * Test bot token
     */
    function testBotToken() {
        const token = $('#bot-token').val();
        
        if (!token) {
            alert('Please enter a bot token');
            return;
        }
        
        const $testButton = $('#test-bot-token');
        const originalButtonText = $testButton.text();
        $testButton.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: farmfaucetTgBotBuilder.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_test_bot_token',
                nonce: farmfaucetTgBotBuilder.nonce,
                token: token
            },
            success: function(response) {
                console.log('Test token response:', response);
                $testButton.prop('disabled', false).text(originalButtonText);
                
                if (response.success) {
                    // Populate username field
                    $('#bot-username').val(response.data.username);
                    
                    alert('Bot token is valid. Bot username: ' + response.data.username);
                } else {
                    alert('Invalid bot token: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $testButton.prop('disabled', false).text(originalButtonText);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                alert('Error testing bot token: ' + errorMessage);
            }
        });
    }
})(jQuery);
