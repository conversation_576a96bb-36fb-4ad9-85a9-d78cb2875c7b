/**
 * Currency Maker Simple Fix V2
 * 
 * This is a simple, targeted fix for the currency maker modal
 * that focuses only on fixing the "Add Currency" button and color picker.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Currency Maker Simple Fix V2 loaded');
        initCurrencyMakerSimpleFix();
    });

    /**
     * Initialize Currency Maker Simple Fix
     */
    function initCurrencyMakerSimpleFix() {
        console.log('Initializing Currency Maker Simple Fix V2');
        
        // Add event handler for the Add Currency button
        $('.farmfaucet-add-currency-button').off('click').on('click', function() {
            console.log('Add Currency button clicked');
            showCurrencyModal();
        });

        // Add event handler for the modal close button
        $('.farmfaucet-modal-close').off('click').on('click', function() {
            console.log('Modal close button clicked');
            closeCurrencyModal();
        });

        // Add event handler for the save button
        $('#farmfaucet-save-currency').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Save Currency button clicked');
            saveCurrency();
        });

        // Add event handler for the form submission
        $('#farmfaucet-currency-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            saveCurrency();
        });

        // Close modal when clicking outside
        $(window).off('click.currencyModal').on('click.currencyModal', function(event) {
            if ($(event.target).hasClass('farmfaucet-modal')) {
                closeCurrencyModal();
            }
        });

        // Initialize color picker
        initColorPicker();

        // Add event handlers for edit buttons
        $('.farmfaucet-edit-currency-button').off('click').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            console.log('Edit button clicked for currency ID:', currencyId);
            editCurrency(currencyId);
        });

        // Add event handlers for delete buttons
        $('.farmfaucet-delete-currency-button').off('click').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            console.log('Delete button clicked for currency ID:', currencyId);
            deleteCurrency(currencyId);
        });
    }

    /**
     * Initialize color picker
     */
    function initColorPicker() {
        if ($.fn.wpColorPicker) {
            console.log('Initializing color picker');
            
            // First destroy any existing color pickers
            if ($('.color-picker').hasClass('wp-color-picker')) {
                $('.color-picker').wpColorPicker('destroy');
            }
            
            // Then initialize the color picker
            $('.color-picker').wpColorPicker({
                defaultColor: '#4CAF50',
                change: function(event, ui) {
                    console.log('Color changed to:', ui.color.toString());
                    $(this).val(ui.color.toString());
                    $(this).attr('data-color', ui.color.toString());
                },
                clear: function() {
                    console.log('Color cleared');
                    $(this).val('#4CAF50');
                    $(this).attr('data-color', '#4CAF50');
                }
            });
        } else {
            console.error('WordPress color picker not available');
        }
    }

    /**
     * Show currency modal
     */
    function showCurrencyModal() {
        console.log('Showing currency modal');
        
        // Reset form
        $('#farmfaucet-currency-form')[0].reset();
        $('#currency-id').val('');
        
        // Reset color picker
        if ($.fn.wpColorPicker) {
            // Set the default color
            const defaultColor = '#4CAF50';
            
            // Update the input value
            $('#currency-color').val(defaultColor);
            $('#currency-color').attr('data-color', defaultColor);
            
            // Update the color picker if it's initialized
            if ($('#currency-color').hasClass('wp-color-picker')) {
                try {
                    // Try to update the color picker
                    $('#currency-color').wpColorPicker('color', defaultColor);
                } catch (e) {
                    console.error('Error updating color picker:', e);
                    
                    // If that fails, destroy and reinitialize
                    $('#currency-color').wpColorPicker('destroy');
                    initColorPicker();
                }
            } else {
                // Initialize color picker if not already initialized
                initColorPicker();
            }
        }

        // Update modal title
        $('.modal-title').text('Add Currency');

        // Show modal
        $('#farmfaucet-currency-modal').fadeIn(300);
    }

    /**
     * Close currency modal
     */
    function closeCurrencyModal() {
        $('#farmfaucet-currency-modal').fadeOut(300);
    }

    /**
     * Edit currency
     */
    function editCurrency(currencyId) {
        // Get currency data
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success && response.data.currency) {
                    const currency = response.data.currency;
                    
                    // Set form values
                    $('#currency-id').val(currency.id);
                    $('#currency-name').val(currency.name);
                    $('#currency-code').val(currency.code);
                    $('#currency-symbol').val(currency.symbol);
                    $('#currency-base').val(currency.base_currency);
                    $('#currency-rate').val(currency.exchange_rate);
                    $('#currency-icon').val(currency.icon || '');
                    $('#currency-type').val(currency.currency_type || 'earnings');
                    $('#currency-active').prop('checked', currency.is_active == 1);
                    
                    // Set color picker
                    if ($.fn.wpColorPicker) {
                        const color = currency.color || '#4CAF50';
                        console.log('Setting color picker to:', color);
                        
                        // Update the input value
                        $('#currency-color').val(color);
                        $('#currency-color').attr('data-color', color);
                        
                        // Update the color picker if it's initialized
                        if ($('#currency-color').hasClass('wp-color-picker')) {
                            try {
                                // Try to update the color picker
                                $('#currency-color').wpColorPicker('color', color);
                            } catch (e) {
                                console.error('Error updating color picker:', e);
                                
                                // If that fails, destroy and reinitialize
                                $('#currency-color').wpColorPicker('destroy');
                                initColorPicker();
                            }
                        } else {
                            // Initialize color picker if not already initialized
                            initColorPicker();
                        }
                    }
                    
                    // Update modal title
                    $('.modal-title').text('Edit Currency');
                    
                    // Show modal
                    $('#farmfaucet-currency-modal').fadeIn(300);
                } else {
                    alert('Failed to get currency data');
                }
            },
            error: function() {
                alert('Failed to get currency data');
            }
        });
    }

    /**
     * Delete currency
     */
    function deleteCurrency(currencyId) {
        if (!confirm('Are you sure you want to delete this currency? This action cannot be undone.')) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success) {
                    alert('Currency deleted successfully.');
                    window.location.reload();
                } else {
                    alert('Failed to delete currency. Please try again.');
                }
            },
            error: function() {
                alert('Failed to delete currency. Please try again.');
            }
        });
    }

    /**
     * Save currency
     */
    function saveCurrency() {
        // Validate form
        const $form = $('#farmfaucet-currency-form');

        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }

        // Get form data
        const currencyId = $('#currency-id').val();
        const isUpdate = currencyId !== '';
        
        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-currency');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');

        // Get color value - try multiple approaches to ensure we get a valid color
        let colorValue;
        
        // First try to get from data attribute (set by color picker)
        if ($('#currency-color').attr('data-color')) {
            colorValue = $('#currency-color').attr('data-color');
            console.log('Got color from data-color attribute:', colorValue);
        }
        // Then try to get from the iris color picker directly
        else if ($('#currency-color').closest('.wp-picker-container').find('.wp-color-result').length) {
            const $colorResult = $('#currency-color').closest('.wp-picker-container').find('.wp-color-result');
            if ($colorResult.css('background-color')) {
                colorValue = $colorResult.css('background-color');
                console.log('Got color from color picker result:', colorValue);
            }
        }
        // Then try the input value
        else if ($('#currency-color').val()) {
            colorValue = $('#currency-color').val();
            console.log('Got color from input value:', colorValue);
        }
        // Finally, use default color
        else {
            colorValue = '#4CAF50';
            console.log('Using default color:', colorValue);
        }
        
        // Ensure we have a valid color
        if (!colorValue || colorValue === 'undefined' || colorValue === 'null') {
            colorValue = '#4CAF50';
            console.log('Color was invalid, using default:', colorValue);
        }
        
        // If color is in RGB format, convert to HEX
        if (colorValue.startsWith('rgb')) {
            colorValue = rgbToHex(colorValue);
            console.log('Converted RGB to HEX:', colorValue);
        }
        
        console.log('Final color value before submission:', colorValue);
        
        // Prepare form data
        const formData = {
            action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
            nonce: farmfaucetCurrencyMakerAdmin.nonce,
            name: $('#currency-name').val(),
            code: $('#currency-code').val(),
            symbol: $('#currency-symbol').val(),
            base_currency: $('#currency-base').val(),
            exchange_rate: $('#currency-rate').val(),
            color: colorValue,
            icon: $('#currency-icon').val(),
            currency_type: $('#currency-type').val(),
            is_active: $('#currency-active').is(':checked') ? 1 : 0
        };
        
        // Add currency ID if updating
        if (isUpdate) {
            formData.currency_id = currencyId;
        }
        
        console.log('Sending form data:', formData);
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    alert(isUpdate ? 'Currency updated successfully.' : 'Currency created successfully.');
                    window.location.reload();
                } else {
                    console.error('Currency save error:', response);
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(isUpdate ? 'Failed to update currency. Please try again.' : 'Failed to create currency. Please try again.');
                }
            },
            error: function() {
                console.error('AJAX error in saveCurrency');
                $saveButton.prop('disabled', false).text(originalButtonText);
                alert('An error occurred. Please try again.');
            }
        });
    }

    /**
     * Convert RGB color to HEX
     * 
     * @param {string} rgb RGB color string (e.g., 'rgb(255, 0, 0)')
     * @return {string} HEX color string (e.g., '#ff0000')
     */
    function rgbToHex(rgb) {
        // Default to green if invalid
        if (!rgb) return '#4CAF50';
        
        // Check if already a hex color
        if (rgb.charAt(0) === '#') {
            return rgb;
        }
        
        // Extract RGB values
        const rgbMatch = rgb.match(/^rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i);
        if (!rgbMatch) return '#4CAF50';
        
        const r = parseInt(rgbMatch[1], 10).toString(16).padStart(2, '0');
        const g = parseInt(rgbMatch[2], 10).toString(16).padStart(2, '0');
        const b = parseInt(rgbMatch[3], 10).toString(16).padStart(2, '0');
        
        return `#${r}${g}${b}`;
    }
})(jQuery);
