/**
 * Farm Faucet - Telegram Bot Builder Direct Fix CSS
 */

/* Create New Bot button styling */
.farmfaucet-add-bot-button {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.farmfaucet-add-bot-button:hover {
    background-color: #3d8b40 !important;
    border-color: #3d8b40 !important;
}

/* Dialog buttons styling */
.ui-dialog-buttonset button:first-child {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.ui-dialog-buttonset button:first-child:hover {
    background-color: #3d8b40 !important;
    border-color: #3d8b40 !important;
}

/* Token status styling */
.token-valid {
    color: #4CAF50;
    font-weight: bold;
    margin-top: 5px;
}

.token-invalid {
    color: #f44336;
    font-weight: bold;
    margin-top: 5px;
}

/* Bot status toggle styling */
.bot-status-toggle {
    display: flex;
    align-items: center;
}

.bot-enabled-toggle {
    margin-right: 10px;
}

.bot-status-label {
    font-weight: bold;
}

/* Switch styling */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:focus + .slider {
    box-shadow: 0 0 1px #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Create New Bot button */
#create-new-bot {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
