/**
 * Farm Faucet - Currency Maker Consolidated
 * This script handles the currency maker functionality
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Currency Maker Consolidated loaded');
        initCurrencyMaker();
    });

    /**
     * Initialize Currency Maker
     */
    function initCurrencyMaker() {
        // Remove any existing event handlers to prevent duplicates
        $('#farmfaucet-add-currency').off('click');
        $('.farmfaucet-edit-currency-button').off('click');
        $('.farmfaucet-delete-currency-button').off('click');
        $('.farmfaucet-modal-close').off('click');
        $('#farmfaucet-save-currency').off('click');
        $('#farmfaucet-currency-form').off('submit');
        
        // Add our clean event handlers
        $('#farmfaucet-add-currency').on('click', function() {
            showCurrencyModal();
        });
        
        $('.farmfaucet-edit-currency-button').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            editCurrency(currencyId);
        });
        
        $('.farmfaucet-delete-currency-button').on('click', function() {
            const currencyId = $(this).closest('tr').data('currency-id');
            deleteCurrency(currencyId);
        });
        
        $('.farmfaucet-modal-close').on('click', function() {
            closeCurrencyModal();
        });
        
        $('#farmfaucet-save-currency').on('click', function(e) {
            e.preventDefault();
            saveCurrency();
        });
        
        $('#farmfaucet-currency-form').on('submit', function(e) {
            e.preventDefault();
            saveCurrency();
        });
        
        // Close modal when clicking outside
        $(window).on('click', function(event) {
            if ($(event.target).hasClass('farmfaucet-modal')) {
                closeCurrencyModal();
            }
        });
        
        // Initialize color picker
        initColorPicker();
    }
    
    /**
     * Initialize color picker
     */
    function initColorPicker() {
        if ($.fn.wpColorPicker) {
            // First remove any existing color pickers
            if ($('#currency-color').hasClass('wp-color-picker')) {
                $('#currency-color').wpColorPicker('destroy');
            }
            
            // Then initialize the color picker
            $('#currency-color').wpColorPicker({
                defaultColor: '#4CAF50',
                change: function(event, ui) {
                    // Store the color value in a data attribute
                    $(this).attr('data-color', ui.color.toString());
                    $(this).val(ui.color.toString());
                    console.log('Color picker changed to:', ui.color.toString());
                }
            });
        }
    }
    
    /**
     * Show currency modal
     */
    function showCurrencyModal() {
        // Reset form
        $('#farmfaucet-currency-form')[0].reset();
        $('#currency-id').val('');
        
        // Reset color picker
        if ($.fn.wpColorPicker) {
            $('#currency-color').val('#4CAF50');
            $('#currency-color').attr('data-color', '#4CAF50');
            $('#currency-color').wpColorPicker('color', '#4CAF50');
        }
        
        // Update modal title
        $('.modal-title').text('Add Currency');
        
        // Show modal
        $('#farmfaucet-currency-modal').fadeIn(300);
    }
    
    /**
     * Edit currency
     */
    function editCurrency(currencyId) {
        // Get currency data
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_get_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success) {
                    const currency = response.data.currency;
                    
                    // Populate form
                    $('#currency-id').val(currency.id);
                    $('#currency-name').val(currency.name);
                    $('#currency-code').val(currency.code);
                    $('#currency-symbol').val(currency.symbol);
                    $('#currency-base').val(currency.base_currency);
                    $('#currency-rate').val(currency.exchange_rate);
                    $('#currency-icon').val(currency.icon || '');
                    $('#currency-type').val(currency.currency_type || 'earnings');
                    $('#currency-active').prop('checked', currency.is_active == 1);
                    
                    // Set color picker
                    if ($.fn.wpColorPicker) {
                        const color = currency.color || '#4CAF50';
                        $('#currency-color').val(color);
                        $('#currency-color').attr('data-color', color);
                        $('#currency-color').wpColorPicker('color', color);
                    }
                    
                    // Update modal title
                    $('.modal-title').text('Edit Currency');
                    
                    // Show modal
                    $('#farmfaucet-currency-modal').fadeIn(300);
                } else {
                    alert(response.data.message || 'Failed to get currency data');
                }
            },
            error: function() {
                alert('Failed to get currency data');
            }
        });
    }
    
    /**
     * Close currency modal
     */
    function closeCurrencyModal() {
        $('#farmfaucet-currency-modal').fadeOut(300);
    }
    
    /**
     * Save currency
     */
    function saveCurrency() {
        // Validate form
        const $form = $('#farmfaucet-currency-form');
        
        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }
        
        // Get form data
        const currencyId = $('#currency-id').val();
        const isUpdate = currencyId !== '';
        
        // Create a button to show saving state
        const $saveButton = $('#farmfaucet-save-currency');
        const originalButtonText = $saveButton.text();
        $saveButton.prop('disabled', true).text('Saving...');
        
        // Get color value - try multiple approaches to ensure we get a valid color
        let colorValue = $('#currency-color').attr('data-color');
        
        // If data-color is not set, try to get the value directly
        if (!colorValue) {
            colorValue = $('#currency-color').val();
        }
        
        // If still no color, use default
        if (!colorValue) {
            colorValue = '#4CAF50';
        }
        
        console.log('Color value before submission:', colorValue);
        
        // Prepare form data
        const formData = {
            action: isUpdate ? 'farmfaucet_update_currency' : 'farmfaucet_create_currency',
            nonce: farmfaucetCurrencyMakerAdmin.nonce,
            name: $('#currency-name').val(),
            code: $('#currency-code').val(),
            symbol: $('#currency-symbol').val(),
            base_currency: $('#currency-base').val(),
            exchange_rate: $('#currency-rate').val(),
            color: colorValue,
            icon: $('#currency-icon').val(),
            currency_type: $('#currency-type').val(),
            is_active: $('#currency-active').is(':checked') ? 1 : 0
        };
        
        // Add currency ID if updating
        if (isUpdate) {
            formData.currency_id = currencyId;
        }
        
        console.log('Sending form data:', formData);
        
        // Send AJAX request
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Currency save response:', response);
                
                if (response.success) {
                    alert(response.data.message || (isUpdate ? 'Currency updated successfully' : 'Currency created successfully'));
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    $saveButton.prop('disabled', false).text(originalButtonText);
                    alert(response.data.message || (isUpdate ? 'Failed to update currency' : 'Failed to create currency'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.responseText);
                $saveButton.prop('disabled', false).text(originalButtonText);
                
                // Try to parse the error response
                let errorMessage = error;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.message) {
                        errorMessage = errorResponse.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error
                }
                
                alert('Error: ' + errorMessage);
            }
        });
    }
    
    /**
     * Delete currency
     */
    function deleteCurrency(currencyId) {
        if (!confirm('Are you sure you want to delete this currency?')) {
            return;
        }
        
        $.ajax({
            url: farmfaucetCurrencyMakerAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_currency',
                nonce: farmfaucetCurrencyMakerAdmin.nonce,
                currency_id: currencyId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || 'Currency deleted successfully');
                    
                    // Reload the page to show the updated table
                    window.location.reload();
                } else {
                    alert(response.data.message || 'Failed to delete currency');
                }
            },
            error: function() {
                alert('Failed to delete currency');
            }
        });
    }
})(jQuery);
