/**
 * Referral System Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initReferralAdmin();
    });

    /**
     * Initialize Referral Admin functionality
     */
    function initReferralAdmin() {
        // Initialize date pickers
        if ($.fn.datepicker) {
            $('.datepicker').datepicker({
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true
            });
        }
        
        // Reward type change handler
        $('#farmfaucet_referral_reward_type').on('change', function() {
            const rewardType = $(this).val();
            
            if (rewardType === 'percentage') {
                $('.reward-percentage-row').show();
                $('.reward-fixed-row').hide();
            } else {
                $('.reward-percentage-row').hide();
                $('.reward-fixed-row').show();
            }
        }).trigger('change');
        
        // Levels change handler
        $('#farmfaucet_referral_levels').on('change', function() {
            const levels = parseInt($(this).val());
            const percentagesInput = $('#farmfaucet_referral_level_percentages');
            const percentages = percentagesInput.val().split(',');
            
            // Ensure we have enough percentages
            while (percentages.length < levels) {
                percentages.push('0');
            }
            
            // Trim to the required number of levels
            percentages.length = levels;
            
            // Update input
            percentagesInput.val(percentages.join(','));
            
            // Update preview
            updateLevelPercentagesPreview();
        });
        
        // Level percentages change handler
        $('#farmfaucet_referral_level_percentages').on('input', function() {
            updateLevelPercentagesPreview();
        });
        
        // Initialize level percentages preview
        updateLevelPercentagesPreview();
        
        // Activate referral handler
        $('.activate-referral').on('click', function(e) {
            e.preventDefault();
            
            const referrerId = $(this).data('referrer-id');
            const referredId = $(this).data('referred-id');
            
            updateReferralStatus(referrerId, referredId, 'active');
        });
        
        // Deactivate referral handler
        $('.deactivate-referral').on('click', function(e) {
            e.preventDefault();
            
            const referrerId = $(this).data('referrer-id');
            const referredId = $(this).data('referred-id');
            
            updateReferralStatus(referrerId, referredId, 'inactive');
        });
        
        // Delete referral handler
        $('.delete-referral').on('click', function(e) {
            e.preventDefault();
            
            if (!confirm(farmfaucetReferralAdmin.i18n.confirmDelete)) {
                return;
            }
            
            const referrerId = $(this).data('referrer-id');
            const referredId = $(this).data('referred-id');
            
            deleteReferral(referrerId, referredId);
        });
    }

    /**
     * Update level percentages preview
     */
    function updateLevelPercentagesPreview() {
        const percentagesInput = $('#farmfaucet_referral_level_percentages');
        const percentages = percentagesInput.val().split(',');
        const previewContainer = $('#level-percentages-preview');
        
        let html = '<table class="level-percentages-table">';
        html += '<tr><th>Level</th><th>Percentage</th></tr>';
        
        for (let i = 0; i < percentages.length; i++) {
            const level = i + 1;
            const percentage = parseFloat(percentages[i]) || 0;
            
            html += '<tr>';
            html += '<td>Level ' + level + '</td>';
            html += '<td>' + percentage + '%</td>';
            html += '</tr>';
        }
        
        html += '</table>';
        
        previewContainer.html(html);
    }

    /**
     * Update referral status
     * 
     * @param {number} referrerId Referrer user ID
     * @param {number} referredId Referred user ID
     * @param {string} status New status (active, inactive)
     */
    function updateReferralStatus(referrerId, referredId, status) {
        // Send AJAX request
        $.ajax({
            url: farmfaucetReferralAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_update_referral_status',
                nonce: farmfaucetReferralAdmin.nonce,
                referrer_id: referrerId,
                referred_id: referredId,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    // Reload page
                    window.location.reload();
                } else {
                    alert(response.data.message || farmfaucetReferralAdmin.i18n.error);
                }
            },
            error: function() {
                alert(farmfaucetReferralAdmin.i18n.error);
            }
        });
    }

    /**
     * Delete referral
     * 
     * @param {number} referrerId Referrer user ID
     * @param {number} referredId Referred user ID
     */
    function deleteReferral(referrerId, referredId) {
        // Send AJAX request
        $.ajax({
            url: farmfaucetReferralAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'farmfaucet_delete_referral',
                nonce: farmfaucetReferralAdmin.nonce,
                referrer_id: referrerId,
                referred_id: referredId
            },
            success: function(response) {
                if (response.success) {
                    // Reload page
                    window.location.reload();
                } else {
                    alert(response.data.message || farmfaucetReferralAdmin.i18n.error);
                }
            },
            error: function() {
                alert(farmfaucetReferralAdmin.i18n.error);
            }
        });
    }

})(jQuery);
