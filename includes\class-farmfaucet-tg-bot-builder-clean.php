<?php

/**
 * Telegram Bot Builder Class
 *
 * This class handles the Telegram Bot Builder functionality.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Telegram Bot Builder Class
 */
class Farmfaucet_Tg_Bot_Builder_Clean
{
    /**
     * Initialize the class
     */
    public static function init()
    {
        // Add admin menu
        add_action('admin_menu', [__CLASS__, 'add_admin_menu']);

        // Register settings
        add_action('admin_init', [__CLASS__, 'register_settings']);

        // Enqueue admin assets
        add_action('admin_enqueue_scripts', [__CLASS__, 'enqueue_admin_assets']);
    }

    /**
     * Add admin menu
     */
    public static function add_admin_menu()
    {
        // Add submenu page
        add_submenu_page(
            'farmfaucet',
            __('Telegram Bot Builder', 'farmfaucet'),
            __('Telegram Bot Builder', 'farmfaucet'),
            'manage_options',
            'farmfaucet-tg-bot-builder',
            [__CLASS__, 'render_admin_page']
        );
    }

    /**
     * Register settings
     */
    public static function register_settings()
    {
        // Register settings
        register_setting('farmfaucet_settings', 'farmfaucet_tg_bot_builder_enabled', [
            'sanitize_callback' => 'intval',
            'default' => 1
        ]);
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook Current admin page
     */
    public static function enqueue_admin_assets($hook)
    {
        // Only load on plugin settings page
        if (strpos($hook, 'farmfaucet-settings') === false) {
            return;
        }

        // Enqueue jQuery UI
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-effects-core');

        // Enqueue jQuery UI CSS
        wp_enqueue_style(
            'jquery-ui-css',
            'https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css',
            [],
            '1.13.2'
        );

        // Enqueue jsPlumb for the flow builder
        wp_enqueue_script(
            'jsplumb',
            'https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js',
            ['jquery'],
            '2.15.6',
            true
        );

        // Enqueue SortableJS for drag and drop
        wp_enqueue_script(
            'sortable',
            'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
            [],
            '1.15.0',
            true
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            [],
            '6.4.0'
        );

        // Enqueue Bot Builder CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-modern.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our simple fix CSS
        wp_enqueue_style(
            'farmfaucet-telegram-bot-builder-fix',
            FARMFAUCET_URL . 'assets/css/telegram-bot-builder-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Add our button fix CSS
        wp_enqueue_style(
            'farmfaucet-tg-bot-builder-button-fix',
            FARMFAUCET_URL . 'assets/css/tg-bot-builder-button-fix.css',
            [],
            FARMFAUCET_VERSION . '.' . time() // Add timestamp to force cache refresh
        );

        // Enqueue Bot Builder JS
        if (isset($_GET['page']) && $_GET['page'] === 'farmfaucet-tg-bot-builder') {
            // Use the new Bot Builder JS for the dedicated page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder-new',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder-new.js',
                ['jquery'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );
        } else {
            // Use the standard Bot Builder JS for the settings page
            wp_enqueue_script(
                'farmfaucet-tg-bot-builder',
                FARMFAUCET_URL . 'assets/js/tg-bot-builder.js',
                ['jquery'],
                FARMFAUCET_VERSION . '.' . time(), // Add timestamp to force cache refresh
                true
            );
        }
    }

    /**
     * Render admin page
     */
    public static function render_admin_page()
    {
        // Include the fixed modern bot builder template
        include(FARMFAUCET_DIR . 'templates/admin/bot-builder-modern-fixed.php');
    }
}
