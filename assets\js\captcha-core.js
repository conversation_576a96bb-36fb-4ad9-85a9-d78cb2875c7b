/**
 * Core captcha handling for Farm Faucet
 * This file contains the core functionality for captcha handling
 */
(function($) {
    'use strict';

    // Store captcha widgets by ID
    window.farmfaucetCaptchaWidgets = {};

    // Flag to track if captchas have been initialized
    window.farmfaucetCaptchasInitialized = false;

    // Define global callback functions
    window.hCaptchaCallback = function(token) {
        console.log('hCaptcha callback called with token:', token ? token.substring(0, 10) + '...' : 'empty');
        $('.farmfaucet-captcha-loading').remove();
        $('.farmfaucet-claim-btn').prop('disabled', false).attr('aria-disabled', 'false');
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
    };

    window.hCaptchaExpiredCallback = function() {
        console.log('hCaptcha expired callback called');
        $('.farmfaucet-claim-btn').prop('disabled', true).attr('aria-disabled', 'true');
        $('.pre-captcha-status').show();
        $('.post-captcha-status').hide();
    };

    window.hCaptchaErrorCallback = function(err) {
        console.error('hCaptcha error callback called:', err);
        $('.farmfaucet-captcha-loading').html('Error loading captcha. Please refresh the page.').addClass('farmfaucet-error');
    };

    window.recaptchaCallback = function(token) {
        console.log('reCAPTCHA callback called with token:', token ? token.substring(0, 10) + '...' : 'empty');
        $('.farmfaucet-captcha-loading').remove();
        $('.farmfaucet-claim-btn').prop('disabled', false).attr('aria-disabled', 'false');
        $('.pre-captcha-status').hide();
        $('.post-captcha-status').show();
    };

    window.recaptchaExpiredCallback = function() {
        console.log('reCAPTCHA expired callback called');
        $('.farmfaucet-claim-btn').prop('disabled', true).attr('aria-disabled', 'true');
        $('.pre-captcha-status').show();
        $('.post-captcha-status').hide();
    };

    window.recaptchaErrorCallback = function(err) {
        console.error('reCAPTCHA error callback called:', err);
        $('.farmfaucet-captcha-loading').html('Error loading captcha. Please refresh the page.').addClass('farmfaucet-error');
    };

    // Function to initialize hCaptcha
    window.farmfaucetHcaptchaLoad = function() {
        console.log('hCaptcha load callback triggered');

        // Make sure hCaptcha is available
        if (typeof hcaptcha === 'undefined') {
            console.error('hCaptcha library not loaded yet');
            return false;
        }

        console.log('hCaptcha library loaded successfully');

        // Find all hCaptcha elements
        $('.h-captcha.farmfaucet-captcha').each(function() {
            try {
                const captchaId = $(this).attr('id');
                const sitekey = $(this).data('sitekey');

                if (!captchaId || !sitekey) {
                    console.error('Missing captcha ID or sitekey');
                    return;
                }

                console.log('Rendering hCaptcha for', captchaId);

                // Remove loading message
                $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading').remove();

                // Make sure the element is visible
                $(this).css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Render the captcha
                try {
                    window.farmfaucetCaptchaWidgets[captchaId] = hcaptcha.render(captchaId, {
                        sitekey: sitekey,
                        theme: 'light',
                        size: 'normal',
                        callback: window.hCaptchaCallback,
                        'expired-callback': window.hCaptchaExpiredCallback,
                        'error-callback': window.hCaptchaErrorCallback
                    });

                    console.log('hCaptcha rendered successfully for', captchaId);
                } catch (e) {
                    console.error('Error rendering hCaptcha:', e);
                }
            } catch (e) {
                console.error('Error initializing hCaptcha:', e);
            }
        });

        return true;
    };

    // Function to initialize reCAPTCHA
    window.farmfaucetRecaptchaLoad = function() {
        console.log('reCAPTCHA load callback triggered');

        // Make sure reCAPTCHA is available
        if (typeof grecaptcha === 'undefined' || typeof grecaptcha.render !== 'function') {
            console.error('reCAPTCHA library not loaded yet');
            return false;
        }

        console.log('reCAPTCHA library loaded successfully');

        // Find all reCAPTCHA elements
        $('.g-recaptcha.farmfaucet-captcha').each(function() {
            try {
                const captchaId = $(this).attr('id');
                const sitekey = $(this).data('sitekey');

                if (!captchaId || !sitekey) {
                    console.error('Missing captcha ID or sitekey');
                    return;
                }

                console.log('Rendering reCAPTCHA for', captchaId);

                // Remove loading message
                $(this).closest('.farmfaucet-captcha-container').find('.farmfaucet-captcha-loading').remove();

                // Make sure the element is visible
                $(this).css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Render the captcha
                try {
                    window.farmfaucetCaptchaWidgets[captchaId] = grecaptcha.render(captchaId, {
                        'sitekey': sitekey,
                        'theme': 'light',
                        'size': 'normal',
                        'callback': window.recaptchaCallback,
                        'expired-callback': window.recaptchaExpiredCallback,
                        'error-callback': window.recaptchaErrorCallback
                    });

                    console.log('reCAPTCHA rendered successfully for', captchaId);
                } catch (e) {
                    console.error('Error rendering reCAPTCHA:', e);
                }
            } catch (e) {
                console.error('Error initializing reCAPTCHA:', e);
            }
        });

        return true;
    };

    // Main function to initialize captchas
    window.farmfaucetInitCaptchas = function() {
        console.log('Initializing captchas');

        // Check if captchas are already initialized
        if (window.farmfaucetCaptchasInitialized) {
            console.log('Captchas already initialized, skipping');
            return;
        }

        // Get captcha type from localized vars
        const captchaType = window.farmfaucet_vars ? window.farmfaucet_vars.captcha_type : 'hcaptcha';
        console.log('Active captcha type:', captchaType);

        // Make sure captcha containers are visible
        $('.farmfaucet-captcha-container').css({
            'display': 'flex',
            'visibility': 'visible',
            'opacity': '1'
        });

        // Initialize the appropriate captcha type
        if (captchaType === 'hcaptcha') {
            if (window.farmfaucetHcaptchaLoad()) {
                window.farmfaucetCaptchasInitialized = true;
            }
        } else if (captchaType === 'recaptcha') {
            if (window.farmfaucetRecaptchaLoad()) {
                window.farmfaucetCaptchasInitialized = true;
            }
        }
    };

    // Initialize captchas when the page is fully loaded
    $(document).ready(function() {
        console.log('Document ready, initializing captchas');

        // Small delay to ensure everything is ready
        setTimeout(function() {
            window.farmfaucetInitCaptchas();
        }, 1000);
    });

    // Also initialize captchas when the window is fully loaded
    $(window).on('load', function() {
        console.log('Window loaded, initializing captchas');

        // Small delay to ensure everything is ready
        setTimeout(function() {
            window.farmfaucetInitCaptchas();

            // Check if captchas are still not initialized after a longer delay
            setTimeout(function() {
                // If loading messages are still visible, try direct rendering
                if ($('.farmfaucet-captcha-loading').length > 0) {
                    console.log('Captchas still not initialized, trying direct rendering');

                    // Try direct rendering for hCaptcha
                    if (window.farmfaucet_vars.captcha_type === 'hcaptcha' && typeof hcaptcha !== 'undefined') {
                        $('.h-captcha').each(function() {
                            try {
                                const captchaId = $(this).attr('id');
                                if (captchaId && !window.farmfaucetCaptchaWidgets[captchaId]) {
                                    console.log('Direct rendering hCaptcha for', captchaId);
                                    window.farmfaucetCaptchaWidgets[captchaId] = hcaptcha.render(captchaId, {
                                        sitekey: window.farmfaucet_vars.hcaptcha_sitekey,
                                        callback: window.hCaptchaCallback
                                    });
                                }
                            } catch (e) {
                                console.error('Error in direct hCaptcha rendering:', e);
                            }
                        });
                    }

                    // Try direct rendering for reCAPTCHA
                    if (window.farmfaucet_vars.captcha_type === 'recaptcha' && typeof grecaptcha !== 'undefined') {
                        $('.g-recaptcha').each(function() {
                            try {
                                const captchaId = $(this).attr('id');
                                if (captchaId && !window.farmfaucetCaptchaWidgets[captchaId]) {
                                    console.log('Direct rendering reCAPTCHA for', captchaId);
                                    window.farmfaucetCaptchaWidgets[captchaId] = grecaptcha.render(captchaId, {
                                        'sitekey': window.farmfaucet_vars.recaptcha_sitekey,
                                        'callback': window.recaptchaCallback
                                    });
                                }
                            } catch (e) {
                                console.error('Error in direct reCAPTCHA rendering:', e);
                            }
                        });
                    }

                    // Remove loading messages
                    $('.farmfaucet-captcha-loading').remove();
                }
            }, 5000); // Wait 5 seconds before trying direct rendering
        }, 1000);
    });

})(jQuery);
